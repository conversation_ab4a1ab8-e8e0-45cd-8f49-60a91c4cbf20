package blockChain.service;

import com.ctsi.pubinfo.digest.DigestMD5Util;
import com.ctsi.ssdc.util.Code2PicUtil;
import com.ctsi.ssdc.util.date.DateTime;
import lombok.AllArgsConstructor;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

@Service
@AllArgsConstructor
public class BiyiService {

  public String apiRequest(String body,String id,String secret,String url,String
    userName){
//对请求体进行 sha256 加密
    String digest = encryptPassword(body);
//GMT 格式时间
    Date date=new Date();
    DateFormat format=new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
    format.setTimeZone(TimeZone.getTimeZone("GMT"));
    String hdate = format.format(date);
/**
 * 生成签名的内容 content */
    StringBuilder stb = new StringBuilder();
    String content = stb.append("x-date: ").append(hdate).append("\n").append("id: ").append(id).append("\n").append("digest: SHA-256=").append(digest).toString();
    String signature = null;
    try {
      signature = new String(Base64.getEncoder().encode(signatureReturnBytes(content, secret)), "US-ASCII");
    } catch (Exception e) {
      e.printStackTrace();
    }
    return httpClient(url,hdate,id,signature,body,userName,digest);
  }
  /**
   * http 请求时需要在请求头中添加认证相关信息
   */
  private String httpClient(String url,String date,String id,String signature,String
    body,String userName,String digest){
    String result = "";
    CloseableHttpClient httpClient = HttpClients.createDefault();
    try{
      HttpPost httpPost = new HttpPost(url);
      //HttpHost proxy = new HttpHost("*************", 7777, "http");
      RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(200000).setConnectTimeout(200000)
        //.setProxy(proxy)
        .build();
      httpPost.setConfig(requestConfig);
      httpPost.setHeader("Content-Type","application/json");
      httpPost.setHeader("Authorization","hmac username=\""+userName+"\", algorithm=\"hmac-sha256\", headers=\"x-date id digest\", signature="+"\""+signature+"\"");
      httpPost.setHeader("x-date",date);
      httpPost.setHeader("Id",id);
      httpPost.setHeader("Digest","SHA-256="+digest);
      StringEntity stringEntity = new StringEntity(body, "UTF-8");
      httpPost.setEntity(stringEntity);
      HttpResponse response = httpClient.execute(httpPost);
      HttpEntity entity = response.getEntity();
      result = EntityUtils.toString(entity);
    }catch (Exception e){
      e.printStackTrace();
    }
    return result;
  }
  private byte[] signatureReturnBytes(String data, String key) throws
    NoSuchAlgorithmException, InvalidKeyException {
    SecretKeySpec signingKey = new SecretKeySpec(key.getBytes(), "HmacSHA256");
    Mac mac = Mac.getInstance("HmacSHA256");
    mac.init(signingKey);
    return mac.doFinal(data.getBytes());
  }
  private String encryptPassword(String password){
    byte[] hashBytes = sha(password);
    return Base64.getEncoder().encodeToString(hashBytes);
  }
  private byte[] sha(final String strText) {
    if (strText != null && strText.length() > 0) {
      try {
        MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
        messageDigest.update(strText.getBytes());
        return messageDigest.digest();
      } catch (NoSuchAlgorithmException e) {
        return null;
      }
    }else{
      return null;
    }
  }

  public String GetMD5Str(String data)
  {
    try {
      return DigestMD5Util.getMD5Str(data);
    }
    catch (Exception e)
    {
      e.printStackTrace();
    }
    return  "";
  }

  public String GetWeekOfYear()
  {
    try {
      return String.valueOf(DateTime.now().getWeekOfYear());
    }
    catch (Exception e)
    {
      e.printStackTrace();
    }
    return  "";
  }

  public java.awt.image.BufferedImage StringtoQRcode(String data)
  {
    try
    {
      return Code2PicUtil.generateCode2Pic(data);
    }
    catch (Exception e)
    {
      e.printStackTrace();
    }
    return null;
  }
}
