package blockChain.controller;

import blockChain.bean.BaseResponseDto;
import blockChain.bean.PageQuery;
import blockChain.bean.PageResponse;
import blockChain.bean.ResultCodeResponse;
import blockChain.dto.message.MessageDto;
import blockChain.dto.message.MessageDtoQueryParam;
import blockChain.facade.service.MessageServiceFacade;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019/12/19 10:51
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("message")
public class MessageController {

  private final MessageServiceFacade messageServiceFacade;

  //@PreAuthorize("hasAnyAuthority('"+ AuthoritiesConstants.MEMBER_CREATE+"')")
  @ApiOperation(value = "创建", nickname = "createMessage", notes = "创建全体消息非消耗大量资源，不要引导用户创建全体消息", tags = {"站内消息（管理）",})
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class)})
  @PostMapping(value = "create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<BaseResponseDto> create(@RequestBody MessageDto params) {
    messageServiceFacade.sendMessage(params);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  @ApiOperation(value = "删除", nickname = "deleteMessage", notes = "删除消息", tags = {"站内消息（管理）",})
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class)})
  @PostMapping(value = "remove", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<BaseResponseDto> deleteMessage(@RequestBody MessageDto params) {
    Assert.notNull(params.getUuid(), "请选择要删除的消息");
    messageServiceFacade.deleteMessage(params.getUuid());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }

  @ApiOperation(value = "查询", nickname = "queryMessage", notes = "query  `Message`s.", tags = {"站内消息（管理）",})
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class)})
  @PostMapping(value = "query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<PageResponse<MessageDto>> findPage(@RequestBody MessageDtoQueryParam pageQuery) {
    PageResponse<MessageDto> page = messageServiceFacade.findPage(pageQuery);
    return ResponseEntity.ok(page);
  }

  @ApiOperation(value = "获取一个详情", nickname = "getOneMessage", notes = "query  `Message`s.", tags = {"站内消息（管理）",})
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class)})
  @PostMapping(value = "getOne", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<MessageDto> getOneMessage(@RequestBody MessageDto pageQuery) {
    MessageDto page = messageServiceFacade.getOneMessage(pageQuery.getUuid());
    return ResponseEntity.ok(page);
  }

  @ApiOperation(value = "更新", nickname = "updateMessage", notes = "update  `Message`s.", tags = {"站内消息（管理）",})
  @ApiResponses(value = {
    @ApiResponse(code = 202, message = "Successful response.", response = BaseResponseDto.class)})
  @PostMapping(value = "update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<BaseResponseDto> updateMessage(@RequestBody MessageDto dto) {
    messageServiceFacade.updateMessage(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  @ApiOperation(value = "查询我的站内消息", nickname = "findPageByUserId", notes = "查询我的站内消息", tags = {"站内消息（个人用户）",})
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class)})
  @PostMapping(value = "personal/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<PageResponse<MessageDto>> findPageByUserId(@RequestBody MessageDtoQueryParam pageQuery) {
    PageResponse<MessageDto> page = messageServiceFacade.findPageByUserId(pageQuery);
    return ResponseEntity.ok(page);
  }

  @ApiOperation(value = "查询我的未读站内消息数量", nickname = "countUnreadSize", notes = "查询我的站内消息", tags = {"站内消息（个人用户）",})
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class)})
  @PostMapping(value = "personal/unread", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<BaseResponseDto<Long>> countUnreadSize() {
    Long size = messageServiceFacade.countUnreadSize();
    return BaseResponseDto.ok(size);
  }

  @ApiOperation(value = "标记消息已读", nickname = "read", notes = "标记我的站内消息已读", tags = {"站内消息（个人用户）",})
  @ApiResponses(value = {
    @ApiResponse(code = 202, message = "Successful response.", response = BaseResponseDto.class)})
  @PostMapping("read")
  public ResponseEntity<BaseResponseDto> read(@RequestBody MessageDto params) {
    messageServiceFacade.readMessage(params.getUuid());
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }
}
