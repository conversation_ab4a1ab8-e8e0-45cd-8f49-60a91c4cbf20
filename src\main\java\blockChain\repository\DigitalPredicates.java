package blockChain.repository;

import blockChain.entities.CopyrightManager;
import blockChain.entities.QCopyrightManager;
import blockChain.entities.QDigital;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 此文件下定义多个查询谓词，谓词相当于SQL中的Where
 *
 * <AUTHOR>
 * @date 2019/6/6 17:31
 */
public final class DigitalPredicates {
  public static Predicate digitalQueryByCode(String code, Integer enable) {
    QDigital digital = QDigital.digital;

    BooleanBuilder builder = new BooleanBuilder();

    if (!StringUtils.isEmpty(code)) {
      builder.and(digital.code.eq(code));
    }
    if (enable != null) {
      builder.and(digital.enable.eq(enable));
    }

    return builder;
  }

  public static Predicate digitalQueryByCode(String code, Integer enable, LocalDateTime startTime, LocalDateTime endTime) {
    QDigital digital = QDigital.digital;
    QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;
    BooleanBuilder builder = new BooleanBuilder();

    if (!StringUtils.isEmpty(code)) {
      builder.and(digital.code.eq(code));
    }
    if (enable != null) {
      builder.and(digital.enable.eq(enable));
    }
    if(startTime!=null){
      builder.and(copyrightManager.certificateCreateTime.after(startTime).or(copyrightManager.certificateCreateTime.eq(startTime)));
    }
    if(endTime!=null){
      builder.and(copyrightManager.certificateCreateTime.before(endTime).or(copyrightManager.certificateCreateTime.eq(endTime)));
    }
    builder.and(copyrightManager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE));
    builder.and(copyrightManager.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
    builder.and(copyrightManager.status_type.eq(CopyrightManager.CERT_CREATED));

    return builder;
  }

  public static Predicate digitalQueryByPid(Integer pid, Integer enable) {
    QDigital digital = QDigital.digital;

    BooleanBuilder builder = new BooleanBuilder();

    if (pid != null) {
      builder.and(digital.pid.eq(pid));
    }
    if (enable != null) {
      builder.and(digital.enable.eq(enable));
    }

    return builder;
  }

  public static Predicate digitalQueryByPid(Integer pid, Integer enable, LocalDateTime startTime,LocalDateTime endTime) {
    QDigital digital = QDigital.digital;
    QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;

    BooleanBuilder builder = new BooleanBuilder();

    if (pid != null) {
      builder.and(digital.pid.eq(pid));
    }
    if (enable != null) {
      builder.and(digital.enable.eq(enable));
    }

    if(startTime!=null){
      builder.and(copyrightManager.certificateCreateTime.after(startTime).or(copyrightManager.certificateCreateTime.eq(startTime)));
    }
    if(endTime!=null){
      builder.and(copyrightManager.certificateCreateTime.before(endTime).or(copyrightManager.certificateCreateTime.eq(endTime)));
    }
    builder.and(copyrightManager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE));
    builder.and(copyrightManager.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
    builder.and(copyrightManager.status_type.eq(CopyrightManager.CERT_CREATED));

    return builder;
  }

  /*public static Predicate digitalQueryByPidAndArea(Integer pid,List<String> area, Integer enable) {
    QDigital digital = QDigital.digital;

    BooleanBuilder builder = new BooleanBuilder();

    if (pid != null) {
      builder.and(digital.pid.eq(pid));
    }
    if (enable != null) {
      builder.and(digital.enable.eq(enable));
    }

    if (area != null && area.size() > 0) {
      for (int i = area.size()-1; i >=0; i--) {
        // 从省开始
        if(area.get(i)!=null && area.get(i)!=""){
          builder.and(digital.id.eq(Integer.parseInt(area.get(i))));
          break;
        }
      }
    }

    return builder;
  }*/


  public static Predicate digitalQueryByPidAndCodeNotEq(Integer pid, Integer enable, String code) {
    QDigital digital = QDigital.digital;
    Predicate predicate = digitalQueryByPid(pid, enable);
    BooleanBuilder builder = new BooleanBuilder(predicate);
    builder.and(digital.code.ne(code));
    return builder;
  }

  public static Predicate digitalQueryByPidAndCodeNotEq(Integer pid, Integer enable, String code, LocalDateTime startTime,LocalDateTime endTime) {
    QDigital digital = QDigital.digital;
    QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;
    Predicate predicate = digitalQueryByPid(pid, enable);
    BooleanBuilder builder = new BooleanBuilder(predicate);
    builder.and(digital.code.ne(code));
    if(startTime!=null){
      builder.and(copyrightManager.certificateCreateTime.after(startTime).or(copyrightManager.certificateCreateTime.eq(startTime)));
    }
    if(endTime!=null){
      builder.and(copyrightManager.certificateCreateTime.before(endTime).or(copyrightManager.certificateCreateTime.eq(endTime)));
    }
//    builder.and(copyrightManager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE));
    builder.and(copyrightManager.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
    builder.and(copyrightManager.status_type.eq(CopyrightManager.CERT_CREATED));
    return builder;
  }
  public static Predicate digitalQueryByDict_Name(String dictName, Integer enable) {
    QDigital digital = QDigital.digital;

    BooleanBuilder builder = new BooleanBuilder();

    if (!StringUtils.isEmpty(dictName)) {
      builder.and(digital.dict_name.eq(dictName));
    }
    if (enable != null) {
      builder.and(digital.enable.eq(enable));
    }

    return builder;
  }

}
