package blockChain.facade.service;

import blockChain.bean.Evaluate.EvaluateRequest;
import blockChain.bean.Evaluate.GetSaveEvaluationBean;
import blockChain.bean.Evaluate.GetServiceListBean;
import blockChain.config.GatewayAutoConfiguration;
import blockChain.config.SpringConfig;
import blockChain.dto.StatisticDto;
import blockChain.entities.*;
import blockChain.repository.CopyrightManagerRepository;
import blockChain.repository.EvaluationRepository;
import blockChain.service.CopyrightManagerService;
import blockChain.service.UserService;
import blockChain.utils.MD5Util;
import blockChain.utils.SM4;
import blockChain.utils.XMLUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.axis.client.Call;
import org.apache.axis.message.SOAPHeaderElement;
import org.json.JSONObject;
import org.json.XML;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static blockChain.entities.QCopyrightManager.copyrightManager;
import static blockChain.entities.QEvaluateEntity.evaluateEntity;

/**
 * <AUTHOR>
 * @date 2023/10/16
 */
@Service
@AllArgsConstructor
@Slf4j
public class EvaluationServiceFacade {
    private final CopyrightManagerService copyrightManagerService;
    private final UserService userService;

    private final EvaluationRepository evaluationRepository;
    private final CopyrightManagerRepository copyrightManagerRepository;

    private final SpringConfig config;

    private final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public boolean getServiceList() {
        try {
            if (GatewayAutoConfiguration.getBusinessCode().length() > 0 && GatewayAutoConfiguration.getBusinessName().length() > 0) {
                return true;
            }
            GetServiceListBean getServiceListBean = new GetServiceListBean();
            getServiceListBean.setCreditCode(GatewayAutoConfiguration.getCreditCode());
            getServiceListBean.setSysNO(GatewayAutoConfiguration.getSysCode());
            //String xmlGetServiceListBean = XMLUtil.convertToXml(getServiceListBean);
            String xmlGetServiceListBean = getServiceListBean.toXmlString();

            String endpoint = config.getEvaluationServiceWsdl();
            org.apache.axis.client.Service service = new org.apache.axis.client.Service();
            Call call = (Call) service.createCall();
            SOAPHeaderElement head = new SOAPHeaderElement(config.getEvaluationNamespace(), "Authorization", "Basic " + GatewayAutoConfiguration.getToken());
            call.addHeader(head);
            call.setTargetEndpointAddress(endpoint);
            call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);
            call.addParameter("appKey", org.apache.axis.encoding.XMLType.XSD_STRING, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.addParameter("xmlStr", org.apache.axis.encoding.XMLType.XSD_STRING, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.setOperationName("getServiceListByCreditCode");// WSDL里面描述的接口名称   newSubmit     submit
            String md5 = MD5Util.md5(GatewayAutoConfiguration.getAppCode());//对密钥进行MD5
            String str = new SM4().encode(xmlGetServiceListBean, md5);//sm4对数据加密
            //对接方传递公钥给我方，我方会根据对接方的公钥查询出密钥对数据解密
            str = str.replaceAll("[\\n\\r]", "");
            String appKey = GatewayAutoConfiguration.getAppKey();//公钥

            String xmlStr = str;
            String a = (String) call.invoke(new Object[]{appKey, xmlStr});
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            DocumentBuilder db = dbf.newDocumentBuilder();
            InputSource is = new InputSource(new StringReader(a));
            Document document = db.parse(is);
            NodeList caseNodeList = document.getChildNodes().item(0).getChildNodes();
            if (document.getElementsByTagName("code") != null
                    && !document.getElementsByTagName("code").item(0).getFirstChild().getTextContent().equals("200")) {
                log.info("【evaluate gateway return ERROR】: " + a);
                return false;
            }
            for (int i = 0; i < caseNodeList.getLength(); i++) {
                Node dataNode = caseNodeList.item(i);
                if (dataNode.getNodeName().equals("data")) {
                    NodeList servicesList = dataNode.getChildNodes().item(0).getChildNodes();
                    for (int j = 0; j < servicesList.getLength(); j++) {
                        String infoprojid = "";
                        String servicename = "";
                        Node node = servicesList.item(j);
                        NodeList valueNodes = node.getChildNodes();
                        for (int k = 0; k < valueNodes.getLength(); k++) {
                            Node valueNode = valueNodes.item(k);
                            try {
                                //System.out.println(valueNode.getNodeName()+":"+valueNode.getTextContent());
                                if (valueNode.getNodeName().equals("ServiceName")) {
                                    servicename = valueNode.getTextContent();
                                }
                                if (valueNode.getNodeName().equals("Infoprojid")) {
                                    infoprojid = valueNode.getTextContent();
                                }
                            } catch (Exception e) {

                            }
                        }
                        if (servicename.contains("作品自愿登记")) {
                            GatewayAutoConfiguration.setBusinessCode(infoprojid);
                            GatewayAutoConfiguration.setBusinessName(servicename);
                            log.info("get businessCode:" + GatewayAutoConfiguration.getBusinessCode() + "  get businessName:" + GatewayAutoConfiguration.getBusinessName());
                            return true;
                        }
                    }
                }
            }
            return false;
        } catch (Exception e) {
            log.error("error", e);
        }
        return false;
    }

    public boolean beforeRequest() {
        if (GatewayAutoConfiguration.getToken().length() == 0) {
            log.error("token get failure!!!");
            return true;
        }
        if (!getServiceList()) {
            log.error("service list get failure!!!");
            return true;
        }
        return false;
    }

    public Map<String, Object> getSaveEvaluation(GetSaveEvaluationBean getSaveEvaluationUrlBean) {
        Map<String, Object> result = new HashMap<>();
        try {
            String endpoint = config.getEvaluationWsdl();
            org.apache.axis.client.Service service = new org.apache.axis.client.Service();
            Call call = (Call) service.createCall();
            SOAPHeaderElement head = new SOAPHeaderElement(config.getEvaluationNamespace(), "Authorization", "Basic " + GatewayAutoConfiguration.getToken());
            call.addHeader(head);
            call.setTargetEndpointAddress(endpoint);
            call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);
            call.addParameter("appKey", org.apache.axis.encoding.XMLType.XSD_STRING, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.addParameter("xmlStr", org.apache.axis.encoding.XMLType.XSD_STRING, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.setOperationName("getSaveEvaluation");// WSDL里面描述的接口名称   newSubmit     submit
            String appKey = GatewayAutoConfiguration.getAppKey();//公钥
            String md5 = MD5Util.md5(GatewayAutoConfiguration.getAppCode());//对密钥进行MD5
            String xmlStr = getSaveEvaluationUrlBean.toXmlString();
            String xmlStrEnc = SM4.getInstance().encode(xmlStr, md5);
            String a = (String) call.invoke(new Object[]{appKey, xmlStrEnc});
            JSONObject object = XML.toJSONObject(a);
            if (object.has("Case")) {
                result = object.getJSONObject("Case").toMap();
            }
        } catch (Exception e) {
            log.error("error", e);
        }
        return result;
    }

    public void autoEvaluation(int startHour, int endHour) {
        if (beforeRequest()) {
            log.error("好差评服务错误！");
        }

        LocalDate today = LocalDate.now();
        String startDay = "2023-01-01 00:00:00";
        LocalDateTime ldt = LocalDateTime.parse(startDay, df);
        Integer autoEvaluationDay = config.getAutoEvaluationDay();

        boolean flag = true;
        while (flag) {
            int now = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
            // 自动提交, {8:00-20:00}点之间，暂停自动推送
            if (startHour > endHour)
                if (now >= endHour && now < startHour) {
                    flag = false;
                    continue;
                }

            List<CopyrightManager> copyDatas = copyrightManagerService.findAll(copyrightManager.status_type.eq(CopyrightManager.CERT_CREATED)
                    .and(copyrightManager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE))
                    .and(copyrightManager.inactiveType.eq(CopyrightManager.InactiveTypeValue.NORMAL))
                    .and(copyrightManager.licenseState.eq(CopyrightManager.LicenseState.REPORTED))
                    .and(copyrightManager.certificateCreateTime.before(today.minusDays(autoEvaluationDay).atStartOfDay()))
                    .and(copyrightManager.certificateCreateTime.after(ldt))
                    .and(copyrightManager.snStatus.eq(CopyrightManager.SnStatus.COMPLETE))
                    .and(copyrightManager.evaluate.id.isNull()), PageRequest.of(0, 500, new Sort(Sort.Direction.DESC, "registrationDate"))).getContent();
            int length = copyDatas.size();
            if (copyDatas.size() == 0) {
                flag = false;
                continue;
            }
            if (copyDatas.size() < 500) flag = false;
            System.out.println("copyData length:" + length);

            if (GatewayAutoConfiguration.getToken().length() == 0) {
                log.error("token get failure!!!");
            }
            EvaluateRequest evaluateUrlRequest = getAutoEvaluateRequest();
            for (CopyrightManager copyrightManager : copyDatas) {
                UserEntity user = userService.getByUserName(copyrightManager.getUserName()).orElseThrow(null);
                if (user == null) continue;

                GetSaveEvaluationBean bean = getSaveEvaluationBean(evaluateUrlRequest, copyrightManager, user);
                Map<String, Object> result = getSaveEvaluation(bean);
                if (result.containsKey("code") && result.get("code").equals(200)) {
                    createEvaluation(evaluateUrlRequest, copyrightManager, user, result);
                } else {
                    log.error("autoEvaluation Error：" + result);
                }
            }
        }
    }

    public long getEvaluationCount(LocalDate day) {
        return evaluationRepository.count(evaluateEntity.assessTime.between(day.atStartOfDay(), day.plusDays(1).atStartOfDay()));
    }

    private EvaluateRequest getAutoEvaluateRequest() {
        EvaluateRequest evaluateUrlRequest = new EvaluateRequest();
        evaluateUrlRequest.setPf(1);
        evaluateUrlRequest.setAlternate(5);
        evaluateUrlRequest.setAppraisald("510");
        evaluateUrlRequest.setAppraisaldnum(1);
        evaluateUrlRequest.setWritingevalua("");
        return evaluateUrlRequest;
    }

    public GetSaveEvaluationBean getSaveEvaluationBean(EvaluateRequest evaluateUrlRequest,
                                                       CopyrightManager copyrightManager,
                                                       UserEntity user) {

        GetSaveEvaluationBean getSaveEvaluationBean = new GetSaveEvaluationBean();
        getSaveEvaluationBean.setPf(evaluateUrlRequest.getPf());
        getSaveEvaluationBean.setAlternate(evaluateUrlRequest.getAlternate());
        getSaveEvaluationBean.setAppraisald(evaluateUrlRequest.getAppraisald());
        getSaveEvaluationBean.setAppraisaldnum(evaluateUrlRequest.getAppraisaldnum());
        getSaveEvaluationBean.setWritingevalua(evaluateUrlRequest.getWritingevalua());
        getSaveEvaluationBean.setAssessTime(LocalDateTime.now().minusMinutes(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        getSaveEvaluationBean.setBusinessCode(GatewayAutoConfiguration.getBusinessCode());
        getSaveEvaluationBean.setBusinessName(GatewayAutoConfiguration.getBusinessName());
        getSaveEvaluationBean.setSysNO(GatewayAutoConfiguration.getSysCode());
        getSaveEvaluationBean.setProjectNo(copyrightManager.getSnCode());
        getSaveEvaluationBean.setProStatus(3);
        // 好差评也需要处理作品名称转义符
        getSaveEvaluationBean.setProjectName(XMLUtil.escapeSpecialCharacters(copyrightManager.getProductionName()));
        getSaveEvaluationBean.setNodeName("证书下发");
        getSaveEvaluationBean.setRegionCode("350000");
        getSaveEvaluationBean.setAcceptDate(copyrightManager.getRegistrationDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        CopyrightOwner copyrightOwner = copyrightManager.getOwnerList().get(0);
        switch (copyrightOwner.getCopyCategory()) {
            case CopyrightOwner.CopyCategoryValue.PEOPLE:
                getSaveEvaluationBean.setUserProp(1);
                break;
            case CopyrightOwner.CopyCategoryValue.COMPANYPEOPLE:
                getSaveEvaluationBean.setUserProp(2);
                break;
            case CopyrightOwner.CopyCategoryValue.GOVERNMENT:
                getSaveEvaluationBean.setUserProp(6);
                break;
            case CopyrightOwner.CopyCategoryValue.INSTITUTION:
                getSaveEvaluationBean.setUserProp(3);
                break;
            case CopyrightOwner.CopyCategoryValue.SOCIALORG:
                getSaveEvaluationBean.setUserProp(4);
                break;
            default:
                getSaveEvaluationBean.setUserProp(9);
                break;
        }
        Agent agent = copyrightManager.getAgentList();
        if (agent != null) {
            getSaveEvaluationBean.setHandleUserName(user.getRealName());

            int userCopyCertificate;// 证件类型
            if (user.getIdentityKind() == 1) {
                userCopyCertificate = user.getCardType();
            } else {
                if (user.getLegalPersionType() == null) {
                    userCopyCertificate = CopyrightOwner.CopyCertificateValue.OTHER2;
                } else {
                    switch (user.getLegalPersionType()) {
                        case UserEntity.LegalPersionTypeValue.COMPANYPEOPLE:
                        case UserEntity.LegalPersionTypeValue.INDIVIDUAL:
                            userCopyCertificate = CopyrightOwner.CopyCertificateValue.CODE;
                            break;
                        default:
                            userCopyCertificate = CopyrightOwner.CopyCertificateValue.OTHER2;
                            break;
                    }
                }
            }

            getSaveEvaluationBean.setHandleUserPageType(convertIDtype(userCopyCertificate));
            getSaveEvaluationBean.setHandleUserPageCode(user.getCardId());
        }
        getSaveEvaluationBean.setUserName(user.getRealName());
        getSaveEvaluationBean.setUserPageType(convertIDtype(copyrightOwner.getCopyCertificate()));
        getSaveEvaluationBean.setCertKey(copyrightOwner.getCopyIdCard());
        getSaveEvaluationBean.setResultDate(copyrightManager.getCertificateCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        getSaveEvaluationBean.setServiceTime(getSaveEvaluationBean.getAssessTime());
        getSaveEvaluationBean.setEvaluateType(1);
        getSaveEvaluationBean.setType(1);
        getSaveEvaluationBean.setContactMobile(user.getPhoneNum());

        return getSaveEvaluationBean;
    }

    //从我们系统的证件类型转换为好差评证件类型
    public String convertIDtype(int managerType) {
        switch (managerType) {
            case 142://身份证
                return ("111");
            case 156://居住证
                return ("154");
            case 451://其他有效身份证件
            case 449://护照
                return ("999");

            case 145://统一社会信用代码(营业执照)
                return ("001");
            case 461://社会团体法人登记证书
            case 462://事业单位统一社会信用代码证书
            case 463://事业单位法人证书
            case 464://其他有效证件
                return ("099");
            case 465://台湾居民来往大陆通行证
                return ("511");
            case 466://港澳居民来往内地通行证
                return ("516");
            default:
                return ("999");
        }
    }

    @Transactional
    public void createEvaluation(EvaluateRequest evaluateUrlRequest, CopyrightManager copyrightManager, UserEntity user, Map<String, Object> result) {
        EvaluateEntity evaluateEntity = new EvaluateEntity();
        evaluateEntity.setManager(copyrightManager);
        evaluateEntity.setAlternate(evaluateUrlRequest.getAlternate());
        evaluateEntity.setAppraisald(evaluateUrlRequest.getAppraisald());
        evaluateEntity.setAppraisaldnum(evaluateUrlRequest.getAppraisaldnum());
        evaluateEntity.setAssessTime(LocalDateTime.now());
        evaluateEntity.setPf(evaluateUrlRequest.getPf());
        evaluateEntity.setWritingevalua(evaluateUrlRequest.getWritingevalua());
        evaluateEntity.setCertificateCreateTime(copyrightManager.getCertificateCreateTime());
        evaluateEntity.setWorksNum(copyrightManager.getWorksNum());
        evaluateEntity.setRealName(user.getRealName());
        if (result.containsKey("data")) {
            evaluateEntity.setData(result.get("data").toString());
        }
        evaluationRepository.save(evaluateEntity);
        copyrightManager.setEvaluate(evaluateEntity);
        copyrightManagerRepository.save(copyrightManager);
    }


    @Async("doSomethingExecutor")
    public void sync() {
        // 手动提交，推送1小时
        int now = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
        autoEvaluation(now, now + 1);
    }

    public List<StatisticDto> countByAlternate(LocalDateTime start, LocalDateTime end) {
        return evaluationRepository.countByAlternate(start, end);
    }
}
