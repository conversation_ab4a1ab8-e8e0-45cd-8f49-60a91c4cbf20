package blockChain.service.Thread;

import blockChain.entities.threads.CertificateCreateEntity;
import blockChain.repository.Thread.CertificateCreateRepository;
import blockChain.service.BaseService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/9/9 16:14
 */
@Service
@AllArgsConstructor
public class CertificateCreateService implements BaseService<CertificateCreateRepository, CertificateCreateEntity, Long> {

  private final CertificateCreateRepository repository;

  @Override
  public CertificateCreateRepository getRepository() {
    return repository;
  }
}
