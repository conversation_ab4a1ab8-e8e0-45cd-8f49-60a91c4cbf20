package blockChain.bean.Evaluate;

import lombok.Data;

import java.math.BigInteger;

/**
 * Created by epcsoft on 2020/8/5.
 */
@Data
public class EvaluateRequest
{
  Long     registrationNum;
  Integer  pf;//	评价渠道（pc端=1，二维码=3
  Integer  alternate;//	整体满意度（1-5）		（值为1和2时，评价详情和文字评价至少一项必填）
  String  appraisald;//	评价详情（勾选的评价详情编号，勾选多个用逗号隔开。例：501,503,504）	String	否
  Integer  appraisaldnum;//	评价详情勾选数量		Appraisald不为空时必填
  String  writingevalua;//	文字评价 	String	否
}
