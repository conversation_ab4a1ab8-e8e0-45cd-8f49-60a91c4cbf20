package blockChain.repository;

import blockChain.entities.message.QuestionAnswerMessageEntity;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/13 9:23
 */
@Repository
public interface QuestionAnswerMessageEntityRepository extends BaseRepository<QuestionAnswerMessageEntity, Long> {
  Optional<QuestionAnswerMessageEntity> findByUuid(String uuid);
}
