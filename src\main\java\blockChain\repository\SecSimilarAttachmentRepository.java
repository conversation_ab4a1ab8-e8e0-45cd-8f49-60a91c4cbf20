package blockChain.repository;

import blockChain.entities.SimilarAttachment;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/25 16:28
 */
@Repository
public interface SecSimilarAttachmentRepository extends BaseRepository<SimilarAttachment, Long> {
  @Query(value = "select a.id,d.copyright_id as attachmentId,u.workUrl as attachmentUrl,a.similar_attachments_id as similarAttachmentId,p.workUrl as similarAttachmentUrl,a.score,m.productionName as similarCopyName,m.status_type as similarStauts " +
          "from tb_similar_attachments a " +
          "LEFT JOIN copyright_attachment d on a.similar_attachments_id=d.attachment_id " +
          "LEFT JOIN tb_attachments u on u.id = a.attachments_id " +
          "LEFT JOIN tb_attachments p on p.id = a.similar_attachments_id " +
          "LEFT JOIN tb_copyrightmanager m on m.registrationNum = d.copyright_id " +
          "WHERE a.attachments_id = ?2  AND d.copyright_id <> ?1 AND m.status_type <> 2 AND m.enable = 0 " +
          "ORDER BY a.score ASC limit 0,10",nativeQuery = true)
  List<Map<String,Object>> getBySimilarAttachmentId(Long copyrightId, Long attachmentId);
}
