package blockChain.controller.statistic;

import blockChain.bean.PageResponse;
import blockChain.dto.NewUserAnalysisDto;
import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.facade.service.UserStatisticServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 用户相关统计数据
 * <AUTHOR>
 * @date 2020/4/3 16:47
 */
@Api("用户相关统计数据")
@Slf4j
@RestController
@RequestMapping("statistic/user")
@AllArgsConstructor
public class UserStatistic {

  private UserStatisticServiceFacade serviceFacade;

  @ApiOperation("用户发布数统计")
  @PostMapping("publish/get")
  public ResponseEntity<PageResponse<StatisticDto>> getUser (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    PageResponse<StatisticDto> companies = serviceFacade.getUser(queryParam);
    return ResponseEntity.ok(companies);
  }

  @ApiOperation(value = "新用户增长分析", notes = "时间一定要按照自然月/年给，不然要出事")
  @PostMapping("new_account_analysis/get")
  public ResponseEntity<NewUserAnalysisDto> getNewUserAnalysis (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    NewUserAnalysisDto dto = serviceFacade.getNewUserAnalysis(queryParam);
    return ResponseEntity.ok(dto);
  }

  @ApiOperation(value = "最近几个周期用户增长柱状图", notes = "按照给定的时间区间，按照自然月/年统计，多给出有个自然月/年数据")
  @PostMapping("recent_user_growth/get")
  public ResponseEntity<PageResponse<StatisticDto>> getRecentUserGrowth (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    PageResponse<StatisticDto> dto = serviceFacade.getRecentUserGrowth(queryParam);
    return ResponseEntity.ok(dto);
  }


  @ApiOperation(value = "用户地域占比", notes = "按地域（市级）分类筛选，非福建省用户归为其它类别，只受时间影响")
  @PostMapping("geography_of_users/get")
  public ResponseEntity<PageResponse<StatisticDto>> getGeographyOfUsers (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    PageResponse<StatisticDto> dto = serviceFacade.getGeographyOfUsers(queryParam);
    return ResponseEntity.ok(dto);
  }


  @ApiOperation(value = "历史用户总数", notes = "")
  @PostMapping("historical_number_of_users/get")
  public ResponseEntity<PageResponse<StatisticDto>> getHistoricalNumberOfUsers (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    PageResponse<StatisticDto> dto = serviceFacade.getHistoricalNumberOfUsers(queryParam);
    return ResponseEntity.ok(dto);
  }
}
