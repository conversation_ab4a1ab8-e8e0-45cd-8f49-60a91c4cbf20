package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name = "tb_dict")
public class Digital {

    public static final int ENABLE = 0;

    public static final int UNENABLE = 1;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer pid;
    private String code;
    private String dict_name;
    private Integer sort;
    private String create_data;
    private Integer enable;
    /**
     * 行政区划编码：例 福州 350100000000
     */
    private String areaCode;
    private Integer level; //字典层级
    private boolean isLeaf; //是否叶子节点
    //创建时间
    @CreatedDate
    private LocalDateTime createTime;
    //更新时间
    @LastModifiedDate
    private LocalDateTime updateTime;
}
