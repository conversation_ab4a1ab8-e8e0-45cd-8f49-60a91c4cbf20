package blockChain.service;

import blockChain.entities.TaskEntity;
import blockChain.exception.EntityNotFoundException;
import blockChain.repository.TaskRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class TaskService {
  @Autowired
  private TaskRepository taskRepository;

  public void save(TaskEntity taskEntity){
    taskRepository.save(taskEntity);
  }

  public TaskEntity findById(Integer id){
    return taskRepository.findById(id).orElseThrow(() -> new EntityNotFoundException("任务不存在，请重新获取"));
  }
}
