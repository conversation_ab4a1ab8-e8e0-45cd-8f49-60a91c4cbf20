package blockChain.controller;

import blockChain.bean.ResultCodeResponse;
import blockChain.dto.UserDto;
import blockChain.dto.user.ChangePasswordParam;
import blockChain.dto.user.UserUpdate;
import blockChain.facade.service.UserServiceFacade;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("personalCenter")
public class PersonalCenterController {

    private final UserServiceFacade userServiceFacade;

    @PostMapping
    @ApiOperation("获取个人信息")
    public UserDto personalInfo(Authentication authentication) {
        return userServiceFacade.findUserById(((CscpUserDetail) authentication.getPrincipal()).getId());
    }

    @ApiOperation("修改基本信息")
    @PostMapping(value = "baseInfo/update")
    public ResponseEntity<Map<String,Object>> update(@RequestBody @Validated UserUpdate userUpdate) {
        Map<String,Object> result = new HashMap<>();
        try {
            userServiceFacade.updateBaseInfo(userUpdate);
            result.put("message","基本信息修改成功");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        }catch (Exception e){
            result.put("message", "操作失败！");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("pwd/update")
    @ApiOperation("更新密码接口")
    public  ResponseEntity<Map<String,Object>> pwUpdate(@ApiParam("更新密码参数") @RequestBody @Validated ChangePasswordParam param) {
        Map<String, Object> result = new HashMap<>();
        try {
            userServiceFacade.passwordUpdate(param);
            result.put("message","更新密码成功");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        }catch (Exception e){
            result.put("message", "操作失败！");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        }
    }
}
