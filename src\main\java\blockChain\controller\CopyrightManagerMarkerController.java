package blockChain.controller;

import blockChain.bean.BaseResponseDto;
import blockChain.dto.CopyrightManagerMarkerDto;
import blockChain.facade.service.CopyrightManagerMarkerServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/14 14:11
 */
@Api("系统公告(强制弹窗类)")
@Slf4j
@RestController
@RequestMapping("cm_marker")
@AllArgsConstructor
public class CopyrightManagerMarkerController {

  private CopyrightManagerMarkerServiceFacade serviceFacade;

  @ApiOperation(value = "批量标星", nickname = "createCopyrightManagerMarker", notes = "Creates a new instance of a `AlertMessage`.", tags={ "标星(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("create")
  public ResponseEntity<BaseResponseDto> createCopyrightManagerMarker(@Valid @RequestBody CopyrightManagerMarkerDto dto) {
    serviceFacade.create(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  @ApiOperation(value = "批量设置侵权", nickname = "createCopyrightManagerMarker", notes = "Creates a new instance of a `AlertMessage`.", tags={ "标星(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("createIsAccusation")
  public ResponseEntity<BaseResponseDto> createIsAccusation(@Valid @RequestBody CopyrightManagerMarkerDto dto) {
    serviceFacade.isAccusationCreate(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }


  @ApiOperation(value = "修改标星", nickname = "updateCopyrightManagerMarker", notes = "update   a new instance of a `AlertMessage`.", tags={ "标星(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("update")
  public ResponseEntity<BaseResponseDto> updateCopyrightManagerMarker(@Valid @RequestBody CopyrightManagerMarkerDto dto) {
    serviceFacade.update(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  @ApiOperation(value = "批量取消标星", nickname = "removeCopyrightManagerMarker", notes = "delete   a new instance of a `AlertMessage`.", tags={ "标星(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("remove")
  public ResponseEntity<BaseResponseDto> removeCopyrightManagerMarker(@Valid @RequestBody CopyrightManagerMarkerDto dto) {
    serviceFacade.delete(dto.getIdList());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }

  @ApiOperation(value = "批量取消侵权", nickname = "removeCopyrightManagerMarker", notes = "delete   a new instance of a `AlertMessage`.", tags={ "标星(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("removeIsAccusation")
  public ResponseEntity<BaseResponseDto> removeIsAccusation(@Valid @RequestBody CopyrightManagerMarkerDto dto) {
    serviceFacade.isAccusationDelete(dto.getIdList());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }

  @ApiOperation(value = "列表侵权操作", nickname = "removeCopyrightManagerMarker", notes = "delete   a new instance of a `AlertMessage`.", tags={ "标星(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("listIsAccusation")
  public ResponseEntity<BaseResponseDto> listIsAccusation(@Valid @RequestBody CopyrightManagerMarkerDto dto) {
    serviceFacade.isAccusationDeleteList(dto.getIdList());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }


  @ApiOperation(value = "设置标签", tags={ "标星(管理)", })
  @ApiResponses(value = {
          @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("saveTags")
  public ResponseEntity<Map<String, Object>> saveTags(@Valid @RequestBody CopyrightManagerMarkerDto dto) {
    return serviceFacade.saveTags(dto);
  }


}
