package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;
import java.sql.Timestamp;

@XmlRootElement
public class OrganizationBean {
	//登记号 长度20
	private String regNum;
	//登记日期
	private Date regDate;
	//作品名称 长度20
	private String productionName;
	//作品类别 NCR_PRODUCTIONTYPE(ID)
	private Integer productionTypeId;
	//著作权人 长度20
	private String owner;
	//著作权人省份 对应字典表NCP_ID_REG_LOCATION（name）
	private String provinceName;
	private Integer province;
	//作品完成日期
	private Timestamp finishTime;
	//首次发表日期
	private Timestamp firstPublishTime;
	//作者姓名
	private String author;
	//申请人姓名
	private String submitter;
	public String getRegNum() {
		return regNum;
	}
	public void setRegNum(String regNum) {
		this.regNum = regNum;
	}
	public Date getRegDate() {
		return regDate;
	}
	public void setRegDate(Date regDate) {
		this.regDate = regDate;
	}
	public String getProductionName() {
		return productionName;
	}
	public void setProductionName(String productionName) {
		this.productionName = productionName;
	}
	public Integer getProductionTypeId() {
		return productionTypeId;
	}
	public void setProductionTypeId(Integer productionTypeId) {
		this.productionTypeId = productionTypeId;
	}
	public String getOwner() {
		return owner;
	}
	public void setOwner(String owner) {
		this.owner = owner;
	}
//	public String getProvinceName() {
//		return provinceName;
//	}
//	public void setProvinceName(String provinceName) {
//		this.provinceName = provinceName;
//	}
	public Integer getProvince() {
		return province;
	}
	public void setProvince(Integer province) {
		this.province = province;
	}
	public Timestamp getFinishTime() {
		return finishTime;
	}
	public void setFinishTime(Timestamp finishTime) {
		this.finishTime = finishTime;
	}
	public Timestamp getFirstPublishTime() {
		return firstPublishTime;
	}
	public void setFirstPublishTime(Timestamp firstPublishTime) {
		this.firstPublishTime = firstPublishTime;
	}
	public String getAuthor() {
		return author;
	}
	public void setAuthor(String author) {
		this.author = author;
	}
	public String getSubmitter() {
		return submitter;
	}
	public void setSubmitter(String submitter) {
		this.submitter = submitter;
	}

	public OrganizationBean(String regNum, Date regDate, String productionName,
			Integer productionTypeId, String owner, String provinceName, Integer province,
			Timestamp finishTime, Timestamp firstPublishTime, String author,
			String submitter) {
		super();
		this.regNum = regNum;
		this.regDate = regDate;
		this.productionName = productionName;
		this.productionTypeId = productionTypeId;
		this.owner = owner;
		this.provinceName = provinceName;
		this.province = province;
		this.finishTime = finishTime;
		this.firstPublishTime = firstPublishTime;
		this.author = author;
		this.submitter = submitter;
	}
	public OrganizationBean(String regNum, Date regDate, String productionName,
			Integer productionTypeId, String owner,  Integer province,
			Timestamp finishTime, Timestamp firstPublishTime, String author,
			String submitter) {
		super();
		this.regNum = regNum;
		this.regDate = regDate;
		this.productionName = productionName;
		this.productionTypeId = productionTypeId;
		this.owner = owner;
		this.province = province;
		this.finishTime = finishTime;
		this.firstPublishTime = firstPublishTime;
		this.author = author;
		this.submitter = submitter;
	}
	public OrganizationBean(){
	}
}
