/*
package blockChain.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Configuration

@EnableJpaRepositories(

  entityManagerFactoryRef = "entityManagerFactoryPrimary",

  transactionManagerRef = "transactionManagerPrimary",

  basePackages = {"blockChain.repository"})

@EnableTransactionManagement
public class PrimaryConfigurer {

  @Primary
  @Bean(name = "entityManagerPrimary")
  public EntityManager entityManager(@Qualifier("entityManagerFactoryPrimary") EntityManagerFactory factory) {
    return factory.createEntityManager();
  }

  @Primary
  @Bean(name = "entityManagerFactoryPrimary")
  public LocalContainerEntityManagerFactoryBean entityManagerFactoryPrimary(EntityManagerFactoryBuilder builder,@Qualifier("primaryDataSource") DataSource dataSource) {
    LocalContainerEntityManagerFactoryBean entityManagerFactory = builder
      .dataSource(dataSource)
      .properties(hibernateProperties())
      .packages("blockChain.entities") //设置实体类所在位置
      .persistenceUnit("primaryPersistenceUnit")
      .build();
    //entityManagerFactory.setJpaProperties(jpaProperties);
    return entityManagerFactory;
  }

  protected Map<String, String> hibernateProperties() {
    return new HashMap<String, String>() {
      {
        put("hibernate.dialect", "org.hibernate.dialect.MySQL57Dialect");
        put("hibernate.hbm2ddl.auto", "update");
        put("hibernate.current_session_context_class", "org.springframework.orm.hibernate5.SpringSessionContext");
      }
    };
  }
  */
/*private Map<String, String> getVendorProperties() {
    Map<String, String> jpaProperties = new HashMap<>(16);
    jpaProperties.put("hibernate.hbm2ddl.auto", "update");
    jpaProperties.put("hibernate.show_sql", env.getProperty("spring.jpa.show-sql"));
    jpaProperties.put("hibernate.format_sql", env.getProperty("spring.jpa.hibernate.format_sql"));
    jpaProperties.put("hibernate.dialect", env.getProperty("spring.jpa.hibernate.primary-dialect"));
    jpaProperties.put("hibernate.current_session_context_class", "org.springframework.orm.hibernate5.SpringSessionContext");
    return jpaProperties;
  }*//*


  @Primary
  @Bean(name = "transactionManagerPrimary")
  public PlatformTransactionManager transactionManagerPrimary(@Qualifier("entityManagerFactoryPrimary") LocalContainerEntityManagerFactoryBean factoryBean) {
    return new JpaTransactionManager(factoryBean.getObject());
  }

}
*/
