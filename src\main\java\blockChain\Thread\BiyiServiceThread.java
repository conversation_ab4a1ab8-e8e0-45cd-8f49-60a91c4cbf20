package blockChain.Thread;

import blockChain.config.BeanContext;
import blockChain.service.BiyiService;
import org.springframework.scheduling.annotation.Async;

public class BiyiServiceThread extends Thread
{
  private static final String URL="https://kong.ai.ctbiyi.com";
  private static final String USER_NAME="liuqiang1";
  private static final String SECRET="7f6e4020e094470f823e3dc326de387b"; //用户的 hmac auth secret
  private String body;
  private String id;
  private String url;

  private BiyiService biyiService;

  public  void TextCorrection(String text){//文本纠错
    if(text==null||text.length()==0)
    {
      return;
    }
    url = URL+"/textCorrection";
    id ="********************************";  //创建的应用 id
    body ="{\"text\":\""+text+"\"}";
    this.start();
  }

  public  void KnowledgeGraphShow(String text){//知识表示
    if(text==null||text.length()==0)
    {
      return;
    }
    url =  URL+"/knowledgeGraphShow";
    id ="ef3c158750144f3cac0cd62c34d4372a";  //创建的应用 id
    body ="{\"entityName\":\""+text+"\"}";
    this.start();
  }

  public  void TextSimilarity(String text1,String text2){//短文本相似度
    if(text1==null||text1.length()==0||text2==null||text2.length()==0)
    {
      return;
    }
    url = URL+"/textSimilarity";
    id ="34f183227bd14a01a992a737ddcf3804";  //创建的应用 id
    body ="{\"text1\":\""+text1+"\",\"text2\":\""+text2+"\"}";
    this.start();
  }

  public  void QaMessage(String text){//常识知识库问答
    if(text==null||text.length()==0)
    {
      return;
    }
    url = URL+"/smartCustomerService/txtQaMessage";
    id ="d684fe44284841649a76d258a797ff06";  //创建的应用 id
    body ="{\"message\":\""+text+"\"}";
    this.start();
  }

  public  void NamedentityRecognition(String text){//命名实体识别
    if(text==null||text.length()==0)
    {
      return;
    }
    url = URL+"/namedentityRecognition";
    id ="f79ab9ac9aab41b589cc58379cf210a0";  //创建的应用 id
    body ="{\"text\":\""+text+"\"}";
    this.start();
  }

  @Async("doSomethingExecutor")
  public void run()
  {
    try {
      this.biyiService= BeanContext.getApplicationContext().getBean(BiyiService.class);
      System.out.println("开始调用比翼线上API：" + url + " 返回：" + biyiService.apiRequest(body, id, SECRET, url, USER_NAME));
    }
    catch (Exception e)
    {
      e.printStackTrace();
    }
  }
}
