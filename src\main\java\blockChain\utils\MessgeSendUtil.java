package blockChain.utils;


import com.mascloud.sdkclient.Client;

public class MessgeSendUtil {
  private static Client client;

  private static String testUrl = "http://112.33.1.13/app/sdk/login";

  private static String officalUrl = "http://mas.ecloud.10086.cn/app/sdk/login";

  private static String userName = "sxsms1";

  private static String password = "sxsms@xu8k";

  public static Boolean identityCheck(){
    client = Client.getInstance();
    Boolean loginresult = client.login(officalUrl,userName, password,"");
    return loginresult;
  }
}
