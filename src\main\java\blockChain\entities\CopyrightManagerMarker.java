package blockChain.entities;

import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/14 12:47
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tb_copyrightmanager_marker")
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
public class CopyrightManagerMarker {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  /**
   * UID
   * 唯一约束
   */
  @Column(unique = true, length = 36)
  @NonNull
  private String uuid;

  @OneToOne(cascade = {CascadeType.DETACH})
  @JoinColumn(name="manager_id", unique=true)
  private CopyrightManager manager;

  /**
   * 标星注释
   */
  @Column(length = 5000)
  private String content;


  @CreatedDate
  private LocalDateTime createTime;

  @LastModifiedDate
  private LocalDateTime updateTime;

}
