package blockChain.repository;

import blockChain.entities.ProcessProgressEntity;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;

import static blockChain.entities.QProcessProgressEntity.processProgressEntity;

/**
 * <AUTHOR>
 * @date 2021/6/24 16:28
 */
public class ProcessProgressRepositoryCustomImpl extends QuerydslRepositorySupport implements ProcessProgressRepositoryCustom {


    public ProcessProgressRepositoryCustomImpl() {
        super(ProcessProgressEntity.class);
    }

    @Override
    public long updateData(String processId, String message, Integer status) {
        return update(processProgressEntity)
                .set(processProgressEntity.state, status)
                .set(processProgressEntity.memo, message)
                .where(processProgressEntity.processId.eq(processId))
                .execute();
    }

}
