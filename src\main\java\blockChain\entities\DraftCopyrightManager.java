package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/4 9:43
 */
@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_draftcopyrightmanager")
public class DraftCopyrightManager {

    public DraftCopyrightManager(Long registrationNum){
      this.registrationNum = registrationNum;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long registrationNum;// 登记号

    private BigInteger worksRegistrationNum;//进入流程的作品，点击保存将作品ID存储起来
    private String worksNum;//作品自增长的分类序列号
    private String productionName;// 作品名称
    private String userName;//作品提交的用户名称
    private String productionTypeDesc;// 作品创作说明
    private String productionTypeId;// 作品类别ID
    private String artWorksId;// 美术类作品类型
    private String opuseDesc;// 作品类别说明
    @JoinColumn(name="authorIdCar_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment authorIdCar;// 作者正面身份证
    @JoinColumn(name="copyIdCardZM_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment copyIdCardZM;// 著作权人正面身份证
    private String copyTRPermit;// 著作权人暂住证
    private int applyType;// 办理方式
    private String opusInditekind;// 作品创作性质ID

    private String otherOpusInditekind;//NEW ADD/作品创作性质，其他
    private String otherObtainMode;//NEW ADD/权利取得方式：其他获取方式
    private String copyrightCountries;//NEW ADD/著作权人地址
    private String   copyrightProvince;//NEW ADD/
    private String copyrightCity;//NEW ADD/
    private String   copyrightCounty;//NEW ADD/

    private String finishTime;// 完成时间
    private String finishAddress;// 作品完成地点
    private String completeCountries;// 作品完成国家
    private String completeProvince;// 作品完成身份
    private String completeCity;// 作品完成市
    private String completeCounty;// 作品完成县
    private String completeArea;// 作品完成详细地址

    private int publishState;// 发表未发表状态
    private String firstPublishTime;// 首次发表时间
    private String firstCountry;// 首次发表地点（国家）
    private String firstProvince;// 首次发表地点（省）
    private String firstCity;// 首次发表地点（市）
  private String firstCounty;// 首次发表地点（县）
    private String workPublishArea;//首次发表的详细地址
    private String workotherAreas;// 其他国家的具体地点
    private int rightOwnMode;// 权利归属方式
    private int obtainMode;// 权利取得方式
    @JoinColumn(name="rightGuarantee_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment rightGuarantee;// 权利保证书
    @JoinColumn(name="agentBook_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment agentBook; //代理委托书
    // 合作作品合同
    @JoinColumn(name="cooperationContract_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment cooperationContract;// 合作作品合同
    // 职务作品合同
    @JoinColumn(name="positionContract_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment positionContract; //职务作品合同
//    private String officeWork;
    // 委托作品合同
    private String entrustType;//
    @JoinColumn(name="entrustContract_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment entrustContract;// 委托作品合同
    @JoinColumn(name="trusteeContract_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment trusteeContract;//受托合同书

    private int rightScope;// 权利拥有状况
    private String emedium;// 电子文档
    private String emediumCD;// 光盘
    private String emediumOther;// 其他
    // 证书领取方式
    private String emediumCertificate;// 电子证书
    private String pmediumCertificate;// 纸质证书
    private String pmediumCount;// 打印件(A4纸)1份

    private int fileType;//上传文件的类型，0是图片，1是文档,2视频

    private int status_type;// 状态
    private String refuseReason;// 拒绝理由
    private String certificateUrl;//证书生成的链接
    private int inactiveType;//0代表没有撤销作品，1代表已撤销的作品
    private String cancelTheProofUrl;//撤销作品的撤销证明
    private int restatus;//审批流程的上一个状态

    private LocalDateTime registrationDate;// 登记时间

    private BigInteger allUpdateId;//获取当前保存插入ID
    private String stateType;//设置当点击提交之后跳到确认信息页，又返回修改数据需要进行修改数据库，不进行再插入数据
    @Transient
    private String starDate;//查询时间段的开始时间。
    @Transient
    private String endDate;//查询时间段的结束时间。

    private String rightScopeString; //权利拥有状况(文字)
    private String publishStatusString; //作品出版状态(文字)
    private String applyTypeString; //申请方式
    private String rightAttributionWay_String;// 权利归属方式(文字)
    private String rightWay_String;// 权利取得方式(文字)
    private String status_String;// 状态(文字)
    private String regNum; //证书生成后登记号
    private int focusWork;//作品的关注状态
    private int certificatePrint;//证书打印了就在列表上消失
    private BigInteger draftSaveId;//作品提交的时候先保存到草稿箱，确认提交在删除草稿箱数据
    private String intherFort;
    private int refuseRevokes;//1是上传撤销 ，2是拒绝撤销
    private int countDown; //倒计时时间
    private String finishAreaStr;//作品完成国家+省份+城市+县区文字
    private String firstAreaStr;//作品首次发表国家+省份+城市+县区文字

    private String dictName;
    private int maskCount;
    private String onBlockToken;
    private String haverightIds;
    private String instructionsBox; //权利拥有状况其他内容
    private Integer isAllowedCopyrightExchange; //是否愿意委托版权交易

    private String originalAuthorName;// 原作品作者名称
    private String originalProdName;// 原作品名称

    /**
     * 代理人
     */
    @JoinColumn(name = "agent_id")
    @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    private Agent agentList;

    /**
     * 作者
     */
    @JoinColumn(name = "draftCopyright_id")
    @OneToMany(cascade={CascadeType.PERSIST, CascadeType.ALL}, fetch = FetchType.LAZY)
    private List<Author> authorList = new ArrayList<>();

    /**
     * 著作权人
     */
    @JoinColumn(name = "draftCopyright_id")
    @OneToMany(cascade={CascadeType.PERSIST, CascadeType.ALL}, fetch = FetchType.LAZY)
    private List<CopyrightOwner> ownerList = new ArrayList<>();

    /**
     * 提交人
     */
    @JoinColumn(name="submitter_id")
    @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    private Submitter submitter;

    @Transient
    private List<Integer> haverightIdList = new ArrayList<>();

    /**
     * 作品样本
     */
    @ManyToMany
    @JoinTable(name="draftcopyright_attachment",	//用来指定中间表的名称
            //用于指定本表在中间表的字段名称，以及中间表依赖的是本表的哪个字段
            joinColumns= {@JoinColumn(name="draftcopyright_id",referencedColumnName="registrationNum")},
            //用于指定对方表在中间表的字段名称，以及中间表依赖的是它的哪个字段
            inverseJoinColumns= {@JoinColumn(name="attachment_id",referencedColumnName="id")}
    )
    private List<UploadAttachment> uploadWorks = new ArrayList<>();

    /**
     * 流程记录
     */
    @JoinColumn(name = "draftCopyright_id")
    @OneToMany(cascade={CascadeType.PERSIST, CascadeType.ALL}, fetch = FetchType.LAZY)
    private List<ProcessRecord> flowRecord = new ArrayList<>();

    public void setHaverightIds(String haverightIds) {
        if(haverightIds!=null) {
            this.haverightIds = haverightIds;
            String[] temp = haverightIds.split(",");
            List<Integer> haverightIdList = new ArrayList<>();
            for (String str : temp) {
                haverightIdList.add(Integer.parseInt(str));
            }
            this.haverightIdList = haverightIdList;
        }
    }

    public void setHaverightIdList(List<Integer> haverightIdList) {
        if(haverightIdList!=null) {
            this.haverightIdList = haverightIdList;
            String haverightIds = "";
            for (int i = 0; i < haverightIdList.size();i++){
                if(i==0){
                    haverightIds = haverightIds+haverightIdList.get(i);
                }else{
                    haverightIds = haverightIds+","+haverightIdList.get(i);
                }
            }
            this.haverightIds = haverightIds;
        }
    }
}
