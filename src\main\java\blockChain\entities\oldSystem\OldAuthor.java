package blockChain.entities.oldSystem;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="old_author")
public class OldAuthor {
	// 旧系统数据迁移用实体类，迁移后删除
	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer author_id;
  private String author_name;// 作者姓名
  private Long copyrightmanagerId;//在线填报表的ID
  private String author_idCard;// 身份证号码
  private int author_type;// 证件类型
  private int author_idType;// 证件类别
  private String author_address;// 地址
  private String author_phone;// 电话
  private int author_signType;// 署名情况
  private String author_opusSign;// 别名
  private int author_classification;//分别是草稿箱中保存的数据，还是作品提交的数据

  private String addImg;
  private String addAuthor;

  private String authorSignature_String; //署名情况(文字)
  private String authorCategory_String; //类别(文字)
  private String authorCertificate_String; //证件类型(文字)


}
