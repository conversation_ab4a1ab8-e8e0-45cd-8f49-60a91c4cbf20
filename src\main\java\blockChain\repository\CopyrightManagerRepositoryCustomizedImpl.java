package blockChain.repository;

import blockChain.bean.Constant;
import blockChain.dto.CopyrightManageExportDto;
import blockChain.dto.StatisticDto;
import blockChain.entities.*;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.StringTemplate;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static blockChain.entities.CopyrightManager.MODIFIED;

/**
 * <AUTHOR>
 * @date 2020/4/9 9:22
 */
public class CopyrightManagerRepositoryCustomizedImpl  extends QuerydslRepositorySupport implements CopyrightManagerRepositoryCustomized {
  public CopyrightManagerRepositoryCustomizedImpl() {
    super(CopyrightManager.class);
  }

  @Override
  public List<StatisticDto> getAreaDistributedForFujian(Predicate... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;
    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), manager.registrationNum.count().as("amount"),digital.isLeaf))
      .from(manager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
      .on(owner.copyCity.eq(digital.id))
      .where(predicate)
      .groupBy(manager.completeCity)
      .fetch();
  }

  @Override
  public StatisticDto getAreaDistributedCountry(Predicate... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), manager.registrationNum.count().as("amount"),digital.isLeaf))
      .from(manager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
      .on(owner.copyCountries.eq(digital.id))
      .where(predicate)
      .fetchOne();
  }

  @Override
  public StatisticDto getAreaDistributedProvince(Predicate... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), manager.registrationNum.count().as("amount"),digital.isLeaf))
      .from(manager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
      .on(owner.copyProvince.eq(digital.id))
      .where(predicate)
      .fetchOne();
  }

  @Override
  public List<StatisticDto> getAreaDistributedCity(Predicate digitalpredicate,Predicate managerperdicate,Predicate ... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), manager.registrationNum.count().as("amount"),digital.isLeaf))
      .from(manager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
      .on(owner.copyCity.eq(digital.id))
      .where(digitalpredicate, managerperdicate)
      .groupBy(digital.dict_name)
      .orderBy(manager.registrationNum.count().desc())
      .fetch();
  }

  @Override
  public List<StatisticDto> getAreaDistributedCity(Integer id,Predicate managerperdicate,Predicate ... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
            .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), manager.registrationNum.count().as("amount"),digital.isLeaf))
            .from(manager)
            .leftJoin(owner)
            .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
            .leftJoin(digital)
            .on(owner.copyCity.eq(digital.id))
            .where(digital.id.eq(id), managerperdicate)
            .groupBy(digital.dict_name)
            .orderBy(manager.registrationNum.count().desc())
            .fetch();
  }

  @Override
  public List<StatisticDto> getAreaDistributedCounty(Predicate digitalpredicate,Predicate managerperdicate,Predicate ... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), manager.registrationNum.count().as("amount"),digital.isLeaf))
      .from(manager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
      .on(owner.copyCounty.eq(digital.id))
      .where(digitalpredicate, managerperdicate)
      .groupBy(digital.dict_name)
      .orderBy(manager.registrationNum.count().desc())
      .fetch();
  }

  @Override
  public List<StatisticDto> getAreaDistributedCounty(Integer id,Predicate managerperdicate,Predicate ... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
            .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), manager.registrationNum.count().as("amount"),digital.isLeaf))
            .from(manager)
            .leftJoin(owner)
            .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
            .leftJoin(digital)
            .on(owner.copyCounty.eq(digital.id))
            .where(digital.id.eq(id), managerperdicate)
            .groupBy(digital.dict_name)
            .orderBy(manager.registrationNum.count().desc())
            .fetch();
  }

  @Override
  public long updateCountDown() {
      QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;
      NumberExpression<Integer> inactiveType = copyrightManager.countDown.when(1).then(CopyrightManager.InactiveTypeValue.REVOKED).otherwise(copyrightManager.inactiveType);
      return update(copyrightManager)
              .set(copyrightManager.inactiveType, inactiveType)
              .set(copyrightManager.countDown, copyrightManager.countDown.subtract(1))
              .where(copyrightManager.inactiveType.eq(CopyrightManager.InactiveTypeValue.NORMAL).and(copyrightManager.countDown.gt(0)).and(copyrightManager.status_type.eq(MODIFIED)))
              .execute();
  }

//  @Override
//  public void updateLicenseStatus() {
//    QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;
//    update(copyrightManager)
//            .set(copyrightManager.licenseStatus, CopyrightManager.LicenseStatus.WAIT)
//            .where(copyrightManager.licenseStatus.ne(CopyrightManager.LicenseStatus.REPORTED))
//            .execute();
//  }

    @Override
    public long countBy(Predicate predicate) {
        QCopyrightManager digital = QCopyrightManager.copyrightManager;
        QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

        return getQuerydsl().createQuery()
                .select(digital.registrationNum.countDistinct())
                .from(digital)
                .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(digital.registrationNum))
      .where(predicate)
      .fetchOne()
      ;
  }

  @Override
  public long countSameBy(Predicate predicate) {
    QCopyrightManager digital = QCopyrightManager.copyrightManager;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    return getQuerydsl().createQuery()
            .select(digital.registrationNum.countDistinct())
            .from(digital)
            .leftJoin(owner)
            .on(owner.manager.registrationNum.eq(digital.registrationNum).and(owner.seq.eq(1)))
            .where(predicate)
            .fetchOne()
            ;
  }

    @Override
    public void updateStateByIds(Integer state, List<Long> ids) {
        QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;
        update(copyrightManager)
                .set(copyrightManager.status_type, state)
                .where(copyrightManager.registrationNum.in(ids))
                .execute();
    }

    /**
     * 上报版保中心作品导出
     */
    @Override
    public List<CopyrightManageExportDto> getExportList(String worksNum, String productionName, List<String> productionTypeIds, Integer rightOwnMode, String startDate, String endDate, List<Integer> status, String userName, String agentName, String copyrightName, Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level, Integer evaluatemin, Integer evaluatemax,
                                                        String certificateStartDate, String certificateEndDate) {
        QCopyrightManager digital = QCopyrightManager.copyrightManager;
        QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
        QAuthor qAuthor = QAuthor.author;
        QDigital digital1 = new QDigital("productionTypeId");
        QDigital digital2 = new QDigital("opusInditekind");
        QDigital digital3 = new QDigital("obtainMode");
        QDigital digital4 = new QDigital("rightOwnMode");

        BooleanBuilder builder = new BooleanBuilder(QCopyrightManager.copyrightManager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE));
        builder.and(digital.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));

        if (!StringUtils.isEmpty(worksNum)) { // 作品登记号
            builder.and(digital.worksNum.contains(worksNum));
        }

        if (!StringUtils.isEmpty(productionName)) { // 作品名称
            builder.and(digital.productionName.contains(productionName));
        }

        if (!StringUtils.isEmpty(agentName)) { // 代理人
            builder.and(digital.agentList.agentName.contains(agentName));
        }

        if (!StringUtils.isEmpty(copyrightName)) { // 著作权人
            builder.and(owner.copyName.contains(copyrightName));
        }

        if (productionTypeIds != null && productionTypeIds.size() > 0) {  // 作品类别
            builder.and(digital.productionTypeId.in(productionTypeIds));
        }

        if (rightOwnMode != null) { //权利方式
            builder.and(digital.rightOwnMode.eq(rightOwnMode));
        }

    /*if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) { //时间

      builder.and(digital.registrationDate.between(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDateTime.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
    }*/
        if (!StringUtils.isEmpty(startDate)) { //时间

            builder.and(digital.registrationDate.after(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }
        if (!StringUtils.isEmpty(endDate)) { //时间

            builder.and(digital.registrationDate.before(LocalDateTime.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }
        if (!StringUtils.isEmpty(certificateStartDate)) { //时间

            builder.and(digital.certificateCreateTime.after(LocalDateTime.parse(certificateStartDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))))
                    .or(digital.certificateCreateTime.eq(LocalDateTime.parse(certificateStartDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }
        if (!StringUtils.isEmpty(certificateEndDate)) { //时间

            builder.and(digital.certificateCreateTime.before(LocalDateTime.parse(certificateEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))))
                    .or(digital.certificateCreateTime.eq(LocalDateTime.parse(certificateEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }
        if (status != null && status.size() > 0) {
            builder.and(digital.status_type.in(status));
        }

        if (!StringUtils.isEmpty(userName)) { // 用户名称
            builder.and(digital.userName.eq(userName));
        }else{
            builder.and(digital.status_type.ne(CopyrightManager.UNSUBMIT));
            if (city_level != null) { //是否拥有市级权限限制
                builder.and(owner.copyCity.eq(city_level));
            }
            if (county_level != null) { //是否拥有县级权限限制
                builder.and(owner.copyCounty.eq(county_level));
            }
        }

        if(isreport!=null){
            if(isreport){
                builder.and(digital.regNum.isNotEmpty());
            }else{
                builder.andAnyOf(digital.regNum.isEmpty(),digital.regNum.isNull());
            }
        }
        if(isCertificate!=null){
            if(isCertificate){
                builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
                //builder.and(digital.certificateUrl.isNotEmpty());
            }else{
                builder.andAnyOf(digital.certificateUrl.isEmpty(),digital.certificateUrl.isNull());
            }
        }
        if(isSubmit!=null){
            if(isSubmit){
                builder.and(digital.licenseState.eq(CopyrightManager.LicenseState.REPORTED));
            }else{
              builder.andAnyOf(digital.licenseState.ne(CopyrightManager.LicenseState.REPORTED));
            }
        }
        if(evaluatemin!=null&&evaluatemax!=null) {
            builder.and(digital.evaluate.alternate.between(evaluatemin,evaluatemax));
        }
        if(evaluatemin!=null&&evaluatemax==null) {
            builder.and(digital.evaluate.alternate.between(evaluatemin,5));
        }
        if(evaluatemin==null&&evaluatemax!=null) {
            builder.and(digital.evaluate.alternate.between(evaluatemin,evaluatemax));
        }

        return getQuerydsl().createQuery()
                .select(Projections.constructor(CopyrightManageExportDto.class,digital.worksNum,digital.certificateCreateTime.as("registrationDate"),
                        digital.productionName,digital1.dict_name.as("productionType"),owner.copyName.as("copyName"),digital.finishTime,
                        digital.firstPublishTime,digital2.dict_name.as("opusInditekind"),digital3.dict_name.as("obtainMode"),
                        digital4.dict_name.as("rightOwnMode"),qAuthor.authorName.as("authorName")))
                .from(digital)
                .leftJoin(owner)
                .on(owner.manager.registrationNum.eq(digital.registrationNum))
                .leftJoin(digital1)
                .on(Expressions.asNumber((Expression)digital.productionTypeId).eq(digital1.id))
                .leftJoin(digital2)
                .on(Expressions.asNumber((Expression)digital.opusInditekind).eq(digital1.id))
                .leftJoin(digital3)
                .on(digital.obtainMode.eq(digital3.id))
                .leftJoin(digital4)
                .on(digital.rightOwnMode.eq(digital4.id))
                .leftJoin(qAuthor)
                .on(digital.authorList.contains(qAuthor))
                .where(builder)
                .groupBy(digital.registrationNum)
                .fetch();
    }

  @Override
  public List<StatisticDto> countListBy(Predicate predicate, Boolean isYear, Boolean isWorkstation) {
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    String format = "%Y-%m";
    if (isYear) format = "%Y";
    StringTemplate certificateCreateTimeExpr;
    if (isWorkstation!=null && isWorkstation) {
      certificateCreateTimeExpr = Expressions.stringTemplate("DATE_FORMAT({0},'"+ format + "')",manager.registrationDate);
    } else {
      certificateCreateTimeExpr = Expressions.stringTemplate("DATE_FORMAT({0},'"+ format + "')",manager.certificateCreateTime);
    }

    return getQuerydsl().createQuery()
            .select(Projections.constructor(StatisticDto.class,
                    certificateCreateTimeExpr.as("id"),
                    certificateCreateTimeExpr.as("key"),
                    certificateCreateTimeExpr.as("name"),
                    manager.count().as("amount"),
                    Expressions.asBoolean(false)))
            .from(manager)
            .where(predicate)
            .groupBy(certificateCreateTimeExpr)
            .orderBy(certificateCreateTimeExpr.asc())
            .fetch();
  }

  @Override
  public List<CopyrightManager> getAllSWBSB(PageRequest of, Predicate predicate) {
    QCopyrightManager manager = QCopyrightManager.copyrightManager;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    return getQuerydsl().createQuery()
            .select(manager)
            .from(manager)
            .leftJoin(owner)
            .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
            .where(predicate)
            .orderBy(manager.certificateCreateTime.asc())
            .limit(of.getPageSize())
            .offset(of.getOffset())
            .fetch();
  }

  /**
   * 工作站统计
   * @param predicate
   * @return
   */
  @Override
  public List<StatisticDto> countWorkstationList(Predicate predicate) {
    QUserEntity user = QUserEntity.userEntity;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;
    return getQuerydsl().createQuery()
            .select(Projections.constructor(StatisticDto.class,
                    user.userId.intValue().as("key"),
                    user.role.roleName.append("__").append(user.realName).as("name"),
                    manager.count().as("amount")))
            .from(user)
            .leftJoin(manager).on(manager.userName.eq(user.userName).and(predicate))
            .where(user.role.isWorkstation.eq(Constant.BYTE_TRUE))
            .groupBy(user.userId)
            .orderBy(user.role.id.asc())
            .fetch();
  }


  /**
   * 其他工作站统计
   * @param predicate
   * @return
   */
  @Override
  public long countWorkstationOther(Predicate predicate) {
    QUserEntity user = QUserEntity.userEntity;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
            .select(manager.count())
            .from(manager)
            .leftJoin(user).on(manager.userName.eq(user.userName))
            .where(predicate, user.role.isWorkstation.ne(Constant.BYTE_TRUE).or(user.role.isWorkstation.isNull()))
            .fetchOne();
  }

  /**
   * 工作站统计
   * @param predicate
   * @return
   */
  @Override
  public long countWorkstation(Predicate predicate) {
    QRoleEntity role = QRoleEntity.roleEntity;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
            .select(manager.count())
            .from(manager)
            .leftJoin(role).on(manager.workstationId.eq(role.id))
            .where(predicate)
            .fetchOne();
  }
}
