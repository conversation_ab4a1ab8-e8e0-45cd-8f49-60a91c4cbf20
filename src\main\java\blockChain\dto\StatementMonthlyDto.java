package blockChain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/16 11:04
 */
@Data
@Accessors(chain = true)
public class StatementMonthlyDto {
  /**
   * 统计月份
   */
  LocalDate monthly;

  /**
   * 累计版权登记量
   */
  Long totalSize;

  /**
   * 种类s
   */
  List<StatisticDto> types = new ArrayList<>();

  /**
   * 上年同期总数
   */
  Long periodLastMonthOrYearTotalSize;


  /**
   * 各地新增数量
   */
  List<StatisticDto> locationAdditions;

  /**
   * 同比率
   */
  Double rate;

}
