package blockChain.service;

import blockChain.entities.MenuEntity;
import blockChain.entities.QMenuEntity;
import blockChain.repository.MenuRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/12/23 10:45
 */
@Service
@RequiredArgsConstructor
public class MenuService implements BaseService<MenuRepository, MenuEntity, Long> {

    private final MenuRepository repository;

    @Override
    public MenuRepository getRepository() {
        return repository;
    }

    public Optional<MenuEntity> findByAuthority(String authority) {
        QMenuEntity qMenuEntity = QMenuEntity.menuEntity;
        return repository.findOne(qMenuEntity.menuUrl.eq(authority));
    }
}
