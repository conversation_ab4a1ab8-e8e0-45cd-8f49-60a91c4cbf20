package blockChain.bean;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import javax.validation.Valid;
import javax.validation.constraints.Min;

@Data
@ApiModel(description = "分页查询参数类")
@Deprecated
public class PageQuery<T> {

	public static final int DEFAULT_PAGE = 1;

	public static final int DEFAULT_PAGE_SIZE = 10;

	@Min(1)
	@ApiModelProperty(notes = "第几页", example = "1")
	private int page;

	@Min(1)
	@ApiModelProperty(notes = "每页大小", example = "10")
	private int pageSize;

    @Valid
	@JsonUnwrapped
	private T queryBody;

	public PageQuery() {
		this(DEFAULT_PAGE, DEFAULT_PAGE_SIZE);
	}

	public PageQuery(int page, int pageSize) {
		this.page = page;
		this.pageSize = pageSize;
	}

	@ApiModelProperty(hidden = true)
	public Pageable getPageRequest(){
		//spring data jpa 采用zero-based page index, 而前端是从1开始的分页。
		return PageRequest.of(this.page - 1, this.pageSize);
	}
}
