
package blockChain.repository;

import blockChain.entities.QCopyrightManager;
import blockChain.entities.QDraftCopyrightManager;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * <AUTHOR> XXX
 * @date 2019/11/29.
 */

public final class DraftCopyrightManagerPredicates {
    public static Predicate digitalQuery(Long registrationNum,String productionName,
                                         String productionTypeId,Integer rightOwnMode,
                                         String startDate, String endDate,
                                         String userName) {

        QDraftCopyrightManager digital = QDraftCopyrightManager.draftCopyrightManager;

        BooleanBuilder builder = new BooleanBuilder();

        if (registrationNum != null) { // 作品登记号
            builder.and(digital.registrationNum.eq(registrationNum));
        }

        if (!StringUtils.isEmpty(productionName)) { // 作品名称
            builder.and(digital.productionName.contains(productionName));
        }

        if (!StringUtils.isEmpty(productionTypeId)) {  // 作品类别
            builder.and(digital.productionTypeId.eq(productionTypeId));
        }

        if (rightOwnMode != null) { //权利方式
            builder.and(digital.rightOwnMode.eq(rightOwnMode));
        }

        if (!StringUtils.isEmpty(startDate)) { //时间

            builder.and(digital.registrationDate.after(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        if (!StringUtils.isEmpty(endDate)) { //时间

          builder.and(digital.registrationDate.before(LocalDateTime.parse(endDate,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        if (!StringUtils.isEmpty(userName)) { // 作品名称
            builder.and(digital.userName.eq(userName));
        }

        return builder;
    }

}

