package blockChain.controller.statistic;

import blockChain.bean.PageResponse;
import blockChain.bean.QueryParam;
import blockChain.config.SpringConfig;
import blockChain.dto.StatementMonthlyDto;
import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.dto.query.StatementQueryParam;
import blockChain.facade.service.StatementServiceFacade;
import blockChain.utils.DocumentHandlerUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Chan
 * @date 2020/4/16 10:01
 */
@Api("报表")
@Slf4j
@RestController
@RequestMapping("statement")
@AllArgsConstructor
public class StatementController {

  private StatementServiceFacade serviceFacade;

  private final SpringConfig config;

  @ApiOperation(value = "月报表", notes = "月报表", tags = { "报表" })
  @PostMapping("monthly/get")
  public ResponseEntity<StatementMonthlyDto> getStatementForMonthly (@Valid @RequestBody StatementQueryParam param){
    StatementMonthlyDto dto = serviceFacade.getStatementForMonthly(param);
    return ResponseEntity.ok(dto);
  }

  @ApiOperation(value = "年报表", notes = "年报表", tags = { "报表" })
  @PostMapping("year/get")
  public ResponseEntity<StatementMonthlyDto> getStatementForYear (@Valid @RequestBody StatementQueryParam param){
    StatementMonthlyDto dto = serviceFacade.getStatementForYears(param);
    return ResponseEntity.ok(dto);
  }

  @ApiOperation(value = "月报表导出", notes = "月报表导出", tags = { "报表" })
  @PostMapping("monthly/export")
  public ResponseEntity<Map<String,Object>> exportStatisticsDate(@Valid @RequestBody QueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception{

    Map<String,Object> result = new HashMap<>();
    HashMap<String, Object> data = new HashMap<String , Object>();
    String contextPath = "File/"+config.getModelRoot();
    String urlRoot = contextPath + "exportDoc";

    String exportUrl = urlRoot + "/";
    String title = "";
    String context = "";
    DocumentHandlerUtil documentHandlerUtil=new DocumentHandlerUtil();
    String template = "";
    String fileName = "";

    data.put("title",param.getString("title"));
    data.put("typesDescript",param.getString("typesDescript"));
    data.put("locationDescript",param.getString("locationDescript"));
    template = "productMonthlyReport.ftl";
    fileName= param.getString("title")+".doc";

    String saveUrl = urlRoot + "/" + fileName;
    documentHandlerUtil.createDoc(request, data, template, saveUrl);
    //documentHandlerUtil.download(response,request,fileName,saveUrl);
    result.put("url",saveUrl);
    return ResponseEntity.ok(result);
  }

  @ApiOperation(value = "年报表导出", notes = "年报表导出", tags = { "报表" })
  @PostMapping("year/export")
  public ResponseEntity<Map<String,Object>> exportStatisticsYear(@Valid @RequestBody QueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception{

    Map<String,Object> result = new HashMap<>();
    HashMap<String, Object> data = new HashMap<String , Object>();
    String contextPath = "File/"+config.getModelRoot();
    String urlRoot = contextPath + "exportDoc";

    String exportUrl = urlRoot + "/";
    String title = "";
    String context = "";
    DocumentHandlerUtil documentHandlerUtil=new DocumentHandlerUtil();
    String template = "";
    String fileName = "";

    data.put("title",param.getString("title"));
    data.put("typesDescript",param.getString("typesDescript"));
    data.put("locationDescript",param.getString("locationDescript"));
    template = "productMonthlyReport.ftl";
    fileName= param.getString("title")+".doc";

    String saveUrl = urlRoot + "/" + fileName;
    documentHandlerUtil.createDoc(request, data, template, saveUrl);
    //documentHandlerUtil.download(response,request,fileName,saveUrl);
    result.put("url",saveUrl);
    return ResponseEntity.ok(result);
  }
}
