package blockChain.dto.user;

import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/20 10:22
 */
@Data
public class UserUpdate {

    /**
     * 主键
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 用户状态
     */
    private Integer userStatus;

    /**
     * 用户真实姓名
     */
    private String realName;

    /**
     * 用户性别
     */
    private Integer sex;

    /**
     * 身份证号码
     */
    private String cardId;

    /**
     * 身份证图片---路径名
     */
    private String cardImgf;

    /**
     * 身份证类型
     */
    private Integer identityKind;

    /**
     * 代理公司名称
     */
    private String agencyCompanyName;

    /**
     * 手机号码
     */
    private String phoneNum;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 固定电话
     */
    private String fixedPhoneNum;

    /**
     * 所在省
     */
    private String provinceName;

    /**
     * 所在市
     */
    private String cityName;

    /**
     * 所在国家
     */
    private String countryName;

    /**
     * 所在区
     */
    private String countyName;

    private List<String> personalArea = new ArrayList<>();
    private String personalAreaStr;

    private String uuid;

    private Integer isProxyer;
}
