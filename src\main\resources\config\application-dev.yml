# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# ===================================================================
logging:
  config: classpath:logback-formate/logback-dev.xml
  level:
    ROOT: debug
    com.ctsi.sampleapp: debug
    org.hibernate: ERROR
    org.hibernate.ejb.HibernatePersistence: ERROR
    org.hibernate.SQL: ERROR
    org.hibernate.type.descriptor.sql.BasicBinder: ERROR
    com.querydsl.jpa.impl.JPAQuery: ERROR
    com.zdww.biyi.component.sdk.log.LogFile: ERROR

spring:
  profiles:
    active: dev
    include: swagger

  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL57Dialect
  liquibase:
    enabled: true
    change-log: classpath:/db/changelog/master.xml
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: root
    password: 876354Capcom
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      poolName: DatebookHikariCP1
      maximumPoolSize: 20
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
      idleTimeout: 120000
      connectionTimeout: 300000
      leakDetectionThreshold: 300000
  messages:
    basename: i18n/messages
    encoding: UTF-8
server:
  port: 19000
  servlet:
    context-path: /api/qkl

ctsi:
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization,Link,X-Total-Count"
    allow-credentials: true
    max-age: 1800
  log:
    operation-log:
      enable: false
    login-log:
      enable: false
  jwtfilter:
    enable: true
block:
  file:
    fileRoot: media/nas/copyright_newupload
    modelRoot: media/nas/
  images:
    imageRoot: certimages
  logs:
    filePath: /media/ffcs/copyright/logs
  blockUrl:
    url: http://**************:8070/
  frontUrl:
    url: http://XXX:19000/
  oldSystemUrl:
    url: http://**************:8081/CopyRight/
  biyiServiceUrl:
    url: http://127.0.0.1:8099/api/qkl/test/
  convergence:
    wsdl: http://*************:809/Convergence/webservice/ConvergenceService?wsdl
    userName: hjpt_bqbkxt
    passWord: hjpt@321
    catalogId_manager: WEB736
    catalogId_owner: WEB735
  proxyIp: none
  proxyPort: 0
  isExtranet: true
  autoSubmit: false
  certificate:
    cerUrl: http://*************:18080/processBuildCertificate
    picUrl: http://*************:18080/processFixTakeStamp
    documentUrl: http://*************:18888/docTakeStamp
  similarScore: 3
  license:
    url: http://**************:90/services/DataCollect
    guid: ********-B96B51EB9423BACE2CAF-60
    surface: sendSurfaceAndAutoSeal
    tokenUrl: http://**************:8040/license-api-release/certificate/oauth/token
    saveUrl: http://**************:8040/license-api-release/certificate/v1/saveCertificateInfo
    accountId: fjsbqj
    priKey: FHrFA8XpCBkSr0pWiG+/a/pcJcEVVfycb1i1fqKHx4M=
