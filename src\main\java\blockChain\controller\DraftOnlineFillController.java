
package blockChain.controller;

import blockChain.bean.QueryParam;
import blockChain.dto.AuthorDto;
import blockChain.dto.CopyrightOwnerDto;
import blockChain.dto.DraftCopyrightManagerDto;
import blockChain.dto.UserDto;
import blockChain.dto.ebus.FormDataDTO;
import blockChain.entities.*;
import blockChain.facade.service.*;
import blockChain.mapper.DigitalDtoMapper;
import blockChain.mapper.DraftCopyrightManagerDtoMapper;
import blockChain.mapper.FormDataMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2019/12/3 10:22
 */

@Api("草稿箱")
@Slf4j
@RestController
@RequestMapping("draftOnlineFill")
@RequiredArgsConstructor
public class DraftOnlineFillController {
    @Autowired
    private DraftCopyrightManagerServiceFacade draftCopyrightManagerServiceFacade;
    @Autowired
    private DigitalServiceFacade digitalServiceFacade;
    @Autowired
    private UserServiceFacade userServiceFacade;
    @Autowired
    private SensitiveWordServiceFacade sensitiveWordServiceFacade;
    private final OutSideServiceFacade outSideServiceFacade;

    @ApiOperation("作品保存")
    @PostMapping("draftSave")
    public ResponseEntity<Map<String, Object>> onlineSubmitData(@Valid @RequestBody DraftCopyrightManager copyrightManager) {
        Map<String, Object> result = new HashMap<>();
        List<CopyrightOwner> owners = copyrightManager.getOwnerList();
        List<String> cardIds = new ArrayList();
        // add 20230630 著作权人重复判断
        if (owners != null && owners.size() > 0) {
          for(CopyrightOwner owner:copyrightManager.getOwnerList()) {
              if (cardIds.contains(owner.getCopyIdCard())) {
                  throw new RuntimeException("著作权人不可重复：" + owner.getCopyIdCard());
              }
              cardIds.add(owner.getCopyIdCard());
              owner.setCopyName(owner.getCopyName().trim());
              if (!ObjectUtils.isEmpty(owner.getCopyCertificateZM()) && owner.getCopyCertificateZM().getId() != null) {
                  if (StringUtils.isEmpty(owner.getProjectId())) {
                      owner.setFormDataId(null);
                  } else {
                      owner.setFormDataId(owner.getCopyCertificateZM().getId());
                      owner.setCopyCertificateZM(null);
                  }
              }
          }
        }
        List<Author> authors = copyrightManager.getAuthorList();
        if (authors != null && authors.size() > 0) {
            for (Author author : authors) {
                author.setAuthorName(author.getAuthorName().trim());

                if (!ObjectUtils.isEmpty(author.getAuthorCertificateZM()) && author.getAuthorCertificateZM().getId() != null) {
                    if (StringUtils.isEmpty(author.getProjectId())) {
                        author.setFormDataId(null);
                    } else {
                        author.setFormDataId(author.getAuthorCertificateZM().getId());
                        author.setAuthorCertificateZM(null);
                    }
                    author.setAuthorIdCar(null);
                } else {
                    author.setAuthorCertificateZM(null);
                    author.setFormDataId(null);
                }
            }
        }

        // 敏感词判断
        if (StringUtils.isNotBlank(copyrightManager.getProductionName())) {
            List<String> sensitiveList = sensitiveWordServiceFacade.getSensitiveWords();
            for (String sensitive : sensitiveList) {
                if (copyrightManager.getProductionName().contains(sensitive)) {
                    throw new RuntimeException("作品名称中不可出现敏感词！");
                }
            }
        }

        draftCopyrightManagerServiceFacade.createCopyright(copyrightManager);
        result.put("message", "作品保存草稿成功！");
        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
    }

  @ApiOperation("作品删除")
  @PostMapping("remove")
  public ResponseEntity<Map<String, Object>> removeCopyright(@Valid @RequestBody QueryParam queryParam) {
    Map<String, Object> result = new HashMap<>();
    try {
      DraftCopyrightManager data = draftCopyrightManagerServiceFacade.getById(queryParam.getLong("id"));
      if (data == null) {
        result.put("message", "未找到该 作品！");
        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
      } else {
        draftCopyrightManagerServiceFacade.remove(data);
        result.put("message", "删除成功！");
        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
      }
    } catch (Exception e) {
      result.put("message", "删除失败！");
      result.put("errorMsg", e.getLocalizedMessage());
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
    }
  }

    @ApiOperation("获取作品详情")
    @PostMapping("get")
    public ResponseEntity<DraftCopyrightManagerDto> getDraftCopyrightManager(@Valid @RequestBody QueryParam queryParam) {
        DraftCopyrightManager data = draftCopyrightManagerServiceFacade.getById(queryParam.getLong("id"));
        DraftCopyrightManagerDto dataDto = DraftCopyrightManagerDtoMapper.INSTANCE.entityToDto(data);
        List<CopyrightOwnerDto> ownerList = dataDto.getOwnerList();
        for (CopyrightOwnerDto dto : ownerList) {
            if (StringUtils.isEmpty(dto.getProjectId()) || dto.getFormDataId() == null)
                continue;
            FormDataEntity formDataEntity = outSideServiceFacade.findFormDataById(dto.getFormDataId());
            FormDataDTO formDataDTO = FormDataMapper.INSTANCE.toDTO(formDataEntity);

            CertificateEntity certificateZM = new CertificateEntity();
            certificateZM.setId(formDataDTO.getId());
            certificateZM.setAreaNamesStr(formDataDTO.getAreaNamesStr());
            dto.setCopyCertificateZM(certificateZM);
//            dto.setCopyIdCardZM(null);
        }
        List<AuthorDto> authorList = dataDto.getAuthorList();
        for (AuthorDto dto : authorList) {
            if (StringUtils.isEmpty(dto.getProjectId()) || dto.getFormDataId() == null)
                continue;
            FormDataEntity formDataEntity = outSideServiceFacade.findFormDataById(dto.getFormDataId());
            FormDataDTO formDataDTO = FormDataMapper.INSTANCE.toDTO(formDataEntity);

            CertificateEntity certificateZM = new CertificateEntity();
            certificateZM.setId(formDataDTO.getId());
            certificateZM.setAreaNamesStr(formDataDTO.getAreaNamesStr());
            dto.setAuthorCertificateZM(certificateZM);
            dto.setAuthorIdCar(null);
        }
        return new ResponseEntity<DraftCopyrightManagerDto>(dataDto, HttpStatus.OK);
    }
    @ApiOperation("作品列表查询")
    @PostMapping("query")
    public ResponseEntity<Map<String,Object>> queryDraftCopyrightManager(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
            //判断是否为用户查询，若是则只查询当前用户的作品
            String userName = null;
            UserDto userDto = userServiceFacade.findUserById(queryParam.getInteger("UID"));
            if(userDto!=null){
                userName = userDto.getUserName();
            }
            Page<DraftCopyrightManager> copyrightManagerPage = draftCopyrightManagerServiceFacade.query(queryParam.getLong("id"),queryParam.getString("productionName"),queryParam.getString("productionTypeId"),
                    queryParam.getInteger("rightOwnMode"),queryParam.getString("startDate"),queryParam.getString("endDate"),userName,
                    new Long(queryParam.getPage()).intValue()-1,new Long(queryParam.getPageSize()).intValue());
            List<DraftCopyrightManager> list = copyrightManagerPage.getContent();
            result.put("listData",DraftCopyrightManagerDtoMapper.INSTANCE.entityToDto(list));
            //设置分页项
            result.put("total",copyrightManagerPage.getTotalElements());
            result.put("page",queryParam.getPage());
            result.put("pageSize",queryParam.getPageSize());
            //获取数据字典
            //获取作品类型
            result.put("productionTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82)));
            //获取权利归属方式字典
            result.put("rightOwnModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(115)));
            //获取权利取得方式字典
            result.put("obtainModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(121)));
            //获取作品创作性质字典
            result.put("opusInditekindList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(126)));
            //获取著作权人类别字典
            result.put("manTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(134)));
            //获取著作权人证件类别字典
            result.put("manIdentityTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(141)));
            //获取国家字典
            result.put("CountryList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(157)));
            //获取省份字典
            result.put("ProvinceList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(393)));
            //获取权力拥有状况字典
            result.put("rightScopeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(429)));
            result.put("message", "查询成功！");
        }catch (Exception e){
            result.put("message", "查询失败！");
            result.put("errorCode",e.getLocalizedMessage());
        }finally {
            return new ResponseEntity<Map<String,Object>>(result,HttpStatus.OK);
        }
    }
}

