package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name="tb_role")
@Where(clause = "deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class RoleEntity {

	public static Long ROLE_USER = 4L;

  /**
   * 主键
   */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

    /**
     * 角色名称
     */
	private String roleName;

    /**
     * 描述
     */
	private String description;

    /**
     * 是否停用
     */
	private Integer deleted;

    /**
     * 是否必须
     */
    private Byte isNecessary;

    /**
     * 是否工作站
     */
    private Byte isWorkstation;

    /**
     * 创建时间
     */
	@CreatedDate
	private LocalDateTime createTime;

    /**
     * 更新时间
     */
	@LastModifiedDate
	private LocalDateTime updateTime;

	@ManyToMany
	@JoinTable(name="role_menu",	//用来指定中间表的名称
			//用于指定本表在中间表的字段名称，以及中间表依赖的是本表的哪个字段
			joinColumns= {@JoinColumn(name="role_id",referencedColumnName="id")},
			//用于指定对方表在中间表的字段名称，以及中间表依赖的是它的哪个字段
			inverseJoinColumns= {@JoinColumn(name="menu_id",referencedColumnName="id")}
	)
	private Set<MenuEntity> menus = new HashSet<>(0);
}
