package blockChain.repository;

import blockChain.dto.StatisticDto;
import blockChain.entities.CopyrightOwner;
import blockChain.entities.QAgent;
import blockChain.entities.QCopyrightManager;
import blockChain.entities.QCopyrightOwner;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:42
 */
public class CopyrightAgentRepositoryCustomImpl extends QuerydslRepositorySupport implements CopyrightAgentRepositoryCustom {
  public CopyrightAgentRepositoryCustomImpl() {
    super(CopyrightOwner.class);
  }

  @Override
  public List<StatisticDto> getAgent(long limit,Predicate... predicate) {
    QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, copyrightManager.agentList.agentName.as("key"), copyrightManager.agentList.agentName.as("name"), copyrightManager.agentList.agentName.count().as("amount")))
      .from(copyrightManager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(copyrightManager.registrationNum).and(owner.seq.eq(1)))
      .where(predicate)
      .groupBy(copyrightManager.agentList.agentName)
      .orderBy(copyrightManager.agentList.agentName.count().desc())
      .limit(limit)
      .fetch();
  }
}
