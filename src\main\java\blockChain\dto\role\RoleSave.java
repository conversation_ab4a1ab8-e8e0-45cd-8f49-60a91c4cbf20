package blockChain.dto.role;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.junit.jupiter.params.shadow.com.univocity.parsers.annotations.Trim;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/12/23 16:28
 */
@Data
public class RoleSave {

    private Long id;

    @JsonProperty("name")
    @Trim
    private String roleName;

    private String description;

    private Byte isNecessary;

    private Set<String> authorities;

    private Byte isWorkstation;
}
