package blockChain.facade.service;

import blockChain.bean.PageResponse;
import blockChain.controller.exception.NotFountException;
import blockChain.dto.message.AlertMessageDto;
import blockChain.dto.message.AlertMessageDtoQueryParam;
import blockChain.entities.UserEntity;
import blockChain.entities.message.AlertMessage;
import blockChain.entities.message.QAlertMessage;
import blockChain.exception.EntityNotFoundException;
import blockChain.mapper.AlertMessageDtoMapper;
import blockChain.repository.AlertMessagePredicates;
import blockChain.service.AlertMessageService;
import blockChain.service.UserService;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.querydsl.QPageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> Chan
 * @date 2020/4/9 14:19
 */
@Service
@AllArgsConstructor
public class AlertMessageServiceFacade {
  private AlertMessageService service;
  private final UserService userService;

  @Transactional
  public void create(AlertMessageDto dto) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
    Assert.notNull(currentUserId, "匿名用户不允许发送站内消息");
    UserEntity userEntity = userService.findById(currentUserId).orElseThrow(() -> new EntityNotFoundException("未找到该用户"));

    dto.setUuid(UUID.randomUUID().toString());
    AlertMessage msg = AlertMessageDtoMapper.INSTANCE.toEntity(dto);
    if(msg.getStatus() == null){
      msg.setStatus(AlertMessageDto.StatusEnum.DISABLE);
    }
    msg.setCreator(userEntity);
    service.save(msg);
  }

  @Transactional
  public void delete(String uuid) {
    AlertMessage message = service.findByUuid(uuid).orElseThrow(NotFountException::new);
    message.setCreator(null);
    service.delete(message);
  }

  @Transactional
  public void update(AlertMessageDto dto) {
    Assert.notNull(dto.getUuid(), "UUID不能为空");
    AlertMessage message = service.findByUuid(dto.getUuid()).orElseThrow(NotFountException::new);
    AlertMessageDtoMapper.INSTANCE.update(message, dto);
    // 保证修改后前端能重新弹窗
    message.setUuid(UUID.randomUUID().toString());
    if(!AlertMessageDto.StatusEnum.ENABLE.equals(dto.getStatus())){
      // 若非可用状态，则清空Deadline值
      message.setDeadline(null).setStatus(AlertMessageDto.StatusEnum.DISABLE);
    }
  }

  @Transactional(readOnly = true)
  public AlertMessageDto getOne(String uuid) {
    AlertMessage message = service.findByUuid(uuid).orElseThrow(NotFountException::new);
    return AlertMessageDtoMapper.INSTANCE.toDto(message);
  }

  @Transactional(readOnly = true)
  public PageResponse<AlertMessageDto> queryAlertMessage(AlertMessageDtoQueryParam dto) {
    Pageable pageable = dto.getPageable();
    Page<AlertMessage> all = service.findAll(AlertMessagePredicates.titleLikeAndContentLikeAndCreateTimeBetween(dto.getTitle(), dto.getContent(),dto.getCreateTimeStart(), dto.getCreateTimeEnd() ), pageable);
    return AlertMessageDtoMapper.INSTANCE.toDto(all);
  }

  /**
   * 结束通告
   */
  @Transactional
  public void closureNotice() {
    Iterable<AlertMessage> messages = service.findAll(AlertMessagePredicates.deadlineBeforeAndStatusIs(LocalDateTime.now(), AlertMessageDto.StatusEnum.ENABLE,null,null));
    messages.forEach(alertMessage -> {
      alertMessage.setStatus(AlertMessageDto.StatusEnum.DISABLE).setDeadline(null);
    });
  }

  public PageResponse<AlertMessageDto> currentMessage(@Valid AlertMessageDtoQueryParam dto) {
    Page<AlertMessage> all = service.findAll(AlertMessagePredicates.titleAndCreateTimeBeforeAndStatusIs(dto.getTitle(), AlertMessageDto.StatusEnum.ENABLE,dto.getCreateTimeStart(),dto.getCreateTimeEnd()), dto.getPageable());
    return AlertMessageDtoMapper.INSTANCE.toDto(all);
  }

  /**
   * 获取当前最新公告
   * @return
   */
  public List<AlertMessageDto> getAlertMessage() {
    Page<AlertMessage> page = service.findAll(AlertMessagePredicates.deadlineBeforeAndStatusIs(null, AlertMessageDto.StatusEnum.ENABLE,null,null), PageRequest.of(0, 10, new Sort(Sort.Direction.DESC,"updateTime")));
//    AlertMessage message =page.getContent().stream().findFirst().orElse(null);
    return AlertMessageDtoMapper.INSTANCE.toDto(page.getContent());
  }
}
