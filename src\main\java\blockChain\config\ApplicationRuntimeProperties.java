package blockChain.config;

import blockChain.entities.Digital;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 应用程序运行时配置
 * <AUTHOR>
 * @date 2020/4/3 10:32
 */
@Component
@ConfigurationProperties(prefix = "application-runtime-properties")
@Data
public class ApplicationRuntimeProperties {
  private String appName = "";
 /*************=============  以下定义来自数据字典表 ============**************/
  /**
   * 城市数据字典定义
   */
  private String cityDigitalDefineCode;

  /**
   * 港澳台地区定义
   */
  private String provinceGatDigitalDefineCode;
  /**
   * 省份区定义
   */
  private String provinceDigitalDefineCode;
  /**
   * 福建省PID定义
   */
  private Integer provinceFujianDigitalDefinePid;
  /**
   * 国家数据字典PID定义
   */
  private Integer countryDigitalDefinePid;
  /**
   * 中国数据字典CODE定义
   */
  private String cnDigitalDefineCode;
  /**
   * 作品类型
   * zplx
   */
  private String productTypeDigitalDefineCode;
  /**
   * 所有者类型
   */
  private String ownerTypeDigitalDefineCode;
 /*************=============  以下定义来自数据字典表 ============**************/

}
