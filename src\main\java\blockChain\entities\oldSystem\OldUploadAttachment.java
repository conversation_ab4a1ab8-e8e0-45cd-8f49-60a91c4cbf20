package blockChain.entities.oldSystem;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="old_upload_works_infos")
public class OldUploadAttachment {

	// 旧系统数据迁移用实体类，迁移后删除
	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer uwinfos_id;// 作品图片ID
  private Long works_id;// 作品表ID
  private String works_url;  // 作品样本图片1
  private String workSampleTwo;  // 作品样本图片2
  private String workSampleThree;//作品样本图片3
  private String workName;//作品名称
  private int fileType;//文件类型
  private String workFileOne;//上传文档作品样本
  private String workVideo;//上传视频作品样本
  private int works_classification;//分别是草稿箱中保存的数据，还是作品提交的数据
}
