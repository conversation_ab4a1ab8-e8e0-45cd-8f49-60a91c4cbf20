package blockChain.service;

import blockChain.entities.UploadAttachment;
import blockChain.repository.UploadAttachmentRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class UploadAttachmentService {

    @Autowired
    private UploadAttachmentRepository uploadAttachmentRepository;

    public void insert(UploadAttachment uploadAttachment){
        uploadAttachmentRepository.save(uploadAttachment);
    }

    public UploadAttachment getById(Long id){
        return uploadAttachmentRepository.getById(id);
    }

    public void removeById(Long id){
        uploadAttachmentRepository.deleteById(id);
    }

    public List<UploadAttachment> getByCopyrightId(Long copyrightId) {
        return uploadAttachmentRepository.getByCopyrightId(copyrightId);
    }

    public String getUserNameByWorkUrl(String workUrl) {
        return uploadAttachmentRepository.getUserNameByWorkUrl(workUrl);
    }

    public String getUserNameByAttachmentId(Long attachmentId) {
        return uploadAttachmentRepository.getUserNameByAttachmentId(attachmentId);
    }

    public String getUserNameById(Long id) {
        return uploadAttachmentRepository.getUserNameById(id);
    }

    public List<Long> getCopyrightId(Long id) {
        return uploadAttachmentRepository.getCopyrightId(id);
    }
}
