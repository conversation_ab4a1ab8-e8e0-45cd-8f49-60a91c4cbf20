package blockChain.bean;

import com.ctsi.ssdc.security.CscpUserDetail;

/**
 * <AUTHOR>
 * @date 2019/11/28 19:00
 */
public class JwtResult {
    private String jwt;
    private CscpUserDetail userDetail;

    public JwtResult(String jwt,CscpUserDetail userDetail){
        this.jwt=jwt;
        this.userDetail = userDetail;
    }

    public String getJwt() {
        return jwt;
    }

    public void setJwt(String jwt) {
        this.jwt = jwt;
    }

    public CscpUserDetail getUserDetail() {
        return userDetail;
    }

    public void setUserDetail(CscpUserDetail userDetail) {
        this.userDetail = userDetail;
    }
}
