/**
 * ApasInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.linewell.apas.info;

public class ApasInfo  implements java.io.Serializable {
    private String accept_deptid;

    private String accept_opinion;

    private String accept_time;

    private String accept_userid;

    private String address;

    private String apply_area_code;

    private String apply_type;

    private int applycount;

    private String applyfrom;

    private String applyname;

    private String area_code;

    private String business_license;

    private String central_approval_itemid;

    private String classify_name;

    private String classify_unid;

    private String contact_address;

    private String contact_email;

    private String contact_idcard;

    private String contact_idcard_type;

    private String contact_mobile;

    private String contact_phone;

    private String contact_postcode;

    private String contact_sex;

    private String contactman;

    private String create_time;

    private String create_userid;

    private String create_username;

    private String deal_man;

    private String deal_office;

    private String deleteflag;

    private int distanceday;

    private String effistate;

    private int effivalue;

    private String email;

    private String finish_gettype;

    private String formid;

    private String fromareacode;

    private String fromareaname;

    private String get_time;

    private String green_way;

    private String handlestate;

    private String idcard;

    private String idcard_type;

    private String infotype;

    private String is_send_result;

    private String isend_bujian_buqi;

    private String ispublic_result;

    private String legal_idcard;

    private String legal_idcard_type;

    private String legalman;

    private String license_status;

    private String memo;

    private String mobile;

    private String node_etime;

    private String node_stime;

    private String open_way;

    private String parentid;

    private String phone;

    private String post_address;

    private String post_mobliephone;

    private String post_phone;

    private String post_postcode;

    private String post_username;

    private String postcode;

    private String prejudge_time;

    private String proj_code;

    private String project_code;

    private String projectname;

    private String projid;

    private String projpwd;

    private String promise_etime;

    private int promiseday;

    private String promiseday_method;

    private String promisetype;

    private String receive_deptcode;

    private String receive_deptid;

    private String receive_deptname;

    private String receive_time;

    private String receive_userid;

    private String receive_username;

    private String receiveask_deptid;

    private String receiveask_end_time;

    private String receiveask_time;

    private String receiveask_userid;

    private String result;

    private String result_code;

    private String sendacceptuserid;

    private String sendacceptusermobile;

    private String sendacceptusername;

    private String service_deptid;

    private String servicecode;

    private String serviceid;

    private String servicename;

    private String servicetype;

    private String sex;

    private int starproperty;

    private String sync;

    private String terminate_userid;

    private String terminate_username;

    private String transact_time;

    private String unid;

    private String unit_type;

    private String unite_notice_code;

    private String unite_opinion_content;

    private String unite_opinion_datetime;

    private String unite_opinion_username;

    private String unite_opinion_userunid;

    private String unite_project_info;

    private String validatedate;

    public ApasInfo() {
    }

    public ApasInfo(
           String accept_deptid,
           String accept_opinion,
           String accept_time,
           String accept_userid,
           String address,
           String apply_area_code,
           String apply_type,
           int applycount,
           String applyfrom,
           String applyname,
           String area_code,
           String business_license,
           String central_approval_itemid,
           String classify_name,
           String classify_unid,
           String contact_address,
           String contact_email,
           String contact_idcard,
           String contact_idcard_type,
           String contact_mobile,
           String contact_phone,
           String contact_postcode,
           String contact_sex,
           String contactman,
           String create_time,
           String create_userid,
           String create_username,
           String deal_man,
           String deal_office,
           String deleteflag,
           int distanceday,
           String effistate,
           int effivalue,
           String email,
           String finish_gettype,
           String formid,
           String fromareacode,
           String fromareaname,
           String get_time,
           String green_way,
           String handlestate,
           String idcard,
           String idcard_type,
           String infotype,
           String is_send_result,
           String isend_bujian_buqi,
           String ispublic_result,
           String legal_idcard,
           String legal_idcard_type,
           String legalman,
           String license_status,
           String memo,
           String mobile,
           String node_etime,
           String node_stime,
           String open_way,
           String parentid,
           String phone,
           String post_address,
           String post_mobliephone,
           String post_phone,
           String post_postcode,
           String post_username,
           String postcode,
           String prejudge_time,
           String proj_code,
           String project_code,
           String projectname,
           String projid,
           String projpwd,
           String promise_etime,
           int promiseday,
           String promiseday_method,
           String promisetype,
           String receive_deptcode,
           String receive_deptid,
           String receive_deptname,
           String receive_time,
           String receive_userid,
           String receive_username,
           String receiveask_deptid,
           String receiveask_end_time,
           String receiveask_time,
           String receiveask_userid,
           String result,
           String result_code,
           String sendacceptuserid,
           String sendacceptusermobile,
           String sendacceptusername,
           String service_deptid,
           String servicecode,
           String serviceid,
           String servicename,
           String servicetype,
           String sex,
           int starproperty,
           String sync,
           String terminate_userid,
           String terminate_username,
           String transact_time,
           String unid,
           String unit_type,
           String unite_notice_code,
           String unite_opinion_content,
           String unite_opinion_datetime,
           String unite_opinion_username,
           String unite_opinion_userunid,
           String unite_project_info,
           String validatedate) {
           this.accept_deptid = accept_deptid;
           this.accept_opinion = accept_opinion;
           this.accept_time = accept_time;
           this.accept_userid = accept_userid;
           this.address = address;
           this.apply_area_code = apply_area_code;
           this.apply_type = apply_type;
           this.applycount = applycount;
           this.applyfrom = applyfrom;
           this.applyname = applyname;
           this.area_code = area_code;
           this.business_license = business_license;
           this.central_approval_itemid = central_approval_itemid;
           this.classify_name = classify_name;
           this.classify_unid = classify_unid;
           this.contact_address = contact_address;
           this.contact_email = contact_email;
           this.contact_idcard = contact_idcard;
           this.contact_idcard_type = contact_idcard_type;
           this.contact_mobile = contact_mobile;
           this.contact_phone = contact_phone;
           this.contact_postcode = contact_postcode;
           this.contact_sex = contact_sex;
           this.contactman = contactman;
           this.create_time = create_time;
           this.create_userid = create_userid;
           this.create_username = create_username;
           this.deal_man = deal_man;
           this.deal_office = deal_office;
           this.deleteflag = deleteflag;
           this.distanceday = distanceday;
           this.effistate = effistate;
           this.effivalue = effivalue;
           this.email = email;
           this.finish_gettype = finish_gettype;
           this.formid = formid;
           this.fromareacode = fromareacode;
           this.fromareaname = fromareaname;
           this.get_time = get_time;
           this.green_way = green_way;
           this.handlestate = handlestate;
           this.idcard = idcard;
           this.idcard_type = idcard_type;
           this.infotype = infotype;
           this.is_send_result = is_send_result;
           this.isend_bujian_buqi = isend_bujian_buqi;
           this.ispublic_result = ispublic_result;
           this.legal_idcard = legal_idcard;
           this.legal_idcard_type = legal_idcard_type;
           this.legalman = legalman;
           this.license_status = license_status;
           this.memo = memo;
           this.mobile = mobile;
           this.node_etime = node_etime;
           this.node_stime = node_stime;
           this.open_way = open_way;
           this.parentid = parentid;
           this.phone = phone;
           this.post_address = post_address;
           this.post_mobliephone = post_mobliephone;
           this.post_phone = post_phone;
           this.post_postcode = post_postcode;
           this.post_username = post_username;
           this.postcode = postcode;
           this.prejudge_time = prejudge_time;
           this.proj_code = proj_code;
           this.project_code = project_code;
           this.projectname = projectname;
           this.projid = projid;
           this.projpwd = projpwd;
           this.promise_etime = promise_etime;
           this.promiseday = promiseday;
           this.promiseday_method = promiseday_method;
           this.promisetype = promisetype;
           this.receive_deptcode = receive_deptcode;
           this.receive_deptid = receive_deptid;
           this.receive_deptname = receive_deptname;
           this.receive_time = receive_time;
           this.receive_userid = receive_userid;
           this.receive_username = receive_username;
           this.receiveask_deptid = receiveask_deptid;
           this.receiveask_end_time = receiveask_end_time;
           this.receiveask_time = receiveask_time;
           this.receiveask_userid = receiveask_userid;
           this.result = result;
           this.result_code = result_code;
           this.sendacceptuserid = sendacceptuserid;
           this.sendacceptusermobile = sendacceptusermobile;
           this.sendacceptusername = sendacceptusername;
           this.service_deptid = service_deptid;
           this.servicecode = servicecode;
           this.serviceid = serviceid;
           this.servicename = servicename;
           this.servicetype = servicetype;
           this.sex = sex;
           this.starproperty = starproperty;
           this.sync = sync;
           this.terminate_userid = terminate_userid;
           this.terminate_username = terminate_username;
           this.transact_time = transact_time;
           this.unid = unid;
           this.unit_type = unit_type;
           this.unite_notice_code = unite_notice_code;
           this.unite_opinion_content = unite_opinion_content;
           this.unite_opinion_datetime = unite_opinion_datetime;
           this.unite_opinion_username = unite_opinion_username;
           this.unite_opinion_userunid = unite_opinion_userunid;
           this.unite_project_info = unite_project_info;
           this.validatedate = validatedate;
    }


    /**
     * Gets the accept_deptid value for this ApasInfo.
     * 
     * @return accept_deptid
     */
    public String getAccept_deptid() {
        return accept_deptid;
    }


    /**
     * Sets the accept_deptid value for this ApasInfo.
     * 
     * @param accept_deptid
     */
    public void setAccept_deptid(String accept_deptid) {
        this.accept_deptid = accept_deptid;
    }


    /**
     * Gets the accept_opinion value for this ApasInfo.
     * 
     * @return accept_opinion
     */
    public String getAccept_opinion() {
        return accept_opinion;
    }


    /**
     * Sets the accept_opinion value for this ApasInfo.
     * 
     * @param accept_opinion
     */
    public void setAccept_opinion(String accept_opinion) {
        this.accept_opinion = accept_opinion;
    }


    /**
     * Gets the accept_time value for this ApasInfo.
     * 
     * @return accept_time
     */
    public String getAccept_time() {
        return accept_time;
    }


    /**
     * Sets the accept_time value for this ApasInfo.
     * 
     * @param accept_time
     */
    public void setAccept_time(String accept_time) {
        this.accept_time = accept_time;
    }


    /**
     * Gets the accept_userid value for this ApasInfo.
     * 
     * @return accept_userid
     */
    public String getAccept_userid() {
        return accept_userid;
    }


    /**
     * Sets the accept_userid value for this ApasInfo.
     * 
     * @param accept_userid
     */
    public void setAccept_userid(String accept_userid) {
        this.accept_userid = accept_userid;
    }


    /**
     * Gets the address value for this ApasInfo.
     * 
     * @return address
     */
    public String getAddress() {
        return address;
    }


    /**
     * Sets the address value for this ApasInfo.
     * 
     * @param address
     */
    public void setAddress(String address) {
        this.address = address;
    }


    /**
     * Gets the apply_area_code value for this ApasInfo.
     * 
     * @return apply_area_code
     */
    public String getApply_area_code() {
        return apply_area_code;
    }


    /**
     * Sets the apply_area_code value for this ApasInfo.
     * 
     * @param apply_area_code
     */
    public void setApply_area_code(String apply_area_code) {
        this.apply_area_code = apply_area_code;
    }


    /**
     * Gets the apply_type value for this ApasInfo.
     * 
     * @return apply_type
     */
    public String getApply_type() {
        return apply_type;
    }


    /**
     * Sets the apply_type value for this ApasInfo.
     * 
     * @param apply_type
     */
    public void setApply_type(String apply_type) {
        this.apply_type = apply_type;
    }


    /**
     * Gets the applycount value for this ApasInfo.
     * 
     * @return applycount
     */
    public int getApplycount() {
        return applycount;
    }


    /**
     * Sets the applycount value for this ApasInfo.
     * 
     * @param applycount
     */
    public void setApplycount(int applycount) {
        this.applycount = applycount;
    }


    /**
     * Gets the applyfrom value for this ApasInfo.
     * 
     * @return applyfrom
     */
    public String getApplyfrom() {
        return applyfrom;
    }


    /**
     * Sets the applyfrom value for this ApasInfo.
     * 
     * @param applyfrom
     */
    public void setApplyfrom(String applyfrom) {
        this.applyfrom = applyfrom;
    }


    /**
     * Gets the applyname value for this ApasInfo.
     * 
     * @return applyname
     */
    public String getApplyname() {
        return applyname;
    }


    /**
     * Sets the applyname value for this ApasInfo.
     * 
     * @param applyname
     */
    public void setApplyname(String applyname) {
        this.applyname = applyname;
    }


    /**
     * Gets the area_code value for this ApasInfo.
     * 
     * @return area_code
     */
    public String getArea_code() {
        return area_code;
    }


    /**
     * Sets the area_code value for this ApasInfo.
     * 
     * @param area_code
     */
    public void setArea_code(String area_code) {
        this.area_code = area_code;
    }


    /**
     * Gets the business_license value for this ApasInfo.
     * 
     * @return business_license
     */
    public String getBusiness_license() {
        return business_license;
    }


    /**
     * Sets the business_license value for this ApasInfo.
     * 
     * @param business_license
     */
    public void setBusiness_license(String business_license) {
        this.business_license = business_license;
    }


    /**
     * Gets the central_approval_itemid value for this ApasInfo.
     * 
     * @return central_approval_itemid
     */
    public String getCentral_approval_itemid() {
        return central_approval_itemid;
    }


    /**
     * Sets the central_approval_itemid value for this ApasInfo.
     * 
     * @param central_approval_itemid
     */
    public void setCentral_approval_itemid(String central_approval_itemid) {
        this.central_approval_itemid = central_approval_itemid;
    }


    /**
     * Gets the classify_name value for this ApasInfo.
     * 
     * @return classify_name
     */
    public String getClassify_name() {
        return classify_name;
    }


    /**
     * Sets the classify_name value for this ApasInfo.
     * 
     * @param classify_name
     */
    public void setClassify_name(String classify_name) {
        this.classify_name = classify_name;
    }


    /**
     * Gets the classify_unid value for this ApasInfo.
     * 
     * @return classify_unid
     */
    public String getClassify_unid() {
        return classify_unid;
    }


    /**
     * Sets the classify_unid value for this ApasInfo.
     * 
     * @param classify_unid
     */
    public void setClassify_unid(String classify_unid) {
        this.classify_unid = classify_unid;
    }


    /**
     * Gets the contact_address value for this ApasInfo.
     * 
     * @return contact_address
     */
    public String getContact_address() {
        return contact_address;
    }


    /**
     * Sets the contact_address value for this ApasInfo.
     * 
     * @param contact_address
     */
    public void setContact_address(String contact_address) {
        this.contact_address = contact_address;
    }


    /**
     * Gets the contact_email value for this ApasInfo.
     * 
     * @return contact_email
     */
    public String getContact_email() {
        return contact_email;
    }


    /**
     * Sets the contact_email value for this ApasInfo.
     * 
     * @param contact_email
     */
    public void setContact_email(String contact_email) {
        this.contact_email = contact_email;
    }


    /**
     * Gets the contact_idcard value for this ApasInfo.
     * 
     * @return contact_idcard
     */
    public String getContact_idcard() {
        return contact_idcard;
    }


    /**
     * Sets the contact_idcard value for this ApasInfo.
     * 
     * @param contact_idcard
     */
    public void setContact_idcard(String contact_idcard) {
        this.contact_idcard = contact_idcard;
    }


    /**
     * Gets the contact_idcard_type value for this ApasInfo.
     * 
     * @return contact_idcard_type
     */
    public String getContact_idcard_type() {
        return contact_idcard_type;
    }


    /**
     * Sets the contact_idcard_type value for this ApasInfo.
     * 
     * @param contact_idcard_type
     */
    public void setContact_idcard_type(String contact_idcard_type) {
        this.contact_idcard_type = contact_idcard_type;
    }


    /**
     * Gets the contact_mobile value for this ApasInfo.
     * 
     * @return contact_mobile
     */
    public String getContact_mobile() {
        return contact_mobile;
    }


    /**
     * Sets the contact_mobile value for this ApasInfo.
     * 
     * @param contact_mobile
     */
    public void setContact_mobile(String contact_mobile) {
        this.contact_mobile = contact_mobile;
    }


    /**
     * Gets the contact_phone value for this ApasInfo.
     * 
     * @return contact_phone
     */
    public String getContact_phone() {
        return contact_phone;
    }


    /**
     * Sets the contact_phone value for this ApasInfo.
     * 
     * @param contact_phone
     */
    public void setContact_phone(String contact_phone) {
        this.contact_phone = contact_phone;
    }


    /**
     * Gets the contact_postcode value for this ApasInfo.
     * 
     * @return contact_postcode
     */
    public String getContact_postcode() {
        return contact_postcode;
    }


    /**
     * Sets the contact_postcode value for this ApasInfo.
     * 
     * @param contact_postcode
     */
    public void setContact_postcode(String contact_postcode) {
        this.contact_postcode = contact_postcode;
    }


    /**
     * Gets the contact_sex value for this ApasInfo.
     * 
     * @return contact_sex
     */
    public String getContact_sex() {
        return contact_sex;
    }


    /**
     * Sets the contact_sex value for this ApasInfo.
     * 
     * @param contact_sex
     */
    public void setContact_sex(String contact_sex) {
        this.contact_sex = contact_sex;
    }


    /**
     * Gets the contactman value for this ApasInfo.
     * 
     * @return contactman
     */
    public String getContactman() {
        return contactman;
    }


    /**
     * Sets the contactman value for this ApasInfo.
     * 
     * @param contactman
     */
    public void setContactman(String contactman) {
        this.contactman = contactman;
    }


    /**
     * Gets the create_time value for this ApasInfo.
     * 
     * @return create_time
     */
    public String getCreate_time() {
        return create_time;
    }


    /**
     * Sets the create_time value for this ApasInfo.
     * 
     * @param create_time
     */
    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }


    /**
     * Gets the create_userid value for this ApasInfo.
     * 
     * @return create_userid
     */
    public String getCreate_userid() {
        return create_userid;
    }


    /**
     * Sets the create_userid value for this ApasInfo.
     * 
     * @param create_userid
     */
    public void setCreate_userid(String create_userid) {
        this.create_userid = create_userid;
    }


    /**
     * Gets the create_username value for this ApasInfo.
     * 
     * @return create_username
     */
    public String getCreate_username() {
        return create_username;
    }


    /**
     * Sets the create_username value for this ApasInfo.
     * 
     * @param create_username
     */
    public void setCreate_username(String create_username) {
        this.create_username = create_username;
    }


    /**
     * Gets the deal_man value for this ApasInfo.
     * 
     * @return deal_man
     */
    public String getDeal_man() {
        return deal_man;
    }


    /**
     * Sets the deal_man value for this ApasInfo.
     * 
     * @param deal_man
     */
    public void setDeal_man(String deal_man) {
        this.deal_man = deal_man;
    }


    /**
     * Gets the deal_office value for this ApasInfo.
     * 
     * @return deal_office
     */
    public String getDeal_office() {
        return deal_office;
    }


    /**
     * Sets the deal_office value for this ApasInfo.
     * 
     * @param deal_office
     */
    public void setDeal_office(String deal_office) {
        this.deal_office = deal_office;
    }


    /**
     * Gets the deleteflag value for this ApasInfo.
     * 
     * @return deleteflag
     */
    public String getDeleteflag() {
        return deleteflag;
    }


    /**
     * Sets the deleteflag value for this ApasInfo.
     * 
     * @param deleteflag
     */
    public void setDeleteflag(String deleteflag) {
        this.deleteflag = deleteflag;
    }


    /**
     * Gets the distanceday value for this ApasInfo.
     * 
     * @return distanceday
     */
    public int getDistanceday() {
        return distanceday;
    }


    /**
     * Sets the distanceday value for this ApasInfo.
     * 
     * @param distanceday
     */
    public void setDistanceday(int distanceday) {
        this.distanceday = distanceday;
    }


    /**
     * Gets the effistate value for this ApasInfo.
     * 
     * @return effistate
     */
    public String getEffistate() {
        return effistate;
    }


    /**
     * Sets the effistate value for this ApasInfo.
     * 
     * @param effistate
     */
    public void setEffistate(String effistate) {
        this.effistate = effistate;
    }


    /**
     * Gets the effivalue value for this ApasInfo.
     * 
     * @return effivalue
     */
    public int getEffivalue() {
        return effivalue;
    }


    /**
     * Sets the effivalue value for this ApasInfo.
     * 
     * @param effivalue
     */
    public void setEffivalue(int effivalue) {
        this.effivalue = effivalue;
    }


    /**
     * Gets the email value for this ApasInfo.
     * 
     * @return email
     */
    public String getEmail() {
        return email;
    }


    /**
     * Sets the email value for this ApasInfo.
     * 
     * @param email
     */
    public void setEmail(String email) {
        this.email = email;
    }


    /**
     * Gets the finish_gettype value for this ApasInfo.
     * 
     * @return finish_gettype
     */
    public String getFinish_gettype() {
        return finish_gettype;
    }


    /**
     * Sets the finish_gettype value for this ApasInfo.
     * 
     * @param finish_gettype
     */
    public void setFinish_gettype(String finish_gettype) {
        this.finish_gettype = finish_gettype;
    }


    /**
     * Gets the formid value for this ApasInfo.
     * 
     * @return formid
     */
    public String getFormid() {
        return formid;
    }


    /**
     * Sets the formid value for this ApasInfo.
     * 
     * @param formid
     */
    public void setFormid(String formid) {
        this.formid = formid;
    }


    /**
     * Gets the fromareacode value for this ApasInfo.
     * 
     * @return fromareacode
     */
    public String getFromareacode() {
        return fromareacode;
    }


    /**
     * Sets the fromareacode value for this ApasInfo.
     * 
     * @param fromareacode
     */
    public void setFromareacode(String fromareacode) {
        this.fromareacode = fromareacode;
    }


    /**
     * Gets the fromareaname value for this ApasInfo.
     * 
     * @return fromareaname
     */
    public String getFromareaname() {
        return fromareaname;
    }


    /**
     * Sets the fromareaname value for this ApasInfo.
     * 
     * @param fromareaname
     */
    public void setFromareaname(String fromareaname) {
        this.fromareaname = fromareaname;
    }


    /**
     * Gets the get_time value for this ApasInfo.
     * 
     * @return get_time
     */
    public String getGet_time() {
        return get_time;
    }


    /**
     * Sets the get_time value for this ApasInfo.
     * 
     * @param get_time
     */
    public void setGet_time(String get_time) {
        this.get_time = get_time;
    }


    /**
     * Gets the green_way value for this ApasInfo.
     * 
     * @return green_way
     */
    public String getGreen_way() {
        return green_way;
    }


    /**
     * Sets the green_way value for this ApasInfo.
     * 
     * @param green_way
     */
    public void setGreen_way(String green_way) {
        this.green_way = green_way;
    }


    /**
     * Gets the handlestate value for this ApasInfo.
     * 
     * @return handlestate
     */
    public String getHandlestate() {
        return handlestate;
    }


    /**
     * Sets the handlestate value for this ApasInfo.
     * 
     * @param handlestate
     */
    public void setHandlestate(String handlestate) {
        this.handlestate = handlestate;
    }


    /**
     * Gets the idcard value for this ApasInfo.
     * 
     * @return idcard
     */
    public String getIdcard() {
        return idcard;
    }


    /**
     * Sets the idcard value for this ApasInfo.
     * 
     * @param idcard
     */
    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }


    /**
     * Gets the idcard_type value for this ApasInfo.
     * 
     * @return idcard_type
     */
    public String getIdcard_type() {
        return idcard_type;
    }


    /**
     * Sets the idcard_type value for this ApasInfo.
     * 
     * @param idcard_type
     */
    public void setIdcard_type(String idcard_type) {
        this.idcard_type = idcard_type;
    }


    /**
     * Gets the infotype value for this ApasInfo.
     * 
     * @return infotype
     */
    public String getInfotype() {
        return infotype;
    }


    /**
     * Sets the infotype value for this ApasInfo.
     * 
     * @param infotype
     */
    public void setInfotype(String infotype) {
        this.infotype = infotype;
    }


    /**
     * Gets the is_send_result value for this ApasInfo.
     * 
     * @return is_send_result
     */
    public String getIs_send_result() {
        return is_send_result;
    }


    /**
     * Sets the is_send_result value for this ApasInfo.
     * 
     * @param is_send_result
     */
    public void setIs_send_result(String is_send_result) {
        this.is_send_result = is_send_result;
    }


    /**
     * Gets the isend_bujian_buqi value for this ApasInfo.
     * 
     * @return isend_bujian_buqi
     */
    public String getIsend_bujian_buqi() {
        return isend_bujian_buqi;
    }


    /**
     * Sets the isend_bujian_buqi value for this ApasInfo.
     * 
     * @param isend_bujian_buqi
     */
    public void setIsend_bujian_buqi(String isend_bujian_buqi) {
        this.isend_bujian_buqi = isend_bujian_buqi;
    }


    /**
     * Gets the ispublic_result value for this ApasInfo.
     * 
     * @return ispublic_result
     */
    public String getIspublic_result() {
        return ispublic_result;
    }


    /**
     * Sets the ispublic_result value for this ApasInfo.
     * 
     * @param ispublic_result
     */
    public void setIspublic_result(String ispublic_result) {
        this.ispublic_result = ispublic_result;
    }


    /**
     * Gets the legal_idcard value for this ApasInfo.
     * 
     * @return legal_idcard
     */
    public String getLegal_idcard() {
        return legal_idcard;
    }


    /**
     * Sets the legal_idcard value for this ApasInfo.
     * 
     * @param legal_idcard
     */
    public void setLegal_idcard(String legal_idcard) {
        this.legal_idcard = legal_idcard;
    }


    /**
     * Gets the legal_idcard_type value for this ApasInfo.
     * 
     * @return legal_idcard_type
     */
    public String getLegal_idcard_type() {
        return legal_idcard_type;
    }


    /**
     * Sets the legal_idcard_type value for this ApasInfo.
     * 
     * @param legal_idcard_type
     */
    public void setLegal_idcard_type(String legal_idcard_type) {
        this.legal_idcard_type = legal_idcard_type;
    }


    /**
     * Gets the legalman value for this ApasInfo.
     * 
     * @return legalman
     */
    public String getLegalman() {
        return legalman;
    }


    /**
     * Sets the legalman value for this ApasInfo.
     * 
     * @param legalman
     */
    public void setLegalman(String legalman) {
        this.legalman = legalman;
    }


    /**
     * Gets the license_status value for this ApasInfo.
     * 
     * @return license_status
     */
    public String getLicense_status() {
        return license_status;
    }


    /**
     * Sets the license_status value for this ApasInfo.
     * 
     * @param license_status
     */
    public void setLicense_status(String license_status) {
        this.license_status = license_status;
    }


    /**
     * Gets the memo value for this ApasInfo.
     * 
     * @return memo
     */
    public String getMemo() {
        return memo;
    }


    /**
     * Sets the memo value for this ApasInfo.
     * 
     * @param memo
     */
    public void setMemo(String memo) {
        this.memo = memo;
    }


    /**
     * Gets the mobile value for this ApasInfo.
     * 
     * @return mobile
     */
    public String getMobile() {
        return mobile;
    }


    /**
     * Sets the mobile value for this ApasInfo.
     * 
     * @param mobile
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }


    /**
     * Gets the node_etime value for this ApasInfo.
     * 
     * @return node_etime
     */
    public String getNode_etime() {
        return node_etime;
    }


    /**
     * Sets the node_etime value for this ApasInfo.
     * 
     * @param node_etime
     */
    public void setNode_etime(String node_etime) {
        this.node_etime = node_etime;
    }


    /**
     * Gets the node_stime value for this ApasInfo.
     * 
     * @return node_stime
     */
    public String getNode_stime() {
        return node_stime;
    }


    /**
     * Sets the node_stime value for this ApasInfo.
     * 
     * @param node_stime
     */
    public void setNode_stime(String node_stime) {
        this.node_stime = node_stime;
    }


    /**
     * Gets the open_way value for this ApasInfo.
     * 
     * @return open_way
     */
    public String getOpen_way() {
        return open_way;
    }


    /**
     * Sets the open_way value for this ApasInfo.
     * 
     * @param open_way
     */
    public void setOpen_way(String open_way) {
        this.open_way = open_way;
    }


    /**
     * Gets the parentid value for this ApasInfo.
     * 
     * @return parentid
     */
    public String getParentid() {
        return parentid;
    }


    /**
     * Sets the parentid value for this ApasInfo.
     * 
     * @param parentid
     */
    public void setParentid(String parentid) {
        this.parentid = parentid;
    }


    /**
     * Gets the phone value for this ApasInfo.
     * 
     * @return phone
     */
    public String getPhone() {
        return phone;
    }


    /**
     * Sets the phone value for this ApasInfo.
     * 
     * @param phone
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }


    /**
     * Gets the post_address value for this ApasInfo.
     * 
     * @return post_address
     */
    public String getPost_address() {
        return post_address;
    }


    /**
     * Sets the post_address value for this ApasInfo.
     * 
     * @param post_address
     */
    public void setPost_address(String post_address) {
        this.post_address = post_address;
    }


    /**
     * Gets the post_mobliephone value for this ApasInfo.
     * 
     * @return post_mobliephone
     */
    public String getPost_mobliephone() {
        return post_mobliephone;
    }


    /**
     * Sets the post_mobliephone value for this ApasInfo.
     * 
     * @param post_mobliephone
     */
    public void setPost_mobliephone(String post_mobliephone) {
        this.post_mobliephone = post_mobliephone;
    }


    /**
     * Gets the post_phone value for this ApasInfo.
     * 
     * @return post_phone
     */
    public String getPost_phone() {
        return post_phone;
    }


    /**
     * Sets the post_phone value for this ApasInfo.
     * 
     * @param post_phone
     */
    public void setPost_phone(String post_phone) {
        this.post_phone = post_phone;
    }


    /**
     * Gets the post_postcode value for this ApasInfo.
     * 
     * @return post_postcode
     */
    public String getPost_postcode() {
        return post_postcode;
    }


    /**
     * Sets the post_postcode value for this ApasInfo.
     * 
     * @param post_postcode
     */
    public void setPost_postcode(String post_postcode) {
        this.post_postcode = post_postcode;
    }


    /**
     * Gets the post_username value for this ApasInfo.
     * 
     * @return post_username
     */
    public String getPost_username() {
        return post_username;
    }


    /**
     * Sets the post_username value for this ApasInfo.
     * 
     * @param post_username
     */
    public void setPost_username(String post_username) {
        this.post_username = post_username;
    }


    /**
     * Gets the postcode value for this ApasInfo.
     * 
     * @return postcode
     */
    public String getPostcode() {
        return postcode;
    }


    /**
     * Sets the postcode value for this ApasInfo.
     * 
     * @param postcode
     */
    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }


    /**
     * Gets the prejudge_time value for this ApasInfo.
     * 
     * @return prejudge_time
     */
    public String getPrejudge_time() {
        return prejudge_time;
    }


    /**
     * Sets the prejudge_time value for this ApasInfo.
     * 
     * @param prejudge_time
     */
    public void setPrejudge_time(String prejudge_time) {
        this.prejudge_time = prejudge_time;
    }


    /**
     * Gets the proj_code value for this ApasInfo.
     * 
     * @return proj_code
     */
    public String getProj_code() {
        return proj_code;
    }


    /**
     * Sets the proj_code value for this ApasInfo.
     * 
     * @param proj_code
     */
    public void setProj_code(String proj_code) {
        this.proj_code = proj_code;
    }


    /**
     * Gets the project_code value for this ApasInfo.
     * 
     * @return project_code
     */
    public String getProject_code() {
        return project_code;
    }


    /**
     * Sets the project_code value for this ApasInfo.
     * 
     * @param project_code
     */
    public void setProject_code(String project_code) {
        this.project_code = project_code;
    }


    /**
     * Gets the projectname value for this ApasInfo.
     * 
     * @return projectname
     */
    public String getProjectname() {
        return projectname;
    }


    /**
     * Sets the projectname value for this ApasInfo.
     * 
     * @param projectname
     */
    public void setProjectname(String projectname) {
        this.projectname = projectname;
    }


    /**
     * Gets the projid value for this ApasInfo.
     * 
     * @return projid
     */
    public String getProjid() {
        return projid;
    }


    /**
     * Sets the projid value for this ApasInfo.
     * 
     * @param projid
     */
    public void setProjid(String projid) {
        this.projid = projid;
    }


    /**
     * Gets the projpwd value for this ApasInfo.
     * 
     * @return projpwd
     */
    public String getProjpwd() {
        return projpwd;
    }


    /**
     * Sets the projpwd value for this ApasInfo.
     * 
     * @param projpwd
     */
    public void setProjpwd(String projpwd) {
        this.projpwd = projpwd;
    }


    /**
     * Gets the promise_etime value for this ApasInfo.
     * 
     * @return promise_etime
     */
    public String getPromise_etime() {
        return promise_etime;
    }


    /**
     * Sets the promise_etime value for this ApasInfo.
     * 
     * @param promise_etime
     */
    public void setPromise_etime(String promise_etime) {
        this.promise_etime = promise_etime;
    }


    /**
     * Gets the promiseday value for this ApasInfo.
     * 
     * @return promiseday
     */
    public int getPromiseday() {
        return promiseday;
    }


    /**
     * Sets the promiseday value for this ApasInfo.
     * 
     * @param promiseday
     */
    public void setPromiseday(int promiseday) {
        this.promiseday = promiseday;
    }


    /**
     * Gets the promiseday_method value for this ApasInfo.
     * 
     * @return promiseday_method
     */
    public String getPromiseday_method() {
        return promiseday_method;
    }


    /**
     * Sets the promiseday_method value for this ApasInfo.
     * 
     * @param promiseday_method
     */
    public void setPromiseday_method(String promiseday_method) {
        this.promiseday_method = promiseday_method;
    }


    /**
     * Gets the promisetype value for this ApasInfo.
     * 
     * @return promisetype
     */
    public String getPromisetype() {
        return promisetype;
    }


    /**
     * Sets the promisetype value for this ApasInfo.
     * 
     * @param promisetype
     */
    public void setPromisetype(String promisetype) {
        this.promisetype = promisetype;
    }


    /**
     * Gets the receive_deptcode value for this ApasInfo.
     * 
     * @return receive_deptcode
     */
    public String getReceive_deptcode() {
        return receive_deptcode;
    }


    /**
     * Sets the receive_deptcode value for this ApasInfo.
     * 
     * @param receive_deptcode
     */
    public void setReceive_deptcode(String receive_deptcode) {
        this.receive_deptcode = receive_deptcode;
    }


    /**
     * Gets the receive_deptid value for this ApasInfo.
     * 
     * @return receive_deptid
     */
    public String getReceive_deptid() {
        return receive_deptid;
    }


    /**
     * Sets the receive_deptid value for this ApasInfo.
     * 
     * @param receive_deptid
     */
    public void setReceive_deptid(String receive_deptid) {
        this.receive_deptid = receive_deptid;
    }


    /**
     * Gets the receive_deptname value for this ApasInfo.
     * 
     * @return receive_deptname
     */
    public String getReceive_deptname() {
        return receive_deptname;
    }


    /**
     * Sets the receive_deptname value for this ApasInfo.
     * 
     * @param receive_deptname
     */
    public void setReceive_deptname(String receive_deptname) {
        this.receive_deptname = receive_deptname;
    }


    /**
     * Gets the receive_time value for this ApasInfo.
     * 
     * @return receive_time
     */
    public String getReceive_time() {
        return receive_time;
    }


    /**
     * Sets the receive_time value for this ApasInfo.
     * 
     * @param receive_time
     */
    public void setReceive_time(String receive_time) {
        this.receive_time = receive_time;
    }


    /**
     * Gets the receive_userid value for this ApasInfo.
     * 
     * @return receive_userid
     */
    public String getReceive_userid() {
        return receive_userid;
    }


    /**
     * Sets the receive_userid value for this ApasInfo.
     * 
     * @param receive_userid
     */
    public void setReceive_userid(String receive_userid) {
        this.receive_userid = receive_userid;
    }


    /**
     * Gets the receive_username value for this ApasInfo.
     * 
     * @return receive_username
     */
    public String getReceive_username() {
        return receive_username;
    }


    /**
     * Sets the receive_username value for this ApasInfo.
     * 
     * @param receive_username
     */
    public void setReceive_username(String receive_username) {
        this.receive_username = receive_username;
    }


    /**
     * Gets the receiveask_deptid value for this ApasInfo.
     * 
     * @return receiveask_deptid
     */
    public String getReceiveask_deptid() {
        return receiveask_deptid;
    }


    /**
     * Sets the receiveask_deptid value for this ApasInfo.
     * 
     * @param receiveask_deptid
     */
    public void setReceiveask_deptid(String receiveask_deptid) {
        this.receiveask_deptid = receiveask_deptid;
    }


    /**
     * Gets the receiveask_end_time value for this ApasInfo.
     * 
     * @return receiveask_end_time
     */
    public String getReceiveask_end_time() {
        return receiveask_end_time;
    }


    /**
     * Sets the receiveask_end_time value for this ApasInfo.
     * 
     * @param receiveask_end_time
     */
    public void setReceiveask_end_time(String receiveask_end_time) {
        this.receiveask_end_time = receiveask_end_time;
    }


    /**
     * Gets the receiveask_time value for this ApasInfo.
     * 
     * @return receiveask_time
     */
    public String getReceiveask_time() {
        return receiveask_time;
    }


    /**
     * Sets the receiveask_time value for this ApasInfo.
     * 
     * @param receiveask_time
     */
    public void setReceiveask_time(String receiveask_time) {
        this.receiveask_time = receiveask_time;
    }


    /**
     * Gets the receiveask_userid value for this ApasInfo.
     * 
     * @return receiveask_userid
     */
    public String getReceiveask_userid() {
        return receiveask_userid;
    }


    /**
     * Sets the receiveask_userid value for this ApasInfo.
     * 
     * @param receiveask_userid
     */
    public void setReceiveask_userid(String receiveask_userid) {
        this.receiveask_userid = receiveask_userid;
    }


    /**
     * Gets the result value for this ApasInfo.
     * 
     * @return result
     */
    public String getResult() {
        return result;
    }


    /**
     * Sets the result value for this ApasInfo.
     * 
     * @param result
     */
    public void setResult(String result) {
        this.result = result;
    }


    /**
     * Gets the result_code value for this ApasInfo.
     * 
     * @return result_code
     */
    public String getResult_code() {
        return result_code;
    }


    /**
     * Sets the result_code value for this ApasInfo.
     * 
     * @param result_code
     */
    public void setResult_code(String result_code) {
        this.result_code = result_code;
    }


    /**
     * Gets the sendacceptuserid value for this ApasInfo.
     * 
     * @return sendacceptuserid
     */
    public String getSendacceptuserid() {
        return sendacceptuserid;
    }


    /**
     * Sets the sendacceptuserid value for this ApasInfo.
     * 
     * @param sendacceptuserid
     */
    public void setSendacceptuserid(String sendacceptuserid) {
        this.sendacceptuserid = sendacceptuserid;
    }


    /**
     * Gets the sendacceptusermobile value for this ApasInfo.
     * 
     * @return sendacceptusermobile
     */
    public String getSendacceptusermobile() {
        return sendacceptusermobile;
    }


    /**
     * Sets the sendacceptusermobile value for this ApasInfo.
     * 
     * @param sendacceptusermobile
     */
    public void setSendacceptusermobile(String sendacceptusermobile) {
        this.sendacceptusermobile = sendacceptusermobile;
    }


    /**
     * Gets the sendacceptusername value for this ApasInfo.
     * 
     * @return sendacceptusername
     */
    public String getSendacceptusername() {
        return sendacceptusername;
    }


    /**
     * Sets the sendacceptusername value for this ApasInfo.
     * 
     * @param sendacceptusername
     */
    public void setSendacceptusername(String sendacceptusername) {
        this.sendacceptusername = sendacceptusername;
    }


    /**
     * Gets the service_deptid value for this ApasInfo.
     * 
     * @return service_deptid
     */
    public String getService_deptid() {
        return service_deptid;
    }


    /**
     * Sets the service_deptid value for this ApasInfo.
     * 
     * @param service_deptid
     */
    public void setService_deptid(String service_deptid) {
        this.service_deptid = service_deptid;
    }


    /**
     * Gets the servicecode value for this ApasInfo.
     * 
     * @return servicecode
     */
    public String getServicecode() {
        return servicecode;
    }


    /**
     * Sets the servicecode value for this ApasInfo.
     * 
     * @param servicecode
     */
    public void setServicecode(String servicecode) {
        this.servicecode = servicecode;
    }


    /**
     * Gets the serviceid value for this ApasInfo.
     * 
     * @return serviceid
     */
    public String getServiceid() {
        return serviceid;
    }


    /**
     * Sets the serviceid value for this ApasInfo.
     * 
     * @param serviceid
     */
    public void setServiceid(String serviceid) {
        this.serviceid = serviceid;
    }


    /**
     * Gets the servicename value for this ApasInfo.
     * 
     * @return servicename
     */
    public String getServicename() {
        return servicename;
    }


    /**
     * Sets the servicename value for this ApasInfo.
     * 
     * @param servicename
     */
    public void setServicename(String servicename) {
        this.servicename = servicename;
    }


    /**
     * Gets the servicetype value for this ApasInfo.
     * 
     * @return servicetype
     */
    public String getServicetype() {
        return servicetype;
    }


    /**
     * Sets the servicetype value for this ApasInfo.
     * 
     * @param servicetype
     */
    public void setServicetype(String servicetype) {
        this.servicetype = servicetype;
    }


    /**
     * Gets the sex value for this ApasInfo.
     * 
     * @return sex
     */
    public String getSex() {
        return sex;
    }


    /**
     * Sets the sex value for this ApasInfo.
     * 
     * @param sex
     */
    public void setSex(String sex) {
        this.sex = sex;
    }


    /**
     * Gets the starproperty value for this ApasInfo.
     * 
     * @return starproperty
     */
    public int getStarproperty() {
        return starproperty;
    }


    /**
     * Sets the starproperty value for this ApasInfo.
     * 
     * @param starproperty
     */
    public void setStarproperty(int starproperty) {
        this.starproperty = starproperty;
    }


    /**
     * Gets the sync value for this ApasInfo.
     * 
     * @return sync
     */
    public String getSync() {
        return sync;
    }


    /**
     * Sets the sync value for this ApasInfo.
     * 
     * @param sync
     */
    public void setSync(String sync) {
        this.sync = sync;
    }


    /**
     * Gets the terminate_userid value for this ApasInfo.
     * 
     * @return terminate_userid
     */
    public String getTerminate_userid() {
        return terminate_userid;
    }


    /**
     * Sets the terminate_userid value for this ApasInfo.
     * 
     * @param terminate_userid
     */
    public void setTerminate_userid(String terminate_userid) {
        this.terminate_userid = terminate_userid;
    }


    /**
     * Gets the terminate_username value for this ApasInfo.
     * 
     * @return terminate_username
     */
    public String getTerminate_username() {
        return terminate_username;
    }


    /**
     * Sets the terminate_username value for this ApasInfo.
     * 
     * @param terminate_username
     */
    public void setTerminate_username(String terminate_username) {
        this.terminate_username = terminate_username;
    }


    /**
     * Gets the transact_time value for this ApasInfo.
     * 
     * @return transact_time
     */
    public String getTransact_time() {
        return transact_time;
    }


    /**
     * Sets the transact_time value for this ApasInfo.
     * 
     * @param transact_time
     */
    public void setTransact_time(String transact_time) {
        this.transact_time = transact_time;
    }


    /**
     * Gets the unid value for this ApasInfo.
     * 
     * @return unid
     */
    public String getUnid() {
        return unid;
    }


    /**
     * Sets the unid value for this ApasInfo.
     * 
     * @param unid
     */
    public void setUnid(String unid) {
        this.unid = unid;
    }


    /**
     * Gets the unit_type value for this ApasInfo.
     * 
     * @return unit_type
     */
    public String getUnit_type() {
        return unit_type;
    }


    /**
     * Sets the unit_type value for this ApasInfo.
     * 
     * @param unit_type
     */
    public void setUnit_type(String unit_type) {
        this.unit_type = unit_type;
    }


    /**
     * Gets the unite_notice_code value for this ApasInfo.
     * 
     * @return unite_notice_code
     */
    public String getUnite_notice_code() {
        return unite_notice_code;
    }


    /**
     * Sets the unite_notice_code value for this ApasInfo.
     * 
     * @param unite_notice_code
     */
    public void setUnite_notice_code(String unite_notice_code) {
        this.unite_notice_code = unite_notice_code;
    }


    /**
     * Gets the unite_opinion_content value for this ApasInfo.
     * 
     * @return unite_opinion_content
     */
    public String getUnite_opinion_content() {
        return unite_opinion_content;
    }


    /**
     * Sets the unite_opinion_content value for this ApasInfo.
     * 
     * @param unite_opinion_content
     */
    public void setUnite_opinion_content(String unite_opinion_content) {
        this.unite_opinion_content = unite_opinion_content;
    }


    /**
     * Gets the unite_opinion_datetime value for this ApasInfo.
     * 
     * @return unite_opinion_datetime
     */
    public String getUnite_opinion_datetime() {
        return unite_opinion_datetime;
    }


    /**
     * Sets the unite_opinion_datetime value for this ApasInfo.
     * 
     * @param unite_opinion_datetime
     */
    public void setUnite_opinion_datetime(String unite_opinion_datetime) {
        this.unite_opinion_datetime = unite_opinion_datetime;
    }


    /**
     * Gets the unite_opinion_username value for this ApasInfo.
     * 
     * @return unite_opinion_username
     */
    public String getUnite_opinion_username() {
        return unite_opinion_username;
    }


    /**
     * Sets the unite_opinion_username value for this ApasInfo.
     * 
     * @param unite_opinion_username
     */
    public void setUnite_opinion_username(String unite_opinion_username) {
        this.unite_opinion_username = unite_opinion_username;
    }


    /**
     * Gets the unite_opinion_userunid value for this ApasInfo.
     * 
     * @return unite_opinion_userunid
     */
    public String getUnite_opinion_userunid() {
        return unite_opinion_userunid;
    }


    /**
     * Sets the unite_opinion_userunid value for this ApasInfo.
     * 
     * @param unite_opinion_userunid
     */
    public void setUnite_opinion_userunid(String unite_opinion_userunid) {
        this.unite_opinion_userunid = unite_opinion_userunid;
    }


    /**
     * Gets the unite_project_info value for this ApasInfo.
     * 
     * @return unite_project_info
     */
    public String getUnite_project_info() {
        return unite_project_info;
    }


    /**
     * Sets the unite_project_info value for this ApasInfo.
     * 
     * @param unite_project_info
     */
    public void setUnite_project_info(String unite_project_info) {
        this.unite_project_info = unite_project_info;
    }


    /**
     * Gets the validatedate value for this ApasInfo.
     * 
     * @return validatedate
     */
    public String getValidatedate() {
        return validatedate;
    }


    /**
     * Sets the validatedate value for this ApasInfo.
     * 
     * @param validatedate
     */
    public void setValidatedate(String validatedate) {
        this.validatedate = validatedate;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ApasInfo)) return false;
        ApasInfo other = (ApasInfo) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.accept_deptid==null && other.getAccept_deptid()==null) || 
             (this.accept_deptid!=null &&
              this.accept_deptid.equals(other.getAccept_deptid()))) &&
            ((this.accept_opinion==null && other.getAccept_opinion()==null) || 
             (this.accept_opinion!=null &&
              this.accept_opinion.equals(other.getAccept_opinion()))) &&
            ((this.accept_time==null && other.getAccept_time()==null) || 
             (this.accept_time!=null &&
              this.accept_time.equals(other.getAccept_time()))) &&
            ((this.accept_userid==null && other.getAccept_userid()==null) || 
             (this.accept_userid!=null &&
              this.accept_userid.equals(other.getAccept_userid()))) &&
            ((this.address==null && other.getAddress()==null) || 
             (this.address!=null &&
              this.address.equals(other.getAddress()))) &&
            ((this.apply_area_code==null && other.getApply_area_code()==null) || 
             (this.apply_area_code!=null &&
              this.apply_area_code.equals(other.getApply_area_code()))) &&
            ((this.apply_type==null && other.getApply_type()==null) || 
             (this.apply_type!=null &&
              this.apply_type.equals(other.getApply_type()))) &&
            this.applycount == other.getApplycount() &&
            ((this.applyfrom==null && other.getApplyfrom()==null) || 
             (this.applyfrom!=null &&
              this.applyfrom.equals(other.getApplyfrom()))) &&
            ((this.applyname==null && other.getApplyname()==null) || 
             (this.applyname!=null &&
              this.applyname.equals(other.getApplyname()))) &&
            ((this.area_code==null && other.getArea_code()==null) || 
             (this.area_code!=null &&
              this.area_code.equals(other.getArea_code()))) &&
            ((this.business_license==null && other.getBusiness_license()==null) || 
             (this.business_license!=null &&
              this.business_license.equals(other.getBusiness_license()))) &&
            ((this.central_approval_itemid==null && other.getCentral_approval_itemid()==null) || 
             (this.central_approval_itemid!=null &&
              this.central_approval_itemid.equals(other.getCentral_approval_itemid()))) &&
            ((this.classify_name==null && other.getClassify_name()==null) || 
             (this.classify_name!=null &&
              this.classify_name.equals(other.getClassify_name()))) &&
            ((this.classify_unid==null && other.getClassify_unid()==null) || 
             (this.classify_unid!=null &&
              this.classify_unid.equals(other.getClassify_unid()))) &&
            ((this.contact_address==null && other.getContact_address()==null) || 
             (this.contact_address!=null &&
              this.contact_address.equals(other.getContact_address()))) &&
            ((this.contact_email==null && other.getContact_email()==null) || 
             (this.contact_email!=null &&
              this.contact_email.equals(other.getContact_email()))) &&
            ((this.contact_idcard==null && other.getContact_idcard()==null) || 
             (this.contact_idcard!=null &&
              this.contact_idcard.equals(other.getContact_idcard()))) &&
            ((this.contact_idcard_type==null && other.getContact_idcard_type()==null) || 
             (this.contact_idcard_type!=null &&
              this.contact_idcard_type.equals(other.getContact_idcard_type()))) &&
            ((this.contact_mobile==null && other.getContact_mobile()==null) || 
             (this.contact_mobile!=null &&
              this.contact_mobile.equals(other.getContact_mobile()))) &&
            ((this.contact_phone==null && other.getContact_phone()==null) || 
             (this.contact_phone!=null &&
              this.contact_phone.equals(other.getContact_phone()))) &&
            ((this.contact_postcode==null && other.getContact_postcode()==null) || 
             (this.contact_postcode!=null &&
              this.contact_postcode.equals(other.getContact_postcode()))) &&
            ((this.contact_sex==null && other.getContact_sex()==null) || 
             (this.contact_sex!=null &&
              this.contact_sex.equals(other.getContact_sex()))) &&
            ((this.contactman==null && other.getContactman()==null) || 
             (this.contactman!=null &&
              this.contactman.equals(other.getContactman()))) &&
            ((this.create_time==null && other.getCreate_time()==null) || 
             (this.create_time!=null &&
              this.create_time.equals(other.getCreate_time()))) &&
            ((this.create_userid==null && other.getCreate_userid()==null) || 
             (this.create_userid!=null &&
              this.create_userid.equals(other.getCreate_userid()))) &&
            ((this.create_username==null && other.getCreate_username()==null) || 
             (this.create_username!=null &&
              this.create_username.equals(other.getCreate_username()))) &&
            ((this.deal_man==null && other.getDeal_man()==null) || 
             (this.deal_man!=null &&
              this.deal_man.equals(other.getDeal_man()))) &&
            ((this.deal_office==null && other.getDeal_office()==null) || 
             (this.deal_office!=null &&
              this.deal_office.equals(other.getDeal_office()))) &&
            ((this.deleteflag==null && other.getDeleteflag()==null) || 
             (this.deleteflag!=null &&
              this.deleteflag.equals(other.getDeleteflag()))) &&
            this.distanceday == other.getDistanceday() &&
            ((this.effistate==null && other.getEffistate()==null) || 
             (this.effistate!=null &&
              this.effistate.equals(other.getEffistate()))) &&
            this.effivalue == other.getEffivalue() &&
            ((this.email==null && other.getEmail()==null) || 
             (this.email!=null &&
              this.email.equals(other.getEmail()))) &&
            ((this.finish_gettype==null && other.getFinish_gettype()==null) || 
             (this.finish_gettype!=null &&
              this.finish_gettype.equals(other.getFinish_gettype()))) &&
            ((this.formid==null && other.getFormid()==null) || 
             (this.formid!=null &&
              this.formid.equals(other.getFormid()))) &&
            ((this.fromareacode==null && other.getFromareacode()==null) || 
             (this.fromareacode!=null &&
              this.fromareacode.equals(other.getFromareacode()))) &&
            ((this.fromareaname==null && other.getFromareaname()==null) || 
             (this.fromareaname!=null &&
              this.fromareaname.equals(other.getFromareaname()))) &&
            ((this.get_time==null && other.getGet_time()==null) || 
             (this.get_time!=null &&
              this.get_time.equals(other.getGet_time()))) &&
            ((this.green_way==null && other.getGreen_way()==null) || 
             (this.green_way!=null &&
              this.green_way.equals(other.getGreen_way()))) &&
            ((this.handlestate==null && other.getHandlestate()==null) || 
             (this.handlestate!=null &&
              this.handlestate.equals(other.getHandlestate()))) &&
            ((this.idcard==null && other.getIdcard()==null) || 
             (this.idcard!=null &&
              this.idcard.equals(other.getIdcard()))) &&
            ((this.idcard_type==null && other.getIdcard_type()==null) || 
             (this.idcard_type!=null &&
              this.idcard_type.equals(other.getIdcard_type()))) &&
            ((this.infotype==null && other.getInfotype()==null) || 
             (this.infotype!=null &&
              this.infotype.equals(other.getInfotype()))) &&
            ((this.is_send_result==null && other.getIs_send_result()==null) || 
             (this.is_send_result!=null &&
              this.is_send_result.equals(other.getIs_send_result()))) &&
            ((this.isend_bujian_buqi==null && other.getIsend_bujian_buqi()==null) || 
             (this.isend_bujian_buqi!=null &&
              this.isend_bujian_buqi.equals(other.getIsend_bujian_buqi()))) &&
            ((this.ispublic_result==null && other.getIspublic_result()==null) || 
             (this.ispublic_result!=null &&
              this.ispublic_result.equals(other.getIspublic_result()))) &&
            ((this.legal_idcard==null && other.getLegal_idcard()==null) || 
             (this.legal_idcard!=null &&
              this.legal_idcard.equals(other.getLegal_idcard()))) &&
            ((this.legal_idcard_type==null && other.getLegal_idcard_type()==null) || 
             (this.legal_idcard_type!=null &&
              this.legal_idcard_type.equals(other.getLegal_idcard_type()))) &&
            ((this.legalman==null && other.getLegalman()==null) || 
             (this.legalman!=null &&
              this.legalman.equals(other.getLegalman()))) &&
            ((this.license_status==null && other.getLicense_status()==null) || 
             (this.license_status!=null &&
              this.license_status.equals(other.getLicense_status()))) &&
            ((this.memo==null && other.getMemo()==null) || 
             (this.memo!=null &&
              this.memo.equals(other.getMemo()))) &&
            ((this.mobile==null && other.getMobile()==null) || 
             (this.mobile!=null &&
              this.mobile.equals(other.getMobile()))) &&
            ((this.node_etime==null && other.getNode_etime()==null) || 
             (this.node_etime!=null &&
              this.node_etime.equals(other.getNode_etime()))) &&
            ((this.node_stime==null && other.getNode_stime()==null) || 
             (this.node_stime!=null &&
              this.node_stime.equals(other.getNode_stime()))) &&
            ((this.open_way==null && other.getOpen_way()==null) || 
             (this.open_way!=null &&
              this.open_way.equals(other.getOpen_way()))) &&
            ((this.parentid==null && other.getParentid()==null) || 
             (this.parentid!=null &&
              this.parentid.equals(other.getParentid()))) &&
            ((this.phone==null && other.getPhone()==null) || 
             (this.phone!=null &&
              this.phone.equals(other.getPhone()))) &&
            ((this.post_address==null && other.getPost_address()==null) || 
             (this.post_address!=null &&
              this.post_address.equals(other.getPost_address()))) &&
            ((this.post_mobliephone==null && other.getPost_mobliephone()==null) || 
             (this.post_mobliephone!=null &&
              this.post_mobliephone.equals(other.getPost_mobliephone()))) &&
            ((this.post_phone==null && other.getPost_phone()==null) || 
             (this.post_phone!=null &&
              this.post_phone.equals(other.getPost_phone()))) &&
            ((this.post_postcode==null && other.getPost_postcode()==null) || 
             (this.post_postcode!=null &&
              this.post_postcode.equals(other.getPost_postcode()))) &&
            ((this.post_username==null && other.getPost_username()==null) || 
             (this.post_username!=null &&
              this.post_username.equals(other.getPost_username()))) &&
            ((this.postcode==null && other.getPostcode()==null) || 
             (this.postcode!=null &&
              this.postcode.equals(other.getPostcode()))) &&
            ((this.prejudge_time==null && other.getPrejudge_time()==null) || 
             (this.prejudge_time!=null &&
              this.prejudge_time.equals(other.getPrejudge_time()))) &&
            ((this.proj_code==null && other.getProj_code()==null) || 
             (this.proj_code!=null &&
              this.proj_code.equals(other.getProj_code()))) &&
            ((this.project_code==null && other.getProject_code()==null) || 
             (this.project_code!=null &&
              this.project_code.equals(other.getProject_code()))) &&
            ((this.projectname==null && other.getProjectname()==null) || 
             (this.projectname!=null &&
              this.projectname.equals(other.getProjectname()))) &&
            ((this.projid==null && other.getProjid()==null) || 
             (this.projid!=null &&
              this.projid.equals(other.getProjid()))) &&
            ((this.projpwd==null && other.getProjpwd()==null) || 
             (this.projpwd!=null &&
              this.projpwd.equals(other.getProjpwd()))) &&
            ((this.promise_etime==null && other.getPromise_etime()==null) || 
             (this.promise_etime!=null &&
              this.promise_etime.equals(other.getPromise_etime()))) &&
            this.promiseday == other.getPromiseday() &&
            ((this.promiseday_method==null && other.getPromiseday_method()==null) || 
             (this.promiseday_method!=null &&
              this.promiseday_method.equals(other.getPromiseday_method()))) &&
            ((this.promisetype==null && other.getPromisetype()==null) || 
             (this.promisetype!=null &&
              this.promisetype.equals(other.getPromisetype()))) &&
            ((this.receive_deptcode==null && other.getReceive_deptcode()==null) || 
             (this.receive_deptcode!=null &&
              this.receive_deptcode.equals(other.getReceive_deptcode()))) &&
            ((this.receive_deptid==null && other.getReceive_deptid()==null) || 
             (this.receive_deptid!=null &&
              this.receive_deptid.equals(other.getReceive_deptid()))) &&
            ((this.receive_deptname==null && other.getReceive_deptname()==null) || 
             (this.receive_deptname!=null &&
              this.receive_deptname.equals(other.getReceive_deptname()))) &&
            ((this.receive_time==null && other.getReceive_time()==null) || 
             (this.receive_time!=null &&
              this.receive_time.equals(other.getReceive_time()))) &&
            ((this.receive_userid==null && other.getReceive_userid()==null) || 
             (this.receive_userid!=null &&
              this.receive_userid.equals(other.getReceive_userid()))) &&
            ((this.receive_username==null && other.getReceive_username()==null) || 
             (this.receive_username!=null &&
              this.receive_username.equals(other.getReceive_username()))) &&
            ((this.receiveask_deptid==null && other.getReceiveask_deptid()==null) || 
             (this.receiveask_deptid!=null &&
              this.receiveask_deptid.equals(other.getReceiveask_deptid()))) &&
            ((this.receiveask_end_time==null && other.getReceiveask_end_time()==null) || 
             (this.receiveask_end_time!=null &&
              this.receiveask_end_time.equals(other.getReceiveask_end_time()))) &&
            ((this.receiveask_time==null && other.getReceiveask_time()==null) || 
             (this.receiveask_time!=null &&
              this.receiveask_time.equals(other.getReceiveask_time()))) &&
            ((this.receiveask_userid==null && other.getReceiveask_userid()==null) || 
             (this.receiveask_userid!=null &&
              this.receiveask_userid.equals(other.getReceiveask_userid()))) &&
            ((this.result==null && other.getResult()==null) || 
             (this.result!=null &&
              this.result.equals(other.getResult()))) &&
            ((this.result_code==null && other.getResult_code()==null) || 
             (this.result_code!=null &&
              this.result_code.equals(other.getResult_code()))) &&
            ((this.sendacceptuserid==null && other.getSendacceptuserid()==null) || 
             (this.sendacceptuserid!=null &&
              this.sendacceptuserid.equals(other.getSendacceptuserid()))) &&
            ((this.sendacceptusermobile==null && other.getSendacceptusermobile()==null) || 
             (this.sendacceptusermobile!=null &&
              this.sendacceptusermobile.equals(other.getSendacceptusermobile()))) &&
            ((this.sendacceptusername==null && other.getSendacceptusername()==null) || 
             (this.sendacceptusername!=null &&
              this.sendacceptusername.equals(other.getSendacceptusername()))) &&
            ((this.service_deptid==null && other.getService_deptid()==null) || 
             (this.service_deptid!=null &&
              this.service_deptid.equals(other.getService_deptid()))) &&
            ((this.servicecode==null && other.getServicecode()==null) || 
             (this.servicecode!=null &&
              this.servicecode.equals(other.getServicecode()))) &&
            ((this.serviceid==null && other.getServiceid()==null) || 
             (this.serviceid!=null &&
              this.serviceid.equals(other.getServiceid()))) &&
            ((this.servicename==null && other.getServicename()==null) || 
             (this.servicename!=null &&
              this.servicename.equals(other.getServicename()))) &&
            ((this.servicetype==null && other.getServicetype()==null) || 
             (this.servicetype!=null &&
              this.servicetype.equals(other.getServicetype()))) &&
            ((this.sex==null && other.getSex()==null) || 
             (this.sex!=null &&
              this.sex.equals(other.getSex()))) &&
            this.starproperty == other.getStarproperty() &&
            ((this.sync==null && other.getSync()==null) || 
             (this.sync!=null &&
              this.sync.equals(other.getSync()))) &&
            ((this.terminate_userid==null && other.getTerminate_userid()==null) || 
             (this.terminate_userid!=null &&
              this.terminate_userid.equals(other.getTerminate_userid()))) &&
            ((this.terminate_username==null && other.getTerminate_username()==null) || 
             (this.terminate_username!=null &&
              this.terminate_username.equals(other.getTerminate_username()))) &&
            ((this.transact_time==null && other.getTransact_time()==null) || 
             (this.transact_time!=null &&
              this.transact_time.equals(other.getTransact_time()))) &&
            ((this.unid==null && other.getUnid()==null) || 
             (this.unid!=null &&
              this.unid.equals(other.getUnid()))) &&
            ((this.unit_type==null && other.getUnit_type()==null) || 
             (this.unit_type!=null &&
              this.unit_type.equals(other.getUnit_type()))) &&
            ((this.unite_notice_code==null && other.getUnite_notice_code()==null) || 
             (this.unite_notice_code!=null &&
              this.unite_notice_code.equals(other.getUnite_notice_code()))) &&
            ((this.unite_opinion_content==null && other.getUnite_opinion_content()==null) || 
             (this.unite_opinion_content!=null &&
              this.unite_opinion_content.equals(other.getUnite_opinion_content()))) &&
            ((this.unite_opinion_datetime==null && other.getUnite_opinion_datetime()==null) || 
             (this.unite_opinion_datetime!=null &&
              this.unite_opinion_datetime.equals(other.getUnite_opinion_datetime()))) &&
            ((this.unite_opinion_username==null && other.getUnite_opinion_username()==null) || 
             (this.unite_opinion_username!=null &&
              this.unite_opinion_username.equals(other.getUnite_opinion_username()))) &&
            ((this.unite_opinion_userunid==null && other.getUnite_opinion_userunid()==null) || 
             (this.unite_opinion_userunid!=null &&
              this.unite_opinion_userunid.equals(other.getUnite_opinion_userunid()))) &&
            ((this.unite_project_info==null && other.getUnite_project_info()==null) || 
             (this.unite_project_info!=null &&
              this.unite_project_info.equals(other.getUnite_project_info()))) &&
            ((this.validatedate==null && other.getValidatedate()==null) || 
             (this.validatedate!=null &&
              this.validatedate.equals(other.getValidatedate())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getAccept_deptid() != null) {
            _hashCode += getAccept_deptid().hashCode();
        }
        if (getAccept_opinion() != null) {
            _hashCode += getAccept_opinion().hashCode();
        }
        if (getAccept_time() != null) {
            _hashCode += getAccept_time().hashCode();
        }
        if (getAccept_userid() != null) {
            _hashCode += getAccept_userid().hashCode();
        }
        if (getAddress() != null) {
            _hashCode += getAddress().hashCode();
        }
        if (getApply_area_code() != null) {
            _hashCode += getApply_area_code().hashCode();
        }
        if (getApply_type() != null) {
            _hashCode += getApply_type().hashCode();
        }
        _hashCode += getApplycount();
        if (getApplyfrom() != null) {
            _hashCode += getApplyfrom().hashCode();
        }
        if (getApplyname() != null) {
            _hashCode += getApplyname().hashCode();
        }
        if (getArea_code() != null) {
            _hashCode += getArea_code().hashCode();
        }
        if (getBusiness_license() != null) {
            _hashCode += getBusiness_license().hashCode();
        }
        if (getCentral_approval_itemid() != null) {
            _hashCode += getCentral_approval_itemid().hashCode();
        }
        if (getClassify_name() != null) {
            _hashCode += getClassify_name().hashCode();
        }
        if (getClassify_unid() != null) {
            _hashCode += getClassify_unid().hashCode();
        }
        if (getContact_address() != null) {
            _hashCode += getContact_address().hashCode();
        }
        if (getContact_email() != null) {
            _hashCode += getContact_email().hashCode();
        }
        if (getContact_idcard() != null) {
            _hashCode += getContact_idcard().hashCode();
        }
        if (getContact_idcard_type() != null) {
            _hashCode += getContact_idcard_type().hashCode();
        }
        if (getContact_mobile() != null) {
            _hashCode += getContact_mobile().hashCode();
        }
        if (getContact_phone() != null) {
            _hashCode += getContact_phone().hashCode();
        }
        if (getContact_postcode() != null) {
            _hashCode += getContact_postcode().hashCode();
        }
        if (getContact_sex() != null) {
            _hashCode += getContact_sex().hashCode();
        }
        if (getContactman() != null) {
            _hashCode += getContactman().hashCode();
        }
        if (getCreate_time() != null) {
            _hashCode += getCreate_time().hashCode();
        }
        if (getCreate_userid() != null) {
            _hashCode += getCreate_userid().hashCode();
        }
        if (getCreate_username() != null) {
            _hashCode += getCreate_username().hashCode();
        }
        if (getDeal_man() != null) {
            _hashCode += getDeal_man().hashCode();
        }
        if (getDeal_office() != null) {
            _hashCode += getDeal_office().hashCode();
        }
        if (getDeleteflag() != null) {
            _hashCode += getDeleteflag().hashCode();
        }
        _hashCode += getDistanceday();
        if (getEffistate() != null) {
            _hashCode += getEffistate().hashCode();
        }
        _hashCode += getEffivalue();
        if (getEmail() != null) {
            _hashCode += getEmail().hashCode();
        }
        if (getFinish_gettype() != null) {
            _hashCode += getFinish_gettype().hashCode();
        }
        if (getFormid() != null) {
            _hashCode += getFormid().hashCode();
        }
        if (getFromareacode() != null) {
            _hashCode += getFromareacode().hashCode();
        }
        if (getFromareaname() != null) {
            _hashCode += getFromareaname().hashCode();
        }
        if (getGet_time() != null) {
            _hashCode += getGet_time().hashCode();
        }
        if (getGreen_way() != null) {
            _hashCode += getGreen_way().hashCode();
        }
        if (getHandlestate() != null) {
            _hashCode += getHandlestate().hashCode();
        }
        if (getIdcard() != null) {
            _hashCode += getIdcard().hashCode();
        }
        if (getIdcard_type() != null) {
            _hashCode += getIdcard_type().hashCode();
        }
        if (getInfotype() != null) {
            _hashCode += getInfotype().hashCode();
        }
        if (getIs_send_result() != null) {
            _hashCode += getIs_send_result().hashCode();
        }
        if (getIsend_bujian_buqi() != null) {
            _hashCode += getIsend_bujian_buqi().hashCode();
        }
        if (getIspublic_result() != null) {
            _hashCode += getIspublic_result().hashCode();
        }
        if (getLegal_idcard() != null) {
            _hashCode += getLegal_idcard().hashCode();
        }
        if (getLegal_idcard_type() != null) {
            _hashCode += getLegal_idcard_type().hashCode();
        }
        if (getLegalman() != null) {
            _hashCode += getLegalman().hashCode();
        }
        if (getLicense_status() != null) {
            _hashCode += getLicense_status().hashCode();
        }
        if (getMemo() != null) {
            _hashCode += getMemo().hashCode();
        }
        if (getMobile() != null) {
            _hashCode += getMobile().hashCode();
        }
        if (getNode_etime() != null) {
            _hashCode += getNode_etime().hashCode();
        }
        if (getNode_stime() != null) {
            _hashCode += getNode_stime().hashCode();
        }
        if (getOpen_way() != null) {
            _hashCode += getOpen_way().hashCode();
        }
        if (getParentid() != null) {
            _hashCode += getParentid().hashCode();
        }
        if (getPhone() != null) {
            _hashCode += getPhone().hashCode();
        }
        if (getPost_address() != null) {
            _hashCode += getPost_address().hashCode();
        }
        if (getPost_mobliephone() != null) {
            _hashCode += getPost_mobliephone().hashCode();
        }
        if (getPost_phone() != null) {
            _hashCode += getPost_phone().hashCode();
        }
        if (getPost_postcode() != null) {
            _hashCode += getPost_postcode().hashCode();
        }
        if (getPost_username() != null) {
            _hashCode += getPost_username().hashCode();
        }
        if (getPostcode() != null) {
            _hashCode += getPostcode().hashCode();
        }
        if (getPrejudge_time() != null) {
            _hashCode += getPrejudge_time().hashCode();
        }
        if (getProj_code() != null) {
            _hashCode += getProj_code().hashCode();
        }
        if (getProject_code() != null) {
            _hashCode += getProject_code().hashCode();
        }
        if (getProjectname() != null) {
            _hashCode += getProjectname().hashCode();
        }
        if (getProjid() != null) {
            _hashCode += getProjid().hashCode();
        }
        if (getProjpwd() != null) {
            _hashCode += getProjpwd().hashCode();
        }
        if (getPromise_etime() != null) {
            _hashCode += getPromise_etime().hashCode();
        }
        _hashCode += getPromiseday();
        if (getPromiseday_method() != null) {
            _hashCode += getPromiseday_method().hashCode();
        }
        if (getPromisetype() != null) {
            _hashCode += getPromisetype().hashCode();
        }
        if (getReceive_deptcode() != null) {
            _hashCode += getReceive_deptcode().hashCode();
        }
        if (getReceive_deptid() != null) {
            _hashCode += getReceive_deptid().hashCode();
        }
        if (getReceive_deptname() != null) {
            _hashCode += getReceive_deptname().hashCode();
        }
        if (getReceive_time() != null) {
            _hashCode += getReceive_time().hashCode();
        }
        if (getReceive_userid() != null) {
            _hashCode += getReceive_userid().hashCode();
        }
        if (getReceive_username() != null) {
            _hashCode += getReceive_username().hashCode();
        }
        if (getReceiveask_deptid() != null) {
            _hashCode += getReceiveask_deptid().hashCode();
        }
        if (getReceiveask_end_time() != null) {
            _hashCode += getReceiveask_end_time().hashCode();
        }
        if (getReceiveask_time() != null) {
            _hashCode += getReceiveask_time().hashCode();
        }
        if (getReceiveask_userid() != null) {
            _hashCode += getReceiveask_userid().hashCode();
        }
        if (getResult() != null) {
            _hashCode += getResult().hashCode();
        }
        if (getResult_code() != null) {
            _hashCode += getResult_code().hashCode();
        }
        if (getSendacceptuserid() != null) {
            _hashCode += getSendacceptuserid().hashCode();
        }
        if (getSendacceptusermobile() != null) {
            _hashCode += getSendacceptusermobile().hashCode();
        }
        if (getSendacceptusername() != null) {
            _hashCode += getSendacceptusername().hashCode();
        }
        if (getService_deptid() != null) {
            _hashCode += getService_deptid().hashCode();
        }
        if (getServicecode() != null) {
            _hashCode += getServicecode().hashCode();
        }
        if (getServiceid() != null) {
            _hashCode += getServiceid().hashCode();
        }
        if (getServicename() != null) {
            _hashCode += getServicename().hashCode();
        }
        if (getServicetype() != null) {
            _hashCode += getServicetype().hashCode();
        }
        if (getSex() != null) {
            _hashCode += getSex().hashCode();
        }
        _hashCode += getStarproperty();
        if (getSync() != null) {
            _hashCode += getSync().hashCode();
        }
        if (getTerminate_userid() != null) {
            _hashCode += getTerminate_userid().hashCode();
        }
        if (getTerminate_username() != null) {
            _hashCode += getTerminate_username().hashCode();
        }
        if (getTransact_time() != null) {
            _hashCode += getTransact_time().hashCode();
        }
        if (getUnid() != null) {
            _hashCode += getUnid().hashCode();
        }
        if (getUnit_type() != null) {
            _hashCode += getUnit_type().hashCode();
        }
        if (getUnite_notice_code() != null) {
            _hashCode += getUnite_notice_code().hashCode();
        }
        if (getUnite_opinion_content() != null) {
            _hashCode += getUnite_opinion_content().hashCode();
        }
        if (getUnite_opinion_datetime() != null) {
            _hashCode += getUnite_opinion_datetime().hashCode();
        }
        if (getUnite_opinion_username() != null) {
            _hashCode += getUnite_opinion_username().hashCode();
        }
        if (getUnite_opinion_userunid() != null) {
            _hashCode += getUnite_opinion_userunid().hashCode();
        }
        if (getUnite_project_info() != null) {
            _hashCode += getUnite_project_info().hashCode();
        }
        if (getValidatedate() != null) {
            _hashCode += getValidatedate().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ApasInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://info.apas.linewell.com", "ApasInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("accept_deptid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "accept_deptid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("accept_opinion");
        elemField.setXmlName(new javax.xml.namespace.QName("", "accept_opinion"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("accept_time");
        elemField.setXmlName(new javax.xml.namespace.QName("", "accept_time"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("accept_userid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "accept_userid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("address");
        elemField.setXmlName(new javax.xml.namespace.QName("", "address"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("apply_area_code");
        elemField.setXmlName(new javax.xml.namespace.QName("", "apply_area_code"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("apply_type");
        elemField.setXmlName(new javax.xml.namespace.QName("", "apply_type"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("applycount");
        elemField.setXmlName(new javax.xml.namespace.QName("", "applycount"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("applyfrom");
        elemField.setXmlName(new javax.xml.namespace.QName("", "applyfrom"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("applyname");
        elemField.setXmlName(new javax.xml.namespace.QName("", "applyname"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("area_code");
        elemField.setXmlName(new javax.xml.namespace.QName("", "area_code"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("business_license");
        elemField.setXmlName(new javax.xml.namespace.QName("", "business_license"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("central_approval_itemid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "central_approval_itemid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("classify_name");
        elemField.setXmlName(new javax.xml.namespace.QName("", "classify_name"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("classify_unid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "classify_unid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("contact_address");
        elemField.setXmlName(new javax.xml.namespace.QName("", "contact_address"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("contact_email");
        elemField.setXmlName(new javax.xml.namespace.QName("", "contact_email"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("contact_idcard");
        elemField.setXmlName(new javax.xml.namespace.QName("", "contact_idcard"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("contact_idcard_type");
        elemField.setXmlName(new javax.xml.namespace.QName("", "contact_idcard_type"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("contact_mobile");
        elemField.setXmlName(new javax.xml.namespace.QName("", "contact_mobile"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("contact_phone");
        elemField.setXmlName(new javax.xml.namespace.QName("", "contact_phone"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("contact_postcode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "contact_postcode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("contact_sex");
        elemField.setXmlName(new javax.xml.namespace.QName("", "contact_sex"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("contactman");
        elemField.setXmlName(new javax.xml.namespace.QName("", "contactman"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("create_time");
        elemField.setXmlName(new javax.xml.namespace.QName("", "create_time"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("create_userid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "create_userid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("create_username");
        elemField.setXmlName(new javax.xml.namespace.QName("", "create_username"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("deal_man");
        elemField.setXmlName(new javax.xml.namespace.QName("", "deal_man"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("deal_office");
        elemField.setXmlName(new javax.xml.namespace.QName("", "deal_office"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("deleteflag");
        elemField.setXmlName(new javax.xml.namespace.QName("", "deleteflag"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("distanceday");
        elemField.setXmlName(new javax.xml.namespace.QName("", "distanceday"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("effistate");
        elemField.setXmlName(new javax.xml.namespace.QName("", "effistate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("effivalue");
        elemField.setXmlName(new javax.xml.namespace.QName("", "effivalue"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("email");
        elemField.setXmlName(new javax.xml.namespace.QName("", "email"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("finish_gettype");
        elemField.setXmlName(new javax.xml.namespace.QName("", "finish_gettype"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("formid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "formid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("fromareacode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "fromareacode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("fromareaname");
        elemField.setXmlName(new javax.xml.namespace.QName("", "fromareaname"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("get_time");
        elemField.setXmlName(new javax.xml.namespace.QName("", "get_time"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("green_way");
        elemField.setXmlName(new javax.xml.namespace.QName("", "green_way"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("handlestate");
        elemField.setXmlName(new javax.xml.namespace.QName("", "handlestate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("idcard");
        elemField.setXmlName(new javax.xml.namespace.QName("", "idcard"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("idcard_type");
        elemField.setXmlName(new javax.xml.namespace.QName("", "idcard_type"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("infotype");
        elemField.setXmlName(new javax.xml.namespace.QName("", "infotype"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("is_send_result");
        elemField.setXmlName(new javax.xml.namespace.QName("", "is_send_result"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isend_bujian_buqi");
        elemField.setXmlName(new javax.xml.namespace.QName("", "isend_bujian_buqi"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ispublic_result");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ispublic_result"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("legal_idcard");
        elemField.setXmlName(new javax.xml.namespace.QName("", "legal_idcard"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("legal_idcard_type");
        elemField.setXmlName(new javax.xml.namespace.QName("", "legal_idcard_type"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("legalman");
        elemField.setXmlName(new javax.xml.namespace.QName("", "legalman"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("license_status");
        elemField.setXmlName(new javax.xml.namespace.QName("", "license_status"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("memo");
        elemField.setXmlName(new javax.xml.namespace.QName("", "memo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("mobile");
        elemField.setXmlName(new javax.xml.namespace.QName("", "mobile"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("node_etime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "node_etime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("node_stime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "node_stime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("open_way");
        elemField.setXmlName(new javax.xml.namespace.QName("", "open_way"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("parentid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "parentid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("phone");
        elemField.setXmlName(new javax.xml.namespace.QName("", "phone"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("post_address");
        elemField.setXmlName(new javax.xml.namespace.QName("", "post_address"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("post_mobliephone");
        elemField.setXmlName(new javax.xml.namespace.QName("", "post_mobliephone"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("post_phone");
        elemField.setXmlName(new javax.xml.namespace.QName("", "post_phone"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("post_postcode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "post_postcode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("post_username");
        elemField.setXmlName(new javax.xml.namespace.QName("", "post_username"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("postcode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "postcode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("prejudge_time");
        elemField.setXmlName(new javax.xml.namespace.QName("", "prejudge_time"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("proj_code");
        elemField.setXmlName(new javax.xml.namespace.QName("", "proj_code"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("project_code");
        elemField.setXmlName(new javax.xml.namespace.QName("", "project_code"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("projectname");
        elemField.setXmlName(new javax.xml.namespace.QName("", "projectname"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("projid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "projid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("projpwd");
        elemField.setXmlName(new javax.xml.namespace.QName("", "projpwd"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promise_etime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promise_etime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promiseday");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promiseday"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promiseday_method");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promiseday_method"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("promisetype");
        elemField.setXmlName(new javax.xml.namespace.QName("", "promisetype"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receive_deptcode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receive_deptcode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receive_deptid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receive_deptid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receive_deptname");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receive_deptname"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receive_time");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receive_time"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receive_userid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receive_userid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receive_username");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receive_username"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receiveask_deptid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receiveask_deptid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receiveask_end_time");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receiveask_end_time"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receiveask_time");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receiveask_time"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("receiveask_userid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "receiveask_userid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("result");
        elemField.setXmlName(new javax.xml.namespace.QName("", "result"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("result_code");
        elemField.setXmlName(new javax.xml.namespace.QName("", "result_code"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sendacceptuserid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "sendacceptuserid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sendacceptusermobile");
        elemField.setXmlName(new javax.xml.namespace.QName("", "sendacceptusermobile"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sendacceptusername");
        elemField.setXmlName(new javax.xml.namespace.QName("", "sendacceptusername"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("service_deptid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "service_deptid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("servicecode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "servicecode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("serviceid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "serviceid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("servicename");
        elemField.setXmlName(new javax.xml.namespace.QName("", "servicename"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("servicetype");
        elemField.setXmlName(new javax.xml.namespace.QName("", "servicetype"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sex");
        elemField.setXmlName(new javax.xml.namespace.QName("", "sex"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("starproperty");
        elemField.setXmlName(new javax.xml.namespace.QName("", "starproperty"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sync");
        elemField.setXmlName(new javax.xml.namespace.QName("", "sync"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("terminate_userid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "terminate_userid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("terminate_username");
        elemField.setXmlName(new javax.xml.namespace.QName("", "terminate_username"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("transact_time");
        elemField.setXmlName(new javax.xml.namespace.QName("", "transact_time"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unit_type");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unit_type"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unite_notice_code");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unite_notice_code"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unite_opinion_content");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unite_opinion_content"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unite_opinion_datetime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unite_opinion_datetime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unite_opinion_username");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unite_opinion_username"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unite_opinion_userunid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unite_opinion_userunid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unite_project_info");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unite_project_info"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("validatedate");
        elemField.setXmlName(new javax.xml.namespace.QName("", "validatedate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
