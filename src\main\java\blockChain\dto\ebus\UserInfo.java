package blockChain.dto.ebus;

import lombok.Data;

/**
 * 解密后属性用户基础信息
 */
@Data
public class UserInfo {
    private String searchValue; // 搜索值
    private String createBy; // 创建人
    private String createTime; // 创建时间
    private String updateBy; // 修改人
    private String updateTime; // 修改时间
    private String remark; // 备注
    private String params; // 参数
    private String id; // 用户Id
    private Long oneId; // oneId
    private String username; // 用户名
    private String name; // 姓名
    private String password; // 密码
    private String mobileCountryCode; // 移动号码-国家区号
    private String phone; // 手机号
    private String email; // 邮箱
    private String country; // 国籍（注意：这个字段与下面的country重复了，可能需要重新考虑命名）
    private String nationality; // 民族
    private Integer cardType; // 证件类型
    private String card; // 证件号码
    private String cardStatus; // 证件状态
    private String cardStartTime; // 证件开始时间
    private String cardEndTime; // 证件结束时间
    private String cardAddress; // 证件地址
    private String permanentAddress; // 常住地址（用户基础表）
    private String occupation; // 职业
    private String status; // 用户状态
    private String delFlag; // 删除标识
    private Integer reallevel; // 实名等级
    private Integer sex; // 性别
    private String birthday; // 出生年月
    private String userImg; // 头像URL
    private String maskName; // 脱敏姓名
    private String maskPhone; // 脱敏手机号
    private String maskCard; // 脱敏身份证
    private String maskCardAddress; // 脱敏证件地址
    private String userCustomizeList; // 用户拓展信息列表
    private String userLabelList; // 用户标签信息列表
    private String userDeviceList; // 用户设备信息列表
    private String appId; // APPID
    private String authType; // 授权类型：0仅系统，1仅用户，2双向授权
    private String freezeState; // 冻结状态
    private String freezeExpiration; // 冻结到期日
    private String forbiddenState; // 禁用状态
    private String realnameState; // 导入状态
    private String userMobile; // 用户手机号，此字段可能为空，取用手机号以phone为准
    private String fjUserId; // 福建用户Id,原长威身份认证平台用户ID
    private String ustatus; // 用户授权状态：0未授权；1已授权
    private String sstatus; // 系统授权状态：0未授权；1已授权
}
