ALTER TABLE copyright.tb_dict ADD area_code varchar(12) AFTER sort;
ALTER TABLE copyright.tb_user ADD accessToken varchar(100) NULL COMMENT '福建省政务服务总线token' after lastCommitTime;
ALTER TABLE copyright.tb_user ADD accessTokenExpireTime varchar(50) NULL COMMENT '总线token过期时间' after accessToken;

ALTER TABLE copyright.tb_copyrightmanager ADD projectId varchar(100)  NULL COMMENT '统一收件码';
ALTER TABLE copyright.tb_copyrightmanager ADD createTime DATETIME NULL COMMENT '创建时间';
ALTER TABLE copyright.tb_copyrightmanager ADD updateTime DATETIME NULL COMMENT '更新时间';

select concat('UPDATE copyright.tb_dict SET areaCode= ',areaCode,' where id=',id,';') from copyright.tb_dict where pid =409;

事项编码：	003591504GF05462	基本编码：	000739001000
实施编码：	11350000003591037R2000739001000	业务办理项编码：	11350000003591037R200073900100001

projectId