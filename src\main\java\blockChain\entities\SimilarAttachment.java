package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_similar_attachments")
public class SimilarAttachment {

	// 用户图片匹配表
	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

  @Transient
  private Long attachmentId;

  @Transient
  private String attachmentUrl;

  @Transient
  private Long similarAttachmentId;

  @Transient
  private String similarAttachmentUrl;

	private Integer score;//比对结果（分数）

  @Transient
  private String similarCopyName;
}
