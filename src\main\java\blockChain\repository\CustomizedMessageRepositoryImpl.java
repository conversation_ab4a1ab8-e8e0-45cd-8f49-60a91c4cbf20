package blockChain.repository;

import blockChain.dto.message.MessageDto;
import blockChain.entities.message.MessageEntity;
import blockChain.entities.message.QMessageEntity;
import blockChain.entities.message.QMessageUserEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.types.Projections;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2019/12/18 17:56
 */
public class CustomizedMessageRepositoryImpl extends QuerydslRepositorySupport implements CustomizedMessageRepository {

    public CustomizedMessageRepositoryImpl() {
        super(MessageEntity.class);
    }

    @Override
    public Page<MessageDto> findPageByUserId(int userId, String title, String content, Byte state, LocalDateTime createTimeStart, LocalDateTime createTimeEnd, Pageable pageable) {
        QMessageEntity qMessageEntity = QMessageEntity.messageEntity;
        QMessageUserEntity qMessageUserEntity = QMessageUserEntity.messageUserEntity;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qMessageUserEntity.user.userId.eq(userId));

        if (StringUtils.hasText(title)) {
            builder.and(qMessageEntity.title.contains(title));
        }

        if (StringUtils.hasText(content)) {
            builder.and(qMessageEntity.content.contains(content));
        }

        if (state != null) {
            builder.and(qMessageUserEntity.state.eq(state));
        }
        if (createTimeStart != null) {
            builder.and(qMessageUserEntity.createTime.after(createTimeStart));
        }
        if (createTimeEnd != null) {
          builder.and(qMessageUserEntity.createTime.before(createTimeEnd));
        }

        QueryResults<MessageDto> results = from(qMessageEntity)
            .select(Projections.fields(
                MessageDto.class,
                qMessageEntity.uuid,
                qMessageEntity.title,
                qMessageEntity.content,
                qMessageEntity.createTime,
                qMessageEntity.updateTime,
                qMessageUserEntity.user.userId.as("creatorId"),
                qMessageUserEntity.user.userName.as("creatorName"),
                qMessageUserEntity.state,
                qMessageUserEntity.confirmTime
            ))
            .innerJoin(qMessageUserEntity).on(qMessageUserEntity.message.id.eq(qMessageEntity.id))
            .where(builder)
            .offset(pageable.getOffset())
            .limit(pageable.getPageSize())
            .orderBy(qMessageEntity.createTime.desc(), qMessageEntity.id.desc())
            .fetchResults();

        return new PageImpl<>(results.getResults(), pageable, results.getTotal());
    }

  @Override
  public Long countUnreadSize(int userId, Byte state) {
    QMessageEntity qMessageEntity = QMessageEntity.messageEntity;
    QMessageUserEntity qMessageUserEntity = QMessageUserEntity.messageUserEntity;

    return from(qMessageEntity)
    .innerJoin(qMessageUserEntity).on(qMessageUserEntity.message.eq(qMessageEntity))
    .where(qMessageUserEntity.user.userId.eq(userId).and(qMessageUserEntity.state.eq(state)))
      .fetchCount();
  }
}
