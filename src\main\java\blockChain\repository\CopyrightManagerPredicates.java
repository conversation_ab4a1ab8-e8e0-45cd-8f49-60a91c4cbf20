
package blockChain.repository;

import blockChain.entities.CopyrightManager;
import blockChain.entities.CopyrightOwner;
import blockChain.entities.QCopyrightManager;
import blockChain.entities.QCopyrightOwner;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static blockChain.entities.CopyrightManager.LicenseState.*;
import static blockChain.entities.CopyrightManager.SnStatus.COMPLETE;


/**
 * <AUTHOR> XXX
 * @date 2019/11/29.
 */

public final class CopyrightManagerPredicates {

    public static Predicate enablePredicate(boolean isenable) {
    if(isenable) {
      return QCopyrightManager.copyrightManager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE);
    }
    return QCopyrightManager.copyrightManager.enable.eq(CopyrightManager.EnableStatusEnum.DISABLE);
  }
  public static Predicate enablePredicate() {
    return enablePredicate(true);
  }
  public static Predicate digitalQuery(String worksNum, String productionName,
                                       String productionTypeId, Integer rightOwnMode,
                                       String startDate, String endDate, List<Integer> status,
                                       String userName, String agentName, String copyrightName,
                                       Boolean isCertificate,Boolean isSubmit,Boolean isreport,Integer city_level,Integer county_level,
                                       Integer evaluateMin,Integer evaluateMax,String certificateStartDate, String certificateEndDate,
                                       Integer province,Integer inactivetype,Boolean isenable) {
    return digitalQuery(worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName,
      isCertificate, isSubmit, isreport, city_level, county_level, evaluateMin, evaluateMax, certificateStartDate, certificateEndDate, province, inactivetype, null,isenable, null, 0, 0);
  }

    /**
     * 省网办推送统计(废弃)
     *
     * @return
     */
  public static Predicate countSWBreport(String monthlyOrYear)
  {
    QCopyrightManager digital = QCopyrightManager.copyrightManager;
    BooleanBuilder builder = new BooleanBuilder(enablePredicate(true));
    // builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
    if (!StringUtils.isEmpty(monthlyOrYear)) {
        builder.and(digital.snCode.like("fj00015" + monthlyOrYear + "%"));
//    } else {
//      builder.and(digital.snCode.like("fj00015%"));
    }
      builder.and(digital.snStatus.eq(COMPLETE)); // ? 为什么把0算进推送的数量中
    return builder;
  }

    /**
     * 汇集平台推送统计(废弃)
     * @return
   */
  public static Predicate countHJreport(String monthlyOrYear)
  {
    QCopyrightManager digital = QCopyrightManager.copyrightManager;
    BooleanBuilder builder = new BooleanBuilder();
    if (!StringUtils.isEmpty(monthlyOrYear)) {
      builder.and(digital.pch.like(monthlyOrYear + "%"));
    } else {
      builder.and(digital.pch.isNotEmpty());
    }
    return builder;
  }

  /**
   * 证照提交统计
   * @return
   */
  public static Predicate countZZreport(LocalDateTime start, LocalDateTime end)
  {
    QCopyrightManager digital = QCopyrightManager.copyrightManager;
    BooleanBuilder builder = new BooleanBuilder();
    builder.and(digital.licenseState.eq(REPORTED));
    if (!StringUtils.isEmpty(start) && !StringUtils.isEmpty(end)) {
      builder.andAnyOf(digital.licenseTime.isNull().and(digital.certificateCreateTime.between(start, end))
              ,digital.licenseTime.between(start, end));
    }
    return builder;
  }

  /**
   * 工作站统计
   * @return
   */
  public static Predicate countWorkstationPredicates(Integer status, String userName, LocalDateTime start, LocalDateTime end)
  {
    QCopyrightManager digital = QCopyrightManager.copyrightManager;
    BooleanBuilder builder = new BooleanBuilder(enablePredicate(true));
    builder.and(digital.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));

    if (status != null) {
      builder.and(digital.status_type.eq(status));
    }
    if (!StringUtils.isEmpty(userName)) {
      builder.and(digital.userName.eq(userName));
      // 工作站用户根据作品登记时间
      if (!StringUtils.isEmpty(start) && !StringUtils.isEmpty(end))
        builder.and(digital.registrationDate.between(start, end));
    } else {
      // 管理员根据证书生成时间
      if (!StringUtils.isEmpty(start) && !StringUtils.isEmpty(end))
        builder.and(digital.certificateCreateTime.between(start, end));
    }
    builder.and(digital.workstationId.gt(0));
    return builder;
  }

  public static Predicate digitalQuery(String worksNum, String productionName,
                                       String productionTypeId, Integer rightOwnMode,
                                       String startDate, String endDate, List<Integer> status,
                                       String userName, String agentName, String copyrightName,
                                       Boolean isCertificate,Boolean isSubmit,Boolean isreport,Integer city_level,Integer county_level,
                                       Integer evaluateMin,Integer evaluateMax,String certificateStartDate, String certificateEndDate,
                                       Integer province,Integer inactivetype, List<Integer> regNums,Boolean isenable, List<Integer> tags
                                       ,Integer isAccusation, Integer focusWork) {
    QCopyrightManager digital = QCopyrightManager.copyrightManager;

    BooleanBuilder builder = null;
    if(isenable==null) {
      builder=new BooleanBuilder();
    }
    else {
      builder = new BooleanBuilder(enablePredicate(isenable));
    }
    if (!StringUtils.isEmpty(worksNum)) { // 作品登记号
      builder.and(digital.worksNum.contains(worksNum));
    }

    if (!StringUtils.isEmpty(productionName)) { // 作品名称
      builder.and(digital.productionName.contains(productionName));
    }

    if (!StringUtils.isEmpty(agentName)) { // 代理人
      builder.and(digital.agentList.agentName.contains(agentName));
    }

    if (!StringUtils.isEmpty(copyrightName)) { // 著作权人
      builder.and(digital.ownerList.any().copyName.contains(copyrightName));
    }

    if (!StringUtils.isEmpty(productionTypeId)) {  // 作品类别
      builder.and(digital.productionTypeId.eq(productionTypeId));
    }

    if (rightOwnMode != null) { //权利方式
      builder.and(digital.rightOwnMode.eq(rightOwnMode));
    }

    /*if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) { //时间

      builder.and(digital.registrationDate.between(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDateTime.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
    }*/
    if (!StringUtils.isEmpty(startDate)) { //时间

      builder.and(digital.registrationDate.after(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).or(digital.registrationDate.eq(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))));
    }
    if (!StringUtils.isEmpty(endDate)) { //时间

      builder.and(digital.registrationDate.before(LocalDateTime.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).or(digital.registrationDate.eq(LocalDateTime.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))));
    }
    if (certificateStartDate!=null && !StringUtils.isEmpty(certificateStartDate)) { //时间

      builder.and(digital.certificateCreateTime.after(LocalDateTime.parse(certificateStartDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).or(digital.certificateCreateTime.eq(LocalDateTime.parse(certificateStartDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))));
    }
    if (certificateEndDate!=null && !StringUtils.isEmpty(certificateEndDate)) { //时间

      builder.and(digital.certificateCreateTime.before(LocalDateTime.parse(certificateEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).or(digital.certificateCreateTime.eq(LocalDateTime.parse(certificateEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))));
    }
    if (status != null && status.size() > 0) {
      builder.and(digital.status_type.in(status));
    }

    if (!StringUtils.isEmpty(userName)) { // 用户名称
      builder.and(digital.userName.eq(userName));
    }else{
      builder.and(digital.status_type.notIn(CopyrightManager.UNSUBMIT, CopyrightManager.SUBMITTING));
      if (province != null){
        builder.and(digital.ownerList.any().copyProvince.eq(province));
      }

      if (city_level != null) { //是否拥有市级权限限制
        builder.and(digital.ownerList.any().copyCity.eq(city_level));
      }
      if (county_level != null) { //是否拥有县级权限限制
        builder.and(digital.ownerList.any().copyCounty.eq(county_level));
      }
    }

    if(isreport!=null){
      if(isreport){
        builder.and(digital.regNum.isNotEmpty().and(digital.regNum.ne("REPORTING")).and(digital.regNum.ne("linshiregnum"))); // 已上报
      }else{
        if(!StringUtils.isEmpty(regNums)){
          BooleanBuilder builderReg = new BooleanBuilder();
          for (Integer regNum : regNums) {
            switch (regNum) {
              case 1: // 待上报
                builderReg.or(digital.regNum.isEmpty());
                builderReg.or(digital.regNum.isNull());
                break;
              case 2: // 已上报
                builderReg.or(digital.regNum.isNotEmpty().and(digital.regNum.ne("REPORTING")).and(digital.regNum.ne("linshiregnum")));
                break;
              case 3: // 上报中
                builderReg.or(digital.regNum.eq("REPORTING"));
                break;
              case 4: // 上报失败
                builderReg.or(digital.regNum.eq("linshiregnum"));
                break;
              default:
                break;
            }
          }
          builder.and(builderReg);
        }else{
          builder.andAnyOf(digital.regNum.isEmpty(),digital.regNum.isNull(),digital.regNum.eq("REPORTING"),digital.regNum.eq("linshiregnum"));
        }
      }
    }
    if(isCertificate!=null){
      if(isCertificate){
        builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
        //builder.and(digital.certificateUrl.isNotEmpty());
      }else{
        builder.andAnyOf(digital.certificateUrl.isEmpty(),digital.certificateUrl.isNull());
      }
    }
    if(isSubmit!=null){
      if(isSubmit){
        builder.and(digital.licenseState.eq(REPORTED));
      }else {
          if (!ObjectUtils.isEmpty(regNums)) {
            builder.and(digital.licenseState.in(regNums));
          } else {
            builder.andAnyOf(digital.licenseState.eq(WAIT), digital.licenseState.eq(FAILED));
          }
      }
    }
    if(evaluateMin!=null&&evaluateMax!=null)
    {
      builder.and(digital.evaluate.alternate.between(evaluateMin,evaluateMax));
    }
    if(evaluateMin!=null&&evaluateMax==null)
    {
      builder.and(digital.evaluate.alternate.between(evaluateMin,5));
    }
    if(evaluateMin==null&&evaluateMax!=null)
    {
      builder.and(digital.evaluate.alternate.between(evaluateMin,evaluateMax));
    }
    if(inactivetype!=null) {
      builder.and(digital.inactiveType.eq(inactivetype));
    }

    BooleanBuilder builderAcc = new BooleanBuilder();
    // 标签
    if (tags != null) {
      List<Long> workstations = new ArrayList<>();
      List<Integer> realTags = new ArrayList<>();
      String tagStr;
      for (Integer tag : tags) {
        tagStr = tag.toString();
        if (tagStr.length()==5) {
          Integer realId = Integer.valueOf(tagStr.substring(1));
          if (tagStr.startsWith("1")) {
            realTags.add(realId);
          } else if (tagStr.startsWith("3")) {
            workstations.add(realId.longValue());
          }
        } else {
          realTags.add(Integer.valueOf(tagStr));
        }
      }
      if (realTags.size()>0)
        builderAcc.or(digital.tags.any().id.in(realTags));
      // 工作站
      if (workstations.size()>0)
        builderAcc.or(digital.workstationId.in(workstations));
    }
    if (isAccusation!=null && isAccusation==1) {
      builderAcc.or(digital.isAccusation.eq(Boolean.TRUE));
        builderAcc.or(digital.isAccusation.eq(Boolean.FALSE));
    }

      if (focusWork != null && focusWork == 1) {
          builderAcc.or(digital.focusWork.eq(1));
      }
      builder.and(builderAcc);
      return builder;
  }

    /**
     * 开放查询
     */
    public static Predicate managerDigitalQuery(String worksNum, String productionName,
                                                String productionTypeId, Integer rightOwnMode,
                                                String startDate, String endDate, List<Integer> status,
                                                String userName, String agentName, String copyrightName,
                                                Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level,
                                                Integer evaluateMin, Integer evaluateMax, String certificateStartDate, String certificateEndDate
    ) {

        QCopyrightManager digital = QCopyrightManager.copyrightManager;

        BooleanBuilder builder = new BooleanBuilder();

        if (!StringUtils.isEmpty(worksNum)) { // 作品登记号
            builder.and(digital.worksNum.eq(worksNum));
        }

        if (!StringUtils.isEmpty(productionName)) { // 作品名称
            builder.and(digital.productionName.contains(productionName));
        }

        if (!StringUtils.isEmpty(agentName)) { // 代理人
            builder.and(digital.agentList.agentName.contains(agentName));
        }

        if (!StringUtils.isEmpty(copyrightName)) { // 著作权人
            builder.and(digital.ownerList.any().copyName.contains(copyrightName));
        }

        if (!StringUtils.isEmpty(productionTypeId)) {  // 作品类别
            builder.and(digital.productionTypeId.eq(productionTypeId));
        }

        if (rightOwnMode != null) { //权利方式
            builder.and(digital.rightOwnMode.eq(rightOwnMode));
        }

    /*if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) { //时间

      builder.and(digital.registrationDate.between(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDateTime.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
    }*/
        if (!StringUtils.isEmpty(startDate)) { //时间

            builder.and(digital.registrationDate.after(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).or(digital.registrationDate.eq(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))));
        }
        if (!StringUtils.isEmpty(endDate)) { //时间

            builder.and(digital.registrationDate.before(LocalDateTime.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).or(digital.registrationDate.eq(LocalDateTime.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))));
        }
        if (certificateStartDate!=null && !StringUtils.isEmpty(certificateStartDate)) { //时间

            builder.and(digital.certificateCreateTime.after(LocalDateTime.parse(certificateStartDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).or(digital.certificateCreateTime.eq(LocalDateTime.parse(certificateStartDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))));
        }
        if (certificateEndDate!=null && !StringUtils.isEmpty(certificateEndDate)) { //时间

            builder.and(digital.certificateCreateTime.before(LocalDateTime.parse(certificateEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).or(digital.certificateCreateTime.eq(LocalDateTime.parse(certificateEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))));
        }
        if (status != null && status.size() > 0) {
            builder.and(digital.status_type.in(status));
        }

        if (!StringUtils.isEmpty(userName)) { // 用户名称
            builder.and(digital.userName.eq(userName));
        }else{
            builder.and(digital.status_type.notIn(CopyrightManager.UNSUBMIT));
            if (city_level != null) { //是否拥有市级权限限制
                builder.and(digital.ownerList.any().copyCity.eq(city_level));
            }
            if (county_level != null) { //是否拥有县级权限限制
                builder.and(digital.ownerList.any().copyCounty.eq(county_level));
            }
        }

        if(isreport!=null){
            if(isreport){
                builder.and(digital.regNum.isNotEmpty());
            }else{
                builder.andAnyOf(digital.regNum.isEmpty(),digital.regNum.isNull());
            }
        }
        if(isCertificate!=null){
            if(isCertificate){
                builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
                //builder.and(digital.certificateUrl.isNotEmpty());
            }else{
                builder.andAnyOf(digital.certificateUrl.isEmpty(),digital.certificateUrl.isNull());
            }
        }
        if(isSubmit!=null){
            if(isSubmit){
              builder.and(digital.licenseState.eq(REPORTED));
            }else{
              builder.andAnyOf(digital.licenseState.ne(REPORTED));
            }
        }
        if(evaluateMin!=null&&evaluateMax!=null) {
            builder.and(digital.evaluate.alternate.between(evaluateMin,evaluateMax));
        }
        if(evaluateMin!=null&&evaluateMax==null) {
            builder.and(digital.evaluate.alternate.between(evaluateMin,5));
        }
        if(evaluateMin==null&&evaluateMax!=null) {
            builder.and(digital.evaluate.alternate.between(evaluateMin,evaluateMax));
        }
//    builder.and(digital.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
        return builder;
    }

  public static Predicate certificateQuery(Boolean isCertificate, Boolean isPch) {

    QCopyrightManager digital = QCopyrightManager.copyrightManager;

    BooleanBuilder builder = new BooleanBuilder(enablePredicate());

    if(isCertificate!=null){
      if(isCertificate){
        builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
        //builder.and(digital.certificateUrl.isNotEmpty());
      }else{
        builder.andAnyOf(digital.certificateUrl.isEmpty(),digital.certificateUrl.isNull());
      }
    }

    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime ldt = LocalDateTime.parse("2021-01-01 00:00:00",df);
    builder.and(digital.certificateCreateTime.after(ldt));
    if (isPch) {
        builder.andAnyOf(digital.pch.isNull(), digital.pch.isEmpty()); // 20210120-10-1 20211008-1-1/1
    }

    return builder;
  }

    public static Predicate certificateSNQuery(Boolean isCertificate, Boolean isSN) {

        QCopyrightManager digital = QCopyrightManager.copyrightManager;
        QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

        BooleanBuilder builder = new BooleanBuilder(enablePredicate());
//      builder.and(digital.ownerList.any().seq.eq(1));
        builder.andAnyOf(owner.copyCertificateZM.isNotNull());
      builder.andAnyOf(owner.copyCategory.eq(CopyrightOwner.CopyCategoryValue.PEOPLE));
      builder.and(digital.licenseState.eq(REPORTED)); // 证照推送成功后再提交省网办

        if(isCertificate!=null){
            if(isCertificate) {
                builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
            } else {
                builder.andAnyOf(digital.certificateUrl.isEmpty(), digital.certificateUrl.isNull());
            }
        }

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime ldt = LocalDateTime.parse("2021-01-01 00:00:00", df);
        builder.and(digital.certificateCreateTime.after(ldt));

        if (isSN) {
            Integer[] arr = {0, 5, 9};
            List<Integer> snStatuss = new ArrayList<>(Arrays.asList(arr));
            builder.and(digital.snStatus.in(snStatuss));
        }

        return builder;
    }

    public static Predicate certificateZJQuery(Boolean isCertificate, Boolean isLicenseStatus) {

        QCopyrightManager digital = QCopyrightManager.copyrightManager;

        BooleanBuilder builder = new BooleanBuilder(enablePredicate());

        if(isCertificate!=null){
            if(isCertificate){
                builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
            }else{
                builder.andAnyOf(digital.certificateUrl.isEmpty(),digital.certificateUrl.isNull());
            }
        }

        if (isLicenseStatus) {
          builder.and(digital.licenseState.eq(WAIT));
        }
        return builder;
    }

  public static Predicate registrationDateBefore(LocalDateTime registrationDateStart,LocalDateTime registrationDateEnd) {

    QCopyrightManager digital = QCopyrightManager.copyrightManager;

    BooleanBuilder builder = new BooleanBuilder(enablePredicate());

    if (registrationDateStart != null) {
      builder.and(digital.registrationDate.after(registrationDateStart));
    }

    if (registrationDateEnd!=null) {
      builder.and(digital.registrationDate.before(registrationDateEnd));
    }

    return builder;
  }

  public static Predicate certificateDateBefore(LocalDateTime certificateDateStart,LocalDateTime certificateDateEnd) {

    QCopyrightManager digital = QCopyrightManager.copyrightManager;

    BooleanBuilder builder = new BooleanBuilder(enablePredicate());

    if (certificateDateStart != null) {
      builder.and(digital.certificateCreateTime.after(certificateDateStart).or(digital.certificateCreateTime.eq(certificateDateStart)));
    }

    if (certificateDateEnd!=null) {
      builder.and(digital.certificateCreateTime.before(certificateDateEnd).or(digital.certificateCreateTime.eq(certificateDateEnd)));
    }
    builder.and(digital.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
    builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));

    return builder;
  }

  public static Predicate certificateDateBeforeByFujian(LocalDateTime certificateDateStart,LocalDateTime certificateDateEnd,String provinceCode) {

    QCopyrightManager digital = QCopyrightManager.copyrightManager;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    BooleanBuilder builder = new BooleanBuilder(enablePredicate());

    if (certificateDateStart != null) {
      builder.and(digital.certificateCreateTime.after(certificateDateStart).or(digital.certificateCreateTime.eq(certificateDateStart)));
    }

    if (certificateDateEnd!=null) {
        builder.and(digital.certificateCreateTime.before(certificateDateEnd).or(digital.certificateCreateTime.eq(certificateDateEnd)));
    }
      if (StringUtils.hasText(provinceCode)) {
          builder.and(owner.copyProvince.eq(Integer.valueOf(provinceCode)));
      }
      builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
      builder.and(digital.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
      return builder;
  }

    /**
     * 证书撤销
     */
    public static Predicate revokePredicates(Integer inactiveType,
                                             String worksNum, String productionName,
                                             String productionTypeId, Integer rightOwnMode,
                                             String startDate, String endDate, List<Integer> status,
                                             String userName, String agentName, String copyrightName,
                                             Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level, Boolean isenable) {

        QCopyrightManager digital = QCopyrightManager.copyrightManager;

        BooleanBuilder builder = new BooleanBuilder(digitalQuery(worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName, isCertificate, isSubmit, isreport, city_level, county_level, null, null, null, null, null, inactiveType, isenable));

        return builder;
    }

    /**
     * 开放查询
     */
    public static Predicate certificatePredicates(String worksNum, String productionName,
                                                  String productionTypeId, Integer rightOwnMode,
                                                  String startDate, String endDate, List<Integer> status,
                                                  String userName, String agentName, String copyrightName,
                                                  Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level,
                                                  String certificateStartDate, String certificateEndDate) {

        QCopyrightManager digital = QCopyrightManager.copyrightManager;

        BooleanBuilder builder = new BooleanBuilder(managerDigitalQuery(worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName, isCertificate, isSubmit, isreport, city_level, county_level, null, null, certificateStartDate, certificateEndDate));

        builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
    /*if(certificateStartDate != null && !certificateStartDate.equals("")){
      builder.and(digital.certificateCreateTime.after(LocalDateTime.parse(certificateStartDate,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
    }

    if(certificateEndDate != null && !certificateEndDate.equals("")){
      builder.and(digital.certificateCreateTime.before(LocalDateTime.parse(certificateEndDate,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
    }*/

        return builder;
    }

    /** 废弃 **/
    public static Predicate secCertificatePredicates(String worksNum, String productionName,
                                                     String productionTypeId, Integer rightOwnMode,
                                                     String startDate, String endDate, List<Integer> status,
                                                     String userName, String agentName, String copyrightName,
                                                     Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level,
                                                     String certificateStartDate, String certificateEndDate) {

        QCopyrightManager digital = QCopyrightManager.copyrightManager;

        BooleanBuilder builder = new BooleanBuilder(digitalQuery(worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName,isCertificate,isSubmit,isreport,city_level,county_level,null,null,certificateStartDate,certificateEndDate,null,CopyrightManager.InactiveTypeValue.REVOKED,true));

        builder.and(digital.status_type.eq(CopyrightManager.CERT_CREATED));
    /*if(certificateStartDate != null && !certificateStartDate.equals("")){
      builder.and(digital.certificateCreateTime.after(LocalDateTime.parse(certificateStartDate,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
    }

    if(certificateEndDate != null && !certificateEndDate.equals("")){
      builder.and(digital.certificateCreateTime.before(LocalDateTime.parse(certificateEndDate,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
    }*/

        return builder;
    }

  public static Predicate homgPageQuery(List<Integer> status, String userName,Integer city_level,Integer county_level,LocalDateTime startTime,LocalDateTime endTime) {

    QCopyrightManager digital = QCopyrightManager.copyrightManager;

    BooleanBuilder builder = new BooleanBuilder(enablePredicate());


    if (status != null && status.size() > 0) {
      builder.and(digital.status_type.in(status));
    }

    if (!StringUtils.isEmpty(userName)) { // 用户名称
      builder.and(digital.userName.eq(userName));
    }else{
      builder.and(digital.status_type.notIn(CopyrightManager.UNSUBMIT));
      if (city_level != null) { //是否拥有市级权限限制
        builder.and(digital.ownerList.any().copyCity.eq(city_level));
      }
      if (county_level != null) { //是否拥有县级权限限制
        builder.and(digital.ownerList.any().copyCounty.eq(county_level));
      }
    }
    if(startTime!=null){
      builder.and(digital.certificateCreateTime.after(startTime));
    }
    if(endTime!=null){
      builder.and(digital.certificateCreateTime.before(endTime));
    }


    return builder;
  }

    public static Predicate byStatus(Integer status) {
        QCopyrightManager digital = QCopyrightManager.copyrightManager;

        BooleanBuilder builder = new BooleanBuilder(enablePredicate());

        builder.and(digital.status_type.eq(status));
        return builder;
    }
    public static Predicate byStatusAndBlockChainToken(Integer status,String blockchianToken) {
      QCopyrightManager digital = QCopyrightManager.copyrightManager;

      BooleanBuilder builder = new BooleanBuilder(enablePredicate());

      builder.and(digital.status_type.eq(status));
      builder.and(digital.onBlockToken.eq(blockchianToken));
      return builder;
    }

  public static Predicate byUserNameAndWorkstationId(String userName, Long oldWorkstationId) {
    QCopyrightManager manager = QCopyrightManager.copyrightManager;
    BooleanBuilder builder = new BooleanBuilder();

    builder.and(manager.userName.eq(userName));
    builder.and(manager.workstationId.eq(oldWorkstationId));
    return builder;
  }

  public static StringBuilder reportSql(String worksNum, String productionName, List<Integer> regNums,
                                        String productionTypeId, Integer rightOwnMode, String agentName, String copyrightName,
                                        String startDate, String endDate, String certificateStartDate, String certificateEndDate, Boolean isCount) {
    StringBuilder sqlAll = new StringBuilder();
    StringBuilder sql = new StringBuilder();
    if (isCount) {
      sqlAll.append(" select sum(num) from ( ");
      sql.append(" select count(*) num from tb_copyrightManager manager");
    } else {
      sqlAll.append(" select registrationNum from ( ");
      sql.append(" select manager.registrationDate, manager.registrationNum from tb_copyrightManager manager");
    }

    if (!StringUtils.isEmpty(agentName))
      sql.append(" left join tb_agent agent on manager.agent_id = agent.id");
    if (!StringUtils.isEmpty(copyrightName))
      sql.append(" left join tb_copyrightOwner owner on manager.registrationNum = owner.copyright_id");

    sql.append(" where manager.enable =0 and manager.inactiveType in (1,2) and manager.status_type =11");
    if (!StringUtils.isEmpty(worksNum))
      sql.append(" and manager.worksNum like '%" + worksNum + "%'");
    if (!StringUtils.isEmpty(productionName))
      sql.append(" and manager.productionName like '%" + productionName + "%'");
    if (!StringUtils.isEmpty(agentName))
      sql.append(" and agent.agentName like '%" + agentName + "%'");
    if (!StringUtils.isEmpty(copyrightName))
      sql.append(" and owner.copyName like '%" + copyrightName + "%'");
    if (!StringUtils.isEmpty(productionTypeId))
      sql.append(" and manager.productionTypeId = " + productionTypeId + "");
    if (rightOwnMode != null)
      sql.append(" and manager.rightOwnMode = " + rightOwnMode + "");
    if (!StringUtils.isEmpty(startDate))  //申请时间
      sql.append(" and manager.registrationDate >='" + startDate + "'");
    if (!StringUtils.isEmpty(endDate))  //申请时间
      sql.append(" and manager.registrationDate <='" + endDate + "'");
    if (!StringUtils.isEmpty(certificateStartDate))  //证书生成时间
      sql.append(" and manager.certificateCreateTime >='" + certificateStartDate + "'");
    if (!StringUtils.isEmpty(certificateEndDate))  //证书生成时间
      sql.append(" and manager.certificateCreateTime <='" + certificateEndDate + "'");
    if (CollectionUtils.isNotEmpty(regNums)) {
      for (int i = 0; i < regNums.size(); i++) {
        StringBuilder sqlTemp = new StringBuilder();
        if (i > 0) sqlTemp.append(" union all");
        sqlTemp.append(sql);
        sqlTemp.append(" and manager.licenseState = " + regNums.get(i));
        sqlAll.append(sqlTemp);
      }
    }
    sqlAll.append(") as ma ");
    return sqlAll;
  }
}

