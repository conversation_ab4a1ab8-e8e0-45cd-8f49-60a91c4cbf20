package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;

/**
 * 作品著作权提交者信息
 */
@XmlRootElement
public class SubmitterBean {
	//姓名或名称  长度20
	private String name;
	//详细地址  长度30
	private String address;
	//邮政编码  长度20
	private String zipCode;
	//联系人  长度20
	private String contactPerson;
	//电话号码  长度30
	private String telephone;
	//E-mail  长度30
	private String email;
	//手机号码 长度30
	private String mobile;
	//传真号 长度30
	private String fax;


	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getZipCode() {
		return zipCode;
	}
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
	public String getContactPerson() {
		return contactPerson;
	}
	public void setContactPerson(String contactPerson) {
		this.contactPerson = contactPerson;
	}
	public String getTelephone() {
		return telephone;
	}
	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getFax() {
		return fax;
	}
	public void setFax(String fax) {
		this.fax = fax;
	}
	public SubmitterBean(String name, String address, String zipCode,
			String contactPerson, String telephone, String email,
			String mobile, String fax) {
		this.name = name;
		this.address = address;
		this.zipCode = zipCode;
		this.contactPerson = contactPerson;
		this.telephone = telephone;
		this.email = email;
		this.mobile = mobile;
		this.fax = fax;
	}
	public SubmitterBean(){

	}

}
