
package org.tempuri;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "AuthenticationSoap", targetNamespace = "http://tempuri.org/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface AuthenticationSoap {


    /**
     * 身份认证
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "LoginByAccount", action = "http://tempuri.org/LoginByAccount")
    @WebResult(name = "LoginByAccountResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "LoginByAccount", targetNamespace = "http://tempuri.org/", className = "org.tempuri.LoginByAccount")
    @ResponseWrapper(localName = "LoginByAccountResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.LoginByAccountResponse")
    public String loginByAccount(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 获取SM4秘钥
     * 
     * @param passWord
     * @param userName
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "GetSecretKey", action = "http://tempuri.org/GetSecretKey")
    @WebResult(name = "GetSecretKeyResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "GetSecretKey", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetSecretKey")
    @ResponseWrapper(localName = "GetSecretKeyResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetSecretKeyResponse")
    public String getSecretKey(
        @WebParam(name = "userName", targetNamespace = "http://tempuri.org/")
        String userName,
        @WebParam(name = "passWord", targetNamespace = "http://tempuri.org/")
        String passWord);

}
