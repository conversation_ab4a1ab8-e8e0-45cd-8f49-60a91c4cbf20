/*
package blockChain.facade.service;


import com.mascloud.sdkclient.Client;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ShortMessgeSendServiceFacade {
  private Client client = Client.getInstance();

  private static String testUrl = "http://112.33.1.13/app/sdk/login";

  private static String officalUrl = "http://mas.ecloud.10086.cn/app/sdk/login";

  private static String userName = "sxsms1";

  private static String password = "sxsms@xu8k";

  public Boolean identityCheck(){
    Boolean loginresult = client.login(testUrl,userName, password,"福建省版权局");
    return loginresult;
  }
}
*/
