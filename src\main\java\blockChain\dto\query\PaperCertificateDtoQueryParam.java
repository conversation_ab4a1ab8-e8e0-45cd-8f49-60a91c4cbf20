package blockChain.dto.query;

import blockChain.bean.BaseQueryParam;
import blockChain.entities.PaperCertificateEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/16 16:49
 */
@Data
public class PaperCertificateDtoQueryParam extends BaseQueryParam {
  private LocalDateTime startDate;
  private LocalDateTime endDate;
  private PaperCertificateEntity.StatusEnum status;
}
