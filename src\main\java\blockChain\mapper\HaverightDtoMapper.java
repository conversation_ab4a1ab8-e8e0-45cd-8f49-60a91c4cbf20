package blockChain.mapper;

import blockChain.dto.HaverightDto;
import blockChain.entities.HaverightEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface HaverightDtoMapper {
    HaverightDtoMapper INSTANCE = Mappers.getMapper(HaverightDtoMapper.class);

    HaverightDto entityToDto(HaverightEntity haverightEntity);

    List<HaverightDto> entityToDto(List<HaverightEntity> haverightEntities);
}
