package blockChain.controller;

import blockChain.config.SpringConfig;
import blockChain.entities.UploadAttachment;
import blockChain.facade.service.UploadAttachmentServiceFacade;
import blockChain.facade.service.UserServiceFacade;
import blockChain.utils.DownloadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@Api("文件管理")
@Slf4j
@RestController
@RequestMapping("file")
@RequiredArgsConstructor
public class FileController {

	@Autowired
	private UploadAttachmentServiceFacade uploadAttachmentServiceFacade;
    @Autowired
    private UserServiceFacade userServiceFacade;
    private final SpringConfig config;

	@ApiOperation("文件上传")
	@PostMapping(value = "upload", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ResponseEntity<Map<String,Object>> upload(@RequestParam MultipartFile file,HttpServletRequest request) {
		Map<String,Object> result = new HashMap<>();
		try {
		    String fileName = file.getOriginalFilename().toLowerCase(Locale.ROOT);
            if(fileName.endsWith(".pdf")
                    || fileName.endsWith(".jpg")
                    || fileName.endsWith(".png")
                    || fileName.endsWith(".jpeg")
                    || fileName.endsWith(".gif")
                    || fileName.endsWith(".bmp")
                    || fileName.endsWith(".mp4")
                    || fileName.endsWith(".mp3")
                    || fileName.endsWith(".wav") ) {
                UploadAttachment uploadAttachment = uploadAttachmentServiceFacade.upload(file,request);
                result.put("file",uploadAttachment);
                result.put("message", "查询成功！");
                return new ResponseEntity<>(result, HttpStatus.OK);
            } else {
                result.put("message", "上传文件格式错误！");
                result.put("errorCode",201);
                return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
            }
		} catch (Exception e) {
			result.put("message", "上传失败！");
			result.put("errorCode",e.getLocalizedMessage());
			return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
		}
	}

	@ApiOperation("文件下载")
	@GetMapping("download/{id}")
	public void download(@ApiParam("文件id") @PathVariable("id") Long id, HttpServletRequest request, HttpServletResponse response) {
		try {
			//String contextPath = "File/";
            if (!userServiceFacade.isManager() && !userServiceFacade.isMyFile(id)) {
                return;
            }
			UploadAttachment fileEntry = uploadAttachmentServiceFacade.getById(id);
			@Cleanup InputStream inputStream = new FileInputStream(System.getProperty("user.dir")+ File.separator +fileEntry.getWorkUrl());
			@Cleanup ServletOutputStream outputStream = response.getOutputStream();

			DownloadUtil.parseHeader(fileEntry.getWorkName(), request, response);
			IOUtils.copyLarge(inputStream, outputStream);
		} catch (Exception e) {
			log.error("error when download file", e);
		}
	}

	@ApiOperation("文件删除")
	@PostMapping(value = "remove", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ResponseEntity<Map<String,Object>> remove(@Valid @RequestBody UploadAttachment uploadAttachment) {
		Map<String,Object> result = new HashMap<>();
		try {
            Long id = uploadAttachment.getId();
            if (!userServiceFacade.isMyAttachment(id)) {
                throw new RuntimeException("权限错误！");
            }
            uploadAttachmentServiceFacade.removeById(id);
            result.put("message", "删除成功！");
        } catch (Exception e) {
            result.put("message", "删除失败！");
            result.put("errorCode", e.getLocalizedMessage());
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
    }

    @ApiOperation("文件详情")
    @GetMapping("{id}")
    public void detail(@ApiParam("文件id") @PathVariable("id") Long id, HttpServletRequest request, HttpServletResponse response) {
        if (!userServiceFacade.isManager() && !userServiceFacade.isMyAttachment(id)) {
            throw new RuntimeException("权限错误！");
        }
        String token = request.getParameter("token");
        UploadAttachment fileEntry = uploadAttachmentServiceFacade.getById(id);
        String returnUrl = config.getFrontUrl() + "api/qkl/" + fileEntry.getWorkUrl() + "?token=" + token;
        try {
            response.sendRedirect(returnUrl);
        } catch (IOException e) {
            log.error("error", e);
        }
    }
}
