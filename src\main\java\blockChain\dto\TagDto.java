package blockChain.dto;

import lombok.Data;

@Data
public class TagDto {
    private Integer id;
    private String name;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public TagDto(Integer id, String name) {
      this.id = id;
      this.name = name;
    }

    public TagDto() {
    }
}
