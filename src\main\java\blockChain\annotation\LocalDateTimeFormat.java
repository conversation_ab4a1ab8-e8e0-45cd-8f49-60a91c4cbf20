package blockChain.annotation;

import org.springframework.core.annotation.AliasFor;
import org.springframework.format.annotation.DateTimeFormat;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2019/7/5 15:03
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
public @interface LocalDateTimeFormat {
  @AliasFor(annotation = DateTimeFormat.class)
  DateTimeFormat.ISO iso() default DateTimeFormat.ISO.DATE_TIME;
}
