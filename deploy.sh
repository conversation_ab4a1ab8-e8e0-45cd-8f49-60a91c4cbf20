#!/bin/sh

JAR_NAME='blockChain.jar'
ENV='23'
LOGS='/home/<USER>/logs/block'
APP_NAME='block'
# CONFIG='--logging.level.org.hibernate.type.descriptor.sql.BasicBinder= trace --spring.jpa.show-sql=true'
CONFIG=''

stop () {
    count=`ps -ef |grep java|grep $JAR_NAME|grep $ENV |grep -v grep|wc -l`
    if [ $count -eq 0 ];
    then
        echo -e "\e[1;31m$JAR_NAME is not running...\e[0m"
    else
        pid=`ps -ef |grep $JAR_NAME | grep $ENV |grep -v grep |awk '{print $2}'`
        kill -15 $pid
        echo -e "\e[1;32m$JAR_NAME stop success...\e[0m"
    fi
}

start () {
    count=`ps -ef |grep java|grep $JAR_NAME |grep $ENV |grep -v grep|wc -l`
    if [ $count -eq 0 ];
    then
        nohup java -jar target/$JAR_NAME --spring.profiles.active=$ENV  $CONFIG > target/dep 2>&1 &
        echo -e "\e[1;32m$JAR_NAME start success...\e[0m"
    else
        echo -e "\e[1;31m$JAR_NAME is running...\e[0m"
    fi
}

status () {
    count=`ps -ef |grep java|grep $JAR_NAME |grep $ENV |grep -v grep|wc -l`
    if [ $count -eq 0 ];
    then
        echo -e "\e[1;31m$JAR_NAME is not running...\e[0m"
    else
        echo -e "\e[1;32m$JAR_NAME is running...\e[0m"
    fi
}

update () {
    git pull
}

package () {
    mvn clean package
}

deploy () {
    stop
    update
    package
    start
}

log () {
    curday=`date +%Y-%m-%d`
    tail -n 200 -f $LOGS/sys/$APP_NAME.$curday.log
}

case $1 in
    stop)
        stop
    ;;
    start)
        start
    ;;
    restart)
        stop
        sleep 1s
        start
    ;;
    update)
        update
    ;;
    status)
        status
    ;;
    log)
        log
    ;;
    *)
        deploy
    ;;
esac
