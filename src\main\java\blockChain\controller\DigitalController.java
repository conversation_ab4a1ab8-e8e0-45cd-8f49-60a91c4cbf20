package blockChain.controller;

import blockChain.bean.QueryParam;
import blockChain.config.SpringConfig;
import blockChain.facade.service.DigitalServiceFacade;
import blockChain.facade.service.TagServiceFacade;
import blockChain.mapper.DigitalDtoMapper;
import blockChain.utils.PictureHttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Jie
 * @date 2019/12/11 17:07
 */
@Api("字典管理")
@Slf4j
@RestController
@RequestMapping("digital")
@RequiredArgsConstructor
public class DigitalController {
    @Autowired
    private DigitalServiceFacade digitalServiceFacade;
    @Autowired
    private TagServiceFacade tagServiceFacade;

    private final SpringConfig config;

    @ApiOperation("获取地域字典")
    @PostMapping("getArea")
    public ResponseEntity<Map<String,Object>> getArea(@Valid @RequestBody QueryParam queryParam){
        Map<String, Object> result = new HashMap<>();
        Integer pid = queryParam.getInteger("parentId");
        if(pid==null){
            Integer level = queryParam.getInteger("level");
            result.put("DataList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.findByLevel(level)));
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        }else {
            result.put("DataList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(pid)));
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        }
    }

    @ApiOperation("获取数据字典")
    @PostMapping("getByCode")
    public ResponseEntity<Map<String,Object>> getByCode(@Valid @RequestBody QueryParam queryParam){
        Map<String, Object> result = new HashMap<>();
        String code = queryParam.getString("code");
        if(code==null){
            result.put("message", "没有关键词判别");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        }else {
            result.put("DataList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByCode(code)));
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        }
    }

    @ApiOperation("获取标签字典")
    @PostMapping("getTags")
    public ResponseEntity<Map<String,Object>> getTags(@Valid @RequestBody QueryParam queryParam){
      Map<String, Object> result = new HashMap<>();
      Integer size = queryParam.getInteger("size");
      result.put("tags", tagServiceFacade.getTagByPage(0, size));
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
    }
}
