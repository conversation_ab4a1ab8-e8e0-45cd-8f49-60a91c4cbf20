package blockChain.mapper;

import blockChain.dto.UploadAttachmentDto;
import blockChain.entities.UploadAttachment;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UploadAttachmentDtoMapper {
    UploadAttachmentDtoMapper INSTANCE = Mappers.getMapper(UploadAttachmentDtoMapper.class);

    UploadAttachmentDto entityToDto(UploadAttachment haverightEntity);

    List<UploadAttachmentDto> entityToDto(List<UploadAttachment> haverightEntities);
}
