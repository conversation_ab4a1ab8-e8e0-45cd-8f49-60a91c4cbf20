package blockChain.utils;

import com.ctsi.ffcs.excel.util.ExcelUtil;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.codec.Charsets;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

@Component
public class ExcelUtils {
    public static final String XLS = ".xls";
    public static final String XLSX = ".xlsx";

    public static <T> List<T> readExcelFileToDTO(MultipartFile file, Class<T> clazz) throws IOException {
        return readExcelFileToDTO(file, clazz, 0);
    }

    public static <T> List<T> readExcelFileToDTO(MultipartFile file, Class<T> clazz, Integer sheetId) throws IOException {
        //将文件转成workbook类型
        Workbook workbook = buildWorkbook(file);
        //第一个表
        return readSheetToDTO(workbook.getSheetAt(sheetId), clazz);
    }

    public static <T> List<T> readSheetToDTO(Sheet sheet, Class<T> clazz) throws IOException {
        List<T> result = new ArrayList<>();
        List<Map<String, String>> sheetValue = changeSheetToMapList(sheet);
        for (Map<String, String> valueMap : sheetValue) {
            T dto = buildDTOByClass(clazz, valueMap);
            if (dto != null) {
                result.add(dto);
            }
        }
        return result;
    }

    //类型转换
    private static Workbook buildWorkbook(MultipartFile file) throws IOException {
        String filename = file.getOriginalFilename();
        if (filename.endsWith(XLS)) {
            return new HSSFWorkbook(file.getInputStream());
        } else if (filename.endsWith(XLSX)) {
            return new XSSFWorkbook(file.getInputStream());
        } else {
            throw new IOException("unknown file format: " + filename);
        }
    }

    private static List<Map<String, String>> changeSheetToMapList(Sheet sheet) {
        List<Map<String, String>> result = new ArrayList<>();
        int rowNumber = sheet.getPhysicalNumberOfRows();
        String[] titles = getSheetRowValues(sheet.getRow(0)); // 第一行作为表头
        for (int i = 1; i < rowNumber; i++) {
            String[] values = getSheetRowValuesOfTitle(sheet.getRow(i), titles.length);
            Map<String, String> valueMap = new HashMap<>();
            for (int j = 0; j < titles.length; j++) {
                valueMap.put(titles[j], values[j]);
            }
            result.add(valueMap);
        }
        return result;
    }

    public static String toDate(String str) {
        String pattern = "\\d{4}-\\d{1,2}-\\d{1,2}";
        if (str != null && Pattern.matches(pattern, str)) {
            String[] date = str.split("-");
            Calendar c1 = Calendar.getInstance();
            c1.set(Integer.parseInt(date[0]), Integer.parseInt(date[1]) - 1, Integer.parseInt(date[2]), 0, 0, 0);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(c1.getTime());
        }
        return str;
    }

    private static <T> T buildDTOByClass(Class<T> clazz, Map<String, String> valueMap) {
        try {
            T dto = clazz.newInstance();
            for (Field field : clazz.getDeclaredFields()) {
                ApiModelProperty desc = field.getAnnotation(ApiModelProperty.class);
                if (desc == null || desc.value() == null) {
                    continue;
                }
                String value = valueMap.get(desc.value());
                if (value != null && StringUtils.hasText(value)) {
                    System.out.println("buildDTOByClass:" + field.getType().getName() + " value:" + value);
                    if (field.getType().getName().equalsIgnoreCase("java.lang.Integer") || field.getType().getName().equalsIgnoreCase("int")) {
                        Method method = clazz.getMethod(getSetMethodName(field.getName()), field.getType());
                        if (value.indexOf(".") >= 0) {
                            Integer age = Integer.parseInt(value.substring(0, value.indexOf(".")));
                            method.invoke(dto, age);
                        } else {
                            Integer age = Integer.parseInt(value);
                            method.invoke(dto, age);
                        }
                    } else if (field.getType().getName().equalsIgnoreCase("java.lang.Long")) {
                        Method method = clazz.getMethod(getSetMethodName(field.getName()), field.getType());
                        Long age = Long.parseLong(value.substring(0, value.indexOf(".")));
                        method.invoke(dto, age);
                    } else if (field.getType().getName().equalsIgnoreCase("java.math.BigDecimal")) {
                        Method method = clazz.getMethod(getSetMethodName(field.getName()), field.getType());
                        BigDecimal age = new BigDecimal(value);
                        method.invoke(dto, age);
                    } else if (field.getType().getName().equalsIgnoreCase("java.time.LocalDateTime")) {
                        Method method = clazz.getMethod(getSetMethodName(field.getName()), field.getType());
                        LocalDateTime age = null;
                        if (value.length() < 11) {
                            value = toDate(value);
                            System.out.println("corrected:" + value);
                        }
                        age = LocalDateTime.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        method.invoke(dto, age);
                    } else {
                        Method method = clazz.getMethod(getSetMethodName(field.getName()), field.getType());
                        method.invoke(dto, value);
                    }
                }
            }

            return dto;
        } catch (Exception e) {
            e.getStackTrace();
        }
        return null;
    }

    private static String getSetMethodName(String name) {
        String firstChar = name.substring(0, 1);
        return "set" + firstChar.toUpperCase() + name.substring(1);
    }

    private static String[] getSheetRowValues(Row row) {
        if (row == null) {
            return new String[]{};
        } else {
            int cellNumber = row.getLastCellNum();
            List<String> cellValueList = new ArrayList<>();
            for (int i = 0; i < cellNumber; i++) {
                cellValueList.add(getValueOnCell(row.getCell(i)));
            }
            return cellValueList.toArray(new String[0]);
        }
    }

    private static String[] getSheetRowValuesOfTitle(Row row, Integer titleNum) {
        if (row == null) {
            return new String[]{};
        } else {
            List<String> cellValueList = new ArrayList<>();
            for (int i = 0; i < titleNum; i++) {
                if (row.getLastCellNum() <= i) {
                    cellValueList.add("");
                } else {
                    cellValueList.add(getValueOnCell(row.getCell(i)));
                }
            }
            return cellValueList.toArray(new String[0]);
        }
    }

    private static String getValueOnCell(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellTypeEnum()) {
            case STRING:
                return cell.getStringCellValue();
//            case NUMERIC: return String.format("%.2f", cell.getNumericCellValue());
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return cell.getBooleanCellValue() ? "true" : "false";
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
//                    return String.format("%.2f", cell.getNumericCellValue());
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return "";
        }
    }

    public static void export(HttpServletResponse response, List<String> headers, List<List<Object>> datas, String excelName) {
        Workbook book = new HSSFWorkbook();
        ExcelUtil excelUtil = new ExcelUtil(book);
        Sheet sheetInput = excelUtil.createSheet(book, "sheet1");
        Row rowHeader = sheetInput.createRow(0);
        //创建表头
        for (int i = 0; i < headers.size(); i++) {
            String title = headers.get(i);
            excelUtil.addHeaderCell(rowHeader, i, title, false);
        }
        //插入数据
        for (int i = 0; i < datas.size(); i++) {
            List<Object> datasources = datas.get(i);
            Row row = sheetInput.createRow(i + 1);
            for (int j = 0; j < datasources.size(); j++) {
                Object data = datasources.get(j);
                Cell cell = row.createCell(j);
                cell.setCellValue(data.toString());
            }
        }
        try {
            response.setHeader("Content-Disposition", "attachment;Filename=" + excelName + ".xls");
            OutputStream os = response.getOutputStream();
            book.write(os);
            os.flush();
            os.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void exportExcel(HttpServletResponse response, List<String> headers, List<List<Object>> datas, String fileName, String sheetName) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet(sheetName);

        //标题字体
        CellStyle titleStyle = workbook.createCellStyle();
        XSSFFont titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 15);
        titleFont.setFontName("黑体");
        titleStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
        titleStyle.setFont(titleFont);
        //标题单元格合并
        CellRangeAddress callRangeAddress = new CellRangeAddress(0, 1, 0, headers.size());//起始行,结束行,起始列,结束列
        sheet.addMergedRegion(callRangeAddress);
        //设置标题
        XSSFCell cell = sheet.createRow(0).createCell(0);
        cell.setCellValue(sheetName);
        cell.setCellStyle(titleStyle);

        //表头字体
        CellStyle headerStyle = workbook.createCellStyle();
        XSSFFont headerFont = workbook.createFont();
        headerFont.setFontName("黑体");
        headerStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
        headerStyle.setFont(headerFont);
        //插入表头数据
        headers.add(0, "序号");
        XSSFRow headerRow = sheet.createRow(2);
        for (int i = 0; i < headers.size(); i++) {
            XSSFCell headerCell = headerRow.createCell(i);
            headerCell.setCellValue(headers.get(i));
            headerCell.setCellStyle(headerStyle);
        }

        //数据字体
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
        //插入数据
        int row = 3;
        for (int i = 0; i < datas.size(); i++) {
            datas.get(i).add(0, i + 1);
            XSSFRow dateRow = sheet.createRow(row);
            for (int j = 0; j < datas.get(i).size(); j++) {
                XSSFCell dateCell = dateRow.createCell(j);
                dateCell.setCellValue(datas.get(i).get(j).toString());
                dateCell.setCellStyle(dateStyle);
            }
            row++;
        }
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding(Charsets.UTF_8.name());
            fileName = URLEncoder.encode(fileName + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()), Charsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        int i = Integer.parseInt("12");
        System.out.println(i);
        Integer integer = Integer.valueOf("12");
        System.out.println(integer);
    }
}
