package com.lic.share.params.appeal;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class CertLackEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 电子证照标识
     */
    private String certificateTypeName;

    /**
     * 证照颁发机构
     */
    private String certificateIssuingAuthorityName;

    /**
     * 证照颁发日期，如有上传扫描件，则非必填
     */
    private String certificateIssuedDate;

    /**
     * 证照编码，如有上传扫描件，则非必填
     */
    private String certificateNumber;

    /**
     * 持证主体
     */
    private String certificateHolderName;

    /**
     * 持证主体代码
     */
    private String certificateHolderCode;

    /**
     * 持证主体代码类型
     */
    private String certificateHolderTypeName;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 证照唯一标识符
     */
    private String certificateIdentifier;


    private List<CertLackFileEntity> fileList;
}