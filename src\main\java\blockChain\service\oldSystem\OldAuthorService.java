package blockChain.service.oldSystem;

import blockChain.entities.oldSystem.OldAgent;
import blockChain.entities.oldSystem.OldAuthor;
import blockChain.repository.oldSystem.OldAgentRepository;
import blockChain.repository.oldSystem.OldAuthorRepository;
import blockChain.service.BaseService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/9 12:08
 */
@Service
@AllArgsConstructor
public class OldAuthorService implements BaseService<OldAuthorRepository, OldAuthor, Integer> {
  private OldAuthorRepository repository;

  @Override
  public OldAuthorRepository getRepository() {
    return repository;
  }

}
