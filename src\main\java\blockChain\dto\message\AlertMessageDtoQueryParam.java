package blockChain.dto.message;

import blockChain.bean.BaseQueryParam;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/9 15:46
 */
@Getter
@Setter
@Accessors(chain = true)
public class AlertMessageDtoQueryParam extends BaseQueryParam {

  /**
   * UUID
   * 唯一约束
   */
  @JsonProperty("UID")
  private String uuid;

  /**
   * 消息标题
   */
  private String title;

  /**
   * 消息内容
   */
  private String content;
  private String creator;

  private LocalDateTime createTime;

  private LocalDateTime createTimeStart;
  private LocalDateTime createTimeEnd;

  private LocalDateTime updateTime;
}
