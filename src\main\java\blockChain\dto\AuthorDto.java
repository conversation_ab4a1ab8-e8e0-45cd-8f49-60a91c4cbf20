package blockChain.dto;

import blockChain.entities.CertificateEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class AuthorDto {
    private Long id;
    private String authorName;// 作者姓名
    /*private BigInteger copyrightmanagerId;//在线填报表的ID*/
    private String authorIdCard;// 身份证号码
    private int authorCategory;// 证件类型
    private int authorCertificate;// 证件类别
    private byte isMinority;// 是否少数民族 (1是)
    private String authorAddress;// 地址
    private String authorPhong;// 电话
    private int authorSignature;// 署名情况
    private String authorSignatureName;// 别名
    private int authorClassification;//分别是草稿箱中保存的数据，还是作品提交的数据

    private String authorSignature_String; //署名情况(文字)
    private String authorCategory_String; //类别(文字)
    private String authorCertificate_String; //证件类型(文字)
    private CertificateEntity authorCertificateZM; //作者电子证照
    private UploadAttachmentDto authorIdCar;// 作者身份证
    @JsonIgnore
    private Long formDataId; // 中枢表单用数ID
    private String projectId; // 统一收件码

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getAuthorIdCard() {
        return authorIdCard;
    }

    public void setAuthorIdCard(String authorIdCard) {
        this.authorIdCard = authorIdCard;
    }

    public int getAuthorCategory() {
        return authorCategory;
    }

    public void setAuthorCategory(int authorCategory) {
        this.authorCategory = authorCategory;
    }

    public int getAuthorCertificate() {
        return authorCertificate;
    }

    public void setAuthorCertificate(int authorCertificate) {
        this.authorCertificate = authorCertificate;
    }

    public String getAuthorAddress() {
        return authorAddress;
    }

    public void setAuthorAddress(String authorAddress) {
        this.authorAddress = authorAddress;
    }

    public String getAuthorPhong() {
        return authorPhong;
    }

    public void setAuthorPhong(String authorPhong) {
        this.authorPhong = authorPhong;
    }

    public int getAuthorSignature() {
        return authorSignature;
    }

    public void setAuthorSignature(int authorSignature) {
        this.authorSignature = authorSignature;
    }

    public String getAuthorSignatureName() {
        return authorSignatureName;
    }

    public void setAuthorSignatureName(String authorSignatureName) {
        this.authorSignatureName = authorSignatureName;
    }

    public int getAuthorClassification() {
        return authorClassification;
    }

    public void setAuthorClassification(int authorClassification) {
        this.authorClassification = authorClassification;
    }

    public String getAuthorSignature_String() {
        return authorSignature_String;
    }

    public void setAuthorSignature_String(String authorSignature_String) {
        this.authorSignature_String = authorSignature_String;
    }

    public String getAuthorCategory_String() {
        return authorCategory_String;
    }

    public void setAuthorCategory_String(String authorCategory_String) {
        this.authorCategory_String = authorCategory_String;
    }

    public String getAuthorCertificate_String() {
        return authorCertificate_String;
    }

    public void setAuthorCertificate_String(String authorCertificate_String) {
        this.authorCertificate_String = authorCertificate_String;
    }

    public CertificateEntity getAuthorCertificateZM() {
      return authorCertificateZM;
    }

    public void setAuthorCertificateZM(CertificateEntity authorCertificateZM) {
      this.authorCertificateZM = authorCertificateZM;
    }

    public UploadAttachmentDto getAuthorIdCar() {
      return authorIdCar;
    }

    public void setAuthorIdCar(UploadAttachmentDto authorIdCar) {
      this.authorIdCar = authorIdCar;
    }
}
