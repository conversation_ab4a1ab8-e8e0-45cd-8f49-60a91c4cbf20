package blockChain.utils.umpay.mascloud.mgw.service.webservice.server;

public class WsSmsServiceProxy implements WsSmsService {
  private String _endpoint = null;
  private WsSmsService wsSmsService = null;

  public WsSmsServiceProxy() {
    _initWsSmsServiceProxy();
  }

  public WsSmsServiceProxy(String endpoint) {
    _endpoint = endpoint;
    _initWsSmsServiceProxy();
  }

  private void _initWsSmsServiceProxy() {
    try {
      wsSmsService = (new WsSmsServiceServiceLocator()).getWsSmsServicePort();
      if (wsSmsService != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)wsSmsService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)wsSmsService)._getProperty("javax.xml.rpc.service.endpoint.address");
      }

    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }

  public String getEndpoint() {
    return _endpoint;
  }

  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (wsSmsService != null)
      ((javax.xml.rpc.Stub)wsSmsService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);

  }

  public WsSmsService getWsSmsService() {
    if (wsSmsService == null)
      _initWsSmsServiceProxy();
    return wsSmsService;
  }

  public String sendSms(String arg0) throws java.rmi.RemoteException{
    if (wsSmsService == null)
      _initWsSmsServiceProxy();
    return wsSmsService.sendSms(arg0);
  }

  public String sendTplSms(String arg0) throws java.rmi.RemoteException{
    if (wsSmsService == null)
      _initWsSmsServiceProxy();
    return wsSmsService.sendTplSms(arg0);
  }


}
