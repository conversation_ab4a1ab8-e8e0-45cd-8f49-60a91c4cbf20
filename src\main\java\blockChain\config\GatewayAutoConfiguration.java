package blockChain.config;

import blockChain.utils.HttpClientUtils;
import blockChain.utils.JsonUtil;
import blockChain.utils.MD5Util;
import org.apache.http.Header;
import org.apache.http.client.methods.HttpUriRequest;

import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


public class GatewayAutoConfiguration {

    private static String GET_TOKEN = "/get-token";
    //private static String GET_REFRESH_TOKEN = "/refresh-token";

    private static GatewayAutoConfiguration instance;
    //认证地址
    //private static String url = "http://api.zwfw.fujian.gov.cn:71/api-gateway/gateway";
    private static String url = "http://api.zwfw.fujian.gov.cn:71/api-gateway/gateway";
    //公钥(请向管理员申请获取)
    private static String appKey = "a09fab5e91b943c0b5fcbe7b6aaf18b1";
    //私钥(请向管理员申请获取)
    private static String appCode = "e4dd6487e74f4962a2110444349276a3";
    private static String accessToken = null;

    private static Timestamp expiredtime=new Timestamp(0);

    private static String sysCode="35000020207302FB015";

    private static String creditCode="11350000003591037R";

    private static String businessCode = "003591504GF05462";

    private static String businessName = "作品自愿登记";

    public static String getAppCode()
    {
      return appCode;
    }

    public static void setBusinessCode(String _businessCode)
    {
      businessCode=_businessCode;
    }

    public static String getBusinessCode()
    {
      return businessCode;
    }

    public static void setBusinessName(String _businessName)
    {
      businessName=_businessName;
    }

    public static String getBusinessName()
    {
      return  businessName;
    }

    public static String getAppKey()
    {
      return appKey;
    }

    public static String getSysCode()
    {
      return sysCode;
    }

    public static String getCreditCode()
    {
      return creditCode;
    }

    public static String getToken()
    {
      try {
        if(expiredtime.after(Timestamp.valueOf(LocalDateTime.now())))
        {
            return accessToken;
        }
        else
        {
          accessToken="";
        }
        if (accessToken == null || accessToken.length() == 0 )
        {
          // 获取accessToken
          String tokenUrl = url + GET_TOKEN;
          Map<String, String> params = new HashMap<>();
          params.put("app_key", appKey);
          long timestamp = System.currentTimeMillis();
          params.put("timestamp", String.valueOf(timestamp));
          params.put("sign", MD5Util.md5(appCode + timestamp));
          HttpClientUtils.HttpResponse httpResponse = HttpClientUtils.httpGetAuth(tokenUrl, params);
          if (httpResponse.getStatusCode() == 200) {
            String json = httpResponse.getBody();
            Map map = JsonUtil.fromJson(json, Map.class);
            accessToken = (String) map.get("access_token");
            expiredtime=Timestamp.valueOf(LocalDateTime.now());
            expiredtime.setTime(expiredtime.getTime()+2*60*60*1000);//2hour
            System.out.println("get token:"+accessToken+" nextExpireTime:"+expiredtime.getTime());
          } else {
            return "";
          }
        }
      }
      catch (Exception e)
      {
        e.printStackTrace();
      }
      return accessToken;
    }

    /**
     * Http 请求前的操作
     *
     * @param request
     * @throws URISyntaxException
     */
    public static void preExecute(HttpUriRequest request) throws Exception {

            synchronized (GatewayAutoConfiguration.class) {
                    if(accessToken==null||accessToken.length()==0)
                    {
                      // 获取accessToken
                      String tokenUrl = url + GET_TOKEN;
                      Map<String, String> params = new HashMap<>();
                      params.put("app_key", appKey);
                      long timestamp = System.currentTimeMillis();
                      params.put("timestamp", String.valueOf(timestamp));
                      params.put("sign", MD5Util.md5(appCode + timestamp));
                      HttpClientUtils.HttpResponse httpResponse = HttpClientUtils.httpGetAuth(tokenUrl, params);
                      if (httpResponse.getStatusCode() == 200) {
                        String json = httpResponse.getBody();
                        Map map = JsonUtil.fromJson(json, Map.class);
                        accessToken = (String) map.get("access_token");
                      } else {
                        return;
                      }
                    }
            }
        // 设置请求头的token
        System.out.println("成功获取Authorization-accessToken："+accessToken);
        request.setHeader("Authorization", "Basic " + accessToken);
    }

    /**
     * http请求后的操作
     *
     * @param response
     * @return
     */
    public static boolean afterExecute(HttpClientUtils.HttpResponse response) {
        if (null == instance) {
            return true;
        }
        boolean clearAccessToken = false;
        if (response.getStatusCode() == 400) {
            for (Header header : response.getHeaders()) {
                if ("x-error-code".equalsIgnoreCase(header.getName())) {
                    if ("access_token_invalid".equalsIgnoreCase(header.getValue())) {
                        clearAccessToken = true;
                        break;
                    }
                }
            }
        }
        if (clearAccessToken) {

            instance.accessToken = null;
            return false;
        }
        return true;
    }


    public String toString() {
        return "GatewayAutoConfiguration{" +
                "url='" + url + '\'' +
                ", appKey='" + appKey + '\'' +
                ", appCode='" + appCode + '\'' +
                '}';
    }

}
