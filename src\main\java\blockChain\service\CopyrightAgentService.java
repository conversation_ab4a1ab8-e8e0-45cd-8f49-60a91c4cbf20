package blockChain.service;

import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.repository.AgentRepository;
import blockChain.repository.BaseRepository;
import blockChain.repository.CopyrightOwnerPredicates;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:25
 */
@Service
public class CopyrightAgentService implements BaseService {

  private final AgentRepository repository;

  public CopyrightAgentService(AgentRepository repository){
    this.repository = repository;
  }

  @Override
  public BaseRepository getRepository() {
    return repository;
  }


  /**
   * 获取代理人统计
   * @param queryParam
   * @return
   */
  public List<StatisticDto> getAgent(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> agent = repository.getAgent(queryParam.getPageSize(), CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null));
    return agent;
  }
}

