package blockChain.entities.oldSystem;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name = "old_copyrightmanager")
public class OldCopyrightManager {

  public OldCopyrightManager(Long registrationNum){
    this.registrationNum = registrationNum;
  }

  // 旧系统数据迁移用实体类，迁移后删除

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long registrationNum;// 登记号

  private BigInteger worksRegistrationNum;//进入流程的作品，点击保存将作品ID存储起来
  private String worksNum;//作品自增长的分类序列号
  private String productionName;// 作品名称
  private String userName;//作品提交的用户名称
  private String productionTypeDesc;// 作品创作说明
  private String productionTypeId;// 作品类别ID
  private String artWorksId;// 美术类作品类型
  private String opuseDesc;// 作品类别说明
  // 作者的正面身份证
  private String authorIdCar;// 作者正面身份证
  private String copyIdCardZM;// 著作权人正面身份证
  private String copyTRPermit;// 著作权人暂住证
  private int applyType;// 办理方式
  private String opusInditekind;// 作品创作性质ID

  private String finishTime;// 完成时间
  private String finishAddress;// 作品完成地点
  private String completeCountries;// 作品完成国家
  private String completeProvince;// 作品完成身份
  private String completeCity;// 作品完成市
  private String completeArea;// 作品完成详细地址

  private int publishStatus;// 发表未发表状态
  private String firstPublishTime;// 首次发表时间
  private String firstCountry;// 首次发表地点（国家）
  private String firstProvince;// 首次发表地点（省）
  private String firstCity;// 首次发表地点（市）
  private String workPublishArea;//首次发表的详细地址
  private String workotherAreas;// 其他国家的具体地点
  private int rightOwnMode;// 权利归属方式
  private int obtainMode;// 权利取得方式
  private String rightGuarantee;// 权利保证书

  // 合作作品合同
  private String cooperationContract;// 合作作品合同
  // 职务作品合同
  private String positionContract;// 职务作品合同
  // 委托作品合同
  private String entrustType;//
  private String entrustContract;// 委托作品合同
  private String trusteeContract;//受托合同书

  private Integer rightScope;// 权利拥有状况
  private String emedium;// 电子文档
  private String emediumCD;// 光盘
  private String emediumOther;// 其他
  // 证书领取方式
  private String emediumCertificate;// 电子证书
  private String pmediumCertificate;// 纸质证书
  private String pmediumCount;// 打印件(A4纸)1份

  private Integer fileType;//上传文件的类型，0是图片，1是文档,2视频

  private Integer haverightId;// 权利拥有表ID
  private Integer submitId;// 提交者ID
  private Integer agentId;// 代理人表
  private Integer status_type;// 状态
  private String refuseReason;// 拒绝理由
  private String certificateUrl;//证书生成的链接
  private Integer inactiveType;//0代表没有撤销作品，1代表已撤销的作品
  private String cancelTheProofUrl;//撤销作品的撤销证明
  private int restatus;//审批流程的上一个状态
  private String registrationDate;// 登记时间

  private BigInteger allUpdateId;//获取当前保存插入ID
  private String stateType;//设置当点击提交之后跳到确认信息页，又返回修改数据需要进行修改数据库，不进行再插入数据
  private String starDate;//查询时间段的开始时间。
  private String endDate;//查询时间段的结束时间。

  private String rightScopeString; //权利拥有状况(文字)
  private String publishStatusString; //作品出版状态(文字)
  private String applyTypeString; //申请方式
  private String rightAttributionWay_String;// 权利归属方式(文字)
  private String rightWay_String;// 权利取得方式(文字)
  private String status_String;// 状态(文字)
  private String regNum; //证书生成后登记号
  private Integer focusWork;//作品的关注状态
  private Integer certificatePrint;//证书打印了就在列表上消失
  private BigInteger draftSaveId;//作品提交的时候先保存到草稿箱，确认提交在删除草稿箱数据
  private String intherFort;
  private Integer refuseRevokes;//1是上传撤销 ，2是拒绝撤销
  private Integer countDown; //倒计时时间

  private String dictName;
  private Integer maskCount;
  private String blockToken; //2019.12.14更新 区块链凭证
  private String licenseStatus;

}
