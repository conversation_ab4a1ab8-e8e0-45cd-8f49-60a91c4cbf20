package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_agent")
public class Agent {
	// 代理人
	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
	private String agentBook;// 委托事项
	private String agent_descript;// 存委托事项路径
	private String agent_descript1;// 存委托事项真实路径
	private String agentName;// 代理人姓名
	private String agentOwner;// 联系人
	private String agentTelephone;// 电话号码
	private String agentPhone;// 手机
	private String agentAddress;// 地址及
	private String agentCode;// 邮编
	private String agentEmail;// E-mail
	private String agentFax;// 传真

	/*private BigInteger copyrightmanagerId;//在线填报表的ID*/

  @JoinColumn(name = "agent_id")
  @OneToOne(fetch = FetchType.LAZY)
  private CopyrightManager manager;
}
