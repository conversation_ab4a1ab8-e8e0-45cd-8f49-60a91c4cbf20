package blockChain.mapper;

import blockChain.bean.PageResponse;
import blockChain.dto.CopyrightManagerDto;
import blockChain.dto.message.AlertMessageDto;
import blockChain.entities.CopyrightManager;
import blockChain.entities.message.AlertMessage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9 14:21
 */
@Mapper(config = CommonConfig.class)
public interface AlertMessageDtoMapper {
  AlertMessageDtoMapper INSTANCE = Mappers.getMapper(AlertMessageDtoMapper.class);


  @Mappings({
    @Mapping(target = "creator", ignore = true),
  })
  AlertMessage toEntity(AlertMessageDto dto);

  @Mappings({
    @Mapping(target = "creator", ignore = true),
    @Mapping(target = "uuid", ignore = true),
    @Mapping(target = "id", ignore = true),
    @Mapping(target = "createTime", ignore = true),
    @Mapping(target = "updateTime", ignore = true),
  })
  void update(@MappingTarget AlertMessage message, AlertMessageDto dto);

  @Mappings({
    @Mapping(target = "creator", source = "creator.realName"),
  })
  AlertMessageDto toDto(AlertMessage message);
  List<AlertMessageDto> toDto(Iterable<AlertMessage> message);

  default PageResponse<AlertMessageDto> toDto(Page<AlertMessage> all){
    List<AlertMessageDto> list = toDto(all.getContent());
    return PageResponse.of((long)all.getPageable().getPageNumber()+1, (long)all.getPageable().getPageSize(), all.getTotalElements(), list);
  }
}
