package blockChain.facade.service;

import blockChain.dto.TagDto;
import blockChain.entities.TagEntity;
import blockChain.mapper.TagMapper;
import blockChain.service.RoleService;
import blockChain.service.TagService;
import blockChain.utils.SM4CommonUtil;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/05/2022/5/7
 */
@Service
@AllArgsConstructor
public class TagServiceFacade {
    @Autowired
    private TagService tagService;
    @Autowired
    private RoleService roleService;

    @Transactional
    public TagEntity createTag(String tagName) {
      Assert.hasText(tagName, "标签名不能为空");
      Assert.isTrue(tagName.length()<=20, "标签名长度超出");
      List<TagEntity> tagEntities = tagService.getTagByName(tagName);
      Assert.isTrue(tagEntities.size()==0, "标签名重复");

      TagEntity tagEntity = new TagEntity();
      tagEntity.setName(tagName);
      tagService.save(tagEntity);
      return tagEntity;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public List<TagDto> getTagByPage(Integer page, Integer size){
      List<TagEntity> tagList;
      if (size == null || size <= 0) {
        tagList = tagService.findAll();
        if (StringUtils.isEmpty(tagList))
          tagList = new ArrayList<>();
        List<TagDto> tagDtos = changeKey(tagList, 1);

        TagDto tagIsAcc = new TagDto(20998, "侵权");
        tagDtos.add(tagIsAcc);
        TagDto tagFocus = new TagDto(20999, "关注");
        tagDtos.add(tagFocus);

        tagDtos.addAll(getWorkstationList());

        return tagDtos;
      }

      List<Sort.Order> list = new ArrayList<>();
      Sort.Order order = new Sort.Order(Sort.Direction.DESC, "id");
      list.add(order);
      Sort sort = Sort.by(list);
      Pageable of = PageRequest.of(page, size, sort);
      tagList = tagService.getTagByPage(of);
      return TagMapper.INSTANCE.toTagDtoList(tagList);
    }

    private List<TagDto> changeKey(List<TagEntity> tagEntities, int key) {
      List<TagDto> tagDtos = new ArrayList<>();
      for (TagEntity tagEntity:tagEntities) {
        TagDto tagDto = new TagDto();
        tagDto.setId(Integer.valueOf(key + SM4CommonUtil.patchHexString(tagEntity.getId().toString(),4)));
        tagDto.setName(tagEntity.getName());
        tagDtos.add(tagDto);
      }
      return tagDtos;
    }

    public List<TagDto> getWorkstationList() {
      return changeKey(TagMapper.INSTANCE.toTagEntityList(roleService.getWorkstationList()), 3);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public List<TagEntity> getTagByIds(List<Integer> ids){
      List<TagEntity> tagList = tagService.getTagByCodeAndIds(ids);
      return tagList;
    }

}
