package blockChain.service;

import blockChain.entities.ProcessRecord;
import blockChain.repository.BaseRepository;
import blockChain.repository.ProcessRecordRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:25
 */
@Service
@AllArgsConstructor
public class ProcessRecordService implements BaseService {

  private ProcessRecordRepository repository;

  @Override
  public BaseRepository getRepository() {
    return repository;
  }

    /**
   * 根据作品id获取作者
   */
  public List<ProcessRecord> getByCopyrightId(Long copyrightId){return repository.getByCopyrightId(copyrightId);}
}

