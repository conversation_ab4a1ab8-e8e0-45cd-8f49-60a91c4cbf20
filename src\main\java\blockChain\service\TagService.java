package blockChain.service;

import blockChain.entities.Digital;
import blockChain.entities.TagEntity;
import blockChain.repository.DigitalPredicates;
import blockChain.repository.DigitalRepository;
import blockChain.repository.TagRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class TagService {
  @Autowired
  private TagRepository tagRepository;

  public List<TagEntity> getTagByCodeAndIds(List<Integer> ids){
    return tagRepository.findAllByIdIn(ids);
  }

  public List<TagEntity> getTagByName(String name){
    return tagRepository.findAllByName(name);
  }

  public void save(TagEntity tagEntity){
    tagRepository.save(tagEntity);
  }

  public List<TagEntity> getTagByPage(Pageable of){
    return tagRepository.findAll(of).getContent();
  }

  public List<TagEntity> findAll(){
    return tagRepository.findAll();
  }
}
