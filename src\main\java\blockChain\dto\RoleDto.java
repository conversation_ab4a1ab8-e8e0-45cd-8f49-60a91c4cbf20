package blockChain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/12/18 10:01
 */
@Data
public class RoleDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 角色名称
     */
    @JsonProperty("name")
    private String roleName;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否必须
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Byte isNecessary;

    /**
     * 是否工作站
     */
    private Byte isWorkstation;

    /**
     * 创建时间
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime updateTime;

    private Set<MenuDto> authorities;
}
