
package org.tempuri;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "PopulationSoap", targetNamespace = "http://tempuri.org/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface PopulationSoap {


    /**
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "IDQuery", action = "http://tempuri.org/IDQuery")
    @WebResult(name = "IDQueryResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "IDQuery", targetNamespace = "http://tempuri.org/", className = "org.tempuri.IDQuery")
    @ResponseWrapper(localName = "IDQueryResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.IDQueryResponse")
    public String idQuery(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "IDCheck", action = "http://tempuri.org/IDCheck")
    @WebResult(name = "IDCheckResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "IDCheck", targetNamespace = "http://tempuri.org/", className = "org.tempuri.IDCheck")
    @ResponseWrapper(localName = "IDCheckResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.IDCheckResponse")
    public String idCheck(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

}
