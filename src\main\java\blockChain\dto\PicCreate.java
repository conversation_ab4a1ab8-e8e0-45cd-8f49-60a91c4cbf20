package blockChain.dto;

import java.util.List;

public class PicCreate {

	private String copyPicStr;// 图章路径
	private List<Integer> copyXY; //图章缩放后长宽
	private List<Integer> picsXY; //原图片等比缩放边界
	private List<String> PicsUrl;//需要处理的原图片路径
	private List<String> ResultPicUrl;//生成的目标图片路径
	
	
	public String getCopyPicStr() {
		return copyPicStr;
	}
	public void setCopyPicStr(String copyPicStr) {
		this.copyPicStr = copyPicStr;
	}
	public List<String> getPicsUrl() {
		return PicsUrl;
	}
	public void setPicsUrl(List<String> picsUrl) {
		PicsUrl = picsUrl;
	}
	public List<String> getResultPicUrl() {
		return ResultPicUrl;
	}
	public void setResultPicUrl(List<String> resultPicUrl) {
		ResultPicUrl = resultPicUrl;
	}
	public List<Integer> getCopyXY() {
		return copyXY;
	}
	public void setCopyXY(List<Integer> copyXY) {
		this.copyXY = copyXY;
	}
	public List<Integer> getPicsXY() {
		return picsXY;
	}
	public void setPicsXY(List<Integer> picsXY) {
		this.picsXY = picsXY;
	}
}
