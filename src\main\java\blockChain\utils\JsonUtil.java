package blockChain.utils;
import java.util.List;

import com.alibaba.fastjson.JSON;

/**
 * JSON 工具类
 *
 * <AUTHOR>
 *
 */
public class JsonUtil {

	/**
	 * Json序列化
	 *
	 * @param object
	 * @return
	 */
	public static String toJson(Object object) {
		return JSON.toJSONStringWithDateFormat(object, "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * Json 反序列化
	 *
	 * @param json
	 * @param clazz
	 * @return
	 */
	public static <T> T fromJson(String json, Class<T> clazz) {
		return JSON.parseObject(json, clazz);
	}

	public static <T> List<T> listFromJson (String json, Class<T> clazz) {
		return JSON.parseArray(json, clazz);
	}

}
