package blockChain.service;

import blockChain.entities.Digital;
import blockChain.repository.DigitalPredicates;
import blockChain.repository.DigitalRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class DigitalService {

  @Autowired
  private DigitalRepository digitalRepository;

  public Iterable<Digital> getDictByCode(String code, Integer enable) {
    Iterable<Digital> all = digitalRepository.findAll(DigitalPredicates.digitalQueryByCode(code, enable));
    return all;
  }

  public Iterable<Digital> getDictByPid(Integer pid, Integer enable) {
    Iterable<Digital> all = digitalRepository.findAll(DigitalPredicates.digitalQueryByPid(pid, enable));
    return all;
  }

  public Page<Digital> getDictByCodePage(String code, Integer enable, Pageable of) {
    Page<Digital> page = digitalRepository.findAll(DigitalPredicates.digitalQueryByCode(code, enable), of);
    return page;
  }

  public Optional<Digital> getById(Integer id) {
    if (id == null) {
      return Optional.empty();
    }
    return digitalRepository.findById(id);
  }

  public List<Digital> findByLevel(Integer level) {
    return digitalRepository.findByLevel(level);
  }

  public List<Digital> findAll(){
    return digitalRepository.findAll();
  }

  public Iterable<Digital> getDictByDict_Name(String dictName, Integer enable) {
    Iterable<Digital> all = digitalRepository.findAll(DigitalPredicates.digitalQueryByDict_Name(dictName, enable));
    return all;
  }

  public Digital getIdByArea(Long areaId, Integer areaLevel, Integer level, Integer pid){
    if (pid==0) {
      return digitalRepository.getIdByArea(areaId, areaLevel, level);
    } else {
      return digitalRepository.getIdByAreaAndPid(areaId, areaLevel, level, pid);
    }
  }
}
