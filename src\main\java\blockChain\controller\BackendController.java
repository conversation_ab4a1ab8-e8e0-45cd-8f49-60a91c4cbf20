package blockChain.controller;

import blockChain.bean.QueryParam;
import blockChain.dto.UserDto;
import blockChain.entities.CallBackEntity;
import blockChain.entities.CopyrightManager;
import blockChain.entities.ProcessProgressEntity;
import blockChain.entities.UserEntity;
import blockChain.facade.service.*;
import blockChain.service.ProcessProgressService;
import blockChain.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.ssdc.security.CscpUserDetail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/8/20
 */
@Api("最多采一次")
@Slf4j
@RestController
@RequestMapping("backend")
@RequiredArgsConstructor
public class BackendController {
    private final BackendServiceFacade backendServiceFacade;
    private final UserServiceFacade userServiceFacade;
    private final CopyrightManagerServiceFacade managerServiceFacade;
    private final OutSideServiceFacade outSideServiceFacade;

    private final ProcessProgressService processProgressService;

    private final CallBackServiceFacade callBackServiceFacade;
    @ApiOperation("获取授权token（内部测试用）")
    @PostMapping("getAccessToken")
    public ResponseEntity<Map<String, Object>> getAccessToken() {
        Map<String, Object> resultMap = new HashMap<>();

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity userEntity = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());

        String accessToken = backendServiceFacade.getAccessToken(userEntity);
        if (accessToken == null) {
            resultMap.put("result", "failed");
        } else {
            resultMap.put("accessToken", accessToken);
            resultMap.put("result", "success");
        }
        return new ResponseEntity<>(resultMap, HttpStatus.OK);
    }

    @ApiOperation("获取统一收件码")
    @PostMapping("getProjectId")
    public ResponseEntity<Map<String, Object>> getProjectId() {
        Map<String, Object> resultMap = new HashMap<>();

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity userEntity = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());

        String accessToken = backendServiceFacade.getAccessToken(userEntity);
        if (StringUtils.isEmpty(accessToken)) {
            resultMap.put("result", "failed");
            return new ResponseEntity<>(resultMap, HttpStatus.OK);
        }

        String projectId = backendServiceFacade.getProjectId(userEntity, accessToken);
        if (projectId == null) {
            resultMap.put("result", "failed");
            return new ResponseEntity<>(resultMap, HttpStatus.OK);
        } else {
            resultMap.put("projectId", projectId);
            resultMap.put("result", "success");
            return new ResponseEntity<>(resultMap, HttpStatus.OK);
        }
    }

    @ApiOperation("统一收件码激活（内部测试用）")
    @PostMapping("projectIdActive")
    public ResponseEntity<Map<String, Object>> projectIdActive(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> resultMap = new HashMap<>();
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity userEntity = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());

        Boolean flag = backendServiceFacade.projectIdActive(userEntity, queryParam.getString("projectId"));
        if (!flag) {
            resultMap.put("result", "failed");
        } else {
            resultMap.put("result", "success");
        }
        return new ResponseEntity<>(resultMap, HttpStatus.OK);
    }

    @ApiOperation("办件信息推送（内部测试用）")
    @PostMapping("startWorkflow")
    public ResponseEntity<Map<String, Object>> startWorkflow(@Valid @RequestBody QueryParam queryParam) {
        String uuid = UUID.randomUUID().toString();
        log.info("办件信息推送（内部测试用）开始, uuid={}，param={}：", uuid, queryParam);
        Map<String, Object> resultMap = new HashMap<>();
        List<String> workflows = new ArrayList<>();
        int type = queryParam.getInteger("type");
        Boolean retry = queryParam.getBoolean("retry");
        retry = null != retry ? retry : false;
        JSONArray idArray = queryParam.getJSONArray("ids");
        if (idArray != null && !idArray.isEmpty()) {
            List<Long> ids = JSONObject.parseArray(idArray.toJSONString(), Long.class);
            for (Long id : ids) {
                CopyrightManager manager = managerServiceFacade.getById(id);
                if (ObjectUtils.isEmpty(manager)) {
                    resultMap.put("result", "failed");
                    return new ResponseEntity<>(resultMap, HttpStatus.NO_CONTENT);
                }
                UserDto userDto = userServiceFacade.getByUserName(manager.getUserName());
                UserEntity userEntity = userServiceFacade.findById(userDto.getUserId());

                List<ProcessProgressEntity> entities = processProgressService.findBySnCodeAndTypeAndState(manager.getSnCode(), type, ProcessProgressEntity.State.FINISHED);
                if (!retry) {
                    manager.setSyncStatus("I");
                } else {
                    manager.setSyncStatus("U");
                }
                if (type == ProcessProgressEntity.Type.PUSH) {
                    if (!retry) {
                        if (ObjectUtils.isEmpty(entities)) {
                            // 表单数据回流
                            outSideServiceFacade.dataReflux(manager);
                        }
                    } else {
                        String accessToken = backendServiceFacade.getAccessToken(userEntity);
                        if (StringUtils.isEmpty(accessToken)) {
                            resultMap.put("result", "failed");
                            return new ResponseEntity<>(resultMap, HttpStatus.OK);
                        }
                        manager.setProjectId(backendServiceFacade.getProjectId(userEntity, accessToken));
                    }
                }

                String cause = "";
                if (type == ProcessProgressEntity.Type.FINISH) {
                    cause = "办结";
                }
                String workflowId = backendServiceFacade.startWorkflow(manager, userEntity, type, cause, retry);
                if (workflowId != null) {
                    workflows.add(workflowId);
                    switch (type) { // test retry sucess
                        case 1:
                            manager.setSnStatus(16);
                            break;
                        case 2:
                            manager.setSnStatus(18);
                            break;
                        case 3:
                            manager.setSnStatus(20);
                            break;
                        case 4:
                            manager.setSnStatus(22);
                            break;
                    }
                } else {
                    switch (type) { // test retry failed
                        case 1:
                            manager.setSnStatus(15);
                            break;
                        case 2:
                            manager.setSnStatus(17);
                            break;
                        case 3:
                            manager.setSnStatus(19);
                            break;
                        case 4:
                            manager.setSnStatus(21);
                            break;
                    }
                }
                managerServiceFacade.save(manager);
            }
        }

        if (ObjectUtils.isEmpty(workflows)) {
            resultMap.put("result", "failed");
        } else {
            resultMap.put("workflowIds", workflows);
            resultMap.put("result", "success");
        }
        log.info("办件信息推送（内部测试用）结束, uuid={}，param={}：", uuid, queryParam);
        return new ResponseEntity<>(resultMap, HttpStatus.OK);
    }

    @ApiOperation("总线流程回调")
    @PostMapping("callback")
    public ResponseEntity<Map<String, Object>> callbackFromWorkflow(@RequestBody String body) {
        log.info("回调传入值, body:{}", body);
        Map<String, Object> resultMap = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(body);
//        log.info("总线回调.jsonObject:{}",jsonObject);
        if(jsonObject.get("workflowId") != null && jsonObject.get("status") != null) {

            Integer status = (Integer) jsonObject.get("status");
            String workflowId = jsonObject.get("workflowId").toString();
            String message = jsonObject.getString("message");
            if (status.equals(CallBackEntity.State.UNFINISHED)) {
                log.info("总线回调失败：{}", workflowId);
            }
            log.info("数据写入回调临时表开始");
            CallBackEntity callBackEntity = new CallBackEntity();
            callBackEntity.setState(status);
            callBackEntity.setProcessId(workflowId);
            callBackEntity.setMessage(message);
            // TODO 回调处理 返回错误，写入错误消息
            callBackServiceFacade.saveData(callBackEntity);
            log.info("数据写入回到临时表结束,processId:{}", workflowId);
        } else {
            resultMap.put("result", "failed");
        }
        return new ResponseEntity<>(resultMap, HttpStatus.OK);
    }

    @ApiOperation("办件信息推送（重推测试用）")
    @PostMapping("startWorkflowRetry")
    public ResponseEntity<Map<String, Object>> startWorkflowRetry(@Valid @RequestBody QueryParam queryParam) {
        String uuid = UUID.randomUUID().toString();
        log.info("办件信息推送（重推测试用）开始, uuid={}，param={}：", uuid, queryParam);
        Map<String, Object> resultMap = new HashMap<>();
        int type = queryParam.getInteger("type");
        String startDate = queryParam.getString("startDate");
        String endDate = queryParam.getString("endDate");
        int size = queryParam.getInteger("size");
        Integer hour = queryParam.getInteger("hour");
        Integer statusType = queryParam.getInteger("statusType");
        // 手动推送，推送1小时
        LocalDateTime startTime = LocalDateTime.now();
        if (null == hour) {
            hour = 1;
        }
        LocalDateTime endTime = startTime.plusHours(hour);
        backendServiceFacade.startWorkflowRetry(startDate, endDate, type, startTime, endTime, size, statusType);

        resultMap.put("result", "success");
        log.info("办件信息推送（重推测试用）结束, uuid={}，param={}：", uuid, queryParam);
        return new ResponseEntity<>(resultMap, HttpStatus.OK);
    }

//    @PostMapping("callBackScheduling")
//    public ResponseEntity<Map<String, Object>> callBackScheduling() {
//        Map<String, Object> resultMap = new HashMap<>();
//        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//        UserEntity userEntity = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());
//
//        if (!Objects.equals(userEntity.getRole().getId(), 1))
//            resultMap.put("result", "false");
//        else {
//            callBackServiceFacade.callBackScheduling();
//            resultMap.put("result", "success");
//        }
//
//        return new ResponseEntity<>(resultMap, HttpStatus.OK);
//    }
}
