package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_process_progress")
public class ProcessProgressEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;// 登记号


    // 流程id
    private  String processId;

    // 类型 1-办件推送 2-受理 3-办件环节 4-办结+证照 10-补齐补正告知 9-补齐补正结束
    private int type;

    //作品id
    private Long registrationNum;

    // 完成状态 0-已完成 1-未完成
    private Integer state;

    //uuid
    private String uuid;

    // 创建时间
    @CreatedDate
    private LocalDateTime createTime;
    private String snCode;
    private String projectId;
    private String memo;

    public interface Type {
        Integer PUSH = 1;   // 作品提交
        Integer ACCEPT = 2; // 初审
        Integer PROCESS = 3; // 终审
        Integer FINISH = 4; // 回收 拒绝 驳回作品倒计时 撤销申请 证照提交
        Integer COMPLETE = 10;
        Integer END = 9;
    }

    public interface State{
        Integer FINISHED = 0;
        Integer UNFINISHED = 1;
    }


}
