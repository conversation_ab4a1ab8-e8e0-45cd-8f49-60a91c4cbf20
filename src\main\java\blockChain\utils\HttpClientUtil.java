package blockChain.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.HttpResponseException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.*;
import java.io.IOException;
import java.io.InputStream;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HttpClientUtil {
	private static volatile HttpClientUtil instance = null;
	private static boolean useProxy = false;
	private static final String proxyHost = "*************";
	private static final int proxyPort = 7777;
	private HttpClient httpClient;

	private HttpClientUtil() {
		httpClient = getHttpClient();
	}

	public HttpClient getHttpClient() {
		if (httpClient == null) {
			HttpClientBuilder builder = HttpClients.custom();
			if (useProxy) {
				builder.setProxy(new HttpHost("*************", 7777));
			}
			/*httpClient = builder.setConnectionTimeToLive(5, TimeUnit.SECONDS).setSSLHostnameVerifier((s, sslSession) -> {
				if (s.equals("**************") || s.equals("**************") || s.equals("**************") || s.equals("**************")) {
					return true;
				} else {
					HostnameVerifier hostnameVerifier = HttpsURLConnection.getDefaultHostnameVerifier();
					return hostnameVerifier.verify(s, sslSession);
				}
			}).setSSLContext(getSSLContext()).build();*/
		}
		return httpClient;
	}

	private SSLContext getSSLContext() {
		SSLContext sslContext = null;
		try {
			String clientCertPassword = "123456";
			KeyStore keyStore = KeyStore.getInstance("PKCS12");
			InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("certificate/ffcs.pfx");
			keyStore.load(inputStream, clientCertPassword.toCharArray());
			inputStream.close();
			KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
			kmf.init(keyStore, clientCertPassword.toCharArray());

			// Create a KeyStore containing our trusted CAs
			String keyStoreType = KeyStore.getDefaultType();
			KeyStore trustKeyStore = KeyStore.getInstance(keyStoreType);
			trustKeyStore.load(null, null);

			CertificateFactory cf = CertificateFactory.getInstance("X.509");
			Certificate ca;
			InputStream caInput = this.getClass().getClassLoader().getResourceAsStream("certificate/tobacoo_root_root.cer");
			ca = cf.generateCertificate(caInput);
			caInput.close();
			trustKeyStore.setCertificateEntry("tobacoo_root_root", ca);

			caInput = this.getClass().getClassLoader().getResourceAsStream("certificate/fjtic_root_root.cer");
			ca = cf.generateCertificate(caInput);
			caInput.close();
			trustKeyStore.setCertificateEntry("fjtic_root_root", ca);

			caInput = this.getClass().getClassLoader().getResourceAsStream("certificate/fjtic.cer");
			ca = cf.generateCertificate(caInput);
			caInput.close();
			trustKeyStore.setCertificateEntry("fjtic", ca);

			String tmfAlgorithm = TrustManagerFactory.getDefaultAlgorithm();
			TrustManagerFactory tmf = TrustManagerFactory.getInstance(tmfAlgorithm);
			tmf.init(trustKeyStore);

			sslContext = SSLContext.getInstance("TLS");
			sslContext.init(kmf.getKeyManagers(), tmf.getTrustManagers(), null);
		} catch (IOException | CertificateException | UnrecoverableKeyException | NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
			log.error(e.getMessage());
		}
		return sslContext;
	}

	public static HttpClientUtil getInstance() {
		if (instance == null) {
			synchronized (HttpClientUtil.class) {
				if (instance == null) {
					instance = new HttpClientUtil();
				}
			}
		}
		return instance;
	}

	public String requestPost(String url, Map<String, Object> map) throws IOException {
		HttpEntity entity = formData(map);
		return requestString(url, false, entity);
	}

	public String requestString(String url, boolean isGet, HttpEntity entity) throws IOException {
		HttpUriRequest request = null;
		if (isGet) {
			if (entity != null) {
				try {
					String params = EntityUtils.toString(entity, Consts.UTF_8);
					url = url + '?' + params;
				} catch (Exception e) {
					log.error("HttpClientUtil", e);
				}
			}
			request = new HttpGet(url);
		} else {
			HttpPost httpPost = new HttpPost(url);
			httpPost.setEntity(entity);
			request = httpPost;
		}
		String result = getHttpClient().execute(request, response -> {
			StatusLine statusLine = response.getStatusLine();
			HttpEntity responseEntity = response.getEntity();
			if (statusLine.getStatusCode() >= 300) {
				throw new HttpResponseException(
						statusLine.getStatusCode(),
						statusLine.getReasonPhrase());
			}
			if (responseEntity == null) {
				throw new ClientProtocolException("Response contains no content");
			}
			return EntityUtils.toString(responseEntity);
		});
		return result;
	}

	public static HttpEntity formData(Map<String, Object> map) {
		List<NameValuePair> list = new ArrayList<NameValuePair>(map.size());
		if (map != null && !map.isEmpty()) {
			Iterator<Map.Entry<String, Object>> it = map.entrySet().iterator();
			while (it.hasNext()) {
				Map.Entry<String, Object> next = it.next();
				String name = next.getKey();
				String value = next.getValue().toString();
				list.add(new BasicNameValuePair(name, value));
			}
		}
		return new UrlEncodedFormEntity(list, Consts.UTF_8);
	}

    /**
     * 创建http连接
     */
    public static CloseableHttpClient createHttpClient() {
        try {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(
                null, new TrustStrategy() {
                    @Override
                    public boolean isTrusted(X509Certificate[] chain,
                                             String authType) throws CertificateException {
                        // trust all
                        return true;
                    }
                }).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslContext,
                SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyStoreException e) {
            e.printStackTrace();
        }
        return HttpClients.createDefault();
    }

    /**
     * 连接数据的封装
     */
    public static void HttpConcent(String data,String httpurl){

        CloseableHttpClient client =createHttpClient();

        HttpPost httpPost = new HttpPost(httpurl);

        try {
            StringEntity entity= new StringEntity(data,"utf-8");
            httpPost.setEntity(entity);

            CloseableHttpResponse httpResponse = client.execute(httpPost);

            InputStream is = httpResponse.getEntity().getContent();

            StringBuffer sb = new StringBuffer();

            byte[] b = new byte[1024];

            while ((is.read(b, 0, 1024)) != -1) {
                sb.append(new String(b, Consts.UTF_8));
            }
            int result = httpResponse.getStatusLine().getStatusCode();
            System.out.println(result);
            String s1 = sb.toString();
            System.out.println(s1);
            is.close();
            httpResponse.close();
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
