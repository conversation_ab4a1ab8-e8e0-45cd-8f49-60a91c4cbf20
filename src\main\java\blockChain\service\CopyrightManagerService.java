package blockChain.service;

import blockChain.config.ApplicationRuntimeProperties;
import blockChain.config.SpringConfig;
import blockChain.dto.CopyrightManageExportDto;
import blockChain.dto.StatisticDto;
import blockChain.entities.CopyrightManager;
import blockChain.entities.UploadAttachment;
import blockChain.repository.CopyrightManagerPredicates;
import blockChain.repository.CopyrightManagerRepository;
import blockChain.repository.DigitalPredicates;
import com.google.common.collect.Lists;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static blockChain.entities.CopyrightManager.SnStatus.COMPLETE;


/**
 * <AUTHOR>
 * @date 2019/11/22 16:53
 */
@Service
//@AllArgsConstructor
public class CopyrightManagerService {
  //@Autowired
  private final CopyrightManagerRepository repository;
  //@Autowired
  private final CopyrightManagerRepository secRepository;
  private final ApplicationRuntimeProperties runtimeProperties;
  private final SpringConfig config;
  private SimilarAttachmentService similarAttachmentService;
  private final EntityManager entityManager;

  public CopyrightManagerService(CopyrightManagerRepository repository, CopyrightManagerRepository secRepository,
                                 ApplicationRuntimeProperties runtimeProperties, SpringConfig config, EntityManager entityManager) {
    this.repository = repository;
    this.secRepository = secRepository;
    this.runtimeProperties = runtimeProperties;
    this.config = config;
    this.entityManager = entityManager;
  }

  @Transactional(rollbackFor = RuntimeException.class)
  public void saveOrUpdateCopyright(CopyrightManager copyrightManager) {
    repository.save(copyrightManager);
  }

  public CopyrightManager findFirstByStatus_type(Integer status_type){
      Pageable of = PageRequest.of(0, 1);
      List<CopyrightManager> managers = Lists.newArrayList(repository.findAll(CopyrightManagerPredicates.byStatus(status_type), of));
      if (managers != null && managers.size() > 0) {
          return managers.get(0);
      } else {
          return null;
      }
  }
  public CopyrightManager findFirstByStatus_typeAndBlockChainToken(Integer status_type,String blockChianToken){
      List<Sort.Order> list = new ArrayList<>();
      Sort.Order order = new Sort.Order(Sort.Direction.ASC, "registrationDate");
      Sort.Order order2 = new Sort.Order(Sort.Direction.ASC, "registrationNum");
      list.add(order);
      list.add(order2);
    Sort sort = Sort.by(list);
    Pageable of = PageRequest.of(0, 1, sort);
    List<CopyrightManager> managers = Lists.newArrayList(repository.findAll(CopyrightManagerPredicates.byStatusAndBlockChainToken(status_type, blockChianToken), of));
    if (managers != null && managers.size() > 0) {
      return managers.get(0);
    } else {
      return null;
    }
  }

  /**
   * 证照提交列表查询
   */
  public Page<CopyrightManager> reportQuery(String worksNum, String productionName, List<Integer> regNums,
                                            String productionTypeId, Integer rightOwnMode, String agentName, String copyrightName,
                                            String startDate, String endDate, String certificateStartDate, String certificateEndDate, Integer page, Integer size) {
    StringBuilder sqlCount = CopyrightManagerPredicates.reportSql(worksNum, productionName, regNums, productionTypeId, rightOwnMode, agentName, copyrightName,
            startDate, endDate, certificateStartDate, certificateEndDate, true);
    Query queryCount = entityManager.createNativeQuery(sqlCount.toString());

    List totalElement = queryCount.getResultList();

    StringBuilder sqlList = CopyrightManagerPredicates.reportSql(worksNum, productionName, regNums, productionTypeId, rightOwnMode, agentName, copyrightName,
            startDate, endDate, certificateStartDate, certificateEndDate, false);
    sqlList.append(" order by ma.registrationDate asc, ma.registrationNum asc");
    sqlList.append(" limit ").append(page * size).append(" , ").append(size);
    Query queryList = entityManager.createNativeQuery(sqlList.toString());
    List<BigInteger> queryResults = queryList.getResultList();
    List<Long> registrationNums = queryResults.stream().map(BigInteger::longValue).collect(Collectors.toList());

    List<CopyrightManager> content = repository.findByRegistrationNumIn(registrationNums).stream()
            .sorted(Comparator.comparing(CopyrightManager::getRegistrationDate)
                    .thenComparing(CopyrightManager::getRegistrationNum))
            .collect(Collectors.toList());
    return new PageImpl<>(content, PageRequest.of(page, size), ((BigDecimal) totalElement.get(0)).intValue());
  }

  public Page<CopyrightManager> query(String worksNum, String productionName,
                                      String productionTypeId, Integer rightOwnMode,
                                      String startDate, String endDate, List<Integer> status, String userName, String agentName, String copyrightName,
                                      Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level, Integer evaluatemin, Integer evaluatemax, Pageable of,
                                      String certificateStartDate, String certificateEndDate, Integer province, List<Integer> regNums, List<Integer> tags, Integer isAccusation, Integer focusWork) {

    Page<CopyrightManager> page = repository.findAll(CopyrightManagerPredicates.digitalQuery(
            worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName, isCertificate, isSubmit, isreport, city_level, county_level, evaluatemin, evaluatemax, certificateStartDate,
            certificateEndDate, province, null, regNums, true, tags, isAccusation, focusWork), of);
      return page;
  }

    /**
     * 上报版保中心作品导出
     */
    public List<CopyrightManageExportDto> queryList(String worksNum, String productionName,
                                                    List<String> productionTypeIds, Integer rightOwnMode,
                                                    String startDate, String endDate, List<Integer> status, String userName, String agentName, String copyrightName,
                                                    Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level, Integer evaluatemin, Integer evaluatemax,
                                                    String certificateStartDate, String certificateEndDate) {

    /*List<CopyrightManager> list = Lists.newArrayList(repository.findAll(CopyrightManagerPredicates.digitalQuery(
      worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName,isCertificate,isSubmit,isreport,city_level,county_level,evaluatemin,evaluatemax)));*/
        List<CopyrightManageExportDto> list = repository.getExportList(worksNum, productionName, productionTypeIds, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName, isCertificate, isSubmit, isreport, city_level, county_level, evaluatemin, evaluatemax,
                certificateStartDate, certificateEndDate);
        return list;
    }

    /**
     * 开放查询
     */
    public Page<CopyrightManager> queryByCertificate(String worksNum, String productionName,
                                                     String productionTypeId, Integer rightOwnMode,
                                                     String startDate, String endDate, List<Integer> status, String userName, String agentName, String copyrightName,
                                                     Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level,
                                                     String certificateStartDate, String certificateEndDate, Pageable of) {

        Page<CopyrightManager> page = repository.findAll(CopyrightManagerPredicates.certificatePredicates(
                worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName, isCertificate, isSubmit, isreport, city_level, county_level, certificateStartDate, certificateEndDate), of);
        return page;
    }

    /**
     * 提交汇聚平台数据
     * @param isCertificate
     * @param pageable
     * @return
     */
  public List<CopyrightManager> queryAllByCertificate(Boolean isCertificate, Pageable pageable) {

    List<CopyrightManager> list = Lists.newArrayList(repository.findAll(CopyrightManagerPredicates.certificateQuery(isCertificate, true),pageable));
    return list;
  }

    /**
     * 提交省网办数据
     * @param isCertificate
     * @param pageable
     * @return
     */
    public List<CopyrightManager> queryAllXLByCertificate(Boolean isCertificate, PageRequest pageable) {

//        List<CopyrightManager> list = Lists.newArrayList(repository.getAllSWBSB(CopyrightManagerPredicates.certificateSNQuery(isCertificate, true),pageable));
      List<CopyrightManager> list = repository.getAllSWBSB(pageable, CopyrightManagerPredicates.certificateSNQuery(isCertificate, true));
        return list;
    }

    /**
     * 提交证照中心数据
     * @param isCertificate
     * @param pageable
     * @return
     */
    public List<CopyrightManager> queryAllZJByCertificate(Boolean isCertificate, Pageable pageable) {

        List<CopyrightManager> list = Lists.newArrayList(repository.findAll(CopyrightManagerPredicates.certificateZJQuery(isCertificate, true), pageable));
        return list;
    }

//  @Transactional
//  public void updateLicenseStatus() {
//    repository.updateLicenseStatus();
//  }

    public Page<CopyrightManager> findAll(Predicate var1, Pageable var2) {
        return repository.findAll(var1, var2);
    }

    public Iterable<CopyrightManager> findAll(Predicate var1) {
        return repository.findAll(var1);
    }

    public CopyrightManager getById(Long id) {
    return repository.getByRegistrationNum(id);
  }

    public List<CopyrightManager> getByIds(List<Long> ids) {
        return repository.findByRegistrationNumIn(ids);
    }

  public void remove(CopyrightManager entity) {
    repository.delete(entity);
  }


  /**
   * 获取作品分类统计总数
   */
  public long count(){
    return repository.count();
  }

  /**
   * 获取作品分类统计总数
   */
  public long count(Predicate predicate){
    return repository.count(predicate);
  }

  /**
   * 获取作品分类统计总数
   */
  public long countBy(Predicate predicate){
    return repository.countBy(predicate);
  }

  /**
   * 按月/年获取作品统计总数
   */
  public List<StatisticDto> countListBy(Predicate predicate, Boolean isYear, Boolean isWorkstation){
    return repository.countListBy(predicate, isYear, isWorkstation);
  }

  public long countHJreport(int yearLen, String start, String end){
    return repository.countHJreport(yearLen, start, end);
  }

  public long countSWBreport(int yearLen, String start, String end) {
      return repository.countSWBreport(yearLen, start, end, COMPLETE) +
              repository.countSWBreportNew(yearLen, start, end, COMPLETE);
  }
  /**
   * 按工作站统计作品总数
   */
  public List<StatisticDto> countWorkstationList(Predicate predicate){
    List<StatisticDto> workstationList = repository.countWorkstationList(predicate);
    long other = repository.countWorkstationOther(predicate);
    if (other>0) {
      workstationList.add(new StatisticDto(999,"其他",other));
    }

    return workstationList;
  }

  /**
   * 按工作站统计作品总数
   */
  public long countWorkstation(Predicate predicate){
    return repository.countWorkstation(predicate);
  }

  /**
   * 重复作品数
   */
  public long countSameBy(Predicate predicate){
    return repository.countSameBy(predicate);
  }

  /**
   * 福建省各个地市数量分布情况
   * @param predicate 附加查询条件
   * @return
   */
  public List<StatisticDto> getAreaDistributedForFujian(Predicate predicate) {
    return secRepository.getAreaDistributedForFujian(DigitalPredicates.digitalQueryByCode(runtimeProperties.getCityDigitalDefineCode(), 0), predicate);
  }

  public StatisticDto getAreaDistributed(Predicate ... predicate) {
    return secRepository.getAreaDistributedCountry(predicate);
  }

  public StatisticDto getAreaDistributedProvince(Predicate ... predicate) {
    return secRepository.getAreaDistributedProvince(predicate);
  }
  public List<StatisticDto> getAreaDistributedCity(Predicate digitalpredicate,Predicate managerperdicate,Predicate ... predicate) {
    return secRepository.getAreaDistributedCity(digitalpredicate,managerperdicate,predicate);
  }

  public List<StatisticDto> getAreaDistributedCity(Integer id,Predicate managerperdicate,Predicate ... predicate) {
    return secRepository.getAreaDistributedCity(id,managerperdicate,predicate);
  }

  public List<StatisticDto> getAreaDistributedCounty(Predicate digitalpredicate,Predicate managerperdicate,Predicate ... predicate) {
    return secRepository.getAreaDistributedCounty(digitalpredicate,managerperdicate,predicate);
  }

  public List<StatisticDto> getAreaDistributedCounty(Integer id,Predicate managerperdicate,Predicate ... predicate) {
    return secRepository.getAreaDistributedCounty(id,managerperdicate,predicate);
  }

  public Integer getCountDownLast(String userName){
    CopyrightManager copyrightManager = repository.findFirstByUserNameAndCountDownNotOrderByCountDownAsc(userName,0);
    if(copyrightManager!=null){
      return copyrightManager.getCountDown();
    }else{
      return 0;
    }
  }

  /**
   * 驳回自动删除倒计时
   */
  public long countDown(){
    return repository.updateCountDown();
  };

  /**
   * 批量更新状态
   */
  public void updateStateByIds(Integer state,List<Long> ids){
    repository.updateStateByIds(state,ids);
  }

  /**
   * 获取图片较对信息(未调用)
   * @param id
   * @return
   */
  public List<List<Map<String,Object>>> getCompareResult(Long id) {
    if(id==null){
      return new ArrayList<>();
    }

    CopyrightManager copyrightManager = getById(id);
    List<UploadAttachment> reportImgs = copyrightManager.getUploadWorks();
    if(reportImgs==null){
      return new ArrayList<>();
    }

    List<List<Map<String,Object>>> resultList = new ArrayList<>();
    for(UploadAttachment reportImg:reportImgs) {
      List<Map<String,Object>> list = similarAttachmentService.getBySimilarAttachmentId(id,reportImg.getId());
//      List<Map<String,Object>> list = similarAttachmentService.getBySimilarAttachmentId(id,reportImg.getId(), true);
      if(list.size()>0){
        Iterator<Map<String,Object>> iterator = list.iterator();
        while(iterator.hasNext()) {
          Map<String, Object> node = iterator.next();
          Integer score = (Integer) node.get("score");
          if (score > config.getSimilarScore()) {
            iterator.remove();
          }
        }
        if(list.size()>0) {
          resultList.add(list);
        }
      }
    }
    return resultList;
  }

    /**
     * 获取当年最后一个登记号编号
     *
     * @param year
     * @return
     */
    public String getLastNum(int year) {
        return repository.getLastNum("闽作登字-" + year + "-%");
    }
}
