package org.tempuri;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.4.4
 * 2021-09-28T16:24:54.255+08:00
 * Generated source version: 3.4.4
 *
 */
@WebServiceClient(name = "ConvergenceServiceService",
                  wsdlLocation = "http://120.35.29.217:809/Convergence/webservice/ConvergenceService?wsdl",
                  targetNamespace = "http://tempuri.org/")
public class ConvergenceServiceService extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://tempuri.org/", "ConvergenceServiceService");
    public final static QName ConvergenceServicePort = new QName("http://tempuri.org/", "ConvergenceServicePort");
    static {
        URL url = null;
        try {
            url = new URL("http://120.35.29.217:809/Convergence/webservice/ConvergenceService?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(ConvergenceServiceService.class.getName())
                .log(java.util.logging.Level.INFO,
                     "Can not initialize the default wsdl from {0}", "http://120.35.29.217:809/Convergence/webservice/ConvergenceService?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public ConvergenceServiceService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public ConvergenceServiceService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ConvergenceServiceService() {
        super(WSDL_LOCATION, SERVICE);
    }

    public ConvergenceServiceService(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public ConvergenceServiceService(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public ConvergenceServiceService(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns ConvergenceService
     */
    @WebEndpoint(name = "ConvergenceServicePort")
    public ConvergenceService getConvergenceServicePort() {
        return super.getPort(ConvergenceServicePort, ConvergenceService.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ConvergenceService
     */
    @WebEndpoint(name = "ConvergenceServicePort")
    public ConvergenceService getConvergenceServicePort(WebServiceFeature... features) {
        return super.getPort(ConvergenceServicePort, ConvergenceService.class, features);
    }

}
