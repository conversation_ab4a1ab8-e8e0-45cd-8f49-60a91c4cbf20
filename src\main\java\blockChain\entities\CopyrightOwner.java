package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 著作权拥有者
 */
@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name = "tb_copyrightowner")
public class CopyrightOwner implements Serializable {
    // 著作权人
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /*private BigInteger copyrightmanagerId;//在线填报表的ID*/
    /**
     * 著作权人姓名
   */
  private String copyName;// 著作权人姓名
  /**
   * 国籍
   */
  private int copyCountries;// 国籍
  /**
   * 省份
   */
  private int copyProvince;// 省份
  /**
   * 所在城市
   */
  private int copyCity;// 所在城市
  /**
   * 乡镇
   */
  private int copyCounty;// 所在城市
  /**
   * 著作权人类别
   */
  private int copyCategory;// 著作权人类别
  /**
   * 著作权人类别
   */
  private String copyCategorys;//著作权人类别
  /**
   * 证件类型
   */
  private int copyCertificate;// 证件类型
  /**
   * 是否少数民族
   */
  private byte isMinority;// 是否少数民族 (1是)

    /**
     * 著作权人电子证照(证件类型为身份证（142）居住证（156）)
     */
    @JoinColumn(name = "certificate_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private CertificateEntity copyCertificateZM; //著作权人电子证照

    @JoinColumn(name = "copyIdCardZM_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment copyIdCardZM; //著作权人证件

    /**
     * 中枢表单用数ID
     */
    private Long formDataId;
    /**
     * 统一收件码
     */
    private String projectId;

    /**
     * 著作权人证件类别值
     */
    public interface CopyCertificateValue {
        int IDCARD = 142; //身份证
        int RESIDENT = 156;  //居住证
        int PASSPORT = 449;  //护照
        int OTHER = 451;  //其他有效身份证件
        int CODE = 145;  //统一社会信用代码(营业执照)
    int SOCIALORGZS = 461;  //社会团体法人登记证书
    int GOVERNMENTZS = 462;  //统一社会信用代码证书
    int INSTITUTIONZS = 463;  //事业单位法人证书
    int OTHER2 = 464;  //其他有效证件
    int TWTXZ = 465;  //台湾居民来往大陆通行证
    int GATTXZ = 466;  //港澳居民来往内地通行证
  }

  /**
   * 著作权人类别值
   */
  public interface CopyCategoryValue {
    int PEOPLE = 135; //自然人
    //   * 法人类型(企业法人: C01 社会组织法人: C02 事业单位法人 C03 个体工商户:C04 )
    int COMPANYPEOPLE = 136;  //企业法人(企业法人C01/个体工商户C04)----营业执照
    int GOVERNMENT = 137;  //机关法人(事业单位法人C03)----统一社会信用代码证书
    int INSTITUTION  = 138;  //事业单位法人(事业单位法人C03)----事业单位法人证书
    int SOCIALORG = 139;  //社会团体法人(社会组织法人C02)----社会团体法人登记证书
    int OTHERORG = 140;  //其他组织
  }

  /**
   * 证件类型
   */
  private String copyCertificates;// 证件类型
  /**
   * 证件号
   */
  private String copyIdCard;// 证件号
  /**
   * 署名情况
   */
  private int copySignature;// 署名情况
  /**
   * 别名
   */
  private String copySignatureName;// 别名
  /**
   * 分别是草稿箱中保存的数据，还是作品提交的数据
   */
  private int copyClassification;//

  /**
   * 著作权人类别(文字)
   */
  private String copy_typeString; //
  /**
   * 证件类型(文字)
   */
  private String copy_idTypeString; //
  /**
   * 署名情况(文字)
   */
  private String copySignature_String; //

  private Boolean noDeleteUpdate;

  private String pch; //数据汇聚操作的批次号

  /**
   * 序号
   */
  private int seq;

  /**
   * 双向关联
   */
  @JoinColumn(name = "copyright_id")
  @ManyToOne
  private CopyrightManager manager;

  @Transient
  private List<String> copyAreaNames = new ArrayList<>();
    private String copyAreaNamesStr;

    public List<String> getCopyAreaNames() {
        if (this.copyAreaNamesStr != null) {
            return Arrays.asList(this.copyAreaNamesStr.split(","));
        } else {
            return new ArrayList<>();
        }
    }

    @Transient
    private List<Integer> copyArea = new ArrayList<>();

    public int setCopyCountries() {
        if (CollectionUtils.isNotEmpty(copyArea)) {
            copyCountries = copyArea.get(0);
        }
        return copyCountries;
    }

    public int getCopyProvince() {
        if (CollectionUtils.isEmpty(copyArea)) {
        } else if (copyArea.size() < 2) {
            copyProvince = 0;
        } else {
            copyProvince = copyArea.get(1);
        }
        return copyProvince;
    }

    public int getCopyCity() {
        if (CollectionUtils.isEmpty(copyArea)) {
        } else if (copyArea.size() < 3) {
            copyCity = 0;
        } else {
            copyCity = copyArea.get(2);
        }
        return copyCity;
    }

    public int getCopyCounty() {
        if (CollectionUtils.isEmpty(copyArea)) {
        } else if (copyArea.size() < 4) {
            copyCounty = 0;
        } else {
            copyCounty = copyArea.get(3);
        }
        return copyCounty;
    }

    public void setCopyAreaNames(List<String> copyAreaNames) {
        this.copyAreaNames = copyAreaNames;
        if (copyAreaNames != null) {
            String str = "";
            for (int i = 0; i < copyAreaNames.size(); i++) {
                if (i == 0) {
                    str = str + copyAreaNames.get(i);
                } else {
                    str = str + "," + copyAreaNames.get(i);
                }
            }
            this.copyAreaNamesStr = str;
        }
    }

    // 【证件类型】居民身份证：SF 机构代码 JGDM(9或18位)
    public String getIDType() {
        if (this.getCopyCategory() == 135) return "SF";
        else return "JGDM"; // 组织机构代码证
//      if (this.getCopyCertificate()==CopyCertificateValue.IDCARD) return "SF";
//      else if (this.getCopyCertificate()==CopyCertificateValue.PERMIT) return "SF";
    }

    // 申报种类。取值为数字“个人业务=0，法人业务=1
  public int getApplyType() {
      if (this.getCopyCategory()==135) return 0;
      else return 1;
  }

  // 机构类型，仅申报种类为法人时填写【必填】，参考字典【机构类型】
  public String getUnitType() {
      if (this.getCopyCategory()==135) return "";
      else return "QY";
  }
}
