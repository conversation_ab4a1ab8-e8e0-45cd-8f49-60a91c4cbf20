package blockChain.Thread;

import blockChain.config.BeanContext;
import blockChain.entities.pictureCompare.PictureCompareEntity;
import blockChain.facade.service.PictureCompareServiceFacade;
import blockChain.utils.HttpClientUtil;
import blockChain.utils.PictureHttpUtil;
import org.springframework.scheduling.annotation.Async;

import javax.servlet.http.HttpServletResponse;

public class PictureCompareThread extends Thread {
  private String bc;
  private String url;
  private HttpServletResponse response;
  private String token;
  private PictureCompareServiceFacade pictureCompareServiceFacade;

  /*public PictureCompareThread(String bc,String url,HttpServletResponse response,String token){
    this.bc = bc;
    this.url = url;
    this.response = response;
    this.token = token;
  }*/

  public void pictureCompare(String bc,String url,HttpServletResponse response,String token){
    this.bc = bc;
    this.url = url;
    this.response = response;
    this.token = token;
    run();
  }

  @Async("doSomethingExecutor")
  public void run(){
    System.out.println("线程启动：");
    this.pictureCompareServiceFacade = BeanContext.getApplicationContext().getBean(PictureCompareServiceFacade.class);
    String result1 = PictureHttpUtil.HttpConcent(bc, url, response);
    PictureCompareEntity pictureCompareEntity = new PictureCompareEntity();
    pictureCompareEntity.setToken(this.token);
    pictureCompareEntity.setResult(result1);
    pictureCompareServiceFacade.save(pictureCompareEntity);
  }
}
