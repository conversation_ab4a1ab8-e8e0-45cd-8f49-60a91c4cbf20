package blockChain.service.oldSystem;

import blockChain.entities.oldSystem.OldCopyrightOwner;
import blockChain.entities.oldSystem.OldSubmitter;
import blockChain.repository.oldSystem.OldCopyrightOwnerRepository;
import blockChain.repository.oldSystem.OldSubmitterRepository;
import blockChain.service.BaseService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/9 12:08
 */
@Service
@AllArgsConstructor
public class OldSubmitterService implements BaseService<OldSubmitterRepository, OldSubmitter, Integer> {
  private OldSubmitterRepository repository;

  @Override
  public OldSubmitterRepository getRepository() {
    return repository;
  }

}
