package blockChain.service;

import blockChain.entities.pictureCompare.PictureCompareEntity;
import blockChain.repository.PictureCompareRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/9/9 16:14
 */
@Service
@AllArgsConstructor
public class PictureCompareService implements BaseService<PictureCompareRepository, PictureCompareEntity, Long> {

  private final PictureCompareRepository repository;

  @Override
  public PictureCompareRepository getRepository() {
    return repository;
  }

  public PictureCompareEntity findByToken(String token){
    return repository.findByToken(token);
  }
}
