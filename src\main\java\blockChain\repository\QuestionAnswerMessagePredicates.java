package blockChain.repository;

import blockChain.entities.message.QQuestionAnswerMessageEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2020/4/13 10:23
 */
public final class QuestionAnswerMessagePredicates {
  public static Predicate titleKeyAndAnswerLikeAndCreateTimeBetween(String key, String answer, LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {
    QQuestionAnswerMessageEntity qaMessage = QQuestionAnswerMessageEntity.questionAnswerMessageEntity;

    BooleanBuilder builder = new BooleanBuilder();

    if (StringUtils.hasText(key)) {
      builder.and(qaMessage.key.contains(key));
    }
    if (StringUtils.hasText(answer)) {
      builder.and(qaMessage.answer.contains(answer));
    }
    /*if(createTimeStart != null && createTimeEnd != null){
      builder.and(qaMessage.createTime.between(createTimeStart, createTimeEnd));
    }*/
    if(createTimeStart != null){
      builder.and(qaMessage.createTime.after(createTimeStart));
    }
    if(createTimeEnd != null){
      builder.and(qaMessage.createTime.before(createTimeEnd));
    }
    return builder;
  }

  public static Predicate qaQuery(String key) {
    QQuestionAnswerMessageEntity qaMessage = QQuestionAnswerMessageEntity.questionAnswerMessageEntity;

    BooleanBuilder builder = new BooleanBuilder();

    if(StringUtils.isEmpty(key) || key.length() < 1){
      builder.and(qaMessage.id.eq(0L));
    }else{
      String[] split = key.split("");
      char[] splitMix = new char[split.length * 2 + 1];
      Arrays.fill(splitMix, '%');
      for (int i = 0; i < split.length; i++) {
        splitMix[ i * 2 + 1] = split[i].charAt(0);
      }
      StringBuilder sb = new StringBuilder();
      sb.append(splitMix);
      builder.and(qaMessage.key.like(sb.toString()));
    }

    return builder;
  }
}
