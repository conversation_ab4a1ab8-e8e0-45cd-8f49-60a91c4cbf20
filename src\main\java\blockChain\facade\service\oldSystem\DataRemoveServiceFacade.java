package blockChain.facade.service.oldSystem;

import blockChain.config.SpringConfig;
import blockChain.entities.*;
import blockChain.entities.oldSystem.*;
import blockChain.facade.service.UserServiceFacade;
import blockChain.service.*;
import blockChain.service.oldSystem.*;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.querydsl.QPageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/22 16:54
 */
@Service
@AllArgsConstructor
public class DataRemoveServiceFacade {
  private CopyrightManagerService copyrightManagerService;
  private UserServiceFacade userServiceFacade;
  private UserService userService;
  private CopyrightOwnerService copyrightOwnerService;
  private UploadAttachmentService uploadAttachmentService;
  private DigitalService digitalService;
  private OldAgentService oldAgentService;
  private OldAuthorService oldAuthorService;
  private OldCopyrightManagerService oldCopyrightManagerService;
  private OldCopyrightOwnerService oldCopyrightOwnerService;
  private OldUploadAttachmentService oldUploadAttachmentService;
  private OldSubmitterService oldSubmitterService;

  private final SpringConfig config;
/*
  public Long selectAll(){
    return oldCopyrightManagerService.selectAll();
  }
*/
  public List<Digital> findAllDigitals(){
    return digitalService.findAll();
  }


  @Async("doSomethingExecutor")
  public void run()
  {
    System.out.print("开始导入旧系统作品：\n");
    //Long total = selectAll();
    //获取所有数据字典
    List<Digital> digitals = findAllDigitals();
    //数据字典处理
    //作品类别
    Map<String,String> typeMap = new HashMap<>();
    //创作性质
    Map<String,String> opusInditekindMap = new HashMap<>();
    //国家
    Map<String,String> countryMap = new HashMap<>();
    //省份
    Map<String,String> provinceMap = new HashMap<>();
    //城市(福建)
    Map<String,String> fjCityMap = new HashMap<>();
    //城市（非福建）
    Map<String,String> cityMap = new HashMap<>();
    //县（福建）
    Map<String,String> countyMap = new HashMap<>();
    //权利取得方式
    Map<String,String> obtainModeMap = new HashMap<>();

    //遍历字典
    for(Digital digital:digitals){
      if(digital.getPid()!=null) {
        switch (digital.getPid()) {
          case 82:
            typeMap.put(digital.getDict_name(), digital.getId().toString());
            break;
          case 126:
            opusInditekindMap.put(digital.getDict_name(), digital.getId().toString());
            break;
          case 157:
            countryMap.put(digital.getDict_name(), digital.getId().toString());
            break;
          case 121:
            obtainModeMap.put(digital.getDict_name(), digital.getId().toString());
            break;
          default:
            break;
        }
      }
      if(digital.getCode()!=null) {
        switch (digital.getCode()) {
          case "pn":
            provinceMap.put(digital.getDict_name(), digital.getId().toString());
            break;
          case "ct":
            cityMap.put(digital.getPid()+","+digital.getDict_name(), digital.getId().toString());
            break;
          default:
            break;
        }
      }
      if(digital.getLevel()!=null) {
        switch (digital.getLevel()) {
          case 4:
            countyMap.put(digital.getPid()+","+digital.getDict_name(), digital.getId().toString());
            break;
          default:
            break;
        }
      }
    }

    //总map设置
    Map<String,Map<String,String>> digitalMap = new HashMap<>();
    digitalMap.put("typeMap",typeMap);
    digitalMap.put("opusInditekindMap",opusInditekindMap);
    digitalMap.put("countryMap",countryMap);
    digitalMap.put("provinceMap",provinceMap);
    digitalMap.put("fjCityMap",fjCityMap);
    digitalMap.put("cityMap",cityMap);
    digitalMap.put("countyMap",countyMap);
    digitalMap.put("obtainModeMap",obtainModeMap);

    Long number = new Long(1);
    while(true)
    {
      Long lastnumber=createCopyright(number,digitalMap);
      System.out.println("组导入完成，本组导入了" + (lastnumber-number) + "件作品：");
      if(lastnumber==number)
      {
        break;
      }
      number=lastnumber;
    }
  }



  @Transactional(rollbackFor = RuntimeException.class)
  public Long createCopyright(Long number, Map<String,Map<String,String>> digitalMap) {
    //迁移系统数据
    //获取旧系统作品数据
    try {
        QOldCopyrightManager qOldCopyrightManager = QOldCopyrightManager.oldCopyrightManager;
        QPageRequest pageRequest = new QPageRequest(0,500);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qOldCopyrightManager.status_type.eq(6));
        builder.and(qOldCopyrightManager.intherFort.isNull());
        Page<OldCopyrightManager> oldsPage = oldCopyrightManagerService.findAll(builder,pageRequest);
        List<OldCopyrightManager> olds = oldsPage.getContent();
        //List<OldCopyrightManager> olds = Lists.newArrayList(oldCopyrightManagerService.findAll(builder));
        if (olds != null && olds.size() > 0)
        {
          System.out.print("olds：" + olds.size());
          //遍历就系统数据逐条操作。
          for (OldCopyrightManager oldCopyrightManager : olds) {
            //判断数据是否已导入
            /*QCopyrightManager qCopyrightManager = QCopyrightManager.copyrightManager;
            BooleanBuilder currentBuider = new BooleanBuilder();
            currentBuider.and(qCopyrightManager.worksNum.eq(oldCopyrightManager.getWorksNum()));
            List<CopyrightManager> currents = Lists.newArrayList(copyrightManagerService.findAll(currentBuider));
            if(currents!=null && currents.size()>0){
              continue;
            }*/
            System.out.print("开始导入第" + number + "件作品：");
            LocalDateTime begintime=LocalDateTime.now();
            //基础数据处理
            CopyrightManager copyrightManager = new CopyrightManager();
            setCopyrightManager(copyrightManager, oldCopyrightManager,digitalMap);
            //代理人、提交人处理
            setPerson(copyrightManager, oldCopyrightManager);
            //文件部分导入
            setFiles(copyrightManager, oldCopyrightManager);
            //保存
            copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
            //著作权人、作者处理
            setOwner(copyrightManager, oldCopyrightManager,digitalMap);
            oldCopyrightManager.setIntherFort("导入完成");
            oldCopyrightManagerService.save(oldCopyrightManager);
            LocalDateTime endtime=LocalDateTime.now();
            Duration  duration = Duration.between(begintime,  endtime);
            System.out.print(" 用时 :["+duration.toMillis()+"ms]");
            System.out.print(" 第" + number + "件作品（旧id"+oldCopyrightManager.getRegistrationNum()+"）导入成功！\n");
            number++;
          }
        }
    }catch (Exception e){
      System.out.print("导入失败，异常原因："+e.getMessage());
      e.printStackTrace();
      throw new RuntimeException();
    }
    return number;
  }

  private void setCopyrightManager(CopyrightManager news,OldCopyrightManager old,Map<String,Map<String,String>> digitalMap){
    //用户名处理
    news.setUserName(old.getUserName());
    if(old.getUserName()!=null) {
      UserEntity oldUser = userService.getByUserName(old.getUserName()).orElse(null);
      if(oldUser!=null){
        QUserEntity qUserEntity = QUserEntity.userEntity;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUserEntity.phoneNum.eq(oldUser.getPhoneNum()));
        builder.and(qUserEntity.uuid.isNotNull());
        List<UserEntity> newUser = Lists.newArrayList(userService.findAll(builder));
        if(newUser!=null && newUser.size()>0){
          news.setUserName(newUser.get(0).getUserName());
        }
      }
    }
    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    //登记日期处理
    news.setRegistrationDate(LocalDate.parse(old.getRegistrationDate(),fmt).atTime(0,0,0));
    //作品类别id处理
    news.setProductionTypeId(digitalMap.get("typeMap").get(old.getProductionTypeId()));
    /*Iterable<Digital> productTypeIdDicts = digitalService.getDictByPid(82,0);
    Iterator<Digital> digitalIterator = productTypeIdDicts.iterator();
    while(digitalIterator.hasNext()){
      Digital digital = digitalIterator.next();
      if(digital.getDict_name().equals(old.getProductionTypeId())){
        news.setProductionTypeId(digital.getId().toString());
        break;
      }
    }*/
    //申请类别处理
    news.setApplyType(old.getApplyType()+1);
    //创作性质字段处理
    news.setOpusInditekind(digitalMap.get("opusInditekindMap").get(old.getOpusInditekind()));
    /*Iterable<Digital> opusInditekindDicts = digitalService.getDictByPid(126,0);
    Iterator<Digital> opusInditekindIterator = opusInditekindDicts.iterator();
    while(opusInditekindIterator.hasNext()){
      Digital digital = opusInditekindIterator.next();
      if(digital.getDict_name().equals(old.getOpusInditekind())){
        news.setOpusInditekind(digital.getId().toString());
        break;
      }
    }*/
    //作品完成地点处理
    news.setFinishAreaStr(old.getFinishAddress());
    news.setCompleteAreaStr(old.getFinishAddress());
    news.setCompleteArea(old.getFinishAddress());
    //发布状态处理
    news.setPublishState(old.getPublishStatus()+1);
    //首次发表地点相关字段处理
    String firstPublishAreaStr = "";
    news.setFirstCountry(digitalMap.get("countryMap").get(old.getFirstCountry()));
    firstPublishAreaStr+=old.getFirstCountry();
    /*Iterable<Digital> firstCountryDicts = digitalService.getDictByPid(157,0);
    Iterator<Digital> firstCountryIterator = firstCountryDicts.iterator();
    while(firstCountryIterator.hasNext()){
      Digital digital = firstCountryIterator.next();
      if(digital.getDict_name().equals(old.getFirstCountry())){
        news.setFirstCountry(digital.getId().toString());
        firstPublishAreaStr+=digital.getDict_name();
        break;
      }
    }*/
    news.setFirstProvince(digitalMap.get("provinceMap").get(old.getFirstProvince()));
    news.setFirstCity(digitalMap.get("cityMap").get(news.getFirstProvince()+","+old.getFirstCity()+"市"));

    /*Iterable<Digital> firstProvinceDicts = digitalService.getDictByCode("pn",0);
    Iterator<Digital> firstProvinceIterator = firstProvinceDicts.iterator();
    while(firstProvinceIterator.hasNext()){
      Digital digital = firstProvinceIterator.next();
      if(digital.getDict_name().equals(old.getFirstProvince())){
        news.setFirstProvince(digital.getId().toString());
        firstPublishAreaStr=firstPublishAreaStr+" "+digital.getDict_name();
        Iterable<Digital> firstCityDicts = digitalService.getDictByPid(digital.getId(),0);
        Iterator<Digital> firstCityIterator = firstCityDicts.iterator();
        while(firstCityIterator.hasNext()){
          Digital city = firstCityIterator.next();
          if(city.getDict_name().contains(old.getFirstCity())){
            news.setFirstCity(city.getId().toString());
            firstPublishAreaStr=firstPublishAreaStr+" "+digital.getDict_name();
            break;
          }
        }
        break;
      }
    }*/
    news.setFirstAreaStr(firstPublishAreaStr);
    news.setWorkPublishAreaStr(firstPublishAreaStr+old.getWorkPublishArea());
    //创作类型处理
    switch (old.getRightOwnMode()){
      case 1:
        news.setRightOwnMode(116);
        break;
      case 2:
        news.setRightOwnMode(117);
        break;
      case 3:
        news.setRightOwnMode(120);
        break;
      case 4:
        news.setRightOwnMode(118);
        break;
      case 5:
        news.setRightOwnMode(119);
        break;
      default:
        break;
    }
    //权利取得方式处理
    String key = digitalMap.get("obtainModeMap").get(old.getObtainMode());
    if(key!=null) {
      news.setObtainMode(Integer.parseInt(digitalMap.get("obtainModeMap").get(old.getObtainMode())));
    }
    /*Iterable<Digital> obtainModeDicts = digitalService.getDictByPid(121,0);
    Iterator<Digital> obtainModeIterator = obtainModeDicts.iterator();
    while(obtainModeIterator.hasNext()){
      Digital digital = obtainModeIterator.next();
      if((digital.getSort()-1)==old.getObtainMode()){
        news.setObtainMode(digital.getId());
        break;
      }
    }*/
    //权利拥有状况处理
    news.setRightScope(old.getRightScope()+1);
    if(old.getHaverightId()!=0){
      news.setHaverightIds("432,434,435,436,437,438,439,440,441,442,443,444,445,446,447");
    }
    //文件类型处理
    news.setFileType(old.getFileType()+1);
    //状态处理
    if(old.getStatus_type()==6){
      news.setStatus_type(11);
    }else{
      news.setStatus_type(6);
    }
    //撤销状态处理
    if(old.getInactiveType()!=null) {
      news.setInactiveType(old.getInactiveType() + 1);
    }
    //作品上报状态处理
    if(old.getLicenseStatus()!=null) {
      if (old.getLicenseStatus().equals("00")) {
        news.setLicenseStatus(old.getWorksNum());
      } else {
        news.setLicenseStatus(old.getLicenseStatus());
      }
    }
    news.setEmedium(old.getEmedium());
    news.setEmediumCD(old.getEmediumCD());
    news.setEmediumCertificate(old.getEmediumCertificate());
    news.setEmediumOther(old.getEmediumOther());
    news.setFinishTime(old.getFinishTime());
    news.setFirstPublishTime(old.getFirstPublishTime());
    news.setFocusWork(old.getFocusWork());
    //news.setMaskCount(old.getMaskCount());
    news.setProductionName(old.getProductionName());
    news.setProductionTypeDesc(old.getProductionTypeDesc());
    //news.setRefuseRevokes(old.getRefuseRevokes());
    news.setWorkPublishArea(old.getWorkPublishArea());
    news.setWorksNum(old.getWorksNum());
    news.setOnBlockToken(old.getBlockToken());
    news.setRegNum(old.getRegNum());
  }

  private void setPerson(CopyrightManager news,OldCopyrightManager old){
    if(old.getAgentId()!=null) {
      OldAgent oldAgent = oldAgentService.findById(old.getAgentId()).orElse(null);
      if (oldAgent != null) {
        Agent agent = new Agent();
        agent.setAgentName(oldAgent.getAgent_name());
        agent.setAgentOwner(oldAgent.getAgent_contactPerson());
        agent.setAgentTelephone(oldAgent.getAgent_telephone());
        agent.setAgentPhone(oldAgent.getAgent_mobile());
        agent.setAgentAddress(oldAgent.getAgent_address());
        agent.setAgentCode(oldAgent.getAgent_zipCode());
        agent.setAgentEmail(oldAgent.getAgent_email());
        agent.setAgentFax(oldAgent.getAgent_fax());
        news.setAgentList(agent);
        UploadAttachment agentDescript = new UploadAttachment();
        agentDescript.setWorkUrl("File/media/nas/copyright_upload/upload" + oldAgent.getAgent_descript());
        agentDescript.setWorkName(getFileName(oldAgent.getAgent_descript()));
        agentDescript.setFileType(0);
        uploadAttachmentService.insert(agentDescript);
        news.setAgentBook(agentDescript);
      }
    }
    if(old.getSubmitId()!=null) {
      OldSubmitter oldSubmitter = oldSubmitterService.findById(old.getSubmitId()).orElse(null);
      if (oldSubmitter != null) {
        Submitter submitter = new Submitter();
        submitter.setCopyrightName(oldSubmitter.getSubmitter_name());
        submitter.setCopyrightOwner(oldSubmitter.getSubmitter_contactPerson());
        submitter.setCopyrightTelephone(oldSubmitter.getSubmitter_telephone());
        submitter.setCopyrightPhone(oldSubmitter.getSubmitter_mobile());
        submitter.setCopyrightAddress(oldSubmitter.getSubmitter_address());
        submitter.setCopyrightCode(oldSubmitter.getSubmitter_zipCode());
        submitter.setCopyrightEmail(oldSubmitter.getSubmitter_email());
        submitter.setCopyrightFax(oldSubmitter.getSubmitter_fax());
        news.setSubmitter(submitter);
      }
    }

    //查询作者信息
    QOldAuthor qOldAuthor = QOldAuthor.oldAuthor;
    BooleanBuilder builder = new BooleanBuilder();
    builder.and(qOldAuthor.copyrightmanagerId.eq(old.getRegistrationNum()));
    builder.and(qOldAuthor.author_classification.eq(0));
    List<OldAuthor> oldAuthors = Lists.newArrayList(oldAuthorService.findAll(builder));
    if(oldAuthors!=null && oldAuthors.size()>0){
      List<Author> newAuthors = new ArrayList<>();
      for(OldAuthor oldAuthor:oldAuthors){
        Author author = new Author();
        author.setAuthorName(oldAuthor.getAuthor_name());
        author.setAuthorIdCard(oldAuthor.getAuthor_idCard());
        author.setAuthorCategory(oldAuthor.getAuthor_type());
        author.setAuthorCertificate(oldAuthor.getAuthor_idType());
        author.setAuthorAddress(oldAuthor.getAuthor_address());
        author.setAuthorPhong(oldAuthor.getAuthor_phone());
        author.setAuthorSignature(oldAuthor.getAuthor_signType());
        author.setAuthorSignatureName(oldAuthor.getAuthor_opusSign());
        newAuthors.add(author);
      }
      news.setAuthorList(newAuthors);
    }
  }

  private void setOwner(CopyrightManager news,OldCopyrightManager old,Map<String,Map<String,String>> digitalMap){
    //查询旧著作权人信息
    QOldCopyrightOwner qOldCopyrightOwner = QOldCopyrightOwner.oldCopyrightOwner;
    BooleanBuilder builder = new BooleanBuilder();
    builder.and(qOldCopyrightOwner.copyrightmanagerId.eq(old.getRegistrationNum()));
    builder.and(qOldCopyrightOwner.copy_classification.eq(0));
    List<OldCopyrightOwner> oldCopyrightOwners = Lists.newArrayList(oldCopyrightOwnerService.findAll(builder));
    if(oldCopyrightOwners!=null && oldCopyrightOwners.size()>0){
      List<CopyrightOwner> newOwners = new ArrayList<>();
      for(OldCopyrightOwner oldOwner:oldCopyrightOwners){
        CopyrightOwner owner = new CopyrightOwner();
        //转换著作权人省市县
        owner.setCopyCountries(Integer.valueOf(digitalMap.get("countryMap").get(oldOwner.getCopy_countries())));
        owner.setCopyProvince(Integer.valueOf(digitalMap.get("provinceMap").get(oldOwner.getCopy_province())));
        owner.setCopyCity(Integer.valueOf(digitalMap.get("cityMap").get(owner.getCopyProvince()+","+oldOwner.getCopy_city()+"市")));
        owner.setCopyCounty(Integer.valueOf(digitalMap.get("countyMap").get(owner.getCopyCity()+","+oldOwner.getCopy_country())));

        /*Iterable<Digital> CountryDicts = digitalService.getDictByPid(157,0);
        Iterator<Digital> CountryIterator = CountryDicts.iterator();
        while(CountryIterator.hasNext()){
          Digital digital = CountryIterator.next();
          if(digital.getDict_name().equals(oldOwner.getCopy_countries())){
            owner.setCopyCountries(digital.getId().toString());
            break;
          }
        }
        Iterable<Digital> ProvinceDicts = digitalService.getDictByCode("pn",0);
        Iterator<Digital> ProvinceIterator = ProvinceDicts.iterator();
        while(ProvinceIterator.hasNext()){
          Digital digital = ProvinceIterator.next();
          if(digital.getDict_name().equals(oldOwner.getCopy_province())){
            owner.setCopyProvince(digital.getId().toString());
            Iterable<Digital> firstCityDicts = digitalService.getDictByPid(digital.getId(),0);
            Iterator<Digital> firstCityIterator = firstCityDicts.iterator();
            while(firstCityIterator.hasNext()){
              Digital city = firstCityIterator.next();
              if(city.getDict_name().contains(oldOwner.getCopy_city())){
                owner.setCopyCity(city.getId().toString());
                Iterable<Digital> CountyDicts = digitalService.getDictByPid(city.getId(),0);
                Iterator<Digital> CountyIterator = CountyDicts.iterator();
                while(CountyIterator.hasNext()){
                  Digital county = CountyIterator.next();
                  if(county.getDict_name().equals(oldOwner.getCopy_country())){
                    owner.setCopyCounty(county.getId().toString());
                    break;
                  }
                }
                break;
              }
            }
            break;
          }
        }*/
        //证件相关数据处理
        owner.setCopyCategory(oldOwner.getCopy_type());
        owner.setCopyCertificate(oldOwner.getCopy_idType());
        owner.setCopyIdCard(oldOwner.getCopy_idCard());
        owner.setCopyName(oldOwner.getCopy_name());
        owner.setCopySignature(oldOwner.getCopy_sign());
        owner.setCopySignatureName(oldOwner.getCopy_opusSign());
        owner.setCopyAreaNamesStr(oldOwner.getCopy_countries()+","+oldOwner.getCopy_province()+","+oldOwner.getCopy_city()+","+oldOwner.getCopy_country());
        owner.setManager(news);
        copyrightOwnerService.save(owner);
      }
    }
  }

  private void setFiles(CopyrightManager news,OldCopyrightManager old){
    if(old.getAuthorIdCar()!=null && StringUtils.hasText(old.getAuthorIdCar())) {
      UploadAttachment uploadAttachment = new UploadAttachment();
      uploadAttachment.setWorkUrl("File/media/nas/copyright_upload/upload" + old.getAuthorIdCar());
      uploadAttachment.setWorkName(getFileName(old.getAuthorIdCar()));
      uploadAttachment.setFileType(0);
      uploadAttachmentService.insert(uploadAttachment);
      news.setAuthorIdCar(uploadAttachment);
    }

    if(old.getCopyIdCardZM()!=null && StringUtils.hasText(old.getCopyIdCardZM())) {
      UploadAttachment copyIdCardZM = new UploadAttachment();
      copyIdCardZM.setWorkUrl("File/media/nas/copyright_upload/upload" + old.getCopyIdCardZM());
      copyIdCardZM.setWorkName(getFileName(old.getCopyIdCardZM()));
      copyIdCardZM.setFileType(0);
      uploadAttachmentService.insert(copyIdCardZM);
      news.setCopyIdCardZM(copyIdCardZM);
    }

    if(old.getCopyTRPermit()!=null && StringUtils.hasText(old.getCopyTRPermit())) {
      UploadAttachment copyTRPermit = new UploadAttachment();
      copyTRPermit.setWorkUrl("File/media/nas/copyright_upload/upload" + old.getCopyTRPermit());
      copyTRPermit.setWorkName(getFileName(old.getCopyTRPermit()));
      copyTRPermit.setFileType(0);
      uploadAttachmentService.insert(copyTRPermit);
      news.setCopyIdCardZM(copyTRPermit);
    }

    if(old.getCooperationContract()!=null && StringUtils.hasText(old.getCooperationContract())) {
      UploadAttachment cooperationContract = new UploadAttachment();
      cooperationContract.setWorkUrl("File/media/nas/copyright_upload/upload" + old.getCooperationContract());
      cooperationContract.setWorkName(getFileName(old.getCooperationContract()));
      cooperationContract.setFileType(0);
      uploadAttachmentService.insert(cooperationContract);
      news.setCooperationContract(cooperationContract);
    }

    if(old.getEntrustContract()!=null && StringUtils.hasText(old.getEntrustContract())) {
      UploadAttachment entrustContract = new UploadAttachment();
      entrustContract.setWorkUrl("File/media/nas/copyright_upload/upload" + old.getEntrustContract());
      entrustContract.setWorkName(getFileName(old.getEntrustContract()));
      entrustContract.setFileType(0);
      uploadAttachmentService.insert(entrustContract);
      news.setEntrustContract(entrustContract);
    }

    if(old.getTrusteeContract()!=null && StringUtils.hasText(old.getTrusteeContract())) {
      UploadAttachment trusteeContract = new UploadAttachment();
      trusteeContract.setWorkUrl("File/media/nas/copyright_upload/upload" + old.getTrusteeContract());
      trusteeContract.setWorkName(getFileName(old.getTrusteeContract()));
      trusteeContract.setFileType(0);
      uploadAttachmentService.insert(trusteeContract);
      news.setTrusteeContract(trusteeContract);
    }

    if(old.getPositionContract()!=null && StringUtils.hasText(old.getPositionContract())) {
      UploadAttachment positionContract = new UploadAttachment();
      positionContract.setWorkUrl("File/media/nas/copyright_upload/upload" + old.getPositionContract());
      positionContract.setWorkName(getFileName(old.getPositionContract()));
      positionContract.setFileType(0);
      uploadAttachmentService.insert(positionContract);
      news.setPositionContract(positionContract);
    }

    if(old.getCertificateUrl()!=null && StringUtils.hasText(old.getCertificateUrl())) {
      UploadAttachment certificateUrl = new UploadAttachment();
      certificateUrl.setWorkUrl("File/media/nas/copyright_upload/fileModel" + old.getCertificateUrl());
      certificateUrl.setWorkName(getFileName(old.getCertificateUrl()));
      certificateUrl.setFileType(0);
      uploadAttachmentService.insert(certificateUrl);
      news.setCertificateUrl(certificateUrl.getWorkUrl());
    }

    if(old.getCancelTheProofUrl()!=null && StringUtils.hasText(old.getCancelTheProofUrl())) {
      UploadAttachment cancelTheProofUrl = new UploadAttachment();
      cancelTheProofUrl.setWorkUrl("File/media/nas/copyright_upload/upload" + old.getCancelTheProofUrl());
      cancelTheProofUrl.setWorkName(getFileName(old.getCancelTheProofUrl()));
      cancelTheProofUrl.setFileType(0);
      uploadAttachmentService.insert(cancelTheProofUrl);
      news.setCancelTheProofUrl(cancelTheProofUrl.getWorkUrl());
    }

    //作品样本处理
    QOldUploadAttachment qOldUploadAttachment = QOldUploadAttachment.oldUploadAttachment;
    BooleanBuilder builder = new BooleanBuilder();
    builder.and(qOldUploadAttachment.works_id.eq(old.getRegistrationNum()));
    builder.and(qOldUploadAttachment.works_classification.eq(0));
    List<OldUploadAttachment> oldUploadAttachments = Lists.newArrayList(oldUploadAttachmentService.findAll(builder));
    if(oldUploadAttachments!=null && oldUploadAttachments.size()>0){
      List<UploadAttachment> uploads = new ArrayList<>();
      for(OldUploadAttachment oldUploadAttachment:oldUploadAttachments){
        UploadAttachment works = new UploadAttachment();
        works.setWorkUrl("File/media/nas/copyright_upload/upload"+oldUploadAttachment.getWorks_url());
        works.setWorkName(getFileName(oldUploadAttachment.getWorks_url()));
        works.setFileType(0);
        uploadAttachmentService.insert(works);
        uploads.add(works);
      }
      news.setUploadWorks(uploads);
    }
  }

  private String getFileName(String path){
    String [] sz=path.split("/");
    return sz[sz.length-1];
  }

}
