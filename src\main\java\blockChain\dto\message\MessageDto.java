package blockChain.dto.message;

import blockChain.entities.message.MessageUserEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/18 17:17
 */
@Data
@ApiModel(value = "MessageDto", description = "  \"content\": \"string\",\n" +
  "  \"title\": \"string\",\n" +
  "  \"toAll\": true,\n" +
  "  \"userIds\": [\n" +
  "    0\n" +
  "  ]\n" +
  "}  ")
public class MessageDto {


  /**
   * UID
   * 唯一约束
   */
  @ApiModelProperty("UUID")
  @JsonProperty("id")
  private String uuid;

  /**
   * 消息标题
   */
  @ApiModelProperty("消息标题")
  private String title;

  /**
   * 消息内容
   */
  private String content;

  /**
   * 创建时间
   */
  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private LocalDateTime createTime;

  /**
   * 更新时间
   */
  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private LocalDateTime updateTime;

  /**
   * 创建者ID
   */
  private Integer creatorId;

  /**
   * 创建者用户名
   */
  private String creatorName;

  /**
   * 状态 {@link MessageUserEntity.MessageUserState}
   */
  private Byte state;

  /**
   * 已读时间
   */
  private LocalDateTime confirmTime;


  /**
   *用户IDs
   */
  private List<Integer> userIds;


  /**
   * 是否发送给所有人
   */
  private Boolean toAll;
}
