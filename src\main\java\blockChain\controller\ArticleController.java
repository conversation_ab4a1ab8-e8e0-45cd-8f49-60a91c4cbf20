package blockChain.controller;

import blockChain.bean.BaseResponseDto;
import blockChain.bean.PageResponse;
import blockChain.dto.ArticleDto;
import blockChain.facade.service.ArticleServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2020/4/15 15:28
 */
@Api("行业消息")
@Slf4j
@RestController
@RequestMapping("article")
@AllArgsConstructor
public class ArticleController {

  private ArticleServiceFacade serviceFacade;


  //@PreAuthorize("hasAnyAuthority('"+ AuthoritiesConstants.MEMBER_CREATE+"')")
  @ApiOperation(value = "创建", nickname = "createArticle", notes = "Creates a new instance of a `Article`.", tags={ "行业消息(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("create")
  public ResponseEntity<BaseResponseDto> createArticle(@Valid @RequestBody ArticleDto dto) {
    serviceFacade.create(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  //@PreAuthorize("hasAnyAuthority('"+ AuthoritiesConstants.MEMBER_CREATE+"')")
  @ApiOperation(value = "删除", nickname = "deleteArticle", notes = "删除的时候只要传入UUID即可", tags={ "行业消息(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("remove")
  public ResponseEntity<BaseResponseDto> deleteArticle(@Valid @RequestBody ArticleDto dto) {
    serviceFacade.delete(dto.getUuid());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }


  @ApiOperation(value = "修改", nickname = "updateArticle", notes = " 使用UUID标识唯一消息", tags={ "行业消息(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 202, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("update")
  public ResponseEntity<BaseResponseDto> updateArticle(@Valid @RequestBody ArticleDto dto) {
    serviceFacade.update(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }


  @ApiOperation(value = "查询单个", nickname = "getOneArticle", notes = " 使用UUID标识唯一消息", tags={ "行业消息(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("get")
  public ResponseEntity<ArticleDto> getOneArticle(@Valid @RequestBody ArticleDto dto) {
    Assert.notNull(dto.getUuid(), "UUID不能为空");
    ArticleDto one = serviceFacade.getOne(dto.getUuid());
    return ResponseEntity.ok(one);
  }

  @ApiOperation(value = "query", nickname = "queryArticle", notes = "query", tags={ "行业消息(管理)", "行业消息(用户读取)" })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("query")
  public ResponseEntity<PageResponse<ArticleDto>> queryArticle(@Valid @RequestBody ArticleDto dto) {
    PageResponse<ArticleDto> page = serviceFacade.queryArticle(dto.getPageable(), dto.getTitle(), dto.getContent(), dto.getCreateTimeStart(), dto.getCreateTimeEnd());
    return ResponseEntity.ok(page);
  }

}
