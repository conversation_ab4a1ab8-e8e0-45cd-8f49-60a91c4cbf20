
package org.tempuri;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 *
 */
@WebServiceClient(name = "Population", targetNamespace = "http://tempuri.org/", wsdlLocation = "http://hjpt.fjzwt.cn:9000/CegnQuery/Population.asmx?wsdl")
public class Population
    extends Service
{

    private final static URL POPULATION_WSDL_LOCATION;
    private final static WebServiceException POPULATION_EXCEPTION;
    private final static QName POPULATION_QNAME = new QName("http://tempuri.org/", "Population");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://hjpt.fjzwt.cn:9000/CegnQuery/Population.asmx?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        POPULATION_WSDL_LOCATION = url;
        POPULATION_EXCEPTION = e;
    }

    public Population() {
        super(__getWsdlLocation(), POPULATION_QNAME);
    }

    public Population(WebServiceFeature... features) {
        super(__getWsdlLocation(), POPULATION_QNAME, features);
    }

    public Population(URL wsdlLocation) {
        super(wsdlLocation, POPULATION_QNAME);
    }

    public Population(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, POPULATION_QNAME, features);
    }

    public Population(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public Population(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     *
     * @return
     *     returns PopulationSoap
     */
    @WebEndpoint(name = "PopulationSoap")
    public PopulationSoap getPopulationSoap() {
        return super.getPort(new QName("http://tempuri.org/", "PopulationSoap"), PopulationSoap.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns PopulationSoap
     */
    @WebEndpoint(name = "PopulationSoap")
    public PopulationSoap getPopulationSoap(WebServiceFeature... features) {
        return super.getPort(new QName("http://tempuri.org/", "PopulationSoap"), PopulationSoap.class, features);
    }

    private static URL __getWsdlLocation() {
        if (POPULATION_EXCEPTION!= null) {
            throw POPULATION_EXCEPTION;
        }
        return POPULATION_WSDL_LOCATION;
    }

}
