package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;

/**
 * 变更项Bean
 */
@XmlRootElement
public class CopyrightChangeItemBean {
	//原数据
	private String original;
	//新数据
	private String newData;
	//变更项
	private String changeItem;
	public String getOriginal() {
		return original;
	}
	public void setOriginal(String original) {
		this.original = original;
	}
	public String getNewData() {
		return newData;
	}
	public void setNewData(String newData) {
		this.newData = newData;
	}
	public String getChangeItem() {
		return changeItem;
	}
	public void setChangeItem(String changeItem) {
		this.changeItem = changeItem;
	}
	public CopyrightChangeItemBean(String original, String newData,
			String changeItem) {
		this.original = original;
		this.newData = newData;
		this.changeItem = changeItem;
	}
	public CopyrightChangeItemBean() {
	}


}
