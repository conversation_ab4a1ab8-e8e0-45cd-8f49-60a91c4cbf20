package blockChain.dto.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/9 14:18
 */
@Getter
@Setter
@Accessors(chain = true)
public class AlertMessageDto {
  /**
   * UUID
   * 唯一约束
   */
  @JsonProperty("id")
  private String uuid;

  /**
   * 消息标题
   */
  private String title;

  /**
   * 消息内容
   */
  private String content;

  /**
   * 通知截至时间
   */
  private LocalDateTime deadline;

  /**
   * 状态
   */
  private StatusEnum status;

  private String creator;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;


  public enum StatusEnum {
    ENABLE("enable"),
    DISABLE("disable");

    private String value;

    StatusEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static StatusEnum fromValue(String text) {
      for (StatusEnum b : StatusEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + text + "'");
    }
  }
}
