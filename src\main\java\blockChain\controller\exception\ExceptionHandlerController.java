package blockChain.controller.exception;

import org.springframework.boot.autoconfigure.web.servlet.error.AbstractErrorController;
import org.springframework.boot.web.servlet.error.ErrorAttributes;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/7/5 16:07
 * 必须设置 server.error.path = /errorpage
 * BasicErrorController
 */
@RestController
public class ExceptionHandlerController extends AbstractErrorController {

  private static final String PATH = "/errorpage";

  public ExceptionHandlerController(ErrorAttributes errorAttributes) {
    super(errorAttributes);
  }

  /**
   * Returns the path of the error page.
   *
   * @return the error path
   */
  @Override
  public String getErrorPath() {
    return PATH;
  }

  @RequestMapping(value = PATH, produces = {MediaType.APPLICATION_JSON_VALUE})
  @ResponseBody
  public ResponseEntity<Map<String, Object>> error(HttpServletRequest request) {
    Map<String, Object> body = getErrorAttributes(request, false);
    HttpStatus status = getStatus(request);
    return new ResponseEntity<>(body, status);
  }

}
