package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;
import java.sql.Timestamp;

/**
 * 关于作品著作权作品信息的Bean
 */
@XmlRootElement
public class CopyrightManageBean {
	//作品名称 长度20
	private String productionName;
	//登记号 长度20
	private String registrationNum;
	//登记时间 例：2014-12-18
	private Date registrationDate;
	//作品类别  对应字典表 NCR_PRODUCTIONTYPE（ID）
	private Integer productionTypeId;
	//作品类别说明 长度40
	private String productionTypeDesc;
	//作品创作性质  NCP_OPUSINDITEKIND(ID)
	private Integer opusInditekind;
	//作品性质说明 长度40
	private String opuseDesc;
	//创作完成日期 例：2014-12-18
	private Timestamp finishTime;
	//创作完成地点（国家） 长度20
	private String finishCountry;
	//创作完成地点（城市） 长度20
	private String finishCity;
	//发表状态 0：已发表  1：未发表
	private Integer publishStatus;
	//首次发表日期 例：2014-12-18  (注：未发表状态无该属性)
	private Timestamp firstPublishTime;
	//首次发表地点（国家） 长度20   (注：未发表状态无该属性)
	private String firstCountry;
	//首次发表地点（城市） 长度20   (注：未发表状态无该属性)
	private String firstCity;
	//权利取得方式 对应字典表NCP_OBTAINMODE（ID）
	private Integer obtainMode;
	//权利取得方式说明 长度50 (注：原始状态无该属性)
	private String obtainModeDesc;
	//权利归属方式 NCP_RIGHTOWNMODE（ID）
	private Integer rightOwnMode;
	//权利归属方式说明 长度50
	private String rightOwnModeDesc;
	//权利拥有状况 对应字典表NCP_COPYRIGHT_RIGHTS（ID 注：type=0） 多权利时以","分割
	private String rightScope;
	//权利拥有状况说明  长度50
	private String rightScopeDesc;
	//电子介质  长度20
	private String emedium;
	//电子介质件数
	private String emediumCount;
	//纸介质  长度20
	private String pmedium;
	//纸介质件数
	private String pmediumCount;
	//办理方式  0：由著作权人申请  1：由代理人申请
	private Integer applyType;


	public String getProductionName() {
		return productionName;
	}
	public void setProductionName(String productionName) {
		this.productionName = productionName;
	}
	public String getRegistrationNum() {
		return registrationNum;
	}
	public void setRegistrationNum(String registrationNum) {
		this.registrationNum = registrationNum;
	}
	public Date getRegistrationDate() {
		return registrationDate;
	}
	public void setRegistrationDate(Date registrationDate) {
		this.registrationDate = registrationDate;
	}
	public Integer getProductionTypeId() {
		return productionTypeId;
	}
	public void setProductionTypeId(Integer productionTypeId) {
		this.productionTypeId = productionTypeId;
	}
	public String getProductionTypeDesc() {
		return productionTypeDesc;
	}
	public void setProductionTypeDesc(String productionTypeDesc) {
		this.productionTypeDesc = productionTypeDesc;
	}
	public Integer getOpusInditekind() {
		return opusInditekind;
	}
	public void setOpusInditekind(Integer opusInditekind) {
		this.opusInditekind = opusInditekind;
	}
	public String getOpuseDesc() {
		return opuseDesc;
	}
	public void setOpuseDesc(String opuseDesc) {
		this.opuseDesc = opuseDesc;
	}
	public Timestamp getFinishTime() {
		return finishTime;
	}
	public void setFinishTime(Timestamp finishTime) {
		this.finishTime = finishTime;
	}
	public String getFinishCountry() {
		return finishCountry;
	}
	public void setFinishCountry(String finishCountry) {
		this.finishCountry = finishCountry;
	}
	public String getFinishCity() {
		return finishCity;
	}
	public void setFinishCity(String finishCity) {
		this.finishCity = finishCity;
	}
	public Integer getPublishStatus() {
		return publishStatus;
	}
	public void setPublishStatus(Integer publishStatus) {
		this.publishStatus = publishStatus;
	}
	public Timestamp getFirstPublishTime() {
		return firstPublishTime;
	}
	public void setFirstPublishTime(Timestamp firstPublishTime) {
		this.firstPublishTime = firstPublishTime;
	}
	public String getFirstCountry() {
		return firstCountry;
	}
	public void setFirstCountry(String firstCountry) {
		this.firstCountry = firstCountry;
	}
	public String getFirstCity() {
		return firstCity;
	}
	public void setFirstCity(String firstCity) {
		this.firstCity = firstCity;
	}
	public Integer getObtainMode() {
		return obtainMode;
	}
	public void setObtainMode(Integer obtainMode) {
		this.obtainMode = obtainMode;
	}
	public String getObtainModeDesc() {
		return obtainModeDesc;
	}
	public void setObtainModeDesc(String obtainModeDesc) {
		this.obtainModeDesc = obtainModeDesc;
	}
	public Integer getRightOwnMode() {
		return rightOwnMode;
	}
	public void setRightOwnMode(Integer rightOwnMode) {
		this.rightOwnMode = rightOwnMode;
	}
	public String getRightOwnModeDesc() {
		return rightOwnModeDesc;
	}
	public void setRightOwnModeDesc(String rightOwnModeDesc) {
		this.rightOwnModeDesc = rightOwnModeDesc;
	}
	public String getRightScope() {
		return rightScope;
	}
	public void setRightScope(String rightScope) {
		this.rightScope = rightScope;
	}
	public String getRightScopeDesc() {
		return rightScopeDesc;
	}
	public void setRightScopeDesc(String rightScopeDesc) {
		this.rightScopeDesc = rightScopeDesc;
	}
	public String getEmedium() {
		return emedium;
	}
	public void setEmedium(String emedium) {
		this.emedium = emedium;
	}
	public String getEmediumCount() {
		return emediumCount;
	}
	public void setEmediumCount(String emediumCount) {
		this.emediumCount = emediumCount;
	}
	public String getPmedium() {
		return pmedium;
	}
	public void setPmedium(String pmedium) {
		this.pmedium = pmedium;
	}
	public String getPmediumCount() {
		return pmediumCount;
	}
	public void setPmediumCount(String pmediumCount) {
		this.pmediumCount = pmediumCount;
	}
	public Integer getApplyType() {
		return applyType;
	}
	public void setApplyType(Integer applyType) {
		this.applyType = applyType;
	}
	public CopyrightManageBean(String productionName, String registrationNum,
			Date registrationDate, Integer productionTypeId,
			String productionTypeDesc, Integer opusInditekind,
			String opuseDesc, Timestamp finishTime, String finishCountry,
			String finishCity, Integer publishStatus,
			Timestamp firstPublishTime, String firstCountry, String firstCity,
			Integer obtainMode, String obtainModeDesc, Integer rightOwnMode,
			String rightOwnModeDesc, String rightScope, String rightScopeDesc,
			String emedium, String emediumCount, String pmedium,
			String pmediumCount, Integer applyType) {
		this.productionName = productionName;
		this.registrationNum = registrationNum;
		this.registrationDate = registrationDate;
		this.productionTypeId = productionTypeId;
		this.productionTypeDesc = productionTypeDesc;
		this.opusInditekind = opusInditekind;
		this.opuseDesc = opuseDesc;
		this.finishTime = finishTime;
		this.finishCountry = finishCountry;
		this.finishCity = finishCity;
		this.publishStatus = publishStatus;
		this.firstPublishTime = firstPublishTime;
		this.firstCountry = firstCountry;
		this.firstCity = firstCity;
		this.obtainMode = obtainMode;
		this.obtainModeDesc = obtainModeDesc;
		this.rightOwnMode = rightOwnMode;
		this.rightOwnModeDesc = rightOwnModeDesc;
		this.rightScope = rightScope;
		this.rightScopeDesc = rightScopeDesc;
		this.emedium = emedium;
		this.emediumCount = emediumCount;
		this.pmedium = pmedium;
		this.pmediumCount = pmediumCount;
		this.applyType = applyType;
	}
	public CopyrightManageBean(){

	}


}
