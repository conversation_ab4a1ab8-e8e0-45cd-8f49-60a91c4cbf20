package blockChain.facade.service;

import blockChain.bean.Constant;
import blockChain.bean.PageQuery;
import blockChain.bean.PageResponse;
import blockChain.bean.ResultCode;
import blockChain.dto.RoleDto;
import blockChain.dto.role.RoleSave;
import blockChain.entities.MenuEntity;
import blockChain.entities.QRoleEntity;
import blockChain.entities.RoleEntity;
import blockChain.entities.UserEntity;
import blockChain.exception.BaseException;
import blockChain.exception.EntityNotFoundException;
import blockChain.mapper.RoleMapper;
import blockChain.service.MenuService;
import blockChain.service.RoleService;
import blockChain.service.UserService;
import com.querydsl.core.BooleanBuilder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static blockChain.entities.QUserEntity.userEntity;

/**
 * <AUTHOR>
 * @date 2019/12/20 11:09
 */
@Slf4j
@Service
@AllArgsConstructor
public class RoleServiceFacade {

    private final RoleService roleService;

    private final MenuService menuService;

    private final UserService userService;


    @Transactional(readOnly = true)
    public PageResponse<RoleDto> findPage(PageQuery<RoleDto> pageQuery) {
        RoleDto queryBody = pageQuery.getQueryBody();
        BooleanBuilder builder = new BooleanBuilder();

        QRoleEntity qRoleEntity = QRoleEntity.roleEntity;

        if (queryBody != null) {
            if (StringUtils.hasText(queryBody.getRoleName())) {
                builder.and(qRoleEntity.roleName.contains(queryBody.getRoleName()));
            }
        }

        Page<RoleEntity> page = roleService.findAll(builder, pageQuery.getPageRequest());
        return PageResponse.of(page, RoleMapper.INSTANCE.toRoleDtoList(page.getContent()));
    }

    @Transactional(readOnly = true)
    public RoleDto findRoleById(Long id) {
        RoleEntity roleEntity = roleService.findById(id).orElseThrow(() -> new EntityNotFoundException("该角色未找到"));
        return RoleMapper.INSTANCE.toRoleDto(roleEntity);
    }

    @Transactional
    public void create(RoleSave roleSave) {
        Assert.hasText(roleSave.getRoleName(), "角色名不能为空");
        Assert.notEmpty(roleSave.getAuthorities(), "菜单权限不能为空");
        Assert.isTrue(roleSave.getRoleName().length()<=255, "角色名长度不能超过255");
        Assert.isTrue((roleSave.getDescription()!=null && roleSave.getDescription().length()<=255), "描述长度不能超过255");

        if (roleService.existRoleName(roleSave.getRoleName()))
            throw new BaseException(ResultCode.RESULT_FAIL, "当前角色名已存在，请更换其他角色名");
        RoleEntity roleEntity = RoleMapper.INSTANCE.fromRoleSave(roleSave);
        roleEntity.setDeleted(Constant.INT_FALSE);
        roleService.save(roleEntity);
        relateRoleMenus(roleSave.getAuthorities(), roleEntity);
    }

    private void relateRoleMenus(Set<String> authorities, RoleEntity roleEntity) {
        if (!CollectionUtils.isEmpty(authorities)) {
            Set<MenuEntity> set = authorities.stream().map(authority -> menuService.findByAuthority(authority).orElseGet(() -> {
                MenuEntity menuEntity = new MenuEntity();
                menuEntity.setMenuUrl(authority);
                return menuService.save(menuEntity);
            })).collect(Collectors.toSet());

            Optional<MenuEntity> homeMenu = set.stream()
                    .filter(menu -> menu.getMenuUrl().equals(MenuEntity.DEFAULT_MENU))
                    .findFirst();
            if (!homeMenu.isPresent()) {
                set.add(menuService.findByAuthority(MenuEntity.DEFAULT_MENU).orElseGet(() -> {
                    MenuEntity menuEntity = new MenuEntity();
                    menuEntity.setMenuUrl(MenuEntity.DEFAULT_MENU);
                    return menuService.save(menuEntity);
                }));
            }

            // 已关联用户首页被取消，默认设置用户首页
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(userEntity.role.id.eq(roleEntity.getId()));
            builder.and(userEntity.homePageKey.notIn(authorities));
            List<UserEntity> userEntityList = userService.findAll(builder);
            for (UserEntity user: userEntityList) {
                user.setHomePageKey(MenuEntity.DEFAULT_MENU);
            }

            roleEntity.setMenus(set);
        }
    }

    @Transactional
    public void update(RoleSave roleSave) {
        Assert.hasText(roleSave.getRoleName(), "角色名不能为空");
        Assert.notEmpty(roleSave.getAuthorities(), "菜单权限不能为空");
        Assert.isTrue(roleSave.getRoleName().length()<=255, "角色名长度不能超过255");
        Assert.isTrue((roleSave.getDescription()!=null && roleSave.getDescription().length()<=255), "描述长度不能超过255");

        RoleEntity roleEntity = roleService.findById(roleSave.getId()).orElseThrow(() -> new EntityNotFoundException("该角色未找到"));
        RoleMapper.INSTANCE.updateRoleEntity(roleSave, roleEntity);
        relateRoleMenus(roleSave.getAuthorities(), roleEntity);
    }

    @Transactional
    public void remove(RoleEntity roleEntity) {
        roleEntity.setDeleted(Constant.INT_TRUE);
        roleEntity.setMenus(null);
    }
}
