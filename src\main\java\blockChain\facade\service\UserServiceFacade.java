package blockChain.facade.service;

import blockChain.bean.Constant;
import blockChain.bean.JwtResult;
import blockChain.bean.PageQuery;
import blockChain.bean.QueryParam;
import blockChain.config.SpringConfig;
import blockChain.controller.exception.VerificationFailedException;
import blockChain.dto.UserDto;
import blockChain.dto.ebus.CompanyInfo;
import blockChain.dto.ebus.CustomizeValues;
import blockChain.dto.ebus.UserInfo;
import blockChain.dto.ebus.UserInfoData;
import blockChain.dto.user.ChangePasswordParam;
import blockChain.dto.user.UserCreate;
import blockChain.dto.user.UserPageResponse;
import blockChain.dto.user.UserUpdate;
import blockChain.entities.*;
import blockChain.exception.EntityNotFoundException;
import blockChain.mapper.RoleMapper;
import blockChain.mapper.UserDtoMapper;
import blockChain.repository.CopyrightManagerPredicates;
import blockChain.service.*;
import blockChain.utils.HttpUtil;
import blockChain.utils.UserPasswordHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.ssdc.model.UserForm;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.security.UserLoginValidator;
import com.ctsi.ssdc.security.jwt.TokenProvider;
import com.querydsl.core.BooleanBuilder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.querydsl.QPageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@AllArgsConstructor
public class UserServiceFacade {

  private final UserService userService;

  private final RoleService roleService;

  private final TokenProvider tokenProvider;

  private final PasswordEncoder passwordEncoder;

    private final AuthenticationManager authenticationManager;

    private final DigitalService dictService;

    private final SpringConfig config;

    private final UploadAttachmentService uploadAttachmentService;

    private final CopyrightManagerService copyrightManagerService;
    private final OutSideServiceFacade outSideServiceFacade;
    private final AppTokenService appTokenService;


    @Transactional
    public UserDto findUserById(Integer id) {
        UserEntity user = userService.findById(id).orElseThrow(() -> new EntityNotFoundException("该用户未找到"));
        return UserDtoMapper.INSTANCE.entityToDto(user);
    }

    @Transactional
    public UserEntity findById(Integer id) {
    UserEntity user = userService.findById(id).orElseThrow(() -> new EntityNotFoundException("该用户未找到"));
    return user;
  }

  @Transactional
  public UserDto getByUserName(String username) {
    UserEntity user = userService.getByUserName(username).orElseThrow(() -> new EntityNotFoundException("该用户登录已过期或未进行实名认证，请重新登录或登录闽政通APP进行实名认证后再进行登录。"));
    return UserDtoMapper.INSTANCE.entityToDto(user);
  }

  /**
   * @param user
   * @param userLoginValidator 自定义验证器
   * @return JWT
   */
  @Transactional
  public JwtResult identityAuthorize(UserForm user, UserLoginValidator userLoginValidator) {
    // 自定义验证
    if (userLoginValidator != null) {
      userLoginValidator.validate(user);
    }

    // security 使用空格拼接姓名和Identity信息
    UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(user.getUsername(), user.getPassword());
    Authentication authentication = this.authenticationManager.authenticate(authenticationToken);
    CscpUserDetail principal = (CscpUserDetail) authentication.getPrincipal();
    SecurityContextHolder.getContext().setAuthentication(authentication);
    String jwt = tokenProvider.createToken(authentication, false);
    return new JwtResult(jwt, principal);
  }

  @Transactional
  public void createUser(UserCreate userCreate) {
    Digital city = dictService.getById(userCreate.getVisibleLevelCity()).orElse(null);
    Digital county = dictService.getById(userCreate.getVisibleLevelCounty()).orElse(null);
    Digital province = dictService.getById(userCreate.getVisibleLevelProvince()).orElse(null);

    UserEntity userEntity = UserDtoMapper.INSTANCE.fromUserCreate(userCreate)
      .setPassword(passwordEncoder.encode("abc123456@"))
      .setDeleted(false)
      .setVisibleLevelCity(city)
      .setVisibleLevelProvince(province)
      .setVisibleLevelCounty(county);
    if (userCreate.getRoleId() != null) {
      userEntity.setRole(roleService.findById(userCreate.getRoleId()).orElseThrow(RuntimeException::new));
    }

    userService.save(userEntity);
  }

  @Transactional
  public void updateUser(UserDto userDto) {
    UserEntity userEntity = userService.findById(userDto.getUserId()).orElseThrow(RuntimeException::new);

    Digital city = dictService.getById(userDto.getVisibleLevelCity()).orElse(null);
    Digital county = dictService.getById(userDto.getVisibleLevelCounty()).orElse(null);
    Digital province = dictService.getById(userDto.getVisibleLevelProvince()).orElse(null);

    UserDtoMapper.INSTANCE.updateUserEntity(userDto, userEntity);
    if (userDto.getRoleId() != null && userEntity.getRole().getId() != userDto.getRoleId()) {
      RoleEntity roleEntity = roleService.findById(userDto.getRoleId()).orElseThrow(() -> new EntityNotFoundException("该角色未找到"));
      userEntity.setRole(roleEntity);
    }
    userEntity.setVisibleLevelCity(city)
      .setVisibleLevelCounty(county)
      .setVisibleLevelProvince(province);

    userService.save(userEntity);
  }

    @Transactional
    public void updateRole(UserDto userDto) {
        UserEntity userEntity = userService.findById(userDto.getUserId()).orElseThrow(RuntimeException::new);

        UserDtoMapper.INSTANCE.updateUserEntity(userDto, userEntity);
        if (userDto.getRoleId() != null && userEntity.getRole().getId() != userDto.getRoleId()) {
            RoleEntity roleEntity = roleService.findById(userDto.getRoleId()).orElseThrow(() -> new EntityNotFoundException("该角色未找到"));

            // 工作站作品转移
            if (userEntity.getRole().getIsWorkstation() != null
                    && roleEntity.getIsWorkstation() != null
                    && userEntity.getRole().getIsWorkstation().equals(Constant.BYTE_TRUE)
                    && roleEntity.getIsWorkstation().equals(Constant.BYTE_TRUE)) {
                Iterable<CopyrightManager> copyrightManagers = copyrightManagerService.findAll(CopyrightManagerPredicates.byUserNameAndWorkstationId(userEntity.getUserName(), userEntity.getRole().getId()));
                copyrightManagers.forEach(copyrightManager -> {
                    copyrightManager.setWorkstationId(userDto.getRoleId());
                    copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
                });
            }
            userEntity.setRole(roleEntity);
            userEntity.setHomePageKey(MenuEntity.DEFAULT_MENU); // 20230608 角色变更，系统首页默认使用首页
        }

        userService.save(userEntity);
    }

  @Transactional
  public void removeBatchById(List<Integer> id) {
    userService.removeBatchById(id);
  }

  public UserPageResponse findPage(PageQuery<UserDto> pageQuery) {
    BooleanBuilder builder = new BooleanBuilder();
    QUserEntity qUserEntity = QUserEntity.userEntity;
    UserDto queryBody = pageQuery.getQueryBody();
    if (queryBody != null) {
      if (StringUtils.hasText(queryBody.getUserName())) {
        builder.and(qUserEntity.userName.contains(queryBody.getUserName()));
      }
      if (StringUtils.hasText(queryBody.getRealName())) {
        builder.and(qUserEntity.realName.contains(queryBody.getRealName()));
      }
      if (queryBody.getRoleId() != null) {
        builder.and(qUserEntity.role.id.eq(queryBody.getRoleId()));
      }
      if (queryBody.getUserStatus() != null) {
        builder.and(qUserEntity.userStatus.eq(queryBody.getUserStatus()));
      }
      if (queryBody.getUserIds() != null && queryBody.getUserIds().size() > 0) {
        builder.and(qUserEntity.userId.in(queryBody.getUserIds()));
      }
    }

    QPageRequest pageRequest = new QPageRequest(pageQuery.getPage() - 1, pageQuery.getPageSize(), qUserEntity.registerTime.desc(), qUserEntity.userId.desc());

    Page<UserEntity> page = userService.findPage(builder, pageRequest);
    UserPageResponse response = new UserPageResponse(pageQuery.getPage(), page.getSize(), page.getTotalElements(), UserDtoMapper.INSTANCE.toUserDtoList(page.getContent()));
    List<RoleEntity> roleList = roleService.findAll();
    response.setRoleList(RoleMapper.INSTANCE.toRoleDtoList(roleList));
    return response;
  }

  @Transactional
  public void updateBaseInfo(UserUpdate userUpdate) {
    int currentUserId = SecurityUtils.getCurrentUserId();
    UserEntity userEntity = userService.findById(currentUserId).orElseThrow(() -> new EntityNotFoundException("未找到该用户"));
    UserDtoMapper.INSTANCE.updateUserEntity(userUpdate, userEntity);
    userEntity.setPersonalArea(userEntity.getPersonalArea());
    userService.save(userEntity);
  }

  @Transactional
  public ResponseEntity<Map<String, Object>> saveBaseInfo(UserUpdate userUpdate) {
    Map<String, Object> result = new HashMap<>();
    UserEntity finduser = userService.getByUserName(userUpdate.getUserName()).orElse(null);
    if (finduser != null) {
      result.put("message", "注册失败,该用户名已被注册");
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
    }
    UserEntity userEntity = new UserEntity();
    UserDtoMapper.INSTANCE.updateUserEntity(userUpdate, userEntity);
    //设置加密密码
    userEntity.setPassword(passwordEncoder.encode(userUpdate.getPassword()));
    userService.save(userEntity);
    result.put("message", "注册成功");
    return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
  }

  @Transactional
  public void passwordUpdate(ChangePasswordParam changePasswordParam) {
    int currentUserId = SecurityUtils.getCurrentUserId();
    UserEntity userEntity = userService.findById(currentUserId).orElseThrow(() -> new EntityNotFoundException("未找到该用户"));

    //用户设置过密码后，若旧密码不匹配抛异常
    if (userEntity.getPassword() != null && !passwordEncoder.matches(changePasswordParam.getPasswordOld(), userEntity.getPassword())) {
      throw new VerificationFailedException("旧密码不正确");
    }
    String newPassword = changePasswordParam.getPassword().trim();
    if (UserPasswordHelper.isWeakPassword(newPassword)) {
      throw new VerificationFailedException("密码必须包含至少一个字母和数字，且长度在[8,20]个字之间");
    }
    userEntity.setPassword(passwordEncoder.encode(newPassword));
    userService.save(userEntity);
  }

  public boolean existsByRoleId(long roleId) {
    return userService.existsByRoleId(roleId);
  }

  @Transactional
  public ResponseEntity<Map<String, Object>> userValidate(@Valid @RequestBody QueryParam queryParam) {
    /*获取前端参数*/
    String username = queryParam.getString("username");//获取用户名
    /*验证用户名和验证码并返回提示信息*/
    Map<String, Object> result = new HashMap<>();
    //判断用户是否存在
    UserEntity userEntity = userService.getByUserName(username).orElseThrow(RuntimeException::new);
    if (userEntity == null) {
      result.put("message", "用户不存在");
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
    }
    UserDto user = UserDtoMapper.INSTANCE.entityToDto(userEntity);
    result.put("message", "验证成功！");
    return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
  }

  @Transactional
  public ResponseEntity<Map<String, Object>> codevalidate(@Valid @RequestBody QueryParam queryParam) {
    /*获取前端参数*/
    String username = queryParam.getString("username");//获取用户名
    String randkey = queryParam.getString("randkey");//获取用户名
    /*验证用户名和验证码并返回提示信息*/
    Map<String, Object> result = new HashMap<>();
    //判断用户是否存在
    UserEntity userEntity = userService.getByUserName(username).orElseThrow(RuntimeException::new);
    if (userEntity == null) {
      result.put("message", "用户不存在");
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
    }
    UserDto user = UserDtoMapper.INSTANCE.entityToDto(userEntity);
    //判断验证码
    if (!randkey.equals(user.getRandKey())) {
      result.put("message", "验证码不正确");
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
    }
    result.put("message", "验证成功！");
    return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
  }

  @Transactional
  public ResponseEntity<Map<String, Object>> changepassword(@Valid @RequestBody QueryParam queryParam) {
    /*获取前端参数*/
    String username = queryParam.getString("username");//获取用户名
    String newpwd = queryParam.getString("newpwd");//新密码
    /*验证用户名和验证码并返回提示信息*/
    Map<String, Object> result = new HashMap<>();
    //判断用户是否存在
    UserEntity userEntity = userService.getByUserName(username).orElseThrow(RuntimeException::new);
    if (userEntity == null) {
      result.put("message", "用户不存在");
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
    }
    UserDto user = UserDtoMapper.INSTANCE.entityToDto(userEntity);
    //判断验证码
    if (userEntity.getPassword().equals(passwordEncoder.encode(newpwd))) {
      result.put("message", "新密码不能与旧密码相同");
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
    }

    userEntity.setPassword(passwordEncoder.encode(newpwd));
    userEntity.setRandKey("");
    userService.save(userEntity);
    result.put("message", "修改密码成功！");
    return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
  }

  @Transactional
  public UserEntity updateProvinceUser(String trustticket) {
      log.info("updateProvinceUser trustticket" + trustticket);
      //从接口获取用户信息
      UserEntity currentUser = null;
      if (trustticket != null) {
          Map<String, Object> param = new HashMap<>();
          param.put("INVOKESERVICE_CODE", "103");
          param.put("INVOKECALLER_CODE", "2c9bbe5d71ff3606017201801f3f75d72c9bbe5d71ff3606");
          param.put("TRUST_TICKET", trustticket);
          String json = JSON.toJSONString(param);
          Map<String, Object> clientParam = new HashMap<String, Object>();
          clientParam.put("POSTPARAM_JSON", json);
          String url = "https://mztapp.fujian.gov.cn:8304/dataset/AppSerController/invokeservice.do";
          String result = HttpUtil.sendPostParams(url, clientParam, config.getProxyIp(), config.getProxyPort());
//      System.out.println(result);
          log.info("登录-闽政通返回：{}", result);
          JSONObject object = JSON.parseObject(result);
          if (object.containsKey("data") && object.get("data") != null && !object.getJSONObject("data").isEmpty()) {
              JSONObject data = object.getJSONObject("data");
//        logger.info(data.toJSONString());
              System.out.println("get currentUser byid:" + data.getString("USER_ID"));
              currentUser = userService.getByUserName(data.getString("USER_ID")).orElse(null);
              if (currentUser == null) {
                  if (StringUtils.isEmpty(data.getString("USER_ID"))
                          || StringUtils.isEmpty(data.getString("USER_NAME"))
                          || StringUtils.isEmpty(data.getString("USER_IDCARD"))
                          || StringUtils.isEmpty(data.getString("USER_MOBILE"))) {
                      return null;
                  }
                  currentUser = new UserEntity();
                  currentUser.setPassword(passwordEncoder.encode("abc123456@"));
                  RoleEntity roleEntity = roleService.getOne(Long.parseLong("4"));
                  currentUser.setRole(roleEntity);
                  currentUser.setHomePageKey("Home");
                  System.out.println("currentUser new one");
              }
              currentUser.setUuid(data.getString("USER_ID"));
              if (data.getString("USER_NAME") != null) {
                  currentUser.setRealName(data.getString("USER_NAME"));
              }
              currentUser.setCardId(data.getString("USER_IDCARD"));
              currentUser.setIdentityKind(data.getInteger("USER_TYPE"));
              currentUser.setPhoneNum(data.getString("USER_MOBILE"));
              currentUser.setEmail(data.getString("USER_EMAIL"));
              //currentUser.setRegisterTime(data.getString("USER_CREATETIME"));
              //currentUser.setPassword(data.getString("USER_PASS"));
              currentUser.setUserStatus(1);
              currentUser.setCardImgf(data.getString("USER_TOKEN"));
              currentUser.setUserName(data.getString("USER_ID"));
              currentUser.setDeleted(false);
              //获取用户籍贯
              if (data.getInteger("USER_TYPE") == 1) {
                  currentUser.setAddress(data.getString("POPULATION_HJDZ"));
                  currentUser.setSex(data.getInteger("POPULATION_SEX"));
                  // TODO 保存闽政通的CARD_TYPE
                  if (data.getInteger("CARD_TYPE") == 111) {
                      // 身份证
                      currentUser.setCardType(CopyrightOwner.CopyCertificateValue.IDCARD);
                  } else if (data.getInteger("CARD_TYPE") == 511) {
                      // 台湾居民来往大陆通行证
                      currentUser.setCardType(CopyrightOwner.CopyCertificateValue.TWTXZ);
                  } else if (data.getInteger("CARD_TYPE") == 516) {
                      // 港澳居民来往内地通行证
                      currentUser.setCardType(CopyrightOwner.CopyCertificateValue.GATTXZ);
                  } else if (data.getInteger("CARD_TYPE") == 414) {
                      // 普通护照
                      currentUser.setCardType(CopyrightOwner.CopyCertificateValue.PASSPORT);
                  } else {
                      // 其他有效身份证件
                      currentUser.setCardType(CopyrightOwner.CopyCertificateValue.OTHER);
                  }
                  // 个人 获取省市区地址(籍贯县区代码)
                  String areacode = data.getString("POPULATION_AREACODE");
                  String citycode = data.getString("POPULATION_CITYCODE");
                  Digital cityDict;
                  Digital areaDict;
                  if (!StringUtils.isEmpty(areacode) && !StringUtils.isEmpty(citycode)) {
                      try {
                          cityDict = dictService.getIdByArea(Long.valueOf(citycode), 2, 3, 0);
                          areaDict = dictService.getIdByArea(Long.valueOf(areacode), 3, 4, cityDict.getId());
                          if (areaDict != null) {
                              currentUser.setCountyName(String.valueOf(areaDict.getId()));
                              currentUser.setCityName(String.valueOf(cityDict.getId()));
                              currentUser.setProvinceName(String.valueOf(cityDict.getPid()));
                          }
                      } catch (Exception e) {
                          log.error("error", e);
                      }
                  }
              } else {
                  // "LEGALPERSON_IDCARD" -> "350182********5711" 法人身份证
                  // "LEGALPERSON_NAME" -> "陈*响" 法定代表人
                  String legalPersionIdcard = data.getString("LEGALPERSON_IDCARD");
                  String legalPersionName = data.getString("LEGALPERSON_NAME");
                  if (StringUtils.isEmpty(legalPersionIdcard) || StringUtils.isEmpty(legalPersionName)) {
                      // 经办人信息
                      currentUser.setJbrName(data.getString("LEGALPERSON_JBRNAME"));
                      currentUser.setJbrCardId(data.getString("LEGALPERSON_JBRIDCARD"));
                      currentUser.setJbrCardType(currentUser.getCardType());
                  } else {
                      // 法人信息
                      currentUser.setLegalPersionIdcard(legalPersionIdcard);
                      currentUser.setLegalPersionName(legalPersionName);
                  }
                  currentUser.setAddress(data.getString("LEGALPERSON_ADDRESS"));
                  currentUser.setSex(data.getInteger("LEGALPERSON_FRXB"));
                  currentUser.setLegalPersionType(data.getString("LEGALPERSON_TYPE"));
              }
              currentUser.setCountryName("158");

              String randKey = UUID.randomUUID().toString();// 产生随机激活码
              /*更新数据库激活码*/
              currentUser.setRandKey(randKey);//设置加密激活码
              currentUser.setRandKeyExpireTime(LocalDateTime.now().plusMinutes(config.getLoginCodeValidity()));
              userService.save(currentUser);
              System.out.println("currentUser save");
          } else {
              throw new IllegalArgumentException(object.getString("msg"));
          }
      }
//    System.out.println("currentUser:"+currentUser.getUserName());
      Map<String, Object> result = new HashMap<>();
      //UserEntity finduser = userService.getByUuid(userUpdate.getUuid()).orElse(null);
      //UserEntity userEntity = new UserEntity();
      //UserDtoMapper.INSTANCE.updateUserEntity(userUpdate, userEntity);
      //设置加密密码
      //
      //}
      result.put("message", "注册成功");
      return currentUser;
  }

  @Transactional
  public ResponseEntity<Map<String, Object>> updateHomePage(QueryParam param){
    Map<String,Object> result = new HashMap<>();
    Integer userId = param.getInteger("UID");
    String homePageKey = param.getString("homePageKey");
    userService.updateHomePage(userId,homePageKey);
    result.put("success","操作成功！");
    return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
  }

    public boolean isManager(Authentication authentication) {
        UserDto userDto = findUserById(((CscpUserDetail) authentication.getPrincipal()).getId());
        if (userDto==null)
            return false;
        if (userDto.getRoleId().equals(RoleEntity.ROLE_USER))
            return false;
        return true;
    }

    public boolean isManager() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null)
            return false;
        return isManager(authentication);
    }

    public boolean isWorkStation(Authentication authentication) {
      UserEntity userEntity = findById(((CscpUserDetail) authentication.getPrincipal()).getId());
      if (userEntity==null)
        return false;

      if (userEntity.getRole().getIsWorkstation()!=null && userEntity.getRole().getIsWorkstation() == Constant.BYTE_TRUE)
        return true;
      return false;
    }
    public boolean isWorkStation() {
      Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
      if (authentication == null)
        return false;
      return isWorkStation(authentication);
    }

    public boolean isMyFile(Long id) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loginUserName = ((CscpUserDetail) authentication.getPrincipal()).getUsername();
        String fileUserName = uploadAttachmentService.getUserNameByAttachmentId(id);
        if (fileUserName == null || StringUtils.isEmpty(fileUserName))
            return false;
        if (!fileUserName.equals(loginUserName))
            return false;
        return true;
    }

    public boolean isMyFile2(Long id) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loginUserName = ((CscpUserDetail) authentication.getPrincipal()).getUsername();
        UploadAttachment file = uploadAttachmentService.getById(id);
        if (file == null)
            throw new EntityNotFoundException("附件不存在");
        String fileUserName = file.getUserName();
        if (!StringUtils.isEmpty(fileUserName) && !fileUserName.equals(loginUserName))
            return false;
        return true;
    }

    public boolean isMyAttachment(Long id) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loginUserName = ((CscpUserDetail) authentication.getPrincipal()).getUsername();
        String fileUserName = uploadAttachmentService.getUserNameById(id);
        if (fileUserName == null || StringUtils.isEmpty(fileUserName))
            return false;
        if (!fileUserName.equals(loginUserName))
            return false;
        return true;
    }

    @Transactional
    public void save(UserEntity userEntity) {
        userService.save(userEntity);
    }

    public List<UserEntity> findByCardId(String cardid) {
        return userService.findByCardId(cardid);
    }

    @Transactional
    public List<UserEntity> findByRealName(String realName) {
        return userService.findByRealName(realName);
    }

    public UserEntity updateProvinceUser2(String code) {
        log.info("updateProvinceUser2 code：" + code);
        //从接口获取用户信息
        UserEntity currentUser = null;
        if (code != null) {
            String appToken;
            AppTokenEntity appTokenEntity = appTokenService.findLastToken();
            if (!ObjectUtils.isEmpty(appTokenEntity) && appTokenEntity.getExpirationTime().isAfter(LocalDateTime.now())) {
                appToken = appTokenEntity.getAppToken();
            } else {
                appToken = outSideServiceFacade.getAppToken();
            }
            if (null == appToken) return null;
            JSONObject data = outSideServiceFacade.getAccessToken(appToken, code, null, "authorization_code");
            String accessToken = data.getString("access_token");
            String refreshToken = data.getString("refresh_token");
            Integer expiresIn = data.getInteger("expires_in");
            if (null == accessToken) return null;
            UserInfoData userInfoData = outSideServiceFacade.getUserInfo(appToken, accessToken);
            if (!ObjectUtils.isEmpty(userInfoData)) {
                UserInfo userInfo = userInfoData.getUserInfo();
                CustomizeValues customizeValues = userInfoData.getCustomizeValues();
                CompanyInfo companyInfo = userInfoData.getCompanyInfo();

                currentUser = userService.getByUserName(userInfo.getId()).orElse(null);
                if (currentUser == null && !Objects.equals(userInfo.getId(), userInfo.getFjUserId())) {
                    currentUser = userService.getByUserName(userInfo.getFjUserId()).orElse(null);
                }
                if (currentUser == null) {
                    // TODO companyInfo.getName() companyInfo.getSocialCreditCode()
                    if (StringUtils.isEmpty(userInfo.getId())
                            || StringUtils.isEmpty(userInfo.getName())
                            || StringUtils.isEmpty(userInfo.getCard())
                            || StringUtils.isEmpty(userInfo.getPhone())) {
                        return null;
                    }
                    currentUser = new UserEntity();
                    currentUser.setPassword(passwordEncoder.encode("abc123456@"));
                    RoleEntity roleEntity = roleService.getOne(Long.parseLong("4"));
                    currentUser.setRole(roleEntity);
                    currentUser.setHomePageKey("Home");
                    currentUser.setUserName(userInfo.getId());
                }
                currentUser.setUuid(userInfo.getId());
                currentUser.setIdentityKind(ObjectUtils.isEmpty(companyInfo) ? 1 : 2);
                currentUser.setPhoneNum(userInfo.getPhone());
                currentUser.setEmail(userInfo.getEmail());
                currentUser.setUserStatus(1);
                currentUser.setOauthAccessToken(accessToken);
                currentUser.setOauthRefreshToken(refreshToken);
                currentUser.setOauthAccessTokenExpireTime(LocalDateTime.now().plusMinutes(expiresIn - 10)); // 10分钟提前过期
                currentUser.setDeleted(false);
                currentUser.setSex(userInfo.getSex());
                switch (userInfo.getCardType()) {
                    case 111:
                        // 身份证
                        currentUser.setCardType(CopyrightOwner.CopyCertificateValue.IDCARD);
                        break;
                    case 511:
                        // 台湾居民来往大陆通行证
                        currentUser.setCardType(CopyrightOwner.CopyCertificateValue.TWTXZ);
                        break;
                    case 516:
                        // 港澳居民来往内地通行证
                        currentUser.setCardType(CopyrightOwner.CopyCertificateValue.GATTXZ);
                        break;
                    case 414:
                        // 普通护照
                        currentUser.setCardType(CopyrightOwner.CopyCertificateValue.PASSPORT);
                        break;
                    default:
                        // 其他有效身份证件
                        currentUser.setCardType(CopyrightOwner.CopyCertificateValue.OTHER);
                }
                //获取用户籍贯
                if (currentUser.getIdentityKind() == 1) {
                    if (StringUtils.hasText(userInfo.getName())) {
                        currentUser.setRealName(userInfo.getName());
                    }
                    currentUser.setCardId(userInfo.getCard());
                    String areacode;
                    String citycode;
                    if (Objects.equals(customizeValues.getPopulationcprocode(), "350000") || Objects.equals(customizeValues.getPopulationProcode(), "350000")) {
                        currentUser.setAddress(customizeValues.getPopulationcaddress());
                        // 个人 获取省市区地址(籍贯县区代码)
                        areacode = customizeValues.getPopulationAreacode();
                        citycode = customizeValues.getPopulationCitycode();
                    } else {
                        // TODO 外省取常驻地址  这个是手动编辑的吗，待确认 文档中的常驻和常住有什么不同
                        currentUser.setAddress(customizeValues.getCzdAddress());
                        areacode = customizeValues.getCzdAreacode();
                        citycode = customizeValues.getCzdCitycode();
                    }
                    Digital cityDict;
                    Digital areaDict;
                    if (!StringUtils.isEmpty(areacode) && !StringUtils.isEmpty(citycode)) {
                        try {
                            cityDict = dictService.getIdByArea(Long.valueOf(citycode), 2, 3, 0);
                            areaDict = dictService.getIdByArea(Long.valueOf(areacode), 3, 4, cityDict.getId());
                            if (areaDict != null) {
                                currentUser.setCountyName(String.valueOf(areaDict.getId()));
                                currentUser.setCityName(String.valueOf(cityDict.getId()));
                                currentUser.setProvinceName(String.valueOf(cityDict.getPid()));
                            }
                        } catch (Exception e) {
                            log.error("error", e);
                        }
                    }
                } else {
                    currentUser.setJbrName(userInfo.getName());
                    currentUser.setJbrCardId(userInfo.getCard());
                    currentUser.setJbrCardType(currentUser.getCardType());
                    currentUser.setCardType(null);

                    // companyInfo
                    if (companyInfo.getName() != null) {
                        currentUser.setRealName(companyInfo.getName());
                    }
                    currentUser.setCardId(companyInfo.getSocialCreditCode());
                    currentUser.setAddress(companyInfo.getAddress());
                    currentUser.setLegalPersionType(companyInfo.getLegalType());
                }
                currentUser.setCountryName("158");// TODO

                String randKey = UUID.randomUUID().toString();// 产生随机激活码
                /*更新数据库激活码*/
                currentUser.setRandKey(randKey);//设置加密激活码
                currentUser.setRandKeyExpireTime(LocalDateTime.now().plusMinutes(config.getLoginCodeValidity()));
                userService.save(currentUser);
            }
        }
        return currentUser;
    }
}
