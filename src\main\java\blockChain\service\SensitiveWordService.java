package blockChain.service;

import blockChain.entities.SensitiveWordEntity;
import blockChain.repository.SensitiveWordRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 敏感词库(tb_sensitive_word)服务接口
 *
 * <AUTHOR>
 * @since 2024-01-02 10:46:42
 */

@Service
@AllArgsConstructor
public class SensitiveWordService implements BaseService<SensitiveWordRepository, SensitiveWordEntity, Integer> {
    private final SensitiveWordRepository repository;

    @Override
    public SensitiveWordRepository getRepository() {
        return repository;
    }

    public SensitiveWordEntity findBySensitiveWordAndIsDelete(String sensitiveword, boolean isDelete) {
        return repository.findBySensitiveWordAndIsDelete(sensitiveword, isDelete);
    }

    public Page<SensitiveWordEntity> findByIsDeleteOrderByIdDesc(boolean isDelete, Pageable pageable) {
        return repository.findByIsDeleteOrderByIdDesc(isDelete, pageable);
    }

    public List<SensitiveWordEntity> findAllByIsDelete(boolean isDelete) {
        return repository.findAllByIsDelete(isDelete);
    }
}
