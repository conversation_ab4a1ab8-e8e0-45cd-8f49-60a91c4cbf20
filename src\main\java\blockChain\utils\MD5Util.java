package blockChain.utils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;


/**
 * MD5实用类
 *
 * <AUTHOR>
 *
 */
public class MD5Util {

	public static final String CHARSET_UTF8 = "UTF-8";

	protected static MessageDigest messagedigest = null;
	static {
		try {
			messagedigest = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException nsaex) {
			System.err.println(MD5Util.class.getName()
				+ "初始化失败，MessageDigest不支持MD5Util。");
			nsaex.printStackTrace();
		}
	}

	/**
	 * 生成字符串的md5校验值
	 *
	 * @params
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static byte[] getMD5Encode(String str) throws UnsupportedEncodingException {
		return getMD5Encode(str.getBytes(CHARSET_UTF8));
	}

	/**
	 * 生成字符串的md5校验值
	 *
	 * @param bytes
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public synchronized static byte[] getMD5Encode(byte[] bytes) throws UnsupportedEncodingException {
		messagedigest.update(bytes);
		bytes = messagedigest.digest();

		StringBuilder ret = new StringBuilder(bytes.length << 1);
		for (int i = 0; i < bytes.length; i++) {
			ret.append(Character.forDigit((bytes[i] >> 4) & 0xf, 16));
			ret.append(Character.forDigit(bytes[i] & 0xf, 16));
		}
		String str = ret.toString();
		return str.getBytes(CHARSET_UTF8);
	}

	/**
	 * 生成字符串的md5 字符串
	 *
	 * @param bytes
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public synchronized static String getMD5EncodeStr(byte[] bytes) throws UnsupportedEncodingException {
		messagedigest.update(bytes);
		bytes = messagedigest.digest();

		StringBuilder ret = new StringBuilder(bytes.length << 1);
		for (int i = 0; i < bytes.length; i++) {
			ret.append(Character.forDigit((bytes[i] >> 4) & 0xf, 16));
			ret.append(Character.forDigit(bytes[i] & 0xf, 16));
		}
		String str = ret.toString();
		return str;
	}

  /**
   *
   * @Title: md5
   * @Description: 根据byte数组计算MD5值
   * <AUTHOR>
   * @param  data 待计算数据
   * @return byte[] 计算后的值
   * @throws
   */
  private static byte[] md5(byte[] data) {
    try {
      MessageDigest md = MessageDigest.getInstance("md5");
      md.update(data);
      return md.digest();
    } catch (NoSuchAlgorithmException e) {
      e.printStackTrace();
    }
    return new byte[] {};
  }

  /**
   *
   * @Title: md5
   * @Description: 根据字符串计算MD5值
   * <AUTHOR>
   * @param  data待计算数据
   * @return String计算后数据
   * @throws
   */
  public static String md5(String data) {
    try {
      byte[] md5 = md5(data.getBytes("utf-8"));
      return toHexString(md5);
    } catch (UnsupportedEncodingException e) {
      e.printStackTrace();
    }
    return "";
  }

  /**
   *
   * @Title: toHexString
   * @Description: 将byte数组型MD5值转为16进制数字字符串
   * <AUTHOR>
   * @param  md5 byte数组型MD5值
   * @return String 转换后字符串
   * @throws
   */
  private static String toHexString(byte[] md5) {
    StringBuilder buf = new StringBuilder();
    for (byte b : md5) {
      buf.append(leftPad(Integer.toHexString(b & 0xff), '0', 2));
    }
    return buf.toString();
  }

  /**
   *
   * @Title: leftPad
   * @Description: 格式化字符串
   * <AUTHOR>
   * @param  hex 16进制MD5值
   * @param  c 占位符
   * @param  size 长度
   * @return String 格式化后结果
   * @throws
   */
  private static String leftPad(String hex, char c, int size) {
    char[] cs = new char[size];
    Arrays.fill(cs, c);
    System.arraycopy(hex.toCharArray(), 0, cs, cs.length - hex.length(),
      hex.length());
    return new String(cs);
  }


  public static void main(String[] args) {
    System.out.println(md5("123456"));
    // e10adc3949ba59abbe56e057f20f883e -> 123456
  }
}
