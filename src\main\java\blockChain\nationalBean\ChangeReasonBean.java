package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;
import java.sql.Timestamp;

/**
 * 变更理由Bean
 */
@XmlRootElement
public class ChangeReasonBean {

	// 变更类型（1=变更，2=补充）
	private int changeType;
	//变更理由
	private String reason;
	// 申请人类型（0=作品登记人办理，1=代理人办理）
	private int type;
	// 变更日期
	private Timestamp changeDate;
	public int getChangeType() {
		return changeType;
	}
	public void setChangeType(int changeType) {
		this.changeType = changeType;
	}
	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	/**
	 * @return the changeDate
	 */
	public Timestamp getChangeDate() {
		return changeDate;
	}
	/**
	 * @param changeDate the changeDate to set
	 */
	public void setChangeDate(Timestamp changeDate) {
		this.changeDate = changeDate;
	}
	public ChangeReasonBean(int changeType, String reason, int type, Timestamp changeDate) {
		this.changeType = changeType;
		this.reason = reason;
		this.type = type;
		this.changeDate = changeDate;
	}
	public ChangeReasonBean() {}
}
