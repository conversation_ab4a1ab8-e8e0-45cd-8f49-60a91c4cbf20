package blockChain.service;

import blockChain.entities.ProcessProgressEntity;
import blockChain.repository.BaseRepository;
import blockChain.repository.ProcessProgressRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/18 17:46
 */
@Service
@AllArgsConstructor
public class ProcessProgressService implements BaseService{

  private final ProcessProgressRepository repository;


  public ProcessProgressEntity findByProcessId(String processId){
    return repository.findByProcessId(processId);
  }

  public ProcessProgressEntity findByRegistrationNumAndStateAndType(Long registrationNum,int state,int type){
    return repository.findByRegistrationNumAndStateAndType(registrationNum,state,type);
  }

  public void saveData(ProcessProgressEntity entity){
    repository.save(entity);
  }


  @Transactional
  public long updateData(String processId, String message, Integer status) {
      return repository.updateData(processId, message, status);
  }

  @Override
  public BaseRepository getRepository() {
    return null;
  }

  public Integer findTypeCountByRegistrationNum(Long registrationNum,Integer type,Integer state) {
    return repository.countByRegistrationNumAndTypeAndState(registrationNum,type,state);
  }

  public List<ProcessProgressEntity> findByRegistrationNumAndTypeAndState(Long registrationNum,Integer type,Integer state) {
    return repository.findByRegistrationNumAndTypeAndState(registrationNum,type,state);
  }

  public List<ProcessProgressEntity> findBySnCodeAndTypeAndState(String snCode,Integer type,Integer state) {
    if(!StringUtils.isEmpty(snCode)){
      return repository.findBySnCodeAndTypeAndState(snCode,type,state);
    }else {
      return new ArrayList<>();
    }
  }

  public List<ProcessProgressEntity> findBySnCode(String snCode){
    return repository.findBySnCode(snCode);
  }

  /**
   * 获取当天最后一个推送编号
   *
   * @param today
   * @return
   */
  public Integer getLastSncode(String today) {
    String LastSncode = repository.getLastSncode("fjszK1003830" + today + "%");
    if (!StringUtils.isEmpty(LastSncode)) {
      return Integer.valueOf(LastSncode);
    }
    return 0;
  }
}
