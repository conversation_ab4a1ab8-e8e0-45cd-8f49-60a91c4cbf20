package blockChain.service;

import blockChain.entities.WorksReport;
import blockChain.repository.WorksReportPredicates;
import blockChain.repository.WorksReportRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/11/22 16:53
 */
@Service
@AllArgsConstructor
public class WorksReportService {
    @Autowired
    private WorksReportRepository repository;

    @Transactional(rollbackFor = RuntimeException.class)
    public void saveOrUpdateWorksReport(WorksReport worksReport){ repository.save(worksReport); }


    public Page<WorksReport> query(String worksNum, String productionName,
                                   String productionTypeId, String startDate, String endDate,
                                   String certificateStartDate, String certificateEndDate, List<Integer> status, String userName, Pageable of){

        Page<WorksReport> page = repository.findAll(WorksReportPredicates.digitalQuery(
                worksNum,productionName,productionTypeId,startDate,endDate,certificateStartDate,certificateEndDate,status,userName),of);
        return page;
    }

    public WorksReport getById(Long id){
        return repository.getById(id);
    }

    public void remove(WorksReport entity){ repository.delete(entity);}


}
