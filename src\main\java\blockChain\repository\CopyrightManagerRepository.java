package blockChain.repository;

import blockChain.entities.CopyrightManager;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/22 16:49
 */

@Repository
public interface CopyrightManagerRepository extends JpaRepository<CopyrightManager, Integer>,QuerydslPredicateExecutor<CopyrightManager>, CopyrightManagerRepositoryCustomized {
    public CopyrightManager getByRegistrationNum(Long registrationNum);

    public CopyrightManager findFirstByWorksNumIsNotNullOrderByRegistrationNumDesc();

    public CopyrightManager findFirstByWorksNumContainsOrderByRegistrationDateDesc(String worksNum);

    public CopyrightManager findFirstByUserNameAndCountDownNotOrderByCountDownAsc(String userName, Integer countDown);

    public List<CopyrightManager> findByRegistrationNumIn(List<Long> registrationNums);

    @Query(value = "SELECT count(1) FROM tb_copyrightmanager WHERE LEFT(pch,?1) between ?2 and ?3 ", nativeQuery = true)
    long countHJreport(int len, String start, String end);

    // 旧版 fj00015  7
    @Query(value = "SELECT count(1) FROM tb_copyrightmanager WHERE snStatus = ?4 and sncode like 'fj00015%' and SUBSTR(snCode FROM 8 FOR ?1) between ?2 and ?3 ", nativeQuery = true)
    long countSWBreport(int len, String start, String end, int snStatus);

    // 新版 fjszK1003830 12
    @Query(value = "SELECT count(1) FROM tb_copyrightmanager WHERE snStatus = ?4 and sncode like 'fjszK1003830%' and SUBSTR(snCode FROM 13 FOR ?1) between ?2 and ?3 ", nativeQuery = true)
    long countSWBreportNew(int len, String start, String end, int snStatus);

    @Query(value = "SELECT RIGHT(worksNum,8) FROM tb_copyrightmanager WHERE worksNum like ?1 ORDER BY RIGHT(worksNum,8) desc limit 1 ", nativeQuery = true)
    String getLastNum(String worksNum);

    @Query(value = "SELECT RIGHT(snCode,6) FROM tb_copyrightmanager WHERE snCode like ?1 ORDER BY RIGHT(snCode,6) desc limit 1 ", nativeQuery = true)
    String getLastSncode(String snCode);
}
