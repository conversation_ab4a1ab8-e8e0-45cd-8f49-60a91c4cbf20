package blockChain.dto.message;

import blockChain.bean.BaseQueryParam;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/9 15:46
 */
@Getter
@Setter
@Accessors(chain = true)
public class QAMessageDtoQueryParam extends BaseQueryParam {

  @ApiModelProperty("UUID")
  @JsonProperty("id")
  private String uuid;

  @ApiModelProperty("触发关键字")
  private String key;

  @ApiModelProperty("回复答案")
  private String answer;

  private LocalDateTime createTime;

  private LocalDateTime createTimeStart;
  private LocalDateTime createTimeEnd;
}
