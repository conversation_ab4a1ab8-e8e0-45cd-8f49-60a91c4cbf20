package blockChain.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.io.StringWriter;
import java.net.MalformedURLException;
import java.net.URL;
import java.rmi.RemoteException;

import javax.xml.namespace.QName;
import javax.xml.rpc.ServiceException;
import javax.xml.stream.XMLEventReader;
import javax.xml.stream.XMLEventWriter;
import javax.xml.stream.XMLOutputFactory;

import de.odysseus.staxon.json.JsonXMLConfig;
import de.odysseus.staxon.json.JsonXMLConfigBuilder;
import de.odysseus.staxon.json.JsonXMLInputFactory;
import de.odysseus.staxon.xml.util.PrettyXMLEventWriter;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;


/**
 * WebServices辅助相关通用类
 *
 * <AUTHOR>
 * @date 2017-4-24
 */
public class WebServicesUtils {

	/**
	 * 远程调用Webservices,返回接口服务信息
	 *
	 * @param serviceUrl
	 * @param serviceMethod
	 * @return
	 * @throws ServiceException
	 * @throws MalformedURLException
	 * @throws RemoteException
	 */
	public static String invoke(String serviceUrl, String serviceMethod, Object[] params) throws ServiceException, MalformedURLException, RemoteException {
		Service service = new Service();
		Call call = null;
		String xml = null;
		call = (Call)service.createCall();
		call.setTargetEndpointAddress(new URL(serviceUrl));
		call.setOperationName(new QName(serviceUrl, serviceMethod));
		xml = (String)call.invoke(params);
		return xml;
	}

	/**
	 * java通过axis访问.net webService
	 *
	 * @param url
	 *            服务地址
	 * @param method
	 *            服务方法
	 * @param returnType
	 *            返回类型
	 * @param paramTypes
	 *            方法参数类型数组
	 * @param params
	 *            方法参数名称数组
	 * @param paramValues
	 *            方法参数值数组
	 * @return
	 * @throws Exception
	 */
	public static Object axisInvoke(String namespace, String url, String method, QName returnType, QName[] paramTypes, String[] params, Object[] paramValues) throws Exception {

		String methodName = method;
		String soapActionURI = namespace + method;
		Service service = new Service();

		Call call = (Call)service.createCall();

		call.setTargetEndpointAddress(new java.net.URL(url));
		call.setUseSOAPAction(true);
		// 这个地方没设对就会出现Server was unable to read request的错误
		call.setSOAPActionURI(soapActionURI);
		// 设置要调用的.net webService方法
		call.setOperationName(new QName(namespace, methodName));
		// 设置该方法的参数，username为.net webService中的参数名称
		for(int i = 0; i < params.length; i++) {
			call.addParameter(new QName(namespace, params[i]), paramTypes[i], javax.xml.rpc.ParameterMode.IN);
		}

		// 设置该方法的返回值
		call.setReturnType(returnType);

		// 返回调用值
		return call.invoke(paramValues);
	}
	/**
	 * 读取XML文件
	 *
	 * @param filename
	 *            文件路径
	 * @return	result 解析后的XML
	 * @throws IOException
	 */
	public static String getXmlByFile(String filename){
		String result = "";
		File file = new File(filename);
		InputStreamReader read=null;
		String lineTxt=null;
		try{
			read = new InputStreamReader(new FileInputStream(file),"UTF-8");//考虑到编码格式
			BufferedReader bufferedReader = new BufferedReader(read);
			while((lineTxt = bufferedReader.readLine()) != null){
				result+=lineTxt;
			}
			read.close();
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			if(read!=null){
				try {
					read.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}

		return result;
	}

	/**
	 * JSON转XML
	 * @param json
	 * @return
	 */
	public static String jsonToXML(String json){
        StringReader input = new StringReader(json);
        StringWriter output = new StringWriter();
        JsonXMLConfig config = new JsonXMLConfigBuilder().multiplePI(false).repairingNamespaces(false).build();
        try {
            XMLEventReader reader = new JsonXMLInputFactory(config).createXMLEventReader(input);
            XMLEventWriter writer = XMLOutputFactory.newInstance().createXMLEventWriter(output);
            writer = new PrettyXMLEventWriter(writer);
            writer.add(reader);
            reader.close();
            writer.close();
        } catch( Exception e){
            e.printStackTrace();
        } finally {
            try {
                output.close();
                input.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
//        if(output.toString().length()>=38){//remove <?xml version="1.0" encoding="UTF-8"?>
//            return output.toString().substring(39);
//        }
        return output.toString();
    }
}
