package blockChain.entities.message;

import blockChain.entities.UserEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2019/12/18 17:31
 */
@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tb_message_user")
@EntityListeners(AuditingEntityListener.class)
@Accessors(chain = true)
public class MessageUserEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  /**
   * UID
   * 唯一约束
   */
  @Column(unique = true, length = 36)
  @NonNull
  private String uuid;

  /**
   * 状态 {@link MessageUserState}
   */
  private Byte state;

  /**
   * 已读时间
   */
  private LocalDateTime confirmTime;

  /**
   * 创建时间
   */
  @CreatedDate
  private LocalDateTime createTime;

  /**
   * 接收消息的用户
   */
  @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.DETACH})
  private UserEntity user;

  /**
   * 消息
   */
  @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.DETACH})
  @JoinColumn(name="message_id")
  private MessageEntity message;


  public interface MessageUserState {

    /**
     * 未读
     */
    byte UNREAD = 1;

    /**
     * 已读
     */
    byte READ = 2;
  }
}
