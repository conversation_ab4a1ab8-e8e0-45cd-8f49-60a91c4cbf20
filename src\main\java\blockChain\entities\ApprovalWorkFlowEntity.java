package blockChain.entities;

import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/8
 */
@Data
@Entity
@Table(name = "tb_approval_work_flow")
@EntityListeners(AuditingEntityListener.class)
public class ApprovalWorkFlowEntity implements Serializable {
    /**
     * 采一次审批待推送表ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @JoinColumn(name = "copyright_id")
    @OneToOne
    private CopyrightManager manager;

    /**
     * 作品状态 1 初审 4复审 7终审
     */
    private Integer state;

    /**
     * 审核结果 pass reject
     */
    private String approvalResult;

    /**
     * 执行状态 0 未执行； 1 已执行
     */
    private Byte resolveState;

    /**
     * 审核人
     */
    @JoinColumn(name = "createBy")
    @ManyToOne
    private UserEntity creator;

    // 创建时间
    @CreatedDate
    private LocalDateTime createTime;

    public interface ResolveState {
        byte TODO = 0;
        byte END = 1;
    }
}
