package blockChain.controller.statistic;

import blockChain.bean.PageResponse;
import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.dto.query.PushQueryParam;
import blockChain.facade.service.CopyrightManagerServiceFacade;
import blockChain.facade.service.CopyrightOwnerStatisticServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/8 17:39
 */
@Api("作品相关统计")
@Slf4j
@RestController
@RequestMapping("statistic/copyright_manager")
@AllArgsConstructor
public class ManagerStatistic {

  private CopyrightOwnerStatisticServiceFacade serviceFacade;
  private CopyrightManagerServiceFacade managerServiceFacade;

  @ApiOperation(value = "作品地域占比", notes = "按地域（市级）分类筛选，非福建省用户归为其它类别，只受时间影响")
  @PostMapping("geography_of_users/get")
  public ResponseEntity<PageResponse<StatisticDto>> getGeographicDistributionOfManager (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    PageResponse<StatisticDto> dto = serviceFacade.getGeographicDistributionOfManager(queryParam);
    return ResponseEntity.ok(dto);
  }

  @ApiOperation(value = "各地域作品数量统计", notes = "按地域（市级）分类筛选，非福建省用户归为其它类别，受时间和作品类别影响")
  @PostMapping("geography_of_total/get")
  public ResponseEntity<Map<String,Object>> getGeographicDistributionOfStaticstic (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    Map<String,Object> dto = serviceFacade.getGeographicDistributionOfStatistc(queryParam);
    return ResponseEntity.ok(dto);
  }

  @ApiOperation(value = "各地域作品数量统计（一级时间）", notes = "按地域（市级）分类筛选，非福建省用户归为其它类别，受时间和作品类别影响")
  @PostMapping("geography_of_time/get")
  public ResponseEntity<Map<String,Object>> getGeographicDistributionOfTime (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    Map<String,Object> dto = serviceFacade.getGeographicDistributionOfTime(queryParam);
    return ResponseEntity.ok(dto);
  }

  @ApiOperation(value = "作品数量统计（一级时间）", notes = "受时间影响")
  @PostMapping("statistic_of_time/get")
  public ResponseEntity<Map<String,Object>> getStatisticDistributionOfTime (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    Map<String,Object> dto = serviceFacade.getStatisticDistributionOfTime(queryParam);
    return ResponseEntity.ok(dto);
  }

  @ApiOperation(value = "各地域作品总数统计", notes = "按地域（市级）分类筛选，非福建省用户归为其它类别，受时间和作品类别影响")
  @PostMapping("geography_of_count/get")
  public ResponseEntity<Map<String,Object>> getGeographicDistributionOfCreateTotal (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
    Map<String,Object> dto = serviceFacade.getGeographicDistributionOfTotal(queryParam);
    return ResponseEntity.ok(dto);
  }

  @ApiOperation(value = "省局推送数据统计", notes = "省局推送数据统计")
  @PostMapping("pushDataCount")
  public ResponseEntity<Map<String,Object>> pushDataCount (@Valid @RequestBody PushQueryParam param){
    Map<String,Object> result = managerServiceFacade.pushDataCount(param);
    return ResponseEntity.ok(result);
  }

  @ApiOperation(value = "工作站数据统计", notes = "用户审核中作品数统计")
  @PostMapping("workStationCount")
  public ResponseEntity<Map<String,Object>> workStationCount (@Valid @RequestBody PushQueryParam param){
    Map<String,Object> result = managerServiceFacade.workStationCount(param);
    return ResponseEntity.ok(result);
  }
}
