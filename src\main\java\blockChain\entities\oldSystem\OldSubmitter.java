package blockChain.entities.oldSystem;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="old_submitter")
public class OldSubmitter {
  // 旧系统数据迁移用实体类，迁移后删除
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer submitter_id;// 提交者ID
  private String submitter_name;// 提交者姓名
  private String submitter_contactPerson;// 联系人
  private String submitter_telephone;// 电话号码
  private String submitter_mobile;// 电话
  private String submitter_address;// 详细住址
  private String submitter_zipCode;// 邮编
  private String submitter_email;// E-mail
  private String submitter_fax;// 传真

  private Long copyrightmanagerId;//在线填报表的ID

}
