package blockChain.facade.service;

import blockChain.bean.PageResponse;
import blockChain.config.ApplicationRuntimeProperties;
import blockChain.config.SpringConfig;
import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.entities.CopyrightOwner;
import blockChain.entities.Digital;
import blockChain.entities.UserEntity;
import blockChain.exception.EntityNotFoundException;
import blockChain.repository.CopyrightManagerPredicates;
import blockChain.repository.CopyrightOwnerPredicates;
import blockChain.repository.DigitalPredicates;
import blockChain.repository.UserPredicates;
import blockChain.service.*;
import blockChain.utils.HttpClientUtils;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.ssdc.security.CscpUserDetail;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Chan
 * @date 2020/3/31 16:18
 */
@Service
@AllArgsConstructor
public class CopyrightOwnerStatisticServiceFacade {

  private CopyrightOwnerService service;
  private CopyrightAgentService agentService;
  private CopyrightManagerService managerService;
  private UserService userService;
  private ApplicationRuntimeProperties runtimeProperties;
  private DigitalServiceFacade digitalServiceFacade;
  private final SpringConfig config;

  @Transactional(readOnly = true)
  public PageResponse<StatisticDto> getCompanies(CopyrightQueryStatisticGetParam queryParam) {
    PageRequest of = PageRequest.of(queryParam.getPage() - 1, queryParam.getPageSize());
    Page<StatisticDto> companies = service.getCompanies(of, queryParam);
    return PageResponse.of(companies);
  }

  @Transactional(readOnly = true)
  public void recoverLinks() {
    service.countBy();
  }

  @Transactional(readOnly = true)
  public PageResponse<StatisticDto> getAgent(CopyrightQueryStatisticGetParam queryParam) {
    queryParam.setPageSize(30);
    List<StatisticDto> companies = agentService.getAgent(queryParam);
    return PageResponse.of(1L, 30L, (long)companies.size(), companies);
  }

  @Transactional(readOnly = true)
  public PageResponse<StatisticDto> getManager(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> companies = service.getOwnerStatistic(queryParam);
    return PageResponse.of(1L, 100L, (long)companies.size(), companies);
  }

  /**
   * 作品类型统计
   * @return
   */
  @Transactional(readOnly = true)
  public PageResponse<StatisticDto> getProductionTypes(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> types = service.getProductionTypes(CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null));
    return PageResponse.of(1L, 100L, (long)types.size(), types);
  }

  /**
   * 福建省内地区类型发表数量统计
   * @return
   */
  public PageResponse<StatisticDto> getAreaDistributedFujian(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> types = service.getAreaDistributedForFujian(CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null));

    StatisticDto areaDistributedForGat = managerService.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceGatDigitalDefineCode(), 0,queryParam.getStartTime(), queryParam.getEndTime()));
    if (areaDistributedForGat.getAmount()>0) types.add(areaDistributedForGat.setName("港澳台地区"));

    return PageResponse.of(1L, 100L, (long)types.size(), types);
  }

  /**
   * 福建省外地区类型发表数量统计 (登记视图统计消息)
   * @return
   */
  public PageResponse<StatisticDto> getAreaDistributed() {
    List<StatisticDto> types = new ArrayList<>(4);

    StatisticDto areaDistributedForForeign = service.getAreaDistributed(DigitalPredicates.digitalQueryByPidAndCodeNotEq(runtimeProperties.getCountryDigitalDefinePid(), 0, runtimeProperties.getCnDigitalDefineCode()));
    types.add(areaDistributedForForeign.setName("国外"));

    StatisticDto areaDistributedForGat = service.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceGatDigitalDefineCode(), 0));
    types.add(areaDistributedForGat.setName("港澳台地区"));

    StatisticDto areaDistributedForOnlyFujian = service.getAreaDistributedCity(DigitalPredicates.digitalQueryByPid(runtimeProperties.getProvinceFujianDigitalDefinePid(), 0));
    types.add(areaDistributedForOnlyFujian.setName("省内"));

    StatisticDto areaDistributedForNoFujian = service.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceDigitalDefineCode(), 0));
    types.add(areaDistributedForNoFujian.setName("省外").setAmount(areaDistributedForNoFujian.getAmount() - areaDistributedForOnlyFujian.getAmount()));

    return PageResponse.of(1L, 4L, (long)types.size(), types);
  }

  /**
   * 作品视图统计地区
   * @param queryParam
   * @return
   */
  public PageResponse<StatisticDto> getGeographicDistributionOfManager(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> list = service.getAreaDistributedForFujian(CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null));

    StatisticDto areaDistributedForForeign = service.getAreaDistributed(DigitalPredicates.digitalQueryByPidAndCodeNotEq(runtimeProperties.getCountryDigitalDefinePid(), 0, runtimeProperties.getCnDigitalDefineCode()));
    //list.add(areaDistributedForForeign.setName("国外"));

    StatisticDto areaDistributedForGat = service.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceGatDigitalDefineCode(), 0));
    //list.add(areaDistributedForGat.setName("港澳台地区"));

    StatisticDto areaDistributedForOnlyFujian = service.getAreaDistributedCity(DigitalPredicates.digitalQueryByPid(runtimeProperties.getProvinceFujianDigitalDefinePid(), 0));

    StatisticDto areaDistributedForNoFujian = service.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceDigitalDefineCode(), 0));
    list.add(areaDistributedForNoFujian.setName("其它").setAmount(areaDistributedForNoFujian.getAmount() - areaDistributedForOnlyFujian.getAmount() + areaDistributedForGat.getAmount() + areaDistributedForForeign.getAmount()));

    return PageResponse.of(1L, (long) list.size(), (long) list.size(), list);
  }

  /**
   * 作品视图统计地区
   * @param queryParam
   * @return
   */
  public Map<String,Object> getGeographicDistributionOfStatistc(CopyrightQueryStatisticGetParam queryParam) {
    System.out.println("作品视图统计地区开始时间:"+ LocalDateTime.now());
    Integer pid = runtimeProperties.getProvinceFujianDigitalDefinePid();
    List<String> area = queryParam.getArea();
    if (area != null && !area.isEmpty()) {
      for (int i = area.size() - 1; i >= 0; i--) {
        if (area.get(i) != null && !area.get(i).equals("")) {
          pid = Integer.parseInt(area.get(i));
          break;
        }
      }
    }
    Digital areaDigital = digitalServiceFacade.getById(pid);
    String code = areaDigital.getCode();
    List<StatisticDto> list = new ArrayList<>();
    if (code!=null && code.equals("pn")) {
      list = managerService.getAreaDistributedCity(DigitalPredicates.digitalQueryByPid(pid, 0), CopyrightManagerPredicates.certificateDateBefore(queryParam.getStartTime(), queryParam.getEndTime()));
    } else if (code!=null && code.equals("ct") && areaDigital.isLeaf()) {
      // 省辖行政管理区(属市级)
      list = managerService.getAreaDistributedCity(pid, CopyrightManagerPredicates.certificateDateBefore(queryParam.getStartTime(), queryParam.getEndTime()));
    } else if (code!=null && code.equals("ct")) {
      list = managerService.getAreaDistributedCounty(DigitalPredicates.digitalQueryByPid(pid, 0), CopyrightManagerPredicates.certificateDateBefore(queryParam.getStartTime(), queryParam.getEndTime()));
    } else {
      // 指定区县
      list = managerService.getAreaDistributedCounty(pid, CopyrightManagerPredicates.certificateDateBefore(queryParam.getStartTime(), queryParam.getEndTime()));
    }
    /*for(StatisticDto statisticDto:list){
      statisticDto.setArea(statisticDto.getName());
      statisticDto.setId(statisticDto.getKey());
      //long amount = service.countBy(CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null));

      if (code.equals("pn")) {
        List<String> temparea = new ArrayList<String>(){{
          add("");
          add(statisticDto.getKey());
        }};
        queryParam.setArea(temparea);
      } else{
        List<String> temparea = new ArrayList<String>(){{
          add("");
          add("");
          add(statisticDto.getKey());
        }};
        queryParam.setArea(temparea);
      }
      *//*JSONObject perResponse = httpRequest(queryParam,null);
      long perOldTotal = perResponse.getLong("total");*//*
      statisticDto.setAmount(statisticDto.getAmount()*//*+perOldTotal*//*);
    }*/
    Map<String,Object> result = new HashMap<>();

    String startDate = queryParam.getStartDate();
    String endDate = queryParam.getEndDate();

    if(area==null || area.isEmpty()) {
      Long FujianAmount = 0L;
      for(StatisticDto statisticDto:list){
        FujianAmount+=statisticDto.getAmount();
      }
//      StatisticDto areaDistributedForForeign = service.getAreaDistributed(DigitalPredicates.digitalQueryByPidAndCodeNotEq(runtimeProperties.getCountryDigitalDefinePid(), 0, runtimeProperties.getCnDigitalDefineCode(),queryParam.getStartTime(), queryParam.getEndTime()));
//      if (areaDistributedForForeign.getAmount()>0) {
//        list.add(areaDistributedForForeign.setName("国外").setIsleaf(true));
//      }

      StatisticDto areaDistributedForGat = service.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceGatDigitalDefineCode(), 0,queryParam.getStartTime(), queryParam.getEndTime()));
      if (areaDistributedForGat.getAmount()>0) {
        list.add(areaDistributedForGat.setName("港澳台地区").setIsleaf(true));
      }

      StatisticDto areaDistributedForNoFujian = service.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceDigitalDefineCode(), 0,queryParam.getStartTime(), queryParam.getEndTime()));
      if (areaDistributedForNoFujian.getAmount()>0) {
        list.add(areaDistributedForNoFujian.setName("其它").setAmount(areaDistributedForNoFujian.getAmount() - FujianAmount).setIsleaf(true));
      }

      //查询全局数量
      long total = service.countBy(CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null));
      StatisticDto areaDistributedForForeign = new StatisticDto(999,"国外",0l,true);
      // 国外=全部-所有省份-港澳台
      list.add(areaDistributedForForeign.setAmount(total-FujianAmount-areaDistributedForNoFujian.getAmount()-areaDistributedForGat.getAmount()));

    }else{
      /*if(area.get(1)!=null && area.get(1)!=""){
        for(StatisticDto statisticDto:list){
          statisticDto.setIsleaf(true);
        }
      }*/
    }
    System.out.println("作品视图统计地区结束时间:"+ LocalDateTime.now());

    result.put("listData",list);
    result.put("startDate",startDate);
    result.put("endDate",endDate);
    result.put("areaArr",area);
    return result;
  }

  /**
   * 作品视图统计地区
   * @param queryParam
   * @return
   */
  public Map<String,Object> getGeographicDistributionOfTime(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> list = new ArrayList<>();

    LocalDateTime startTime = queryParam.getStartTime();
    LocalDateTime endTime = queryParam.getEndTime();
    Assert.notNull(startTime, "开始时间不能为空");
    Assert.notNull(endTime, "结束时间不能为空");

    if (startTime.isAfter(endTime)) {
      Assert.notNull(endTime, "开始时间不能在结束时间之后");
    }


    String startDate = queryParam.getStartDate();
    String endDate = queryParam.getEndDate();
    //查询月份全局数量
    long total = service.countBy(CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null));
    list = service.countListBy(CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null),queryParam.isYear());

    if(!startDate.equals(endDate)) {
      list.add(new StatisticDto(startDate + "~" + endDate, startDate + "~" + endDate, startDate + "~" + endDate, total, false));
    }
    Map<String,Object> listmap = new HashedMap();
    listmap.put("listData",list);
    Map<String,Object> result = new HashMap<>();
    result.put("timeWorkBar",listmap);
    result.put("startDate",startDate);
    result.put("workTotal",total);
    result.put("createtotal",total);
    result.put("endDate",endDate);
    result.put("areaArr",queryParam.getArea());
    return result;
  }

  /**
   * 作品视图统计地区
   * @param queryParam
   * @return
   */
  public Map<String,Object> getGeographicDistributionOfTotal(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> list = new ArrayList<>();

    LocalDateTime startTime = queryParam.getStartTime();
    LocalDateTime endTime = queryParam.getEndTime();
    Assert.notNull(startTime, "开始时间不能为空");
    Assert.notNull(endTime, "结束时间不能为空");

    if (startTime.isAfter(endTime)) {
      Assert.notNull(endTime, "开始时间不能在结束时间之后");
    }


    String startDate = queryParam.getStartDate();
    String endDate = queryParam.getEndDate();
    //查询月份全局数量
    long total = service.countBy(CopyrightOwnerPredicates.copyrightManagerTotalStatisticPredicates(queryParam, null));
    /*JSONObject response = httpRequest(queryParam,null);
    long oldTotal = response.getLong("total");
    total = total+oldTotal;*/

    if(!queryParam.isYear()) {
      //获取月数差
      long cyclical = ChronoUnit.MONTHS.between(startTime, endTime);
      for (long i = 0; i <= cyclical; i++) {
        LocalDateTime date = startTime.plusMonths(i).with(TemporalAdjusters.firstDayOfMonth());
        String dateString = date.getYear() + "-" + (date.getMonthValue() < 10 ? "0" + date.getMonthValue() : date.getMonthValue());
        queryParam.setStartDate(null);
        queryParam.setEndDate(dateString);
        long amount = service.countBy(CopyrightOwnerPredicates.copyrightManagerTotalStatisticPredicates(queryParam, null));
        /*JSONObject perResponse = httpRequest(queryParam,null);
        long perOldTotal = perResponse.getLong("total");
        amount = amount+perOldTotal;*/
        list.add(new StatisticDto(dateString, dateString, amount, false));
      }
    }else{
      //获取年数差
      long cyclical = ChronoUnit.YEARS.between(startTime, endTime);
      for (long i = 0; i <= cyclical; i++) {
        LocalDateTime date = startTime.plusYears(i).with(TemporalAdjusters.firstDayOfMonth());
        String dateString = date.getYear()+"";
        queryParam.setStartDate(null);
        queryParam.setEndDate(dateString);
        long amount = service.countBy(CopyrightOwnerPredicates.copyrightManagerTotalStatisticPredicates(queryParam, null));
        /*JSONObject perResponse = httpRequest(queryParam,null);
        long perOldTotal = perResponse.getLong("total");
        amount = amount+perOldTotal;*/
        list.add(new StatisticDto(dateString, dateString, amount, false));
      }
    }
    //list.add(new StatisticDto(startDate+"~"+endDate,startDate+"~"+endDate,total,false));
    Map<String,Object> result = new HashMap<>();
    result.put("totalWorkBar",list);
    //result.put("total",total);
    //result.put("startDate",startDate);
    //result.put("endDate",endDate);
    //result.put("areaArr",queryParam.getArea());
    return result;
  }

  /**
   * 作品视图统计（首页）
   * @param queryParam
   * @return
   */
  public Map<String,Object> getStatisticDistributionOfTime(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> list = new ArrayList<>();

    LocalDateTime startTime = queryParam.getStartTime();
    LocalDateTime endTime = queryParam.getEndTime();
    Assert.notNull(startTime, "开始时间不能为空");
    Assert.notNull(endTime, "结束时间不能为空");

    if (startTime.isAfter(endTime)) {
      Assert.notNull(endTime, "开始时间不能在结束时间之后");
    }

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    UserEntity loginUser = userService.findById(((CscpUserDetail) authentication.getPrincipal()).getId()).orElseThrow(() -> new EntityNotFoundException("该用户未找到"));
    queryParam.setUserName(loginUser.getUserName());

    String startDate = queryParam.getStartDate();
    String endDate = queryParam.getEndDate();
    //查询月份全局数量
    long total = managerService.count(CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null));
    list = managerService.countListBy(CopyrightOwnerPredicates.copyrightManagerStatisticPredicates(queryParam, null),queryParam.isYear(), queryParam.getIsWorkstation());

    if(!startDate.equals(endDate)) {
      list.add(new StatisticDto(startDate + "~" + endDate, startDate + "~" + endDate, startDate + "~" + endDate, total, false));
    }
    Map<String,Object> listmap = new HashedMap();
    listmap.put("listData",list);
    Map<String,Object> result = new HashMap<>();
    result.put("timeWorkBar",listmap);
    result.put("startDate",startDate);
    result.put("workTotal",total);
    result.put("createtotal",total);
    result.put("endDate",endDate);
    result.put("areaArr",queryParam.getArea());
    return result;
  }

  @Transactional
  public List<CopyrightOwner> getOwnersByUserId(Integer userId){
    return service.getOwnerFilled(UserPredicates.userIdEqual(userId));
  }

  private void setOldParam(Map<String,Object> oldParam,CopyrightQueryStatisticGetParam queryParam, List<Integer> copyCategorys){
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    if(queryParam.getStartDate()!=null) {
      oldParam.put("startTime", queryParam.getStartTime().toLocalDate().format(formatter)+" 00:00:00");
      oldParam.put("endTime", queryParam.getEndTime().toLocalDate().format(formatter)+" 23:59:59");
    }else{
      oldParam.put("totalEndTime", queryParam.getEndTime().toLocalDate().format(formatter)+" 23:59:59");
    }
    List<String> productTypes = new ArrayList<>();
    if (queryParam.getProductionTypeIds() != null && queryParam.getProductionTypeIds().size() > 0) {
      for(String productTypeId:queryParam.getProductionTypeIds()){
        productTypes.add(digitalServiceFacade.getById(Integer.parseInt(productTypeId)).getDict_name());
      }
    }

    if(copyCategorys != null && copyCategorys.size() > 0){
      oldParam.put("copyCategorys",copyCategorys);
    }
    oldParam.put("productTypes",productTypes);
    List<String> area = queryParam.getArea();
    if (area != null && area.size() > 0) {
      for (int i = 0; i < area.size(); i++) {
        // 从省开始
        switch (i) {
         /* case 0:
            builder.and(owner.copyCountries.eq(area.get(i)));
            break;*/
          case 0:
            if(area.get(i)!=null && area.get(i)!="") {
              String province = digitalServiceFacade.getById(Integer.parseInt(area.get(i))).getDict_name();
              oldParam.put("province",province);
            }
            break;
          case 1:
            if(area.get(i)!=null && area.get(i)!="") {
              String city = digitalServiceFacade.getById(Integer.parseInt(area.get(i))).getDict_name();
              oldParam.put("city",city.replace("市",""));
            }
            break;
          case 2:
            if(area.get(i)!=null && area.get(i)!="") {
              String county = digitalServiceFacade.getById(Integer.parseInt(area.get(i))).getDict_name();
              oldParam.put("county",county);
            }
            break;
        }
      }
    }
  }

  private JSONObject httpRequest(CopyrightQueryStatisticGetParam queryParam, List<Integer> copyCategorys){
    //处理旧参数，并请求旧系统数据
    Map<String,Object> oldParam = new HashedMap();
    setOldParam(oldParam,queryParam,null);
    RestTemplate restTemplate = new RestTemplate();
    String url = config.getOldSystemUrl()+"dataStatistics/geographyOfTime.do";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
    HttpEntity<Map<String, Object>> request = new HttpEntity<>(oldParam, headers);
    ResponseEntity<String> response = restTemplate.postForEntity( url, request , String.class );
    return JSONObject.parseObject(response.getBody());
  }

}
