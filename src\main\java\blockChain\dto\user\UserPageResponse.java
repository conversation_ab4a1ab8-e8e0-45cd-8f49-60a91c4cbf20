package blockChain.dto.user;

import blockChain.bean.PageResponse;
import blockChain.dto.RoleDto;
import blockChain.dto.UserDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/18 10:00
 */
@Data
public class UserPageResponse extends PageResponse<UserDto> {

    private List<RoleDto> roleList;

    public UserPageResponse(long page, long pageSize, long total, List<UserDto> listData) {
        super(page, pageSize, total, listData);
    }
}
