/**
 * WaiLianServiceImplServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx;

public class WaiLianServiceImplServiceLocator extends org.apache.axis.client.Service implements cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImplService {

    public WaiLianServiceImplServiceLocator() {
    }


    public WaiLianServiceImplServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public WaiLianServiceImplServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for WaiLianServiceAsmx
    private java.lang.String WaiLianServiceAsmx_address = "http://www.fjbs.gov.cn:82/WaiLianService/services/WaiLianService.asmx";

    public java.lang.String getWaiLianServiceAsmxAddress() {
        return WaiLianServiceAsmx_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String WaiLianServiceAsmxWSDDServiceName = "WaiLianService.asmx";

    public java.lang.String getWaiLianServiceAsmxWSDDServiceName() {
        return WaiLianServiceAsmxWSDDServiceName;
    }

    public void setWaiLianServiceAsmxWSDDServiceName(java.lang.String name) {
        WaiLianServiceAsmxWSDDServiceName = name;
    }

    public cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImpl getWaiLianServiceAsmx() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(WaiLianServiceAsmx_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getWaiLianServiceAsmx(endpoint);
    }

    public cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImpl getWaiLianServiceAsmx(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceAsmxSoapBindingStub _stub = new cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceAsmxSoapBindingStub(portAddress, this);
            _stub.setPortName(getWaiLianServiceAsmxWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setWaiLianServiceAsmxEndpointAddress(java.lang.String address) {
        WaiLianServiceAsmx_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImpl.class.isAssignableFrom(serviceEndpointInterface)) {
                cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceAsmxSoapBindingStub _stub = new cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceAsmxSoapBindingStub(new java.net.URL(WaiLianServiceAsmx_address), this);
                _stub.setPortName(getWaiLianServiceAsmxWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("WaiLianService.asmx".equals(inputPortName)) {
            return getWaiLianServiceAsmx();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://www.fjbs.gov.cn:82/WaiLianService/services/WaiLianService.asmx", "WaiLianServiceImplService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://www.fjbs.gov.cn:82/WaiLianService/services/WaiLianService.asmx", "WaiLianService.asmx"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("WaiLianServiceAsmx".equals(portName)) {
            setWaiLianServiceAsmxEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
