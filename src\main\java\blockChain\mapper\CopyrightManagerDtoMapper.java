package blockChain.mapper;

import blockChain.bean.PageResponse;
import blockChain.dto.CopyrightManagerDto;
import blockChain.entities.CopyrightManager;
import blockChain.mapper.transform.CopyrightManagerTransform;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR> XXX
 * @date 2019/11/29.
 */
@Mapper(uses={CopyrightManagerTransform.class, CopyrightManagerMarkerDtoMapper.class})
public interface CopyrightManagerDtoMapper {
    CopyrightManagerDtoMapper INSTANCE = Mappers.getMapper(CopyrightManagerDtoMapper.class);

    @Mapping(target = "id", source = "registrationNum")
    @Mapping(target = "haverightIdList", source = "haverightIds")
    CopyrightManagerDto entityToDto(CopyrightManager digital);

    List<CopyrightManagerDto> entityToDto(List<CopyrightManager> digitals);

    List<CopyrightManagerDto> digitalPageToDto(Page<CopyrightManager> pageable);

  default PageResponse<CopyrightManagerDto> toDto(Page<CopyrightManager> all){
    List<CopyrightManagerDto> list = entityToDto(all.getContent());
    return PageResponse.of((long)all.getPageable().getPageNumber()+1, (long)all.getPageable().getPageSize(), all.getTotalElements(), list);
  }
}
