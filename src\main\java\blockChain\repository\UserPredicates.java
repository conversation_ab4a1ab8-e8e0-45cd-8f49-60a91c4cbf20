package blockChain.repository;

import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.entities.QCopyrightManager;
import blockChain.entities.QUserEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/7 9:41
 */
public class UserPredicates {
  public static Predicate statisticByManagerProductionTypesAndManagerRegistrationDateAndUserArea(CopyrightQueryStatisticGetParam queryParam) {

    Assert.notNull(queryParam, "查询对象不能为NULL");
    QUserEntity user = QUserEntity.userEntity;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    BooleanBuilder builder = new BooleanBuilder();

    if(queryParam.getProductionTypeIds() != null && queryParam.getProductionTypeIds().size() > 0){
      builder.and(manager.productionTypeId.in(queryParam.getProductionTypeIds()));
    }
    if (queryParam.getEndTime() != null && queryParam.getStartTime() != null){
      builder.and(manager.registrationDate.between(queryParam.getStartTime(), queryParam.getEndTime()));
    }
    List<String> area = queryParam.getArea();
    if (area != null && area.size() > 0) {
      for (int i = 0; i < area.size(); i++) {
        // 从省开始
        switch (i) {
         /* case 0:
            builder.and(owner.copyCountries.eq(area.get(i)));
            break;*/
          case 0:
            builder.and(user.provinceName.eq(area.get(i)));
            break;
          case 1:
            builder.and(user.cityName.eq(area.get(i)));
            break;
          case 2:
            builder.and(user.countyName.eq(area.get(i)));
            break;
        }
      }
    }
    return builder;
  }

  public static Predicate statisticRegistrationDateAndUserArea(LocalDateTime begin, LocalDateTime end, List<String> area) {
    BooleanBuilder builder = new BooleanBuilder();
    QUserEntity user = QUserEntity.userEntity;
    if(begin != null && end != null){
      builder.and(user.registerTime.between(begin, end));
    }
    if (area != null && area.size() > 0) {
      for (int i = 0; i < area.size(); i++) {
        // 从省开始
        switch (i) {
         /* case 0:
            builder.and(owner.copyCountries.eq(area.get(i)));
            break;*/
          case 0:
            if(area.get(i)==null || area.get(i).isEmpty()){break;}
            builder.and(user.provinceName.eq(area.get(i)));
            break;
          case 1:
            if(area.get(i)==null || area.get(i).isEmpty()){break;}
            builder.and(user.cityName.eq(area.get(i)));
            break;
          case 2:
            if(area.get(i)==null || area.get(i).isEmpty()){break;}
            builder.and(user.countyName.eq(area.get(i)));
            break;
        }
      }
    }
    return builder;
  }

  public static Predicate statisticRegistrationDateBeforeAndUserArea(LocalDateTime lastDateTime, List<String> area) {
    BooleanBuilder builder = new BooleanBuilder();
    QUserEntity user = QUserEntity.userEntity;
    if(lastDateTime != null){
      builder.and(user.registerTime.before(lastDateTime));
    }
    if (area != null && area.size() > 0) {
      for (int i = 0; i < area.size(); i++) {
        // 从省开始
        switch (i) {
         /* case 0:
            builder.and(owner.copyCountries.eq(area.get(i)));
            break;*/
          case 0:
            if(area.get(i) == null || area.get(i).isEmpty()){break;}
            builder.and(user.provinceName.eq(area.get(i)));
            break;
          case 1:
            if(area.get(i) == null || area.get(i).isEmpty()){break;}
            builder.and(user.cityName.eq(area.get(i)));
            break;
          case 2:
            if(area.get(i) == null || area.get(i).isEmpty()){break;}
            builder.and(user.countyName.eq(area.get(i)));
            break;
        }
      }
    }
    return builder;
  }

  public static Predicate provincePidAndRegistrationDateBefore(Integer provincePid, LocalDateTime lastDateTime) {
    BooleanBuilder builder = new BooleanBuilder();
    QUserEntity user = QUserEntity.userEntity;
    if(lastDateTime != null){
      builder.and(user.registerTime.before(lastDateTime));
    }
    if(provincePid != null){
      builder.and(user.provinceName.eq(String.valueOf(provincePid)));
    }
    return builder;
  }

  public static Predicate neProvincePidAndRegistrationDateBefore(Integer provincePid, LocalDateTime lastDateTime) {
    BooleanBuilder builder = new BooleanBuilder();
    QUserEntity user = QUserEntity.userEntity;
    if(lastDateTime != null){
      builder.and(user.registerTime.before(lastDateTime));
    }
    if(provincePid != null){
      builder.and(user.provinceName.ne(String.valueOf(provincePid)));
    }
    return builder;
  }

  public static Predicate userIdEqual(Integer userId){
    BooleanBuilder builder = new BooleanBuilder();
    QUserEntity userEntity = QUserEntity.userEntity;
    if(userId!=null){
      builder.and(userEntity.userId.eq(userId));
    }
    return builder;
  }
}
