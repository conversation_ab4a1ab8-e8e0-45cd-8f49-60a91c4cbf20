package blockChain.repository;

import blockChain.entities.Digital;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/29 14:44
 */
@Repository
public interface DigitalRepository extends JpaRepository<Digital, Integer>, QuerydslPredicateExecutor<Digital> {
    public List<Digital> findByLevel(Integer level);

    @Query(value = "SELECT d.* FROM tb_dict_area da \n" +
            "LEFT JOIN tb_dict d on d.dict_name=da.dict_name \n" +
            "WHERE da.id=?1 and da.level=?2 and d.level=?3",nativeQuery = true)
    Digital getIdByArea(Long areaId, Integer areaLevel, Integer level);

    @Query(value = "SELECT d.* FROM tb_dict_area da \n" +
            "LEFT JOIN tb_dict d on d.dict_name=da.dict_name \n" +
            "WHERE da.id=?1 and da.level=?2 and d.level=?3 and d.pid=?4",nativeQuery = true)
    Digital getIdByAreaAndPid(Long areaId, Integer areaLevel, Integer level, Integer pid);
}
