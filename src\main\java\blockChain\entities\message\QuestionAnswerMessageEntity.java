package blockChain.entities.message;

import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * QA 表
 * 智能客服用
 * <AUTHOR>
 * @date 2020/4/10 15:07
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tb_question_answer_message")
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
public class QuestionAnswerMessageEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  /**
   * UID
   * 唯一约束
   */
  @Column(unique = true, length = 40)
  @NonNull
  private String uuid;

  @Column(name = "_key")
  private String key;

  @Column(length = 4096)
  private String answer;

  @CreatedDate
  private LocalDateTime createTime;

  @LastModifiedDate
  private LocalDateTime updateTime;

}
