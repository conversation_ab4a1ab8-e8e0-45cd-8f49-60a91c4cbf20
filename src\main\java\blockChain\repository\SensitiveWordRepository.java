package blockChain.repository;

import blockChain.entities.SensitiveWordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 敏感词库(tb_sensitive_word)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-02 10:46:42
 */
@Repository
public interface SensitiveWordRepository extends BaseRepository<SensitiveWordEntity, Integer> {

    SensitiveWordEntity findBySensitiveWordAndIsDelete(String sensitiveWord, boolean isDelete);

    Page<SensitiveWordEntity> findByIsDeleteOrderByIdDesc(boolean isDelete, Pageable pageable);

    List<SensitiveWordEntity> findAllByIsDelete(boolean isDelete);
}

