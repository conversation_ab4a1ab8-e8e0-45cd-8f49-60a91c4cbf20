package blockChain.repository;

import blockChain.entities.pictureCompare.PictureCompareEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * Created by epcsoft on 2020/8/6.
 */
@Repository
public interface PictureCompareRepository extends BaseRepository<PictureCompareEntity, Long> {
  PictureCompareEntity findByToken(String token);
}
