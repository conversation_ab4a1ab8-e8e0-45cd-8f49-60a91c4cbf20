package blockChain.scheduling;

import blockChain.config.SpringConfig;
import blockChain.facade.service.CopyrightManagerServiceFacade;
import blockChain.facade.service.CopyrightOwnerStatisticServiceFacade;
import blockChain.facade.service.EvaluationServiceFacade;
import blockChain.facade.service.ReportServiceFacade;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;

/**
 * <AUTHOR> jie
 * @date 2020/8/24 16:39
 */
@Component
@AllArgsConstructor
@Slf4j
@Async
public class CopyrightManagerScheduling {

  private CopyrightManagerServiceFacade facade;
  private CopyrightOwnerStatisticServiceFacade copyrightOwnerStatisticServiceFacade;
  private ReportServiceFacade reportServiceFacade;
    private final SpringConfig config;
    private EvaluationServiceFacade evaluationServiceFacade;

  /**
   * 每日凌晨1点对驳回的作品倒计时更新
   */
  @Scheduled(cron = "0 0 1 * * ?")
  @Transactional
  public void modifiedCountdown(){
    facade.modifiedCountdown();
  }

  /**
   * 每日凌晨1点进行自动数据汇聚
   */
  @Scheduled(cron = "0 0 1 * * ?")
  @Transactional
  public void onportemp(){
    if (config.getAutoSubmit()) {
      facade.onportemp();
    }
  }

  /**
   * 每日凌晨2点进行自动证照提交
   */
  /*@Scheduled(cron = "0 0 2 * * ?")
  @Transactional
  public void licenseSendInfoAutoSubmit(){
      reportServiceFacade.licenseSendInfoAutoSubmit();
  }*/

  /**
   * 每日凌晨3点进行自动上报省网办办件
   */
  /*@Scheduled(cron = "0 0 3 * * ?")
  @Transactional
  public void wl(){
      try {
          WaiLianServiceImplService waiLianServiceImplService=new WaiLianServiceImplServiceLocator();
          WaiLianServiceImpl waiLianService= waiLianServiceImplService.getWaiLianServiceAsmx();
          facade.wailianPush(waiLianService);
      }
      catch (Exception e)
      {
          e.printStackTrace();
      }
  }*/

    /**
     * 每日20点进行自动证照提交、上报省网办办件
     */
    @Scheduled(cron = "0 0 20 * * ?")
    @Async("doSomethingExecutor")
    public void autoSubmit() {
      if (config.getAutoSubmit()) {
        try {
            reportServiceFacade.licenseSendInfoSubmit(20, 8);

//            WaiLianServiceImplService waiLianServiceImplService = new WaiLianServiceImplServiceLocator();
//            WaiLianServiceImpl waiLianService = waiLianServiceImplService.getWaiLianServiceAsmx();
//            // 自动提交，8点后停止
//            facade.wailianPush(waiLianService, 20, 8);

            evaluationServiceFacade.autoEvaluation(20, 8);
        } catch (Exception e) {
          log.error("error", e);
        }
      }
    }
  /**
   * 每日凌晨3点进行自动证书生成
   */
  /*@Scheduled(cron = "0 0 3 * * ?")
  @Transactional
  public void onportemp(){
    facade.onportemp();
  }*/

  /**
   * 每三秒轮询证书进行生成
   */
  /*@Scheduled(cron = "0/3 * * * * ?")
  @Transactional
  public void certificateCreate(){
    facade.certificateCreateScheduling();
  }*/

  @Scheduled(cron = "0 0 */4 * * ?")
  @Transactional
  public void recoverLink(){
    copyrightOwnerStatisticServiceFacade.recoverLinks();
  }
}
