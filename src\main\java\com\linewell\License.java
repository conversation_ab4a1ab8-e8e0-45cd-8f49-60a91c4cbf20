package com.linewell;

import blockChain.config.SpringConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lic.share.api.SnowflakeIdWorker3rd;
import com.lic.share.config.RestHelper;
import com.lic.share.params.ClientInfo;
import com.lic.share.params.ProjectInfo;
import com.lic.share.params.RequestHead;
import com.lic.share.params.RequestParam;
import com.lic.share.response.ResponseResult;
import com.lic.share.untils.SmCall;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;

import java.security.SignatureException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/08/2022/8/5
 */
@Api("电子提交")
@Slf4j
public class License {

  private String accountId;
  private String priKey;
  private String authUrl;
  private String saveCertificateUrl;
  private RestHelper restHelper = new RestHelper();

  public License(SpringConfig config) {
    priKey = config.getLicensePriKey();
    accountId = config.getLicenseAccountId();
    authUrl = config.getLicenseTokenUrl();
    saveCertificateUrl = config.getLicenseSaveUrl();
  }

  @GetMapping(value = {"token"})
  public String token() throws Exception {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
    String timeStr = sdf.format(new Date());
    SnowflakeIdWorker3rd idWorker = new SnowflakeIdWorker3rd();


    int id = idWorker.createId();
    String accessId = timeStr + accountId + id;
    JSONObject object = new JSONObject();
    object.put("accountId", accountId);
    object.put("accessId", accessId);
    object.put("sign", SmCall.createSM2Signature(accessId, priKey));
    String token = restHelper.postRestTemplateByJson(authUrl, object.toJSONString());
    return token;
  }

  private String getAccessToken() throws Exception {
    String token = token();
    ResponseResult tokenResult = JSON.parseObject(token, ResponseResult.class);
    if (tokenResult.getHead().getStatus() != 0) {
      throw new SignatureException(tokenResult.getHead().getMessage());
    }
    return tokenResult.getData().getAccessToken();
  }

  public String saveCertificate(JSONObject jsonInfo) throws Exception {
    String accessToken = getAccessToken();
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
    String timeStr = sdf.format(new Date());
    SnowflakeIdWorker3rd idWorker = new SnowflakeIdWorker3rd();

    int id = idWorker.createId();
    String accessId = timeStr + accountId + id;

    RequestParam requestParam = new RequestParam();
    // 调用方信息
    ClientInfo clientInfo = new ClientInfo().setAreaCode("35000")
            .setAreaName("福建省")
            .setDeptCode("DeptCode")
            .setDeptName("中共福建省委宣传部")
            .setOperId("OperId")
            .setOperName("谢处")
            .setSystemName("福建省版权局作品自愿登记系统");

    ProjectInfo projectInfo = new ProjectInfo()
            .setProjectNo("ProjectNo")
            .setTaskCode("TaskCode").setTaskName("TaskName");
    RequestHead head = new RequestHead().setAccessToken(accessToken)
            .setAccessId(accessId)
            .setAccountId(accountId)
            .setReqTime(LocalDateTime.now())
            .setClientInfo(clientInfo)
            .setProjectInfo(projectInfo);//
    requestParam.setHead(head);
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("jsonInfo", jsonInfo);
    jsonObject.put("state", 2);
    requestParam.setData(jsonObject);
    String jsonData = JSON.toJSONString(requestParam);

    String sign = SmCall.createSM2Signature(jsonData, priKey);

    String s = restHelper.postRestTemplateByJson(saveCertificateUrl, accountId, sign, jsonData);
    return s;
  }

}
