package blockChain.repository;

import blockChain.entities.Author;
import blockChain.entities.CopyrightOwner;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:28
 */
@Repository
public interface AuthorRepository extends  BaseRepository<Author, Long> {
  @Query(value = "select a.* from tb_author a left join tb_copyrightmanager c on a.copyright_id = c.registrationNum where a.copyright_id = ?1",nativeQuery = true)
  List<Author> getByCopyrightId(Long copyrightId);
}
