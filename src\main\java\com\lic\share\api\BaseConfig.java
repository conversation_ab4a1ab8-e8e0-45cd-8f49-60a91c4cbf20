package com.lic.share.api;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <pre>
 * ClassName BaseConfig
 * Description
 * Author glq
 * Date 2020-7-2
 * </pre>
 */
public class BaseConfig {

//    String accountId = "A300";
//    String authUrl = "http://dev.owler.cn:11480/apptwodev/auth/token";
//    String sharePreUrl = "http://dev.owler.cn:11480/apptwodev/share/";
    String dynamicAuthorizationPreUrl = "http://dev.owler.cn:11480/apptwodev/dynamicauthorization/";
    String submitPreUrl = "http://dev.owler.cn:11480/apptwodev/submit/";
    String appealPreUrl = "http://dev.owler.cn:11480/apptwodev/appeal/";//证照数据纠错
//
//    String priKey="MHcCAQEEIB70ofrJ7L6v/hghulCjvZfGTs99NbzyG9C8hYwMcZIAoAoGCCqBHM9V\n" +
//            "AYItoUQDQgAEHUjH3h26KGUwPi/iRigC1HsXXNnBFftWELo7PS+7/AGWFyD6c5V4\n" +
//            "ue39vpTX6Zzs4gHzAuK9D52iBaOs2CGy0g==\n";

    String accountId = "A086";
    String authUrl = "https://dzzz.fjzwt.cn:28081/auth/token";
    String sharePreUrl = "https://dzzz.fjzwt.cn:28081/share/";

    String priKey="MHcCAQEEILCi4UYINuDTaehanoi7jT7EVP65ZKrBE+17GIMPvl4CoAoGCCqBHM9V\n" +
            "AYItoUQDQgAEwgbSlqmq11Xuy/eEY259yacPlvEDUT8sQnYucBIw3hvpDt4dyBpX\n" +
            "2/ZwQp/1jutJ7FMY7d8mGqtfL2EvikYzOw==\n";

    
    /**
     * 字符串转换成日期
     *
     * @param str
     * @return date
     */
    public Date StrToDate(String str) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }


}
