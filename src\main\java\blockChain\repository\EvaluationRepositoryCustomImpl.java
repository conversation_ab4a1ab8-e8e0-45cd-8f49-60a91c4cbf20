package blockChain.repository;

import blockChain.dto.StatisticDto;
import blockChain.entities.EvaluateEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;

import java.time.LocalDateTime;
import java.util.List;

import static blockChain.entities.QEvaluateEntity.evaluateEntity;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public class EvaluationRepositoryCustomImpl extends QuerydslRepositorySupport implements EvaluationRepositoryCustom {
    public EvaluationRepositoryCustomImpl() {
        super(EvaluateEntity.class);
    }
    @Override
    public List<StatisticDto> countByAlternate(LocalDateTime start, LocalDateTime end) {
        // 根据评分分组统计出各评分的数量
        BooleanBuilder predicate = new BooleanBuilder();
        if(start != null) {
            predicate.and(evaluateEntity.assessTime.goe(start));
        }
        if(end != null) {
            predicate.and(evaluateEntity.assessTime.loe(end));
        }
        return getQuerydsl().<StatisticDto>createQuery()
                .select(Projections.constructor(StatisticDto.class, evaluateEntity.alternate.as("key"), evaluateEntity.count().as("amount")))
                .from(evaluateEntity)
                .where(predicate)
                .groupBy(evaluateEntity.alternate)
                .orderBy(evaluateEntity.alternate.desc())
                .fetch()
                ;
    }
}
