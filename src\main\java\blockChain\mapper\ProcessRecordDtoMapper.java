package blockChain.mapper;

import blockChain.dto.AgentDto;
import blockChain.dto.ProcessRecordDto;
import blockChain.entities.Agent;
import blockChain.entities.ProcessRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProcessRecordDtoMapper {
    ProcessRecordDtoMapper INSTANCE = Mappers.getMapper(ProcessRecordDtoMapper.class);

    ProcessRecordDto entityToDto(ProcessRecord agent);

    List<ProcessRecordDto> entityToDto(List<ProcessRecord> agents);
}
