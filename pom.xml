<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>ctff</groupId>
  <artifactId>blockChain</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>jar</packaging>
  <name>blockChain Web App</name>

  <properties>
    <failOnMissingWebXml>false</failOnMissingWebXml>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <imageVersion>1.0.0</imageVersion>
    <imageName>image.docker.ssdc.solutions/ctsi/blockChain</imageName>

    <spring-boot.version>2.1.6.RELEASE</spring-boot.version>
    <mapstruct.version>1.3.0.Final</mapstruct.version>
    <fastjson.version>1.2.83</fastjson.version>
    <lombok.version>1.18.8</lombok.version>
    <livesense.version>1.0.5</livesense.version>
    <commons-compress.version>1.9</commons-compress.version>
    <UserAgentUtils.version>1.21</UserAgentUtils.version>
  </properties>

  <parent>
    <groupId>com.ctbiyi</groupId>
    <artifactId>biyi-parent</artifactId>
    <version>4.6.0</version>
  </parent>

  <dependencies>

      <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-data-jpa</artifactId>
      </dependency>

      <dependency>
          <groupId>com.ctbiyi</groupId>
          <artifactId>biyi-core</artifactId>
          <version>4.5.0</version>
      </dependency>
      <dependency>
          <groupId>com.ctbiyi</groupId>
          <artifactId>biyi-ffcs-excelutil</artifactId>
          <version>1.0.1</version>
          <exclusions>
              <exclusion>
                  <artifactId>biyi-zdww-license-sdk</artifactId>
                  <groupId>com.ctbiyi</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.ctbiyi</groupId>
          <artifactId>biyi-admin</artifactId>
          <version>3.3.2</version>
      </dependency>

      <dependency>
          <groupId>com.ctsi.ssdc</groupId>
          <artifactId>biyi-util</artifactId>
      <version>1.2.0</version>
    </dependency>

    <dependency>
      <groupId>com.ctbiyi</groupId>
      <artifactId>biyi-captcha</artifactId>
      <version>3.1.0</version>
    </dependency>

    <dependency>
      <groupId>com.ctbiyi</groupId>
      <artifactId>biyi-zdww-util</artifactId>
      <version>1.0.1</version>
    </dependency>

    <dependency>
      <groupId>com.ctbiyi</groupId>
      <artifactId>biyi-digest</artifactId>
      <version>1.5.0</version>
    </dependency>

    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
      <version>2.3.28</version>
    </dependency>

    <dependency>
      <artifactId>component-core</artifactId>
      <groupId>com.ctbiyi</groupId>
      <version>1.0.8</version>
    </dependency>
    <dependency>
      <artifactId>component-sdk</artifactId>
      <groupId>com.ctbiyi</groupId>
      <version>1.0.37</version>
    </dependency>


    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>com.querydsl</groupId>
      <artifactId>querydsl-apt</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.querydsl</groupId>
      <artifactId>querydsl-jpa</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.jsonx/jsonxml -->
    <!-- https://mvnrepository.com/artifact/de.odysseus.staxon/staxon-jackson -->
    <dependency>
      <groupId>de.odysseus.staxon</groupId>
      <artifactId>staxon-jackson</artifactId>
      <version>1.2</version>
    </dependency>


    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${mapstruct.version}</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>${fastjson.version}</version>
    </dependency>

    <dependency>
      <groupId>com.github.livesense</groupId>
      <artifactId>
        org.liveSense.fragment.com.sun.image.codec.jpeg
      </artifactId>
      <version>${livesense.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
      <version>${commons-compress.version}</version>
    </dependency>

    <dependency>
      <groupId>eu.bitwalker</groupId>
      <artifactId>UserAgentUtils</artifactId>
      <version>${UserAgentUtils.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-frontend-jaxws</artifactId>
      <version>3.1.4</version>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-transports-http</artifactId>
      <version>3.1.4</version>
    </dependency>

    <dependency>
      <groupId>com.mascloud</groupId>
      <artifactId>com.mascloud</artifactId>
      <version>2.2-SNAPSHOT</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/src/main/resources/lib/sms.jar</systemPath>
    </dependency>
    <dependency>
      <groupId>com.fgi.egbase</groupId>
      <artifactId>com.fgi.egbase</artifactId>
      <version>1.0.4.0-SNAPSHOT</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/src/main/resources/lib/egbase-1.0.4.0-SNAPSHOT.jar</systemPath>
    </dependency>

    <dependency>
      <groupId>com.sunfirestudio.common</groupId>
      <artifactId>com.sunfirestudio.common</artifactId>
      <version>1.0.3.1-20200706.034244-2</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/src/main/resources/lib/sunfire-common-1.0.3.1-20200706.034244-2.jar</systemPath>
    </dependency>

    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <version>1.64</version>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk15on</artifactId>
      <version>1.64</version>
    </dependency>

    <dependency>
      <groupId>com.thoughtworks.xstream</groupId>
      <artifactId>com.thoughtworks.xstream</artifactId>
      <version>1.4.2</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/src/main/resources/lib/xstream-1.4.2.jar</systemPath>
    </dependency>

    <dependency>
      <groupId>net.sourceforge.jexcelapi</groupId>
      <artifactId>jxl</artifactId>
      <version>2.6.12</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/javax.servlet/javax.servlet-api -->
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <version>4.0.1</version>
      <scope>provided</scope>
    </dependency>
    <!-- https://mvnrepository.com/artifact/javax.xml.ws/jaxws-api -->
    <dependency>
      <groupId>javax.xml.ws</groupId>
      <artifactId>jaxws-api</artifactId>
      <version>2.3.1</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.glassfish.main.javaee-api/javax.jws -->
    <dependency>
      <groupId>org.glassfish.main.javaee-api</groupId>
      <artifactId>javax.jws</artifactId>
      <version>3.1.2.2</version>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.ws</groupId>
      <artifactId>jaxws-rt</artifactId>
      <version>2.1.4</version>
    </dependency>

    <!--**** 阿里的数据源 ****-->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid</artifactId>
      <version>1.1.10</version>
    </dependency>

    <!--<dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-config</artifactId>
      <version>2.2.5.RELEASE</version>
    </dependency>-->

      <dependency>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-all</artifactId>
          <version>5.7.19</version>
      </dependency>
      <dependency>
          <groupId>org.junit.jupiter</groupId>
          <artifactId>junit-jupiter-params</artifactId>
      </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <profiles>
    <profile>
      <id>swagger</id>
      <properties>
        <profile.swagger>,swagger</profile.swagger>
      </properties>
    </profile>
  </profiles>

  <build>
    <finalName>${project.artifactId}</finalName>
    <defaultGoal>spring-boot:run</defaultGoal>
    <resources>
      <resource>
        <directory>${basedir}/src/main/webapp</directory>
        <!--注意此次必须要放在此目录下才能被访问到-->
        <targetPath>META-INF/resources</targetPath>
        <includes>
          <include>**/**</include>
        </includes>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <includes>
          <include>**/**</include>
        </includes>
        <excludes>
          <exclude>**/license.lc</exclude>
        </excludes>
        <filtering>true</filtering>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <includes>
          <include>**/license.lc</include><!--添加证书-->
        </includes>
        <filtering>false</filtering>
      </resource>
      <resource>
        <directory>${basedir}/src/main/java</directory>
        <includes>
          <include>**/*.properties</include>
          <include>**/*.xml</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.mybatis.generator</groupId>
        <artifactId>mybatis-generator-maven-plugin</artifactId>
        <version>1.4.0-ctsi</version>
        <dependencies>
          <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.34</version>
          </dependency>

          <dependency>
            <groupId>com.ctsi.ssdc</groupId>
            <artifactId>generator</artifactId>
            <version>4.0.3</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <!--跳过编译单元测试代码-->
          <skip>false</skip>
          <annotationProcessorPaths>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${mapstruct.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!--跳过单元测试-->
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.1.6.RELEASE</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.spotify</groupId>
        <artifactId>dockerfile-maven-plugin</artifactId>
        <version>1.3.6</version>
        <configuration>
          <repository>${imageName}</repository>
          <tag>${imageVersion}</tag>
          <buildArgs>
            <WAR_FILE>target/${project.build.finalName}.war</WAR_FILE>
          </buildArgs>
          <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.mysema.maven</groupId>
        <artifactId>apt-maven-plugin</artifactId>
        <version>1.1.3</version>
        <executions>
          <execution>
            <goals>
              <goal>process</goal>
            </goals>
            <configuration>
              <outputDirectory>target/generated-sources/java</outputDirectory>
              <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
