# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# ===================================================================
logging:
  config: classpath:logback-formate/logback-dev.xml
  level:
    ROOT: debug
    com.ctsi.sampleapp: inforemoveAbandonedTimeout
    org.hibernate: warn
    org.hibernate.ejb.HibernatePersistence: trace
    org.hibernate.SQL: trace
    org.hibernate.type.descriptor.sql.BasicBinder: trace
  file: you_application_name.log
  path: /var/logs/
spring:
  profiles:
    active: dev
    include: swagger

  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL57Dialect
  liquibase:
    enabled: true
    change-log: classpath:/db/changelog/master.xml
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************
    username: root
    password: 123456
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
        poolName: DatebookHikariCP1
        maximumPoolSize: 20
        data-source-properties:
          cachePrepStmts: true
          prepStmtCacheSize: 250
          prepStmtCacheSqlLimit: 2048
          useServerPrepStmts: true
    secondary:
      driverClassName: com.mysql.cj.jdbc.Driver
      name: secondaryDB
      url: *****************************************************************************************************************
      username: root
      password: XXXXX
      type: com.zaxxer.hikari.HikariDataSource
      testOnBorrow: false
      testWhileIdle: true
      timeBetweenEvictionRunsMillis: 3600000
      hikari:
          poolName: DatebookHikariCP2
          maximumPoolSize: 20
          data-source-properties:
            cachePrepStmts: true
            prepStmtCacheSize: 250
            prepStmtCacheSqlLimit: 2048
            useServerPrepStmts: true
  messages:
    basename: i18n/messages
    encoding: UTF-8
server:
  port: 9001
  servlet:
    context-path: /api/qkl

ctsi:
  # CORS is only enabled by default with the "dev" profile, so BrowserSync can access the API
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization,Link,X-Total-Count"
    allow-credentials: true
    max-age: 1800
  log:
    operation-log:
      enable: false
    login-log:
      enable: false
  jwtfilter:
    enable: true

block:
  blockUrl:
    url: http://XXXX:50002/
  frontUrl:
    url: http://localhost:8001/
  oldSystemUrl:
    url: http://localhost:50015/CopyRight/
  biyiServiceUrl:
    url: http://************:8099/api/qkl/test/
  file:
    fileRoot: media/nas/copyright_newupload
    modelRoot: media/nas/
  convergence:
    wsdl: http://*************:809/Convergence/webservice/ConvergenceService?wsdl
    userName: hjpt_bqbkxt
    passWord: hjpt@321
    catalogId_manager: WEB736
    catalogId_owner: WEB735
  proxyIp: ************
  proxyPort: 7777
  isExtranet: false
#  proxyIp: none
#  proxyPort: 0
  autoSubmit: false
  certificate:
    cerUrl: http://**************:18080/processBuildCertificate
    picUrl: http://**************:18080/processFixTakeStamp
    documentUrl: http://**************:18888/docTakeStamp
  similarScore: 3
  license:
    url: http://**************:90/services/DataCollect
    guid: ********-B96B51EB9423BACE2CAF-60
    surface: sendSurfaceAndAutoSeal
    tokenUrl: http://**************:8040/license-api-release/certificate/oauth/token
    saveUrl: http://**************:8040/license-api-release/certificate/v1/saveCertificateInfo
    accountId: fjsbqj
    priKey: FHrFA8XpCBkSr0pWiG+/a/pcJcEVVfycb1i1fqKHx4M=
