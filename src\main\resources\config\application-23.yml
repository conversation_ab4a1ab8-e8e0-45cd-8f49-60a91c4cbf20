# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# ===================================================================
logging:
  config: classpath:logback-formate/logback-dev.xml
  level:
    ROOT: debug
    com.ctsi.sampleapp: inforemoveAbandonedTimeout
    org.hibernate: warn
    org.hibernate.ejb.HibernatePersistence: trace
    org.hibernate.SQL: trace
    org.hibernate.type.descriptor.sql.BasicBinder: trace
  file: you_application_name.log
  path: /var/logs/
spring:
  profiles:
    active: dev
    include: swagger

  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL57Dialect
  liquibase:
    enabled: true
    change-log: classpath:/db/changelog/master.xml
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************
    # url: ***************************************************************************************************************************************************************************
    username: root
    password: 123456
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      poolName: DatebookHikariCP1
      maximumPoolSize: 20
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
    secondary:
      driverClassName: com.mysql.cj.jdbc.Driver
      name: secondaryDB
      url: *********************************************************************************************************************************************
      #url: ****************************************************************************************************************************************************
      username: root
      password: 123456
      type: com.zaxxer.hikari.HikariDataSource
      testOnBorrow: false
      testWhileIdle: true
      timeBetweenEvictionRunsMillis: 3600000
      hikari:
        poolName: DatebookHikariCP2
        maximumPoolSize: 20
        data-source-properties:
          cachePrepStmts: true
          prepStmtCacheSize: 250
          prepStmtCacheSqlLimit: 2048
          useServerPrepStmts: true
  messages:
    basename: i18n/messages
    encoding: UTF-8
server:
  port: 9002
  servlet:
    context-path: /api/qkl

ctsi:
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization,Link,X-Total-Count"
    allow-credentials: true
    max-age: 1800
  log:
    operation-log:
      enable: false
    login-log:
      enable: false
  jwtfilter:
    enable: true

block:
  blockUrl:
    url: http://hm.epcdiy.org:50002/
  frontUrl:
    url: http://localhost:8001/
  oldSystemUrl:
    url: http://localhost:50015/CopyRight/
  biyiServiceUrl:
    url: http://************:8099/api/qkl/test/
  file:
    fileRoot: media/nas/copyright_newupload
    modelRoot: media/nas/
  convergence:
    wsdl: http://*************:809/Convergence/webservice/ConvergenceService?wsdl
    userName: hjpt_bqbkxt
    passWord: hjpt@321
    catalogId_manager: WEB736
    catalogId_owner: WEB735
  proxyIp: *************
  proxyPort: 7777
