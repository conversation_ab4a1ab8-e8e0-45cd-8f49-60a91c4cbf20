
package org.tempuri;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 *
 */
@WebServiceClient(name = "Cegn_GSZJQueryData", targetNamespace = "http://tempuri.org/", wsdlLocation = "http://hjpt.fjzwt.cn:9000/CegnQuery/Cegn_GSZJQueryData.asmx?wsdl")
public class CegnGSZJQueryData
    extends Service
{

    private final static URL CEGNGSZJQUERYDATA_WSDL_LOCATION;
    private final static WebServiceException CEGNGSZJQUERYDATA_EXCEPTION;
    private final static QName CEGNGSZJQUERYDATA_QNAME = new QName("http://tempuri.org/", "Cegn_GSZJQueryData");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://hjpt.fjzwt.cn:9000/CegnQuery/Cegn_GSZJQueryData.asmx?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        CEGNGSZJQUERYDATA_WSDL_LOCATION = url;
        CEGNGSZJQUERYDATA_EXCEPTION = e;
    }

    public CegnGSZJQueryData() {
        super(__getWsdlLocation(), CEGNGSZJQUERYDATA_QNAME);
    }

    public CegnGSZJQueryData(WebServiceFeature... features) {
        super(__getWsdlLocation(), CEGNGSZJQUERYDATA_QNAME, features);
    }

    public CegnGSZJQueryData(URL wsdlLocation) {
        super(wsdlLocation, CEGNGSZJQUERYDATA_QNAME);
    }

    public CegnGSZJQueryData(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, CEGNGSZJQUERYDATA_QNAME, features);
    }

    public CegnGSZJQueryData(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public CegnGSZJQueryData(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     *
     * @return
     *     returns CegnGSZJQueryDataSoap
     */
    @WebEndpoint(name = "Cegn_GSZJQueryDataSoap")
    public CegnGSZJQueryDataSoap getCegnGSZJQueryDataSoap() {
        return super.getPort(new QName("http://tempuri.org/", "Cegn_GSZJQueryDataSoap"), CegnGSZJQueryDataSoap.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns CegnGSZJQueryDataSoap
     */
    @WebEndpoint(name = "Cegn_GSZJQueryDataSoap")
    public CegnGSZJQueryDataSoap getCegnGSZJQueryDataSoap(WebServiceFeature... features) {
        return super.getPort(new QName("http://tempuri.org/", "Cegn_GSZJQueryDataSoap"), CegnGSZJQueryDataSoap.class, features);
    }

    private static URL __getWsdlLocation() {
        if (CEGNGSZJQUERYDATA_EXCEPTION!= null) {
            throw CEGNGSZJQUERYDATA_EXCEPTION;
        }
        return CEGNGSZJQUERYDATA_WSDL_LOCATION;
    }

}
