package blockChain.mapper;

import blockChain.dto.ebus.FormDataDTO;
import blockChain.entities.FormDataEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = CommonConfig.class)
public interface FormDataMapper {
    FormDataMapper INSTANCE = Mappers.getMapper(FormDataMapper.class);

    @Mapping(source = "applyName", target = "name")
    FormDataDTO toDTO(FormDataEntity entity);
}
