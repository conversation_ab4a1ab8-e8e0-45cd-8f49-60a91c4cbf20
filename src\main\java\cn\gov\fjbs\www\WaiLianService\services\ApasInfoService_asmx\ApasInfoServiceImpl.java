/**
 * ApasInfoServiceImpl.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx;

public interface ApasInfoServiceImpl extends java.rmi.Remote {
    public java.lang.String getCrudInfoListNew(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String count, java.lang.String action, java.lang.String times, java.lang.String sortsign) throws java.rmi.RemoteException;
    public java.lang.String getServiceListNew(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String count, java.lang.String sortsign, java.lang.String times) throws java.rmi.RemoteException;
    public java.lang.String getServiceList(java.lang.String deptcode, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String getObjectToStr(java.lang.Object obj) throws java.rmi.RemoteException;
    public java.lang.String getServiceSimpleList(java.lang.String deptcode, java.lang.String password, java.lang.String count, java.lang.String sortsign, java.lang.String times) throws java.rmi.RemoteException;
    public java.lang.String getServiceSimpleListByChange(java.lang.String deptcode, java.lang.String password, java.lang.String count, java.lang.String sortsign, java.lang.String times, java.lang.String start_time, java.lang.String end_time) throws java.rmi.RemoteException;
    public java.lang.String getServiceNew(java.lang.String deptcode, java.lang.String password, java.lang.String serviceunid) throws java.rmi.RemoteException;
    public java.lang.String getServiceFileListNew(java.lang.String deptcode, java.lang.String password, java.lang.String materialunid) throws java.rmi.RemoteException;
    public java.lang.String getInfoListNew(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String handlestatesing, java.lang.String count, java.lang.String sortsign, java.lang.String times) throws java.rmi.RemoteException;
    public boolean voidTime2(java.lang.String starttime, java.lang.String endtime) throws java.rmi.RemoteException;
    public void getApasInfoXml(java.lang.Object sb, com.linewell.apas.info.ApasInfo apasinfo) throws java.rmi.RemoteException;
    public java.lang.String getInfoListByMonitor(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String handlestatesing, java.lang.String count, java.lang.String sortsign, java.lang.String times) throws java.rmi.RemoteException;
    public java.lang.String getInfoCountNew(java.lang.String deptcode, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String getInfoByProjid(java.lang.String deptcode, java.lang.String password, java.lang.String projid) throws java.rmi.RemoteException;
    public java.lang.String getInfoByProjidNew(java.lang.String projid, java.lang.String projpwd) throws java.rmi.RemoteException;
    public java.lang.String deleteFavorite(java.lang.String unid) throws java.rmi.RemoteException;
    public java.lang.String getOpinionList(java.lang.String deptcode, java.lang.String password, java.lang.String projid) throws java.rmi.RemoteException;
    public java.lang.String getAllCityServiceList(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String count, java.lang.String sortsign, java.lang.String times, java.lang.String cityDeptCode) throws java.rmi.RemoteException;
    public java.lang.String getAllCityInfoListNew(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String handlestatesing, java.lang.String count, java.lang.String sortsign, java.lang.String times, java.lang.String cityDeptCode) throws java.rmi.RemoteException;
    public java.lang.String validatesParam(java.lang.String deptcode, java.lang.String password, java.lang.String size, java.lang.String current) throws java.rmi.RemoteException;
    public java.lang.String getServiceListByCreditCode(java.lang.String deptcode, java.lang.String password, java.lang.String size, java.lang.String current, java.lang.String creditCode) throws java.rmi.RemoteException;
    public java.lang.String getServiceListByCreditCodeAndCondition(java.lang.String deptcode, java.lang.String password, java.lang.String size, java.lang.String current, java.lang.String creditCode, java.lang.String condition) throws java.rmi.RemoteException;
    public java.lang.String getServiceListByChange(java.lang.String deptCode, java.lang.String passWord, java.lang.String size, java.lang.String current, java.lang.String creditCode, java.lang.String start_time, java.lang.String end_time) throws java.rmi.RemoteException;
    public java.lang.String voidTime(java.lang.String btime, java.lang.String etime) throws java.rmi.RemoteException;
    public java.lang.String getServiceListByChangeAndCondition(java.lang.String deptCode, java.lang.String passWord, java.lang.String size, java.lang.String current, java.lang.String creditCode, java.lang.String start_time, java.lang.String end_time, java.lang.String condition) throws java.rmi.RemoteException;
    public java.lang.String getTradesDeptCode(java.lang.String deptcode, java.lang.String password, java.lang.String trades) throws java.rmi.RemoteException;
    public java.lang.String getServiceListByEngineering(java.lang.String citycode, java.lang.String password, java.lang.String size, java.lang.String current, java.lang.String isInit) throws java.rmi.RemoteException;
    public java.lang.String getServiceDetailByServiceUnid(java.lang.String deptCode, java.lang.String passWord, java.lang.String serviceUnid) throws java.rmi.RemoteException;
    public java.lang.String getSNbyRowId(java.lang.String deptcode, java.lang.String password, java.lang.String rowid) throws java.rmi.RemoteException;
    public boolean getTureTime() throws java.rmi.RemoteException;
    public java.lang.String getServiceListByChangeforzj(java.lang.String size, java.lang.String current, java.lang.String creditCode, java.lang.String start_time, java.lang.String end_time) throws java.rmi.RemoteException;
    public java.lang.String geDeptByAll(java.lang.String areaCode) throws java.rmi.RemoteException;
    public java.lang.String getServiceListByCreditCodeforzj(java.lang.String size, java.lang.String current, java.lang.String creditCode) throws java.rmi.RemoteException;
    public java.lang.String getServiceDetailByServiceUnidforzj(java.lang.String serviceUnid) throws java.rmi.RemoteException;
    public java.lang.String getServiceCataRelationListNew(java.lang.String deptcode, java.lang.String password, java.lang.String serviceunid) throws java.rmi.RemoteException;
    public java.lang.String getDirectoryList(java.lang.String deptcode, java.lang.String password) throws java.rmi.RemoteException;
    public java.lang.String getPromisesSerivce() throws java.rmi.RemoteException;
    public java.lang.String getPromisesSerivce(java.lang.String deptCode, java.lang.String size, java.lang.String current, java.lang.String token, java.lang.String serviceName, java.lang.String infoprojid) throws java.rmi.RemoteException;
    public boolean checkToken(java.lang.String detpcode, java.lang.String token) throws java.rmi.RemoteException;
    public java.lang.String getJGSerivce(java.lang.String deptCode, java.lang.String type, java.lang.String size, java.lang.String current, java.lang.String token, java.lang.String serviceName, java.lang.String infoprojid) throws java.rmi.RemoteException;
    public java.lang.String getJGService(java.lang.String type) throws java.rmi.RemoteException;
    public java.lang.String getPromisesSerivceByTime(java.lang.String date, java.lang.String size, java.lang.String current, java.lang.String token, java.lang.String serviceName, java.lang.String infoprojid) throws java.rmi.RemoteException;
    public java.lang.String getJGServiceByTime(java.lang.String date, java.lang.String type, java.lang.String size, java.lang.String current, java.lang.String token, java.lang.String serviceName, java.lang.String infoprojid) throws java.rmi.RemoteException;
    public java.lang.String getServiceBydirUnid(java.lang.String dirUnid, java.lang.String deptCode) throws java.rmi.RemoteException;
    public java.lang.String getDirectoryByDeptCode(java.lang.String deptcode) throws java.rmi.RemoteException;
    public java.lang.String getServiceByInfoprojid(java.lang.String infoprojid) throws java.rmi.RemoteException;
    public java.lang.String getdirBydirUnid(java.lang.String dirUnid) throws java.rmi.RemoteException;
    public void downFileByUnid(java.lang.String attrUnid) throws java.rmi.RemoteException;
}
