package blockChain.repository;

import blockChain.dto.StatisticDto;
import blockChain.entities.*;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringTemplate;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:42
 */
public class CopyrightOwnerRepositoryCustomImpl extends QuerydslRepositorySupport implements CopyrightOwnerRepositoryCustom {
  public CopyrightOwnerRepositoryCustomImpl() {
    super(CopyrightOwner.class);
  }

  @Override
  public List<StatisticDto> getCompanies(PageRequest of, Predicate... predicate) {
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, owner.copyIdCard.as("key"), owner.copyName.as("name"), owner.manager.countDistinct().as("amount")))
      .from(owner)
      .where(predicate)
      .groupBy(owner.copyIdCard)
      .orderBy(owner.copyIdCard.count().desc())
      .limit(of.getPageSize())
      .offset(of.getOffset())
      .fetch();
  }

  @Override
  public List<StatisticDto> getAgent(long limit,Predicate... predicate) {
    QAgent agent = QAgent.agent;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, agent.agentName.as("key"), agent.agentName.as("name"), agent.agentName.count().as("amount")))
      .from(agent)
      .where(predicate)
      .groupBy(agent.agentName)
      .limit(limit)
      .fetch();
  }
  @Override
  public List<StatisticDto> getOwner(long limit, Predicate... predicate) {
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, owner.copyIdCard.as("key"), owner.copyName.as("name"), owner.copyIdCard.count().as("amount")))
      .from(owner)
      .where(predicate)
      .groupBy(owner.copyIdCard)
      .orderBy(owner.copyIdCard.count().desc())
      .limit(limit)
      .fetch();
  }

  @Override
  public Long countCompanies(Predicate... predicate) {
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    return
    getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, owner.copyIdCard.as("key"), owner.copyName.as("name"), owner.manager.count().as("amount")))
      .from(owner)
      .where(predicate)
      .groupBy(owner.copyIdCard)
      .fetchCount();
  }


  @Override
  public List<StatisticDto> getProductionTypes(Predicate... predicate) {
    QCopyrightManager manager = QCopyrightManager.copyrightManager;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QDigital digital = QDigital.digital;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, manager.productionTypeId.as("key"), digital.dict_name.as("name"), manager.registrationNum.countDistinct().as("amount")))
      .from(manager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
        .on(manager.productionTypeId.eq(digital.id.stringValue()))
      .where(predicate)
      .groupBy(digital.id)
      .fetch()
      ;
  }

  @Override
  public List<StatisticDto> getProductionTypesByFujian(Predicate... predicate) {
    QCopyrightManager manager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, manager.productionTypeId.as("key"), manager.productionTypeId.stringValue().as("name"), manager.registrationNum.countDistinct().as("amount")))
      .from(manager)
      .where(predicate)
      .groupBy(manager.productionTypeId)
      .fetch()
      ;
  }

  @Override
  public List<StatisticDto> getAreaDistributedForFujian(Predicate... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), copyrightManager.registrationNum.count().as("amount")))
      .from(copyrightManager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(copyrightManager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
      .on(owner.copyCity.eq(digital.id))
      .where(predicate)
      .groupBy(owner.copyCity)
      .orderBy(copyrightManager.registrationNum.count().desc())
      .fetch();
  }

  @Override
  public StatisticDto getAreaDistributedCountry(Predicate... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), copyrightManager.registrationNum.count().as("amount")))
      .from(copyrightManager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(copyrightManager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
      .on(owner.copyCountries.eq(digital.id))
      .where(predicate)
      .fetchOne();
  }

  @Override
  public StatisticDto getAreaDistributedProvince(Predicate... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), copyrightManager.registrationNum.count().as("amount")))
      .from(copyrightManager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(copyrightManager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
      .on(owner.copyProvince.eq(digital.id))
      .where(predicate)
      .fetchOne();
  }

  @Override
  public StatisticDto getAreaDistributedCity(Predicate... predicate) {
    QDigital digital = QDigital.digital;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;

    return getQuerydsl().createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), copyrightManager.registrationNum.count().as("amount")))
      .from(copyrightManager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(copyrightManager.registrationNum).and(owner.seq.eq(1)))
      .leftJoin(digital)
      .on(owner.copyCity.eq(digital.id))
      .where(predicate)
      .fetchOne();
  }

  @Override
  public List<CopyrightOwner> getOwnerFilled(Predicate... predicate) {
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
    QCopyrightManager manager = QCopyrightManager.copyrightManager;
    QUserEntity userEntity = QUserEntity.userEntity;
    return getQuerydsl().createQuery()
      .select(QCopyrightOwner.copyrightOwner)
      .from(owner)
      .leftJoin(manager)
      .on(owner.manager.registrationNum.eq(manager.registrationNum))
      .leftJoin(userEntity)
      .on(manager.userName.eq(userEntity.userName))
      .where(predicate)
      .groupBy(owner.copyIdCard)
      .fetch();
  }

  @Override
  public long countBy(Predicate... predicate) {
    QCopyrightManager manager = QCopyrightManager.copyrightManager;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    return getQuerydsl().createQuery()
      .select(manager.registrationNum.count())
      .from(manager)
      .leftJoin(owner)
      .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
      .where(predicate)
      .fetchOne()
      ;
  }

  /*@Override
  public List<CopyrightOwner> getByCopyrightId(Long copyrightId) {
    QCopyrightManager manager = QCopyrightManager.copyrightManager;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    return getQuerydsl().createQuery()
      .select(QCopyrightOwner.copyrightOwner)
      .from(owner)
      .leftJoin(manager)
      .on(owner.manager.registrationNum.eq(manager.registrationNum))
      .fetch()
      ;
  }*/

  @Override
  public List<StatisticDto> countListBy(Predicate predicate, Boolean isYear) {
    QCopyrightManager manager = QCopyrightManager.copyrightManager;
    QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;

    String format = "%Y-%m";
    if (isYear) format = "%Y";
    StringTemplate certificateCreateTimeExpr = Expressions.stringTemplate("DATE_FORMAT({0},'"+ format + "')",manager.certificateCreateTime);

    return getQuerydsl().createQuery()
            .select(Projections.constructor(StatisticDto.class,
                    certificateCreateTimeExpr.as("id"),
                    certificateCreateTimeExpr.as("key"),
                    certificateCreateTimeExpr.as("name"),
                    manager.count().as("amount"),
                    Expressions.asBoolean(false)))
            .from(manager)
            .leftJoin(owner)
            .on(owner.manager.registrationNum.eq(manager.registrationNum).and(owner.seq.eq(1)))
            .where(predicate)
            .groupBy(certificateCreateTimeExpr)
            .orderBy(certificateCreateTimeExpr.asc())
            .fetch()
            ;
  }
}
