package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_call_back")
public class CallBackEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;//

    // 流程id
    private String processId;

    // 完成状态 0-已完成 1-未完成
    private Integer state;

    private String message;

    // 创建时间
    @CreatedDate
    private LocalDateTime createTime;


    public interface State {
        Integer FINISHED = 0;
        Integer UNFINISHED = 1;
    }


}
