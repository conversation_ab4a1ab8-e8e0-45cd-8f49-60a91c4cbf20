package blockChain.dto;

import blockChain.entities.CopyrightManager;
import blockChain.entities.ProcessRecord;
import blockChain.entities.UploadAttachment;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class WorksReportDto {

	private Long id;// 登记号
	private Integer reportStatus;// 状态
	private LocalDateTime createTime; //创建时间
	private LocalDateTime updateTime; //更新时间
	private LocalDateTime certificateCreateTime; //证书生成时间
	private String onBlockChain;  //上链凭证;
	private String reportDescript; //举报图描述
	private CopyrightManager copyrightManager;
	private UploadAttachment reportImg;
	private UploadAttachment tortReport;
	private List<ProcessRecord> flowRecord=null;
	private boolean updateFlag;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getReportStatus() {
		return reportStatus;
	}

	public void setReportStatus(Integer reportStatus) {
		this.reportStatus = reportStatus;
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public LocalDateTime getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(LocalDateTime updateTime) {
		this.updateTime = updateTime;
	}

	public LocalDateTime getCertificateCreateTime() {
		return certificateCreateTime;
	}

	public void setCertificateCreateTime(LocalDateTime certificateCreateTime) {
		this.certificateCreateTime = certificateCreateTime;
	}

	public String getOnBlockChain() {
		return onBlockChain;
	}

	public void setOnBlockChain(String onBlockChain) {
		this.onBlockChain = onBlockChain;
	}

	public CopyrightManager getCopyrightManager() {
		return copyrightManager;
	}

	public void setCopyrightManager(CopyrightManager copyrightManager) {
		this.copyrightManager = copyrightManager;
	}

	public UploadAttachment getReportImg() {
		return reportImg;
	}

	public void setReportImg(UploadAttachment reportImg) {
		this.reportImg = reportImg;
	}

	public UploadAttachment getTortReport() {
		return tortReport;
	}

	public void setTortReport(UploadAttachment tortReport) {
		this.tortReport = tortReport;
	}

	public List<ProcessRecord> getFlowRecord() {
		return flowRecord;
	}

	public void setFlowRecord(List<ProcessRecord> flowRecord) {
		this.flowRecord = flowRecord;
	}

	public String getReportDescript() {
		return reportDescript;
	}

	public void setReportDescript(String reportDescript) {
		this.reportDescript = reportDescript;
	}

	public boolean isUpdateFlag() {
		return updateFlag;
	}

	public void setUpdateFlag(boolean updateFlag) {
		this.updateFlag = updateFlag;
	}
}
