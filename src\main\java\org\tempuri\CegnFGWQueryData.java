
package org.tempuri;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 *
 */
@WebServiceClient(name = "Cegn_FGWQueryData", targetNamespace = "http://tempuri.org/", wsdlLocation = "http://hjpt.fjzwt.cn:9000/CegnQuery/Cegn_FGWQueryData.asmx?wsdl")
public class CegnFGWQueryData
    extends Service
{

    private final static URL CEGNFGWQUERYDATA_WSDL_LOCATION;
    private final static WebServiceException CEGNFGWQUERYDATA_EXCEPTION;
    private final static QName CEGNFGWQUERYDATA_QNAME = new QName("http://tempuri.org/", "Cegn_FGWQueryData");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://hjpt.fjzwt.cn:9000/CegnQuery/Cegn_FGWQueryData.asmx?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        CEGNFGWQUERYDATA_WSDL_LOCATION = url;
        CEGNFGWQUERYDATA_EXCEPTION = e;
    }

    public CegnFGWQueryData() {
        super(__getWsdlLocation(), CEGNFGWQUERYDATA_QNAME);
    }

    public CegnFGWQueryData(WebServiceFeature... features) {
        super(__getWsdlLocation(), CEGNFGWQUERYDATA_QNAME, features);
    }

    public CegnFGWQueryData(URL wsdlLocation) {
        super(wsdlLocation, CEGNFGWQUERYDATA_QNAME);
    }

    public CegnFGWQueryData(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, CEGNFGWQUERYDATA_QNAME, features);
    }

    public CegnFGWQueryData(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public CegnFGWQueryData(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     *
     * @return
     *     returns CegnFGWQueryDataSoap
     */
    @WebEndpoint(name = "Cegn_FGWQueryDataSoap")
    public CegnFGWQueryDataSoap getCegnFGWQueryDataSoap() {
        return super.getPort(new QName("http://tempuri.org/", "Cegn_FGWQueryDataSoap"), CegnFGWQueryDataSoap.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns CegnFGWQueryDataSoap
     */
    @WebEndpoint(name = "Cegn_FGWQueryDataSoap")
    public CegnFGWQueryDataSoap getCegnFGWQueryDataSoap(WebServiceFeature... features) {
        return super.getPort(new QName("http://tempuri.org/", "Cegn_FGWQueryDataSoap"), CegnFGWQueryDataSoap.class, features);
    }

    private static URL __getWsdlLocation() {
        if (CEGNFGWQUERYDATA_EXCEPTION!= null) {
            throw CEGNFGWQUERYDATA_EXCEPTION;
        }
        return CEGNFGWQUERYDATA_WSDL_LOCATION;
    }

}
