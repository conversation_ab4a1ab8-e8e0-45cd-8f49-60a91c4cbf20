package blockChain.bean;

/**
 * Created by epcsoft on 2020/8/17.
 */

public class IDCheckRawXML
{
  private final String templateXml="<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
    "<root>\n" +
    "<guid>[GUID]</guid>\n" +
    "<JLS>1</JLS>\n" +
    "<QQYWLXDM>JZZ</QQYWLXDM>\n" +
    "<QQYWLXMC>居住证办理</QQYWLXMC>\n" +
    "<DATA>\n" +
    "<RECORD no=\"1\">\n" +
    "<GMSFHM>[IDNO]</GMSFHM>\n" +
    "<XM>[IDNAME]</XM>\n" +
    "</RECORD>\n" +
    "</DATA>\n" +
    "</root>\n";
    public String getXML(String guid,String IDNo,String IDName)
    {
      return templateXml.replace("[GUID]",guid).replace("[IDNO]",IDNo).replace("[IDNAME]",IDName);
    }
}
