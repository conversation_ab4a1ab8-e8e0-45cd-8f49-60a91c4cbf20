package blockChain.controller;

import blockChain.bean.BaseResponseDto;
import blockChain.bean.PageResponse;
import blockChain.dto.message.AlertMessageDto;
import blockChain.dto.message.AlertMessageDtoQueryParam;
import blockChain.dto.message.QAMessageDtoQueryParam;
import blockChain.dto.message.QuestionAnswerMessageDto;
import blockChain.facade.service.QuestionAnswerMessageServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR> Chan
 * @date 2020/4/13 9:41
 */
@Api("智能问答")
@Slf4j
@RestController
@RequestMapping("qa_message")
@AllArgsConstructor
public class QuestionAnswerMessageController {
  private QuestionAnswerMessageServiceFacade serviceFacade;
  @ApiOperation(value = "创建", nickname = "createQAMessage", notes = "Creates a new instance of a `QAMessage`.", tags={ "智能问答(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("create")
  private ResponseEntity<BaseResponseDto> createQAMessage(@Valid @RequestBody QuestionAnswerMessageDto dto) {
    serviceFacade.create(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  @ApiOperation(value = "删除", nickname = "deleteQAMessage", notes = "删除的时候只要传入UUID即可", tags={ "智能问答(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("remove")
  public ResponseEntity<BaseResponseDto> deleteQAMessage(@Valid @RequestBody QuestionAnswerMessageDto dto) {
    serviceFacade.delete(dto.getUuid());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }

  @ApiOperation(value = "修改", nickname = "updateQAMessage", notes = " 使用UUID标识唯一消息", tags={ "智能问答(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 202, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("update")
  public ResponseEntity<BaseResponseDto> updateQAMessage(@Valid @RequestBody QuestionAnswerMessageDto dto) {
    serviceFacade.update(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  @ApiOperation(value = "查询单个", nickname = "getOneQAMessage", notes = " 使用UUID标识唯一消息", tags={ "智能问答(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("get")
  public ResponseEntity<QuestionAnswerMessageDto> getOneQAMessage(@Valid @RequestBody AlertMessageDto dto) {
    Assert.notNull(dto.getUuid(), "UUID不能为空");
    QuestionAnswerMessageDto one = serviceFacade.getOne(dto.getUuid());
    return ResponseEntity.ok(one);
  }


  @ApiOperation(value = "query", nickname = "queryQAMessage", notes = "query", tags={ "智能问答(管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("query")
  public ResponseEntity<PageResponse<QuestionAnswerMessageDto>> queryQAMessage(@Valid @RequestBody QAMessageDtoQueryParam dto) {
    PageResponse<QuestionAnswerMessageDto> page = serviceFacade.queryQAMessage(dto);
    return ResponseEntity.ok(page);
  }


  @ApiOperation(value = "questionAndAnswer", nickname = "questionAndAnswer", notes = "query", tags={ "智能问答(用户提问)", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("questionAndAnswer")
  public ResponseEntity<PageResponse<QuestionAnswerMessageDto>> questionAndAnswer(@Valid @RequestBody QAMessageDtoQueryParam dto) {
    PageResponse<QuestionAnswerMessageDto> page = serviceFacade.questionAndAnswer(dto);
    return ResponseEntity.ok(page);
  }
}
