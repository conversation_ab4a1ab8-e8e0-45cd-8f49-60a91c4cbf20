package blockChain.entities;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Entity
@Data
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name = "tb_form_data")
public class FormDataEntity {

    /**
     * 中枢表单用数ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 中枢表单字段数据
     * [{"standardFieldBody":"CJJS103","standardFieldName":"作品名称","standardFieldValue":null,"standardFieldCode":"99999901520","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品类别","standardFieldValue":null,"standardFieldCode":"99999901521","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"申请人","standardFieldValue":null,"standardFieldCode":"50000059454","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"著作权人署名情况","standardFieldValue":null,"standardFieldCode":"99999901528","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"著作权人所在城市","standardFieldValue":"台中市","standardFieldCode":"99999901527","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"著作权人所在省份","standardFieldValue":null,"standardFieldCode":"99999901526","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"著作权人国籍","standardFieldValue":null,"standardFieldCode":"99999901525","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"著作权人身份证件号码","standardFieldValue":null,"standardFieldCode":"99999901524","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"著作权人身份证件类型","standardFieldValue":null,"standardFieldCode":"99999901523","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"著作权人作品类别","standardFieldValue":null,"standardFieldCode":"99999901522","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"创作/制作完成日期","standardFieldValue":null,"standardFieldCode":"99999901530","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"创作/制作完成地点","standardFieldValue":null,"standardFieldCode":"99999901531","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品首次发表日期","standardFieldValue":null,"standardFieldCode":"99999901532","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品发表地点城市","standardFieldValue":null,"standardFieldCode":"99999901534","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品署名","standardFieldValue":null,"standardFieldCode":"99999901529","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"联系电话","standardFieldValue":"15168581578","standardFieldCode":"99999990197","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"联系地址","standardFieldValue":null,"standardFieldCode":"99999993229","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"邮政编码","standardFieldValue":"350000","standardFieldCode":"99999990042","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"姓名","standardFieldValue":"李清扬","standardFieldCode":"99999990004","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"手机号码","standardFieldValue":"15168581578","standardFieldCode":"99999990011","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"电子邮箱","standardFieldValue":null,"standardFieldCode":"99999990031","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"联系传真","standardFieldValue":null,"standardFieldCode":"99999995007","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品著作权登记被委托人联系传真","standardFieldValue":null,"standardFieldCode":"99999901542","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品著作权登记被委托人电子邮箱","standardFieldValue":null,"standardFieldCode":"99999901541","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品著作权登记被委托人手机号码","standardFieldValue":null,"standardFieldCode":"99999901540","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品著作权登记被委托人姓名","standardFieldValue":null,"standardFieldCode":"99999901539","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品著作权登记被委托人邮政编码","standardFieldValue":null,"standardFieldCode":"99999901538","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品著作权登记被委托人联系地址","standardFieldValue":null,"standardFieldCode":"99999901537","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品著作权登记被委托人联系电话","standardFieldValue":null,"standardFieldCode":"99999901536","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"作品著作权登记代理申请人","standardFieldValue":null,"standardFieldCode":"99999901535","shareSource":null},{"standardFieldBody":"CJJS103","standardFieldName":"著作权人姓名","standardFieldValue":null,"standardFieldCode":"999999901889","shareSource":null}],"formData":[]
     */
    private String fieldData;
    /**
     * 用户类型，1：个人；2：企业
     */
    private String userType;
    /**
     * 证件号码
     */
    private String certificateNumber;
    /**
     * 证件类别 用户类型个人时，必须；111：身份证 414：普通护照 516：港澳居民往来内地通行证 511：台湾居民往来内地通行证
     */
    private String idCardType;
    /**
     * 申请人
     * standardFieldCode: 50000059454
     */
    private String applyName;
    /**
     * 姓名
     * standardFieldCode: 99999990004
     */
    private String name;
    /**
     * 著作权人国籍
     * standardFieldCode: 99999901525
     */
    private String countries;
    /**
     * 著作权人所在省份
     * standardFieldCode: 99999901526
     */
    private String province;// 省份
    /**
     * 著作权人所在城市
     * standardFieldCode: 99999901527
     */
    private String city;// 所在地级市
    /**
     * 著作权人所在区县
     * standardFieldCode:99999991572
     */
    private String county;// 所在区县

    private String fullAddress;// 联系地址

    /**
     * 国籍编码
     */
    private Integer certCountries = 0;

    /**
     * 省份编码
     */
    private Integer certProvince = 0;

    /**
     * 城市编码
     */
    private Integer certCity = 0;

    /**
     * 区县编码
     */
    private Integer certCounty = 0;
    /**
     * 创建者
     */
    private Integer createBy;
    /**
     * 创建时间
     */
    @CreatedDate
    private LocalDateTime createTime;
    /**
     * 更新者
     */
    private Integer updateBy;
    /**
     * 更新时间
     */
    @LastModifiedDate
    private LocalDateTime updateTime;

}
