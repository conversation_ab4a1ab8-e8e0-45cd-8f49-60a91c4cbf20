<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true">
  <include resource="org/springframework/boot/logging/logback/base.xml"/>

  <!-- The FILE and ASYNC appenders are here as examples for a production configuration -->
  <!--
	  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		  <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			  <fileNamePattern>logFile.%d{yyyy-MM-dd}.log</fileNamePattern>
			  <maxHistory>90</maxHistory>
		  </rollingPolicy>
		  <encoder>
			  <charset>utf-8</charset>
			  <Pattern>%d %-5level [%thread] %logger{0}: %msg%n</Pattern>
		  </encoder>
	  </appender>

	  <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
		  <queueSize>512</queueSize>
		  <appender-ref ref="FILE"/>
	  </appender>
  -->
  <springProperty scope="context" name="FILE_PATH" source="ctsi.access.filePath"/>
  <springProperty scope="context" name="URL" source="ctsi.access.url"/>

  <appender name="STAT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${FILE_PATH}/stat.log</file>
    <append>true</append>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${FILE_PATH}/stat-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxHistory>200</maxHistory>
      <maxFileSize>20MB</maxFileSize>
    </rollingPolicy>
    <encoder>
      <pattern>%msg%n</pattern>
    </encoder>
  </appender>
  <!--写到文件 -->
  <Logger name="writeFile" level="INFO">
    <appender-ref ref="STAT_FILE"/>
  </Logger>
  <!-- 发送组件统计信息 -->
  <appender name="STAT_HTTP" class="com.ctsi.ssdc.access.HttpSenderAppender"></appender>
  <Logger name="httpSender" level="INFO">
    <appender-ref ref="STAT_HTTP"/>
  </Logger>

  <logger name="javax.activation" level="WARN"/>
  <logger name="javax.mail" level="WARN"/>
  <logger name="javax.xml.bind" level="WARN"/>
  <logger name="ch.qos.logback" level="WARN"/>
  <logger name="com.codahale.metrics" level="WARN"/>
  <logger name="com.ryantenney" level="WARN"/>
  <logger name="com.sun" level="WARN"/>
  <logger name="com.zaxxer" level="WARN"/>
  <logger name="io.undertow" level="WARN"/>
  <logger name="io.undertow.websockets.jsr" level="ERROR"/>
  <logger name="org.apache" level="WARN"/>
  <logger name="org.apache.http" level="DEBUG"/>
  <logger name="org.apache.catalina.startup.DigesterFactory" level="OFF"/>
  <logger name="org.bson" level="WARN"/>
  <logger name="org.hibernate.validator" level="WARN"/>
  <logger name="org.hibernate" level="WARN"/>
  <logger name="org.hibernate.ejb.HibernatePersistence" level="OFF"/>
  <logger name="org.hibernate.SQL" level="OFF"/>
  <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>
  <logger name="org.springframework" level="INFO"/>
  <logger name="org.springframework.web" level="INFO"/>
  <logger name="org.springframework.security" level="INFO"/>
  <logger name="org.springframework.cache" level="INFO"/>
  <logger name="org.thymeleaf" level="WARN"/>
  <logger name="org.xnio" level="WARN"/>
  <logger name="springfox" level="WARN"/>
  <logger name="sun.rmi" level="WARN"/>
  <logger name="liquibase" level="WARN"/>
  <logger name="LiquibaseSchemaResolver" level="INFO"/>
  <logger name="sun.rmi.transport" level="WARN"/>
  <logger name="com.netflix.turbine.monitor.cluster" level="INFO"/>
  <logger name="com.netflix.turbine.discovery" level="WARN"/>
  <logger name="org.reflections.Reflections" level="ERROR"/>
  <logger name="com.ctsi" level="INFO"/>

  <!-- https://logback.qos.ch/manual/configuration.html#shutdownHook and https://jira.qos.ch/browse/LOGBACK-1090 -->
  <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

  <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
    <resetJUL>true</resetJUL>
  </contextListener>

</configuration>
