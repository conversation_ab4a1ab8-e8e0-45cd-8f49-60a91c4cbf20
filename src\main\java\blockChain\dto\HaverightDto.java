package blockChain.dto;

import lombok.*;

@Data
public class HaverightDto {
    //权利拥有表
	private Long id;
	private String signatureBox;// 署名权
	private String publishedBox;// 发表权
	private String modifyBox;// 修改权
	private String protectBox;// 保护作品完整权
	private String copyBox;// 复制权
	private String issueBox;// 发行权
	private String rentBox;// 出租权
	private String exhibitionBox;// 展览权
	private String performanceBox;// 表演权
	private String screeningBox;// 放映权
	private String radioBox;// 广播权
	private String informationBox;// 信息网络传播权
	private String filmBox;// 摄制权
	private String adaptationBox;// 改编权
	private String translationBox;// 翻译权
	private String assemblyBox;// 汇编权
	private String otherBox;// 其他
	private String instructionsBox;// 其他说明

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getSignatureBox() {
		return signatureBox;
	}

	public void setSignatureBox(String signatureBox) {
		this.signatureBox = signatureBox;
	}

	public String getPublishedBox() {
		return publishedBox;
	}

	public void setPublishedBox(String publishedBox) {
		this.publishedBox = publishedBox;
	}

	public String getModifyBox() {
		return modifyBox;
	}

	public void setModifyBox(String modifyBox) {
		this.modifyBox = modifyBox;
	}

	public String getProtectBox() {
		return protectBox;
	}

	public void setProtectBox(String protectBox) {
		this.protectBox = protectBox;
	}

	public String getCopyBox() {
		return copyBox;
	}

	public void setCopyBox(String copyBox) {
		this.copyBox = copyBox;
	}

	public String getIssueBox() {
		return issueBox;
	}

	public void setIssueBox(String issueBox) {
		this.issueBox = issueBox;
	}

	public String getRentBox() {
		return rentBox;
	}

	public void setRentBox(String rentBox) {
		this.rentBox = rentBox;
	}

	public String getExhibitionBox() {
		return exhibitionBox;
	}

	public void setExhibitionBox(String exhibitionBox) {
		this.exhibitionBox = exhibitionBox;
	}

	public String getPerformanceBox() {
		return performanceBox;
	}

	public void setPerformanceBox(String performanceBox) {
		this.performanceBox = performanceBox;
	}

	public String getScreeningBox() {
		return screeningBox;
	}

	public void setScreeningBox(String screeningBox) {
		this.screeningBox = screeningBox;
	}

	public String getRadioBox() {
		return radioBox;
	}

	public void setRadioBox(String radioBox) {
		this.radioBox = radioBox;
	}

	public String getInformationBox() {
		return informationBox;
	}

	public void setInformationBox(String informationBox) {
		this.informationBox = informationBox;
	}

	public String getFilmBox() {
		return filmBox;
	}

	public void setFilmBox(String filmBox) {
		this.filmBox = filmBox;
	}

	public String getAdaptationBox() {
		return adaptationBox;
	}

	public void setAdaptationBox(String adaptationBox) {
		this.adaptationBox = adaptationBox;
	}

	public String getTranslationBox() {
		return translationBox;
	}

	public void setTranslationBox(String translationBox) {
		this.translationBox = translationBox;
	}

	public String getAssemblyBox() {
		return assemblyBox;
	}

	public void setAssemblyBox(String assemblyBox) {
		this.assemblyBox = assemblyBox;
	}

	public String getOtherBox() {
		return otherBox;
	}

	public void setOtherBox(String otherBox) {
		this.otherBox = otherBox;
	}

	public String getInstructionsBox() {
		return instructionsBox;
	}

	public void setInstructionsBox(String instructionsBox) {
		this.instructionsBox = instructionsBox;
	}
}
