
package blockChain.facade.service;


import blockChain.entities.DraftCopyrightManager;
import blockChain.service.DraftCopyrightManagerService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/12/3 9:45
 */

@Service
@AllArgsConstructor
public class DraftCopyrightManagerServiceFacade {
    @Autowired
    private DraftCopyrightManagerService draftCopyrightManagerService;

    @Transactional(rollbackFor = RuntimeException.class)
    public DraftCopyrightManager getById(Long id){
        return draftCopyrightManagerService.getById(id);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void createCopyright(DraftCopyrightManager draftCopyrightManager){
        draftCopyrightManagerService.saveOrUpdateCopyright(draftCopyrightManager);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public Page<DraftCopyrightManager> query(Long registrationNum, String productionName,
                                        String productionTypeId, Integer rightOwnMode,
                                        String startDate, String endDate, String userName, Integer page, Integer size){
        Sort sort = new Sort(Sort.Direction.DESC, "registrationNum");
        Pageable of = PageRequest.of(page,size,sort);
        return draftCopyrightManagerService.query(registrationNum,productionName,productionTypeId,rightOwnMode,startDate,endDate,userName,of);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void remove(DraftCopyrightManager draftCopyrightManager){ draftCopyrightManagerService.remove(draftCopyrightManager);}
}

