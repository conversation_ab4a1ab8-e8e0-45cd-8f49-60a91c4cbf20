package blockChain.facade.service;

import blockChain.bean.PageResponse;
import blockChain.controller.exception.VerificationFailedException;
import blockChain.dto.PaperCertificateDto;
import blockChain.dto.query.PaperCertificateDtoQueryParam;
import blockChain.entities.CopyrightManager;
import blockChain.entities.PaperCertificateEntity;
import blockChain.entities.UserEntity;
import blockChain.exception.EntityNotFoundException;
import blockChain.mapper.PaperCertificateDtoMapper;
import blockChain.repository.PaperCertificatePredicates;
import blockChain.service.CopyrightManagerService;
import blockChain.service.PaperCertificateService;
import blockChain.service.UserService;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2020/4/16 15:40
 */
@Service
@AllArgsConstructor
public class PaperCertificateServiceFacade {

  private CopyrightManagerService copyrightManagerService;
  private PaperCertificateService service;
  private UserService userService;

  /**
   * 申请证书
   *
   * @param dto
   */
  @Transactional
  public void create(PaperCertificateDto dto) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
    Assert.notNull(currentUserId, "匿名用户不允许申请");
    Assert.notNull(dto.getCopyrightId(), "作品不能为空");

    CopyrightManager copyrightManager = copyrightManagerService.getById(dto.getCopyrightId());

    UserEntity userEntity = userService.findById(currentUserId).orElseThrow(() -> new EntityNotFoundException("未找到该用户"));

    dto.setUuid(UUID.randomUUID().toString());
    PaperCertificateEntity entity = PaperCertificateDtoMapper.INSTANCE.toEntity(dto).setStatus(PaperCertificateEntity.StatusEnum.PENDING)
      .setCopyright(copyrightManager);
    service.save(entity);
    entity.setCreator(userEntity);
    entity.appendProcessRecords(
      PaperCertificateEntity.StatusEnum.PENDING.toString(),
      "申请证书",
      userEntity.getUserName(),
      "申请证书"
    );

  }

  /**
   * 删除申请
   * 物理删除
   *
   * @param uuid
   */
  @Transactional
  public void delete(String uuid) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
    Assert.notNull(currentUserId, "匿名用户不允许操作");

    PaperCertificateEntity entity = service.findByUuid(uuid).orElseThrow(EntityNotFoundException::new);

    Assert.isTrue(entity.getCreator().getUserId().equals(currentUserId), "不能删除非自己的申请");

    service.delete(entity);
  }

  /**
   * 驳回申请，不删除
   *
   * @param uuids
   * @param rejectReason
   * @param status
   */
  @Transactional
  public void handle(List<String> uuids, String rejectReason, PaperCertificateEntity.StatusEnum status) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
    Assert.notNull(currentUserId, "匿名用户不允许操作");
    Assert.notNull(status, "请选择操作类型");
    UserEntity userEntity = userService.findById(currentUserId).orElseThrow(() -> new EntityNotFoundException("未找到该用户"));

    uuids.forEach(uuid -> {
      PaperCertificateEntity entity = service.findByUuid(uuid).orElseThrow(EntityNotFoundException::new);
      switch (status) {
        case REJECT:
          entity.setStatus(PaperCertificateEntity.StatusEnum.REJECT);
          entity.appendProcessRecords(
            PaperCertificateEntity.StatusEnum.REJECT.toString(),
            "驳回",
            userEntity.getUserName(),
            rejectReason
          );
          break;
        case AWAITING:
          entity.setStatus(PaperCertificateEntity.StatusEnum.AWAITING);
          entity.appendProcessRecords(
            PaperCertificateEntity.StatusEnum.AWAITING.toString(),
            "等待寄送",
            userEntity.getUserName(),
            rejectReason
          );
          break;
      }

    });


  }

  /**
   * 查询我的申请
   *
   * @param dto
   * @return
   */
  public PageResponse<PaperCertificateDto> queryMinePaperCertificate(PaperCertificateDtoQueryParam dto) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
    Assert.notNull(currentUserId, "匿名用户不允许操作");

    Page<PaperCertificateEntity> page = service.findAll(PaperCertificatePredicates.statusEqAndCreateTimeBetween(currentUserId, dto.getStatus(), dto.getStartDate(), dto.getEndDate()), dto.getPageable());


    return PageResponse.of((long) page.getNumber() + 1, (long) page.getSize(), page.getTotalElements(), PaperCertificateDtoMapper.INSTANCE.toDto(page));
  }

  /**
   * 查询所有申请
   *
   * @param dto
   * @return
   */
  public PageResponse<PaperCertificateDto> queryPaperCertificate(PaperCertificateDtoQueryParam dto) {
    Page<PaperCertificateEntity> page = service.findAll(PaperCertificatePredicates.statusEqAndCreateTimeBetween(null, dto.getStatus(), dto.getStartDate(), dto.getEndDate()), dto.getPageable());
    return PageResponse.of((long) page.getNumber() + 1, (long) page.getSize(), page.getTotalElements(), PaperCertificateDtoMapper.INSTANCE.toDto(page));
  }

  public PaperCertificateDto getOne(String uuid) {
    PaperCertificateEntity entity = service.findByUuid(uuid).orElseThrow(EntityNotFoundException::new);
    return PaperCertificateDtoMapper.INSTANCE.toDto(entity);
  }

  public PageResponse<PaperCertificateDto> queryPaperCertificateForPassed(PaperCertificateDtoQueryParam dto) {
    Page<PaperCertificateEntity> page = service.findAll(
      PaperCertificatePredicates.statusEqAndCreateTimeBetweenAndStatusNotIn(
        null,
        dto.getStatus(),
        dto.getStartDate(),
        dto.getEndDate(),
        PaperCertificateEntity.StatusEnum.PENDING,
        PaperCertificateEntity.StatusEnum.REJECT
      ),
      dto.getPageable());
    return PageResponse.of((long) page.getNumber() + 1, (long) page.getSize(), page.getTotalElements(), PaperCertificateDtoMapper.INSTANCE.toDto(page));
  }

  /**
   * 重新提交申请
   *
   * @param uuid
   * @param reason
   */
  public void update(String uuid, String reason) {

    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
    Assert.notNull(currentUserId, "匿名用户不允许申请");
    Assert.notNull(uuid, "id不能为空");
    Assert.isTrue(!StringUtils.isEmpty(reason), "原因不能为空");

    PaperCertificateEntity entity = service.findByUuid(uuid).orElseThrow(EntityNotFoundException::new);
    if (
      PaperCertificateEntity.StatusEnum.REJECT.equals(entity.getStatus())
        ||
        PaperCertificateEntity.StatusEnum.AWAITING.equals(entity.getStatus())
    ) {
      entity.setStatus(PaperCertificateEntity.StatusEnum.AWAITING)
        .setReason(reason);
    } else {
      throw new VerificationFailedException("当前状态不可修改");
    }

  }

  @Transactional
  public void updateLogisticsNum(String uuid, String logisticsNum) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
    Assert.notNull(currentUserId, "匿名用户不允许操作");

    PaperCertificateEntity entity = service.findByUuid(uuid).orElseThrow(EntityNotFoundException::new);

    entity.setLogisticsNum(logisticsNum)
      .setLogisticsComCn("EMS")
      .setStatus(PaperCertificateEntity.StatusEnum.SEND)
      .setLogisticsComNum("EMS");

  }
}
