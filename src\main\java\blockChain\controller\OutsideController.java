package blockChain.controller;

import blockChain.dto.ebus.FormDataDTO;
import blockChain.dto.ebus.FormNumberQueryParam;
import blockChain.entities.UserEntity;
import blockChain.facade.service.OutSideServiceFacade;
import blockChain.facade.service.UserServiceFacade;
import cn.hutool.core.util.ObjectUtil;
import com.ctsi.ssdc.security.CscpUserDetail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api("调用外部接口")
@Slf4j
@RestController
@RequestMapping("outside")
@AllArgsConstructor
public class OutsideController {

    private final UserServiceFacade userServiceFacade;

    private final OutSideServiceFacade outSideServiceFacade;

    @ApiOperation("表单用数获取")
    @PostMapping("getFormData")
    public ResponseEntity<Map<String, Object>> getFormData(@Valid @RequestBody FormNumberQueryParam formNumberQueryParam) {
        log.info("表单用数获取开始：{}", formNumberQueryParam);
        Map<String, Object> resultMap = new HashMap<>();

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity userEntity = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());

        FormDataDTO dto = outSideServiceFacade.getFormDataDetail(formNumberQueryParam, userEntity);
        log.info("表单用数获取结束：{}", formNumberQueryParam);
        if (ObjectUtil.isEmpty(dto)) {
            resultMap.put("result", "failed");
            return new ResponseEntity<>(resultMap, HttpStatus.OK);
        } else {
            resultMap.put("data", dto);
            resultMap.put("result", "success");
            return new ResponseEntity<>(resultMap, HttpStatus.OK);
        }
    }
}
