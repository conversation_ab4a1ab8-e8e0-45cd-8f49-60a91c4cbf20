package blockChain.security;

import com.ctsi.ssdc.security.jwt.JWTConfigurer;
import com.ctsi.ssdc.security.jwt.TokenProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2019/12/18 15:27
 */
public class JWTFilter extends GenericFilterBean {

    private static final String AUTHORIZATION_PARAMETER = "token";

    private TokenProvider tokenProvider;

    public JWTFilter(TokenProvider tokenProvider) {
        this.tokenProvider = tokenProvider;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
        throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        String jwt = resolveToken(httpServletRequest);
        if (StringUtils.hasText(jwt) && this.tokenProvider.validateToken(jwt)) {
            Authentication authentication = this.tokenProvider.getAuthentication(jwt);
            SecurityContextHolder.getContext().setAuthentication(authentication);

        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    private String resolveToken(HttpServletRequest request){
        String bearerToken = request.getHeader(JWTConfigurer.AUTHORIZATION_HEADER);
        if (bearerToken == null) {
            bearerToken = request.getParameter(AUTHORIZATION_PARAMETER);
        }
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(JWTConfigurer.AUTHORIZATION_BEARER)) {
            return bearerToken.substring(JWTConfigurer.AUTHORIZATION_BEARER.length());
        }
        return null;
    }
}
