package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;

/**
 * 作品著作权，著作权人Bean
 */
@XmlRootElement
public class CopyrightOwnerBean {
	//姓名/单位名称  长度20
	private String name;
	//类别 1：自然人  21：企业法人  22：机关法人  23：事业法人  24：社会团体法人 3：其他组织  4：其他
	private Integer type;
	//国籍 对应字典表NCP_COUNTRY（ID）
	private Integer country;
	//省份 对应字典表NCP_ID_REG_LOCATION（ID）
	private Integer province;
	//城市 city  长度15
	private String city;
	//证件类型 对应字典表NCP_ID_TYPE（id）
	private Integer idType;
	//证件号码  长度30
	private String idCard;
	//署名情况  0：本名  1：别名  2：匿名
	private Integer sign;
	//别名  长度20（注：别名情况下显示该属性  赋值为""）
	private String opusSign;


	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getCountry() {
		return country;
	}
	public void setCountry(Integer country) {
		this.country = country;
	}
	public Integer getProvince() {
		return province;
	}
	public void setProvince(Integer province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public Integer getIdType() {
		return idType;
	}
	public void setIdType(Integer idType) {
		this.idType = idType;
	}
	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	public Integer getSign() {
		return sign;
	}
	public void setSign(Integer sign) {
		this.sign = sign;
	}
	public String getOpusSign() {
		return opusSign;
	}
	public void setOpusSign(String opusSign) {
		this.opusSign = opusSign;
	}
	public CopyrightOwnerBean(String name, Integer type, Integer country,
			Integer province, String city, Integer idType, String idCard,
			Integer sign, String opusSign) {
		this.name = name;
		this.type = type;
		this.country = country;
		this.province = province;
		this.city = city;
		this.idType = idType;
		this.idCard = idCard;
		this.sign = sign;
		this.opusSign = opusSign;
	}
	public CopyrightOwnerBean(){

	}

}
