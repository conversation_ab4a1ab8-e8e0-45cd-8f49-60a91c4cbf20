package blockChain.dto.query;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 * @date 2020/4/16 12:01
 */
@Data
public class StatementQueryParam {

  /**
   * 统计的月或者年
   */
  LocalDate monthlyOrYear;

  /**
   * 获取传入月份最后一天
   * @return
   */
  public LocalDateTime getLastMonthly(){
    if(monthlyOrYear == null){
      return  null;
    }
    return LocalDateTime.of(monthlyOrYear.with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
  }

  /**
   * 获取传入月份第一天
   * @return
   */
  public LocalDateTime getFirstMonthly(){
    if(monthlyOrYear == null){
      return  null;
    }
    return LocalDateTime.of(monthlyOrYear.with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
  }

  /**
   * 获取传入年份最后一天
   * @return
   */
  public LocalDateTime getLastYear(){
    if(monthlyOrYear == null){
      return  null;
    }
    return LocalDateTime.of(monthlyOrYear.with(TemporalAdjusters.lastDayOfYear()), LocalTime.MAX);
  }

  /**
   * 获取传入年份第一天
   * @return
   */
  public LocalDateTime getFirstYear(){
    if(monthlyOrYear == null){
      return  null;
    }
    return LocalDateTime.of(monthlyOrYear.with(TemporalAdjusters.firstDayOfYear()), LocalTime.MIN);
  }

  /**
   * 获取传入月份前一年当月最后一天
   * @return
   */
  public LocalDateTime getLastLastMonthly(){
    if(monthlyOrYear == null){
      return  null;
    }
    return LocalDateTime.of(monthlyOrYear.minusYears(1).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
  }

  /**
   * 获取传入月份前一年当月第一天
   * @return
   */
  public LocalDateTime getLastFirstMonthly(){
    if(monthlyOrYear == null){
      return  null;
    }
    return LocalDateTime.of(monthlyOrYear.minusYears(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
  }

  /**
   * 获取传入年份上一年最后一天
   * @return
   */
  public LocalDateTime getLastLastYear(){
    if(monthlyOrYear == null){
      return  null;
    }
    return LocalDateTime.of(monthlyOrYear.minusYears(1).with(TemporalAdjusters.lastDayOfYear()), LocalTime.MAX);
  }

  /**
   * 获取传入年份上一年第一天
   * @return
   */
  public LocalDateTime getLastFirstYear(){
    if(monthlyOrYear == null){
      return  null;
    }
    return LocalDateTime.of(monthlyOrYear.minusYears(1).with(TemporalAdjusters.firstDayOfYear()), LocalTime.MIN);
  }
}
