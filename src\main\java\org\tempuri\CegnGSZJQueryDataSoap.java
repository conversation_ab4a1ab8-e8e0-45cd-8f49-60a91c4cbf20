
package org.tempuri;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "Cegn_GSZJQueryDataSoap", targetNamespace = "http://tempuri.org/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface CegnGSZJQueryDataSoap {


    /**
     * 工商总局_企业基本信息查询接口服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryEntInfo", action = "http://tempuri.org/QueryEntInfo")
    @WebResult(name = "QueryEntInfoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryEntInfo", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryEntInfo")
    @ResponseWrapper(localName = "QueryEntInfoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryEntInfoResponse")
    public String queryEntInfo(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

}
