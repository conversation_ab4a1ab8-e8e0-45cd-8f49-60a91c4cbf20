package blockChain.mapper;

import blockChain.dto.EvaluateDto;
import blockChain.entities.EvaluateEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Created by epcsoft on 2020/8/6.
 */
@Mapper
public interface EvaluateDtoMapper
{
  EvaluateDtoMapper INSTANCE = Mappers.getMapper(EvaluateDtoMapper.class);

  EvaluateDto entityToDto(EvaluateEntity evaluateEntity);

  List<EvaluateDto> entityToDto(List<EvaluateEntity> haverightEntities);
}
