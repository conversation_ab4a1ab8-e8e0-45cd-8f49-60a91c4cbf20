package blockChain.config;


import blockChain.entities.CallBackEntity;
import blockChain.facade.service.CallBackServiceFacade;
import blockChain.repository.CallBackRepository;
import blockChain.service.CallBackService;
import blockChain.service.ProcessProgressService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class ApplicationCallBackImpl implements ApplicationRunner {

  private final CallBackServiceFacade facade;

  @Override
  public void run(ApplicationArguments args) throws Exception {
    facade.callBackScheduling();
  }
}
