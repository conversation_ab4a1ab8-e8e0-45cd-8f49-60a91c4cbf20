package blockChain.utils.umpay.mascloud.mgw.service.webservice.server;

import blockChain.utils.umpay.mascloud.mgw.service.webservice.server.data.SendSmsResponse;

import java.io.StringReader;
import java.security.MessageDigest;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import javax.xml.transform.stream.StreamSource;

/**
 * 单位名称：政企分公司测试
用户名：deckan
密码：passwd@4865
签名编码：DWItALe3A
--成功返回返回值
<?xml version="1.0" encoding="utf-8"?><SendSmsResponse>
  <success>true</success>
  <rspcod>success</rspcod>
  <msgGroup>1212090135001001657725</msgGroup>
</SendSmsResponse>
--失败返回
<?xml version="1.0" encoding="utf-8"?><SendSmsResponse>
  <success>false</success>
  <rspcod>InvalidMobile</rspcod>
</SendSmsResponse>


 * <AUTHOR>
 *
 */
public class SmsTesta2 {

	static String EcName="福建省委宣传部";
	static String apId="sxsms1";
	static String secretKey="sxsms@XU8K";
	static String mobiles="18259086771";	//计算，这里是占位符
	static String mobiles1="";	//计算，这里是占位符
	static String content="测试测试";		//计算，这里是占位符
	static String sign="7SKUpbpDP";
	static String addSerial="";


	public static void main() throws Exception {
		// TODO 自动生成的方法存根
		WsSmsServiceServiceLocator locateor=new WsSmsServiceServiceLocator();
		WsSmsService service = locateor.getWsSmsServicePort();
		String md5=EcName+apId+secretKey+mobiles+mobiles1+content+sign+addSerial;
		String encodeStr=MD5(md5);

		//10:34
		String xml="<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
				"<WsSubmitReq>" +
				"  <apId>"+apId+"</apId>" +
				"  <secretKey>"+secretKey+"</secretKey>" +
				"  <ecName>"+EcName+"</ecName>" +
				"  <mobiles>" +
				"    <string>"+mobiles+"</string>" ;
		if(!mobiles1.equals(""))xml+="<string>"+mobiles1+"</string>" ;
		xml+=	"  </mobiles>" +
				"  <content>"+content+"</content>" +
				"  <sign>"+sign+"</sign>" +
				"  <addSerial></addSerial>" +
				"  <mac>"+encodeStr.toLowerCase()+"</mac>" +
				"</WsSubmitReq>";

			String resultXML=service.sendSms(xml) ;

			StringReader reader = new StringReader(resultXML);
			JAXBContext jaxbContext = JAXBContext.newInstance(SendSmsResponse.class);
			Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
		      StreamSource xmlSource = new StreamSource(reader);
		      SendSmsResponse root = (SendSmsResponse) jaxbUnmarshaller.unmarshal(xmlSource,SendSmsResponse.class).getValue();
		      System.out.println( root );



	}

	private static String MD5(String s) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			byte[] bytes = md.digest(s.getBytes("utf-8"));
			return toHex(bytes);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	private static String toHex(byte[] bytes) {
		final char[] HEX_DIGITS = "0123456789ABCDEF".toCharArray();
		StringBuilder ret = new StringBuilder(bytes.length * 2);
		for (int i = 0; i < bytes.length; i++) {
			ret.append(HEX_DIGITS[(bytes[i] >> 4) & 0x0f]);
			ret.append(HEX_DIGITS[bytes[i] & 0x0f]);
		}
		return ret.toString();
	}
}
