
package blockChain.repository;

import blockChain.dto.message.AlertMessageDto;
import blockChain.entities.QCopyrightManager;
import blockChain.entities.message.QAlertMessage;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.Pageable;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * <AUTHOR> XXX
 * @date 2019/11/29.
 */

public final class AlertMessagePredicates {
  public static Predicate titleLikeAndContentLikeAndCreateTimeBetween(String title, String content, LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {

    QAlertMessage message = QAlertMessage.alertMessage;

    BooleanBuilder builder = new BooleanBuilder();

    if (!StringUtils.isEmpty(title)) {
      builder.and(message.title.contains(title));
    }

    if (!StringUtils.isEmpty(content)) {
      builder.and(message.content.contains(content));
    }

    /*if(createTimeStart != null && createTimeEnd != null){
      builder.and(message.createTime.between(createTimeStart, createTimeEnd));
    }*/
    if(createTimeStart != null){
      builder.and(message.createTime.after(createTimeStart));
    }
    if(createTimeEnd != null){
      builder.and(message.createTime.before(createTimeEnd));
    }

    return builder;
  }

  public static Predicate deadlineBeforeAndStatusIs(LocalDateTime now, AlertMessageDto.StatusEnum status,LocalDateTime start,LocalDateTime end) {
    QAlertMessage message = QAlertMessage.alertMessage;

    BooleanBuilder builder = new BooleanBuilder();

    if(now != null){
      builder.and(message.deadline.before(now));
    }
    if(status != null){
      builder.and(message.status.eq(status));
    }
    if(start!=null){
      builder.and(message.deadline.after(start));
    }
    if(end!=null){
      builder.and(message.deadline.before(end));
    }

    return builder;
  }

  public static Predicate titleAndCreateTimeBeforeAndStatusIs(String title, AlertMessageDto.StatusEnum status,LocalDateTime start,LocalDateTime end) {
    QAlertMessage message = QAlertMessage.alertMessage;

    BooleanBuilder builder = new BooleanBuilder();

    if (!StringUtils.isEmpty(title)) {
      builder.and(message.title.contains(title));
    }
    if(status != null){
      builder.and(message.status.eq(status));
    }
    if(start!=null){
      builder.and(message.createTime.after(start));
    }
    if(end!=null){
      builder.and(message.createTime.before(end));
    }

    return builder;
  }

}

