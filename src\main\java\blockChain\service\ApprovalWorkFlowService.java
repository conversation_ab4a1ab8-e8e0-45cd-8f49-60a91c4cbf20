package blockChain.service;

import blockChain.entities.ApprovalWorkFlowEntity;
import blockChain.repository.ApprovalWorkFlowRepository;
import com.querydsl.core.BooleanBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static blockChain.entities.QApprovalWorkFlowEntity.approvalWorkFlowEntity;

/**
 * (tb_approval_work_flow)服务接口
 *
 * <AUTHOR>
 * @since 2024-10-08 14:59:19
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ApprovalWorkFlowService implements BaseService<ApprovalWorkFlowRepository, ApprovalWorkFlowEntity, Long> {
    private final ApprovalWorkFlowRepository repository;

    @Override
    public ApprovalWorkFlowRepository getRepository() {
        return repository;
    }

    public List<ApprovalWorkFlowEntity> findAllResolve(byte resolveState, int pageSize) {
        PageRequest of = PageRequest.of(0, pageSize, new Sort(Sort.Direction.ASC, "id"));
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(approvalWorkFlowEntity.resolveState.eq(resolveState));
        return repository.findAll(builder, of).getContent();
    }

    public void deleteByResolveStateAndCreateTimeBefore(byte resolveState, LocalDateTime createTimeEnd) {
        repository.deleteByResolveStateAndCreateTimeBefore(resolveState, createTimeEnd);
    }
}
