package blockChain.repository;

import blockChain.entities.UploadAttachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/29 14:44
 */
@Repository
public interface UploadAttachmentRepository extends JpaRepository<UploadAttachment, Long>, QuerydslPredicateExecutor<UploadAttachment> {
  UploadAttachment getById(Long id);

    @Query(value = "select a.* from tb_attachments a left join copyright_attachment c on a.id = c.attachment_id where c.copyright_id = ?1", nativeQuery = true)
    List<UploadAttachment> getByCopyrightId(Long copyrightId);

    @Query(value = "select m.userName from tb_copyrightmanager m LEFT JOIN copyright_attachment ca on ca.copyright_id=m.registrationNum " +
            "LEFT JOIN tb_attachments a on a.id=ca.attachment_id where a.workUrl = ?1", nativeQuery = true)
    String getUserNameByWorkUrl(String workUrl);

    @Query(value = "select m.userName from tb_copyrightmanager m LEFT JOIN copyright_attachment ca on ca.copyright_id=m.registrationNum " +
            "LEFT JOIN tb_attachments a on a.id=ca.attachment_id where a.id = ?1", nativeQuery = true)
    String getUserNameByAttachmentId(Long attachmentId);

    @Query(value = "select a.userName from tb_attachments a where a.id = ?1", nativeQuery = true)
    String getUserNameById(Long attachmentId);

    @Query(value = "select a.copyright_id from copyright_attachment a where a.attachment_id = ?1", nativeQuery = true)
    List<Long> getCopyrightId(Long attachmentId);
}
