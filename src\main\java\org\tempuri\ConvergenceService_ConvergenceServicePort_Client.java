
package org.tempuri;

/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.4.4
 * 2021-09-28T16:24:54.157+08:00
 * Generated source version: 3.4.4
 *
 */
public final class ConvergenceService_ConvergenceServicePort_Client {

    private static final QName SERVICE_NAME = new QName("http://tempuri.org/", "ConvergenceServiceService");

    private ConvergenceService_ConvergenceServicePort_Client() {
    }

    public static void main(String args[]) throws java.lang.Exception {
        URL wsdlURL = ConvergenceServiceService.WSDL_LOCATION;
        if (args.length > 0 && args[0] != null && !"".equals(args[0])) {
            File wsdlFile = new File(args[0]);
            try {
                if (wsdlFile.exists()) {
                    wsdlURL = wsdlFile.toURI().toURL();
                } else {
                    wsdlURL = new URL(args[0]);
                }
            } catch (MalformedURLException e) {
                e.printStackTrace();
            }
        }

        ConvergenceServiceService ss = new ConvergenceServiceService(wsdlURL, SERVICE_NAME);
        ConvergenceService port = ss.getConvergenceServicePort();

        {
        System.out.println("Invoking allDeleteAndAllAdd...");
        java.lang.String _allDeleteAndAllAdd_guid = "";
        java.lang.String _allDeleteAndAllAdd_catalogid = "";
        java.lang.String _allDeleteAndAllAdd_xmlstr = "";
        java.lang.String _allDeleteAndAllAdd_type = "";
        java.lang.String _allDeleteAndAllAdd__return = port.allDeleteAndAllAdd(_allDeleteAndAllAdd_guid, _allDeleteAndAllAdd_catalogid, _allDeleteAndAllAdd_xmlstr, _allDeleteAndAllAdd_type);
        System.out.println("allDeleteAndAllAdd.result=" + _allDeleteAndAllAdd__return);


        }
        {
        System.out.println("Invoking loginByAccount...");
        java.lang.String _loginByAccount_userid = "";
        java.lang.String _loginByAccount_password = "";
        java.lang.String _loginByAccount__return = port.loginByAccount(_loginByAccount_userid, _loginByAccount_password);
        System.out.println("loginByAccount.result=" + _loginByAccount__return);


        }
        {
        System.out.println("Invoking getCatalogidByMatterid...");
        java.lang.String _getCatalogidByMatterid_guid = "";
        java.lang.String _getCatalogidByMatterid_matterid = "";
        java.lang.String _getCatalogidByMatterid__return = port.getCatalogidByMatterid(_getCatalogidByMatterid_guid, _getCatalogidByMatterid_matterid);
        System.out.println("getCatalogidByMatterid.result=" + _getCatalogidByMatterid__return);


        }
        {
        System.out.println("Invoking getSM4Key...");
        java.lang.String _getSM4Key_guid = "";
        java.lang.String _getSM4Key_sm2Publickey = "";
        java.lang.String _getSM4Key__return = port.getSM4Key(_getSM4Key_guid, _getSM4Key_sm2Publickey);
        System.out.println("getSM4Key.result=" + _getSM4Key__return);


        }
        {
        System.out.println("Invoking pushLargeXml...");
        java.lang.String _pushLargeXml_guid = "";
        java.lang.String _pushLargeXml_catalogid = "";
        java.lang.String _pushLargeXml_xmlstr = "";
        java.lang.String _pushLargeXml__return = port.pushLargeXml(_pushLargeXml_guid, _pushLargeXml_catalogid, _pushLargeXml_xmlstr);
        System.out.println("pushLargeXml.result=" + _pushLargeXml__return);


        }
        {
        System.out.println("Invoking getProblemCollect...");
        java.lang.String _getProblemCollect_webid = "";
        java.lang.String _getProblemCollect__return = port.getProblemCollect(_getProblemCollect_webid);
        System.out.println("getProblemCollect.result=" + _getProblemCollect__return);


        }
        {
        System.out.println("Invoking pushXmlBackflowInnerFun...");
        java.lang.String _pushXmlBackflowInnerFun_guid = "";
        java.lang.String _pushXmlBackflowInnerFun_catalogid = "";
        java.lang.String _pushXmlBackflowInnerFun_xmlstr = "";
        java.lang.String _pushXmlBackflowInnerFun_clientIp = "";
        java.lang.String _pushXmlBackflowInnerFun__return = port.pushXmlBackflowInnerFun(_pushXmlBackflowInnerFun_guid, _pushXmlBackflowInnerFun_catalogid, _pushXmlBackflowInnerFun_xmlstr, _pushXmlBackflowInnerFun_clientIp);
        System.out.println("pushXmlBackflowInnerFun.result=" + _pushXmlBackflowInnerFun__return);


        }
        {
        System.out.println("Invoking getTimeGroup...");
        java.lang.String _getTimeGroup_arg0 = "";
        java.lang.String _getTimeGroup__return = port.getTimeGroup(_getTimeGroup_arg0);
        System.out.println("getTimeGroup.result=" + _getTimeGroup__return);


        }
        {
        System.out.println("Invoking endAddAllDeleteAll...");
        java.lang.String _endAddAllDeleteAll_guid = "";
        java.lang.String _endAddAllDeleteAll_catalogid = "";
        java.lang.String _endAddAllDeleteAll__return = port.endAddAllDeleteAll(_endAddAllDeleteAll_guid, _endAddAllDeleteAll_catalogid);
        System.out.println("endAddAllDeleteAll.result=" + _endAddAllDeleteAll__return);


        }
        {
        System.out.println("Invoking crossNetCheck...");
        java.lang.String _crossNetCheck_guid = "";
        java.lang.String _crossNetCheck_catalogid = "";
        java.lang.String _crossNetCheck_xmlstr = "";
        java.lang.String _crossNetCheck__return = port.crossNetCheck(_crossNetCheck_guid, _crossNetCheck_catalogid, _crossNetCheck_xmlstr);
        System.out.println("crossNetCheck.result=" + _crossNetCheck__return);


        }
        {
        System.out.println("Invoking pushXml...");
        java.lang.String _pushXml_guid = "";
        java.lang.String _pushXml_catalogid = "";
        java.lang.String _pushXml_xmlstr = "";
        java.lang.String _pushXml__return = port.pushXml(_pushXml_guid, _pushXml_catalogid, _pushXml_xmlstr);
        System.out.println("pushXml.result=" + _pushXml__return);


        }
        {
        System.out.println("Invoking pushXmlBySM4...");
        java.lang.String _pushXmlBySM4_guid = "";
        java.lang.String _pushXmlBySM4_catalogid = "";
        java.lang.String _pushXmlBySM4_xmlstr = "";
        java.lang.String _pushXmlBySM4__return = port.pushXmlBySM4(_pushXmlBySM4_guid, _pushXmlBySM4_catalogid, _pushXmlBySM4_xmlstr);
        System.out.println("pushXmlBySM4.result=" + _pushXmlBySM4__return);


        }
        {
        System.out.println("Invoking pushXmlBackflow...");
        java.lang.String _pushXmlBackflow_guid = "";
        java.lang.String _pushXmlBackflow_catalogid = "";
        java.lang.String _pushXmlBackflow_xmlstr = "";
        java.lang.String _pushXmlBackflow__return = port.pushXmlBackflow(_pushXmlBackflow_guid, _pushXmlBackflow_catalogid, _pushXmlBackflow_xmlstr);
        System.out.println("pushXmlBackflow.result=" + _pushXmlBackflow__return);


        }
        {
        System.out.println("Invoking loginByAccountBySM2Key...");
        java.lang.String _loginByAccountBySM2Key_userinfo = "";
        java.lang.String _loginByAccountBySM2Key_sm2Publickey = "";
        java.lang.String _loginByAccountBySM2Key__return = port.loginByAccountBySM2Key(_loginByAccountBySM2Key_userinfo, _loginByAccountBySM2Key_sm2Publickey);
        System.out.println("loginByAccountBySM2Key.result=" + _loginByAccountBySM2Key__return);


        }
        {
        System.out.println("Invoking pushXmlInnerFun...");
        java.lang.String _pushXmlInnerFun_guid = "";
        java.lang.String _pushXmlInnerFun_catalogid = "";
        java.lang.String _pushXmlInnerFun_xmlstr = "";
        java.lang.String _pushXmlInnerFun_clientIp = "";
        java.lang.String _pushXmlInnerFun__return = port.pushXmlInnerFun(_pushXmlInnerFun_guid, _pushXmlInnerFun_catalogid, _pushXmlInnerFun_xmlstr, _pushXmlInnerFun_clientIp);
        System.out.println("pushXmlInnerFun.result=" + _pushXmlInnerFun__return);


        }
        {
        System.out.println("Invoking getSM2PublicKey...");
        java.lang.String _getSM2PublicKey__return = port.getSM2PublicKey();
        System.out.println("getSM2PublicKey.result=" + _getSM2PublicKey__return);


        }
        {
        System.out.println("Invoking pushInterAllXml...");
        java.lang.String _pushInterAllXml_guid = "";
        java.lang.String _pushInterAllXml_catalogid = "";
        java.lang.String _pushInterAllXml_xmlstr = "";
        java.lang.String _pushInterAllXml_type = "";
        java.lang.String _pushInterAllXml_ip = "";
        java.lang.String _pushInterAllXml_beLongNet = "";
        java.lang.String _pushInterAllXml__return = port.pushInterAllXml(_pushInterAllXml_guid, _pushInterAllXml_catalogid, _pushInterAllXml_xmlstr, _pushInterAllXml_type, _pushInterAllXml_ip, _pushInterAllXml_beLongNet);
        System.out.println("pushInterAllXml.result=" + _pushInterAllXml__return);


        }
        {
        System.out.println("Invoking getXmlByCatalogid...");
        java.lang.String _getXmlByCatalogid_guid = "";
        java.lang.String _getXmlByCatalogid_catalogid = "";
        java.lang.String _getXmlByCatalogid__return = port.getXmlByCatalogid(_getXmlByCatalogid_guid, _getXmlByCatalogid_catalogid);
        System.out.println("getXmlByCatalogid.result=" + _getXmlByCatalogid__return);


        }

        System.exit(0);
    }

}
