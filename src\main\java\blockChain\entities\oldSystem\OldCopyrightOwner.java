package blockChain.entities.oldSystem;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

/**
 * 著作权拥有者
 */
@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name = "old_copyrightowner")
public class OldCopyrightOwner {
  // 旧系统数据迁移用实体类，迁移后删除
  /**
   * 主键
   */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer copy_id;
  private Long copyrightmanagerId;//在线填报表的ID
  private String copy_name;// 著作权人姓名
  private String copy_countries;// 国籍
  private String copy_province;// 省份
  private String copy_city;// 所在城市
  private String copy_country;// 所在城市
  private int copy_type;// 著作权人类别
  private String copyCategorys;//著作权人类别
  private int copy_idType;// 证件类型
  private String copyCertificates;// 证件类型
  private String copy_idCard;// 证件号
  private int copy_sign;// 署名情况
  private String copy_opusSign;// 别名
  private int copy_classification;//分别是草稿箱中保存的数据，还是作品提交的数据

  private String addImg;
  private String addInit;

  private String copy_typeString; //著作权人类别(文字)
  private String copy_idTypeString; //证件类型(文字)
  private String copySignature_String; //署名情况(文字)
}
