package blockChain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class UserDto {

  /**
   * 主键
   */
  private Integer userId;
  /**
   * ids
   */
  private List<Integer> userIds;

  /**
   * 用户名称
   */
  private String userName;

  /**
   * 用户状态
   */
  private Integer userStatus;

  /**
   * 用户真实姓名
   */
  private String realName;

  /**
   * 用户性别
   */
  private Integer sex;

  /**
   * 身份证号码
   */
  private String cardId;

  /**
   * 身份证图片---用户令牌accessToken
   */
  @JsonIgnore
  private String cardImgf;

  /**
   * 身份证类型
   */
  private Integer identityKind;

  /**
   * 证件类型
   */
  private Integer cardType;

  /**
   * 法人类型(企业法人: C01 社会组织法人: C02 事业单位法人 C03 个体工商户:C04 )
   */
  private String legalPersionType;

  /**
   * 代理公司名称
   */
  private String agencyCompanyName;

  /**
   * 手机号码
   */
  private String phoneNum;

  /**
   * 邮箱
   */
  private String email;

  /**
   * 地址
   */
  private String address;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 随机激活码
     */
    @JsonIgnore
    private String randKey;

    /**
     * 随机码过期时间
     */
    @JsonIgnore
    private LocalDateTime randKeyExpireTime;

    /**
     * 固定电话
     */
    private String fixedPhoneNum;

    /**
     * 所在国家
     */
    private String countryName;

  /**
   * 所在省
   */
  private String provinceName;

  /**
   * 所在市
   */
  private String cityName;

  /**
   * 所在区
   */
  private String countyName;

  /**
   * 角色ID
   */
  private Long roleId;

  /**
   * 角色名称
   */
  private String roleName;

  /**
   * 是否工作站
   */
  private Byte isWorkstation;


  /**
   * 省可见级别
   */
  private Integer visibleLevelProvince;
  /**
   * 市可见级别
   */
  private Integer visibleLevelCity;
  /**
   * 区可见级别
   */
  private Integer visibleLevelCounty;

  private Integer isProxyer;

  /**
   * 登陆人ip
   */
  private String ip;

  /**
   * 注册日期
   */
  @JsonIgnore
  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private LocalDateTime registerTime;

  private Boolean deleted;

  private List<String> personalArea;
  private String personalAreaStr;

  private String uuid;

  private String homePageKey; //首页设置参数
}
