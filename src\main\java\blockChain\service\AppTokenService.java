package blockChain.service;

import blockChain.entities.AppTokenEntity;
import blockChain.repository.AppTokenRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/3
 */
@Service
@AllArgsConstructor
public class AppTokenService implements BaseService<AppTokenRepository, AppTokenEntity, Long> {
    private AppTokenRepository repository;

    @Override
    public AppTokenRepository getRepository() {
        return repository;
    }

    public AppTokenEntity findLastToken() {
        return repository.findLastToken();
    }
}
