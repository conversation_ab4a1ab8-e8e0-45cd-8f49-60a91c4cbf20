package com.lic.share.api;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lic.share.config.RestHelper;
import com.lic.share.params.ClientInfo;
import com.lic.share.params.ProjectInfo;
import com.lic.share.params.RequestHead;
import com.lic.share.params.RequestParam;
import com.lic.share.params.cerAuthority.CerAuthorityData;
import com.lic.share.params.cerAuthority.CerAuthorityPersonData;
import com.lic.share.untils.SmCall;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.SignatureException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <pre>
 * ClassName Authorization
 * Description 证照动态授权接口
 * Author glq
 * Date 2019/4/9
 * </pre>
 */
@RestController
@RequestMapping(value = {"/dynamicAuthorization/"})
public class DynamicAuthorizationService extends BaseConfig {


    @Autowired
    RestHelper restHelper;

    @PostMapping(value = {"getPublicCer"})
    public String getPublicCer(String accessToken) throws SignatureException {
        String cerTypeUrl = dynamicAuthorizationPreUrl + "getPublicCer";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeStr = sdf.format(new Date());
        SnowflakeIdWorker3rd idWorker = new SnowflakeIdWorker3rd();

        int id = idWorker.createId();
        String accessId = timeStr + accountId + id;

        RequestParam requestParam = new RequestParam();
        ClientInfo clientInfo = new ClientInfo().setAreaCode("35000")
                .setAreaName("福建省")
                .setDeptCode("DeptCode")
                .setDeptName("DeptName")
                .setOperId("OperId")
                .setOperName("OperName")
                .setSystemName("SystemName");

        ProjectInfo projectInfo = new ProjectInfo()
                .setProjectNo("ProjectNo")
                .setTaskCode("TaskCode").setTaskName("TaskName");
        RequestHead head = new RequestHead().setAccessToken(accessToken)
                .setAccessId(accessId)
                .setAccountId(accountId)
                .setReqTime(LocalDateTime.now())
                .setClientInfo(clientInfo)
                .setProjectInfo(projectInfo);
        requestParam.setHead(head);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", "");
        jsonObject.put("page", "1");
        jsonObject.put("size", "10");
        requestParam.setData(jsonObject);
        String jsonData = JSON.toJSONString(requestParam);

        String sign = SmCall.createSM2Signature(jsonData, priKey);

        String s = restHelper.postRestTemplateByJson(cerTypeUrl, accountId, sign, jsonData);
        return s;
    }


    @PostMapping(value = {"submitCerAuthority"})
    public String submitCerAuthority(String accessToken) throws SignatureException {
        String cerTypeUrl = dynamicAuthorizationPreUrl + "submitCerAuthority";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeStr = sdf.format(new Date());
        SnowflakeIdWorker3rd idWorker = new SnowflakeIdWorker3rd();

        int id = idWorker.createId();
        String accessId = timeStr + accountId + id;

        RequestParam requestParam = new RequestParam();

        RequestHead head = new RequestHead().setAccessToken(accessToken)
                .setAccessId(accessId)
                .setAccountId(accountId)
                .setReqTime(LocalDateTime.now());
        requestParam.setHead(head);

        CerAuthorityData cerAuthorityData = new CerAuthorityData();

        cerAuthorityData.setAuthorityEffectiveDate(StrToDate("2020-06-18 12:00:00"));
        cerAuthorityData.setAuthorityExpiringDate(StrToDate("2020-06-19 12:00:00"));

        List<CerAuthorityPersonData> cerAuthorityPersonDataList = new ArrayList<>();
        cerAuthorityPersonDataList.add(new CerAuthorityPersonData()
                .setCode("350301192103077411")
                .setName("张三").setCodeType("111"));
        cerAuthorityData.setAuthorizedPerson(cerAuthorityPersonDataList);
        cerAuthorityData.setPromoter("李小龙");
        cerAuthorityData.setPromoterCode("3503011931111111411");
        ArrayList<String> idList = new ArrayList<String>();
        idList.add("1.2.156.3005.2.111000000000131433001.111101080000576990.************.001 .1");
        cerAuthorityData.setCertificateIdentifier(idList);
        requestParam.setData(cerAuthorityData);

        String jsonData = JSON.toJSONString(requestParam);

        String sign = SmCall.createSM2Signature(jsonData, priKey);

        String s = restHelper.postRestTemplateByJson(cerTypeUrl, accountId, sign, jsonData);
        return s;
    }


}
