package blockChain.facade.service;

import blockChain.config.SpringConfig;
import blockChain.controller.exception.UnknownException;
import blockChain.entities.UploadAttachment;
import blockChain.service.UploadAttachmentService;
import blockChain.utils.MD5Util;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.util.RandomNumUtil;
import lombok.AllArgsConstructor;
import lombok.Cleanup;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR> Jie
 * @date 2019/11/22 16:54
 */
@Service
@AllArgsConstructor
public class UploadAttachmentServiceFacade {
    @Autowired
    private UploadAttachmentService uploadAttachmentService;

    private final SpringConfig config;

    @Transactional(rollbackFor = RuntimeException.class)
    public UploadAttachment upload(MultipartFile file, HttpServletRequest request) throws IOException {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loginUserName = ((CscpUserDetail) authentication.getPrincipal()).getUsername();
        if (file == null) {
            throw new UnknownException("文件未找到，请重新上传");
        }
        //设置文件上传路径
        String fileRoot = config.getFileRoot();
        String contextPath = "File/";
        String fileOrginName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        String lowerfileOrginName = fileOrginName.toLowerCase(Locale.ROOT);
        @Cleanup InputStream newIn = file.getInputStream();
        String newFileName = MD5Util.getMD5EncodeStr((file.getOriginalFilename() + LocalDateTime.now().toString() + RandomNumUtil.findRandomNums(5)).getBytes());
        LocalDate now = LocalDate.now();
        StringBuilder sb = new StringBuilder(fileRoot);
        sb.append(File.separatorChar).append(now.getYear());
        sb.append(File.separatorChar).append(now.getMonthValue());
        sb.append(File.separatorChar).append(now.getDayOfMonth());
        sb.append(File.separatorChar).append(newFileName+fileOrginName);
        String path = contextPath+sb.toString();

        //创建文件保存路径
        File temp = new File(path);

        File parentFile = temp.getParentFile();
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        if (!temp.exists()) {
            temp.createNewFile();
        }
        @Cleanup FileOutputStream fout = new FileOutputStream(temp);

        long size = IOUtils.copyLarge(newIn, fout);
        fout.close();

        if (size == 0L) {
            System.gc();
            temp.delete();
            throw new UnknownException("上传文件异常");
        }

        // 大小限制 add 2023
        if (lowerfileOrginName.equals(".pdf")) {
            if (size > config.getFileSize()) {
                System.gc();
                temp.delete();
                throw new UnknownException("上传文件的大小超出限制");
            }
        } else if (lowerfileOrginName.equals(".jpg")
                || lowerfileOrginName.equals(".png")
                || lowerfileOrginName.equals(".jpeg")
                || lowerfileOrginName.equals(".gif")
                || lowerfileOrginName.equals(".bmp")) {
            if (size > config.getImgSize()) {
                System.gc();
                temp.delete();
                throw new UnknownException("上传文件的大小超出限制");
            }
        } else if (lowerfileOrginName.equals(".mp4")
                || lowerfileOrginName.equals(".mp3")
                || lowerfileOrginName.equals(".wav")) {
            if (size > config.getVideoSize()) {
                System.gc();
                temp.delete();
                throw new UnknownException("上传文件的大小超出限制");
            }
        } else {
            System.gc();
            temp.delete();
            throw new UnknownException("上传文件格式错误");
        }

        UploadAttachment uploadAttachment = new UploadAttachment();
        uploadAttachment.setWorkName(newFileName + fileOrginName);
        uploadAttachment.setWorkUrl(contextPath + sb.toString());
        uploadAttachment.setSize(size);
        uploadAttachment.setUserName(loginUserName);

        uploadAttachmentService.insert(uploadAttachment);

//        StringBuilder sb2 = new StringBuilder(fileRoot);
//        sb2.append(File.separatorChar).append("attachments");
//        sb2.append(File.separatorChar).append(newFileName + fileOrginName);
//        String path2 = contextPath + sb2.toString();
//        //创建文件保存路径
//        File temp2 = new File(path2);
//
//        File parentFile2 = temp2.getParentFile();
//        if (!parentFile2.exists()) {
//            parentFile2.mkdirs();
//        }
//        if (!temp2.exists()) {
//            temp2.createNewFile();
//        }
//        @Cleanup FileOutputStream fout2 = new FileOutputStream(temp2);
//        @Cleanup InputStream newIn2 = file.getInputStream();
//
//        IOUtils.copyLarge(newIn2, fout2);
//        fout2.close();
//
//        StringBuilder sb3 = new StringBuilder(fileRoot);
//        sb3.append(File.separatorChar).append("attachments");
//        sb3.append(File.separatorChar).append(uploadAttachment.getId().toString());
//        String path3 = contextPath + sb3.toString();
//        //创建文件保存路径
//        File temp3 = new File(path3);
//
//        File parentFile3 = temp3.getParentFile();
//        if (!parentFile3.exists()) {
//            parentFile3.mkdirs();
//        }
//        if (!temp3.exists()) {
//            temp2.renameTo(temp3);
//        }
        return uploadAttachment;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public UploadAttachment getById(Long id){
        return uploadAttachmentService.getById(id);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void removeById(Long id) {
        uploadAttachmentService.removeById(id);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public String getUserNameByWorkUrl(String workUrl) {
        return uploadAttachmentService.getUserNameByWorkUrl(workUrl);
    }

    public List<Long> getCopyrightId(Long id) {
        return uploadAttachmentService.getCopyrightId(id);
    }

//    public boolean existsByWorkUrl(String workUrl) {
//        return uploadAttachmentService.existsByWorkUrl(workUrl);
//    }
}
