
package blockChain.repository;

import blockChain.entities.QCopyrightManager;
import blockChain.entities.QWorksReport;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * <AUTHOR> XXX
 * @date 2019/11/29.
 */

public final class WorksReportPredicates {
    public static Predicate digitalQuery(String worksNum,String productionName,
                                         String productionTypeId, String startDate, String endDate,
                                         String certificateStartDate, String certificateEndDate,List<Integer> status, String userName) {

        QWorksReport digital = QWorksReport.worksReport;

        BooleanBuilder builder = new BooleanBuilder();

        if (!StringUtils.isEmpty(worksNum)) { // 作品登记号
            builder.and(digital.copyrightManager.worksNum.contains(worksNum));
        }

        if (!StringUtils.isEmpty(productionName)) { // 作品名称
            builder.and(digital.copyrightManager.productionName.contains(productionName));
        }

        if (!StringUtils.isEmpty(productionTypeId)) {  // 作品类别
            builder.and(digital.copyrightManager.productionTypeId.eq(productionTypeId));
        }

        if (!StringUtils.isEmpty(certificateStartDate) && !StringUtils.isEmpty(certificateEndDate)) { //证书生成时间
            builder.and(digital.certificateCreateTime.between(LocalDateTime.parse(certificateStartDate,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),LocalDateTime.parse(certificateEndDate,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) { //时间
            builder.and(digital.createTime.between(LocalDateTime.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),LocalDateTime.parse(endDate,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        if(status.size()>0){
            builder.and(digital.reportStatus.in(status));
        }

        if (!StringUtils.isEmpty(userName)) { // 作品名称
            builder.and(digital.copyrightManager.userName.eq(userName));
        }

        return builder;
    }

}

