package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_submitter")
public class Submitter {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	private String copyrightName;// 提交者姓名
	private String copyrightOwner;// 联系人
	private String copyrightTelephone;// 电话号码
	private String copyrightPhone;// 电话
	private String copyrightAddress;// 详细住址
	private String copyrightCode;// 邮编
	private String copyrightEmail;// E-mail
	private String copyrightFax;// 传真

}
