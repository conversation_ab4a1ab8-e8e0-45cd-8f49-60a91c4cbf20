package blockChain.service;

import blockChain.dto.message.MessageDto;
import blockChain.entities.message.MessageEntity;
import blockChain.entities.message.MessageUserEntity;
import blockChain.repository.MessageRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/12/18 16:14
 */
@Service
@AllArgsConstructor
public class MessageService implements BaseService<MessageRepository, MessageEntity, Long> {

  private final MessageRepository repository;

  @Override
  public MessageRepository getRepository() {
    return repository;
  }


  public Page<MessageDto> findPageByUserId(int userId, String title, String content, Byte state, LocalDateTime createTimeStart, LocalDateTime createTimeEnd, Pageable pageable) {
    return repository.findPageByUserId(userId, title, content, state, createTimeStart, createTimeEnd, pageable);
  }

  public Optional<MessageEntity> findByUuid(String uuid) {
    return repository.findByUuid(uuid);
  }

  public Long countUnreadSize(int userId) {
    return repository.countUnreadSize(userId, MessageUserEntity.MessageUserState.UNREAD);
  }
}
