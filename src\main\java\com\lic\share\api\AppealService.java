package com.lic.share.api;


import cn.hutool.core.io.file.FileReader;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lic.share.config.RestHelper;
import com.lic.share.params.ClientInfo;
import com.lic.share.params.ProjectInfo;
import com.lic.share.params.RequestHead;
import com.lic.share.params.RequestParam;
import com.lic.share.params.appeal.CertLackEntity;
import com.lic.share.params.appeal.CertLackFileEntity;
import com.lic.share.params.cerAuthority.CerAuthorityData;
import com.lic.share.params.cerAuthority.CerAuthorityPersonData;
import com.lic.share.untils.SmCall;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.SignatureException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <pre>
 * ClassName Authorization
 * Description
 * Author glq
 * Date 2019/4/9
 * </pre>
 */
@RestController
@RequestMapping(value = {"/appeal/"})
public class AppealService extends BaseConfig {


    @Autowired
    RestHelper restHelper;

    @PostMapping(value = {"submitLackCer"})
    public String submitLackCer(String accessToken) throws SignatureException {
        String submitLackCerUrl = appealPreUrl + "submitLackCer";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeStr = sdf.format(new Date());
        SnowflakeIdWorker3rd idWorker = new SnowflakeIdWorker3rd();

        int id = idWorker.createId();
        String accessId = timeStr + accountId + id;

        RequestParam requestParam = new RequestParam();
        ClientInfo clientInfo = new ClientInfo().setAreaCode("35000")
                .setAreaName("福建省")
                .setDeptCode("DeptCode")
                .setDeptName("DeptName")
                .setOperId("OperId")
                .setOperName("OperName")
                .setSystemName("SystemName");

        ProjectInfo projectInfo = new ProjectInfo()
                .setProjectNo("ProjectNo")
                .setTaskCode("TaskCode").setTaskName("TaskName");
        RequestHead head = new RequestHead().setAccessToken(accessToken)
                .setAccessId(accessId)
                .setAccountId(accountId)
                .setReqTime(LocalDateTime.now())
                .setClientInfo(clientInfo)
                .setProjectInfo(projectInfo);
        requestParam.setHead(head);

        List<CertLackFileEntity> fileList = new ArrayList<>();

        CertLackEntity certLackEntity = new CertLackEntity()
                .setCertificateTypeName("营业执照")
                .setCertificateIssuingAuthorityName("福州市工商局")
                .setCertificateIssuedDate("2019年9月24日")
                .setCertificateNumber("xxxxxxxxxxxxxxx")
                .setCertificateHolderName("xxx公司")
                .setCertificateHolderCode("xxxxxxxxx")
                .setCertificateHolderTypeName("统一社会信用代码")
                .setTelephone("***********")
                .setFileList(fileList);

        requestParam.setData(certLackEntity);
        String jsonData = JSON.toJSONString(requestParam);

        String sign = SmCall.createSM2Signature(jsonData, priKey);

        String s = restHelper.postRestTemplateByJson(submitLackCerUrl, accountId, sign, jsonData);

        return s;
    }
}
