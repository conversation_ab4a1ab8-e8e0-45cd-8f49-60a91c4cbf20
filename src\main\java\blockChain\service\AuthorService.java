package blockChain.service;

import blockChain.entities.Author;
import blockChain.repository.AuthorRepository;
import blockChain.repository.BaseRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:25
 */
@Service
@AllArgsConstructor
public class AuthorService implements BaseService {

  private AuthorRepository repository;

  @Override
  public BaseRepository getRepository() {
    return repository;
  }

    /**
   * 根据作品id获取作者
   */
  public List<Author> getAuthorByCopyrightId(Long copyrightId){return repository.getByCopyrightId(copyrightId);}
}

