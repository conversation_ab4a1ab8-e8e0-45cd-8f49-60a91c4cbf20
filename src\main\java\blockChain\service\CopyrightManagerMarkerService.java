package blockChain.service;

import blockChain.entities.CopyrightManagerMarker;
import blockChain.repository.CopyrightManagerMarkerRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/14 14:32
 */
@Service
@AllArgsConstructor
public class CopyrightManagerMarkerService implements BaseService<CopyrightManagerMarkerRepository, CopyrightManagerMarker, Long>{

    private CopyrightManagerMarkerRepository repository;

    @Override
    public CopyrightManagerMarkerRepository getRepository() {
        return repository;
    }

    public Optional<CopyrightManagerMarker> findByUuid(String uuid) {
        return repository.findByUuid(uuid);
    }

    public List<CopyrightManagerMarker> findByManagerId(Long managerId) {
        return repository.findByManagerId(managerId);
    }
}
