package blockChain.dto.query;

import blockChain.bean.BaseQueryParam;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:14
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CopyrightQueryStatisticGetParam extends BaseQueryParam {
  private String startDate;
  private String endDate;
  private OwnerEnum ownerType;
  private String certificateStartDate;
  private String certificateEndDate;
  private List<String> productionTypeIds;
  private List<String> area;
  private Boolean isWorkstation;
  private String userName;

  public enum OwnerEnum {
    PERSONAL("Personal"),
    COMPANY("Company");
    private String value;

    OwnerEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static OwnerEnum fromValue(String text) {
      for (OwnerEnum b : OwnerEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + text + "'");
    }
  }

  public Boolean isYear(){
    if(startDate == null || endDate == null){
      return true;
    }
    String[] splitStartDate = startDate.split("-");
    String[] splitEndDate = endDate.split("-");
    if(splitStartDate.length < 2 && splitEndDate.length < 2){
      return true;
    }
    return false;
  }

  public LocalDateTime getStartTime() {
    if (startDate == null) {
      return null;
    }
    String[] splitDate = startDate.split("-");
    int month = 1;
    if(splitDate.length >= 2){
      month = Integer.parseInt(splitDate[1]);
    }
    LocalDate localDate = LocalDate.of(Integer.parseInt(splitDate[0]), month, 1);
    LocalDate nowDate = LocalDate.now();
    Assert.isTrue(localDate.isBefore(nowDate),"开始时间不能在当前时间之后");
    return LocalDateTime.of(localDate, LocalTime.MIN);
  }

  public LocalDateTime getEndTime() {
    if (endDate == null) {
      return null;
    }
    String[] splitDate = endDate.split("-");
    int month = 12;
    if(splitDate.length >= 2){
      month = Integer.parseInt(splitDate[1]);
    }
    int dayOfMonth = 30;
    int year = Integer.parseInt(splitDate[0]);
    switch (month) {
      case 1:
      case 3:
      case 5:
      case 7:
      case 8:
      case 10:
      case 12:
        dayOfMonth = 31;
        break;
      case 2:
        boolean leapYear = LocalDate.of(year, month, 1).isLeapYear();
        if(leapYear){
          dayOfMonth = 29;
        }else{
          dayOfMonth = 28;
        }
    }
    LocalDate localDate = LocalDate.of(year, month, dayOfMonth);
    LocalDate nowDate = LocalDate.now();
    if(localDate.isAfter(nowDate)) return LocalDateTime.of(nowDate, LocalTime.MAX);
    return LocalDateTime.of(localDate, LocalTime.MAX);
  }
}
