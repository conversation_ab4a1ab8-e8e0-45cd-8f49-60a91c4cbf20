package org.tempuri;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.4.4
 * 2021-09-28T16:24:54.237+08:00
 * Generated source version: 3.4.4
 *
 */
@WebService(targetNamespace = "http://tempuri.org/", name = "ConvergenceService")
@XmlSeeAlso({ObjectFactory.class})
public interface ConvergenceService {

    @WebMethod
    @RequestWrapper(localName = "allDeleteAndAllAdd", targetNamespace = "http://tempuri.org/", className = "org.tempuri.AllDeleteAndAllAdd")
    @ResponseWrapper(localName = "allDeleteAndAllAddResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.AllDeleteAndAllAddResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String allDeleteAndAllAdd(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid,
        @WebParam(name = "xmlstr", targetNamespace = "")
        java.lang.String xmlstr,
        @WebParam(name = "type", targetNamespace = "")
        java.lang.String type
    );

    @WebMethod(operationName = "LoginByAccount")
    @RequestWrapper(localName = "LoginByAccount", targetNamespace = "http://tempuri.org/", className = "org.tempuri.LoginByAccount")
    @ResponseWrapper(localName = "LoginByAccountResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.LoginByAccountResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String loginByAccount(

        @WebParam(name = "userid", targetNamespace = "")
        java.lang.String userid,
        @WebParam(name = "password", targetNamespace = "")
        java.lang.String password
    );

    @WebMethod
    @RequestWrapper(localName = "getCatalogidByMatterid", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetCatalogidByMatterid")
    @ResponseWrapper(localName = "getCatalogidByMatteridResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetCatalogidByMatteridResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String getCatalogidByMatterid(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "matterid", targetNamespace = "")
        java.lang.String matterid
    );

    @WebMethod
    @RequestWrapper(localName = "getSM4Key", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetSM4Key")
    @ResponseWrapper(localName = "getSM4KeyResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetSM4KeyResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String getSM4Key(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "sm2publickey", targetNamespace = "")
        java.lang.String sm2Publickey
    );

    @WebMethod
    @RequestWrapper(localName = "pushLargeXml", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushLargeXml")
    @ResponseWrapper(localName = "pushLargeXmlResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushLargeXmlResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String pushLargeXml(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid,
        @WebParam(name = "xmlstr", targetNamespace = "")
        java.lang.String xmlstr
    );

    @WebMethod
    @RequestWrapper(localName = "getProblemCollect", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetProblemCollect")
    @ResponseWrapper(localName = "getProblemCollectResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetProblemCollectResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String getProblemCollect(

        @WebParam(name = "webid", targetNamespace = "")
        java.lang.String webid
    );

    @WebMethod
    @RequestWrapper(localName = "pushXmlBackflowInnerFun", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXmlBackflowInnerFun")
    @ResponseWrapper(localName = "pushXmlBackflowInnerFunResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXmlBackflowInnerFunResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String pushXmlBackflowInnerFun(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid,
        @WebParam(name = "xmlstr", targetNamespace = "")
        java.lang.String xmlstr,
        @WebParam(name = "clientIp", targetNamespace = "")
        java.lang.String clientIp
    );

    @WebMethod
    @RequestWrapper(localName = "getTimeGroup", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetTimeGroup")
    @ResponseWrapper(localName = "getTimeGroupResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetTimeGroupResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String getTimeGroup(

        @WebParam(name = "arg0", targetNamespace = "")
        java.lang.String arg0
    );

    @WebMethod
    @RequestWrapper(localName = "endAddAllDeleteAll", targetNamespace = "http://tempuri.org/", className = "org.tempuri.EndAddAllDeleteAll")
    @ResponseWrapper(localName = "endAddAllDeleteAllResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.EndAddAllDeleteAllResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String endAddAllDeleteAll(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid
    );

    @WebMethod
    @RequestWrapper(localName = "crossNetCheck", targetNamespace = "http://tempuri.org/", className = "org.tempuri.CrossNetCheck")
    @ResponseWrapper(localName = "crossNetCheckResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.CrossNetCheckResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String crossNetCheck(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid,
        @WebParam(name = "xmlstr", targetNamespace = "")
        java.lang.String xmlstr
    );

    @WebMethod
    @RequestWrapper(localName = "pushXml", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXml")
    @ResponseWrapper(localName = "pushXmlResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXmlResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String pushXml(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid,
        @WebParam(name = "xmlstr", targetNamespace = "")
        java.lang.String xmlstr
    );

    @WebMethod
    @RequestWrapper(localName = "pushXmlBySM4", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXmlBySM4")
    @ResponseWrapper(localName = "pushXmlBySM4Response", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXmlBySM4Response")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String pushXmlBySM4(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid,
        @WebParam(name = "xmlstr", targetNamespace = "")
        java.lang.String xmlstr
    );

    @WebMethod
    @RequestWrapper(localName = "pushXmlBackflow", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXmlBackflow")
    @ResponseWrapper(localName = "pushXmlBackflowResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXmlBackflowResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String pushXmlBackflow(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid,
        @WebParam(name = "xmlstr", targetNamespace = "")
        java.lang.String xmlstr
    );

    @WebMethod(operationName = "LoginByAccountBySM2Key")
    @RequestWrapper(localName = "LoginByAccountBySM2Key", targetNamespace = "http://tempuri.org/", className = "org.tempuri.LoginByAccountBySM2Key")
    @ResponseWrapper(localName = "LoginByAccountBySM2KeyResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.LoginByAccountBySM2KeyResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String loginByAccountBySM2Key(

        @WebParam(name = "userinfo", targetNamespace = "")
        java.lang.String userinfo,
        @WebParam(name = "sm2publickey", targetNamespace = "")
        java.lang.String sm2Publickey
    );

    @WebMethod
    @RequestWrapper(localName = "pushXmlInnerFun", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXmlInnerFun")
    @ResponseWrapper(localName = "pushXmlInnerFunResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushXmlInnerFunResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String pushXmlInnerFun(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid,
        @WebParam(name = "xmlstr", targetNamespace = "")
        java.lang.String xmlstr,
        @WebParam(name = "clientIp", targetNamespace = "")
        java.lang.String clientIp
    );

    @WebMethod
    @RequestWrapper(localName = "getSM2PublicKey", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetSM2PublicKey")
    @ResponseWrapper(localName = "getSM2PublicKeyResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetSM2PublicKeyResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String getSM2PublicKey()
;

    @WebMethod
    @RequestWrapper(localName = "pushInterAllXml", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushInterAllXml")
    @ResponseWrapper(localName = "pushInterAllXmlResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PushInterAllXmlResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String pushInterAllXml(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid,
        @WebParam(name = "xmlstr", targetNamespace = "")
        java.lang.String xmlstr,
        @WebParam(name = "type", targetNamespace = "")
        java.lang.String type,
        @WebParam(name = "ip", targetNamespace = "")
        java.lang.String ip,
        @WebParam(name = "beLongNet", targetNamespace = "")
        java.lang.String beLongNet
    );

    @WebMethod
    @RequestWrapper(localName = "getXmlByCatalogid", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetXmlByCatalogid")
    @ResponseWrapper(localName = "getXmlByCatalogidResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.GetXmlByCatalogidResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String getXmlByCatalogid(

        @WebParam(name = "guid", targetNamespace = "")
        java.lang.String guid,
        @WebParam(name = "catalogid", targetNamespace = "")
        java.lang.String catalogid
    );
}
