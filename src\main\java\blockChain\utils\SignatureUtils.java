package blockChain.utils;

import java.security.MessageDigest;
import java.util.Date;
import java.util.LinkedHashMap;

/**
 * @date 2023年07月12日 9:30 上午
 * 说明 SignatureUtils
 */
public class SignatureUtils {
    /**
     * 计算调用网关接口时需要的签名头信息
     *
     * @param paasId    分配的paasId
     * @param paasToken 分配的paasToken
     * @return 计算好签名和其它必要header的hashmap
     * @throws Exception 参数异常
     */
    public static LinkedHashMap<String, String> computeSignatureHeaders(String paasId, String paasToken) throws Exception {
        if (paasId == null || paasId.isEmpty() || paasToken == null || paasToken.isEmpty()) {
            throw new Exception("paasId and paasToken must be set");
        }
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
        long now = new Date().getTime();
        String timestamp = Long.toString((long) Math.floor(now / 1000));
        String nonce = Long.toHexString(now) + "-" + Long.toHexString((long) Math.floor(Math.random() * 0xFFFFFF));
        map.put("x-rio-paasid", paasId);
        map.put("x-rio-timestamp", timestamp);
        map.put("x-rio-nonce", nonce);
        map.put("x-rio-signature", toSHA256(timestamp + paasToken + nonce + timestamp));
        return map;
    }

    protected static String toSHA256(String str) throws Exception {
        MessageDigest messageDigest;
        String encodeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes("UTF-8"));
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (Exception e) {
            throw e;
        }
        return encodeStr;
    }

    // byte转换成16进制
    protected static String byte2Hex(byte[] bytes) {
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }
}
