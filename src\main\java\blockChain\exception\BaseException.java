package blockChain.exception;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class BaseException extends RuntimeException {

    @Getter
    @Setter
    private int resultCode;

    @Getter
    @Setter
    private Object[] args;

    public BaseException(int resultCode, String message) {
        super(message);
        this.resultCode = resultCode;
    }

    public BaseException(int resultCode, String message, Object[] args) {
        super(message);
        this.resultCode = resultCode;
        this.args = args;
    }

    public BaseException(int resultCode, String message, Throwable cause) {
        super(message, cause);
        this.resultCode = resultCode;
    }

    public BaseException(int resultCode, String message, Object[] args, Throwable cause) {
        super(message, cause);
        this.resultCode = resultCode;
        this.args = args;
    }
}
