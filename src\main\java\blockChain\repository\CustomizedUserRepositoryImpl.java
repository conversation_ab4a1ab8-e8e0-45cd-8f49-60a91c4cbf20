package blockChain.repository;

import blockChain.dto.StatisticDto;
import blockChain.entities.QCopyrightManager;
import blockChain.entities.QDigital;
import blockChain.entities.QUserEntity;
import blockChain.entities.UserEntity;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.core.types.dsl.StringTemplate;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/20 15:21
 */
public class CustomizedUserRepositoryImpl extends QuerydslRepositorySupport implements CustomizedUserRepository {

    public CustomizedUserRepositoryImpl() {
        super(UserEntity.class);
    }

    @Override
    public void removeBatchById(List<Integer> userIds) {
        QUserEntity qUserEntity = QUserEntity.userEntity;
        update(qUserEntity)
            .set(qUserEntity.deleted, true)
            .where(qUserEntity.userId.in(userIds))
            .execute();
    }

  @Override
  public List<StatisticDto> getUserStatisticInCopyrightManager(Pageable pageable, Predicate... predicate) {
    QUserEntity user = QUserEntity.userEntity;
    QCopyrightManager owner = QCopyrightManager.copyrightManager;

    return getQuerydsl().<StatisticDto>createQuery()
      .select(Projections.constructor(StatisticDto.class, user.userId.as("key"), user.userName.as("name"), owner.count().as("amount")))
      .from(user)
        .leftJoin(owner)
          .on(user.userName.eq(owner.userName))
      .where(predicate)
      .groupBy(user.userId)
      .offset(pageable.getOffset())
      .limit(pageable.getPageSize())
      .fetch()
      ;
  }

  @Override
  public Long countUserStatisticInCopyrightManager(Pageable pageable, Predicate... predicate) {
    QUserEntity user = QUserEntity.userEntity;
    QCopyrightManager owner = QCopyrightManager.copyrightManager;

    return getQuerydsl().<Long>createQuery()
      .select(Projections.constructor(Long.class, user.userId.count()))
      .from(user)
      .leftJoin(owner)
      .on(user.userName.eq(owner.userName))
      .where(predicate)
      .groupBy(user.userId)
      .fetchCount()
      ;
  }

  @Override
  public List<StatisticDto> countGeographyOfUsers(Predicate ... predicate) {
    QUserEntity user = QUserEntity.userEntity;
    QDigital digital = QDigital.digital;
    QDigital digital_city = new QDigital("digital_city");

    return getQuerydsl().<StatisticDto>createQuery()
      .select(Projections.constructor(StatisticDto.class, digital_city.id.as("key"), digital_city.dict_name.as("name"), user.count().as("amount")))
      .from(user)
      .leftJoin(digital).on(digital.id.like(user.provinceName))
      .leftJoin(digital_city).on(digital_city.id.like(user.cityName))
      .where(predicate)
      .groupBy(digital_city.id)
      .fetch();
  }

  @Override
  public StatisticDto countGeographyOfUsersCountAll(Predicate... predicate) {
    QUserEntity user = QUserEntity.userEntity;
    QDigital digital = QDigital.digital;

    return getQuerydsl().<StatisticDto>createQuery()
      .select(Projections.constructor(StatisticDto.class, digital.id.as("key"), digital.dict_name.as("name"), user.count().as("amount")))
      .from(user)
      .leftJoin(digital).on(digital.id.like(user.provinceName))
      .where(predicate)
      .fetchOne();
  }

  @Override
  public List<Integer> getAllIds(Predicate... predicate) {
    QUserEntity user = QUserEntity.userEntity;

    return getQuerydsl().<StatisticDto>createQuery()
      .select(Projections.constructor(Integer.class, user.userId))
      .from(user)
      .where(predicate)
      .fetch();
  }

  @Override
  public void updateHomePage(Integer userId,String homePageKey) {
    QUserEntity qUserEntity = QUserEntity.userEntity;
    update(qUserEntity)
      .set(qUserEntity.homePageKey, homePageKey)
      .where(qUserEntity.userId.eq(userId))
      .execute();
  }
}
