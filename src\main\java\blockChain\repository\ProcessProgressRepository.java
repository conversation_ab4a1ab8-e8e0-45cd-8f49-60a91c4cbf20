package blockChain.repository;

import blockChain.entities.Agent;
import blockChain.entities.EvaluateEntity;
import blockChain.entities.ProcessProgressEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/24 16:28
 */
@Repository
public interface ProcessProgressRepository extends JpaRepository<ProcessProgressEntity, Long>, QuerydslPredicateExecutor<ProcessProgressEntity>,ProcessProgressRepositoryCustom{

    ProcessProgressEntity findByProcessId(String processId);

    ProcessProgressEntity findByRegistrationNumAndStateAndType(Long registrationNum,int state,int type);

    Integer countByRegistrationNumAndTypeAndState(Long registrationNum,int type,int state);

    List<ProcessProgressEntity> findByRegistrationNumAndTypeAndState(Long registrationNum,int type,int state);

    List<ProcessProgressEntity> findBySnCodeAndTypeAndState(String snCode,int type,int state);

    List<ProcessProgressEntity> findBySnCode(String snCode);

    @Query(value = "SELECT RIGHT(snCode,6) FROM tb_process_progress WHERE snCode like ?1 ORDER BY RIGHT(snCode,6) desc limit 1 ", nativeQuery = true)
    String getLastSncode(String snCode);


}
