package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;
import java.sql.Timestamp;

@XmlRootElement
public class CopyrightCancelBean {

	/**
	 * 撤销登记理由 最大长度为50的字符串
	 */
	private String reason;

	/**
	 * 申请办理方式 整形：0（由作品登记人办理 ），1（由代理人办理）
	 */
	private int applyType;

	/**
	 * 变更/补充登记证明编号 最大长度为20的字符串
	 */
	private String proof_num;
	/**
	 * 撤销日期
	 */
	private Timestamp cancelDate;

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public int getApplyType() {
		return applyType;
	}

	public void setApplyType(int applyType) {
		this.applyType = applyType;
	}

	public String getProof_num() {
		return proof_num;
	}

	public void setProof_num(String proof_num) {
		this.proof_num = proof_num;
	}

	/**
	 * @return the cancelDate
	 */
	public Timestamp getCancelDate() {
		return cancelDate;
	}

	/**
	 * @param cancelDate
	 *            the cancelDate to set
	 */
	public void setCancelDate(Timestamp cancelDate) {
		this.cancelDate = cancelDate;
	}

	public CopyrightCancelBean() {

	}

	public CopyrightCancelBean(String reason, int applyType, String proof_num,
			Timestamp cancelDate) {
		this.applyType = applyType;
		this.proof_num = proof_num;
		this.reason = reason;
		this.cancelDate = cancelDate;
	}
}
