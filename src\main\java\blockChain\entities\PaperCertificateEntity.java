package blockChain.entities;

import blockChain.dto.message.AlertMessageDto;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 纸质证书
 * <AUTHOR>
 * @date 2020/4/16 14:39
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tb_paper_certificate")
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
public class PaperCertificateEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  /**
   * UUID
   * 唯一约束
   */
  @Column(unique = true, length = 40)
  @NonNull
  private String uuid;

  /**
   * 申请理由
   */
  @Column(length = 1000)
  private String reason;

  /**
   * 拒绝理由
   */
  private String rejectReason;

  /**
   * 申请人
   */
  @JoinColumn(name="user_id")
  @ManyToOne
  private UserEntity creator;

  /**
   * 物流编号
   */
  private String logisticsNum;

  /**
   * 物流公司代码
   */
  private String logisticsComNum;

  /**
   * 物流公司中文描述
   */
  private String logisticsComCn;

  /**
   * 物流状态
   */
  private StatusEnum status = StatusEnum.PENDING;

  /**
   * 物流状态枚举
   */
  public enum StatusEnum{
    /**
     * 请求证书中，审核中
     */
    PENDING("pending"),
    /**
     * 拒绝
     */
    REJECT("reject"),
    /**
     * 等待寄送
     */
    AWAITING("awaiting"),
    /**
     * 已发送
     */
    SEND("send"),
    ;
    private String value;

    StatusEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static StatusEnum fromValue(String text) {
      for (StatusEnum b : StatusEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + text + "'");
    }
  }

  /**
   * 关联的作品
   */
  @OneToOne
  private CopyrightManager copyright;

  @CreatedDate
  private LocalDateTime createTime;

  @LastModifiedDate
  private LocalDateTime updateTime;

  @OneToMany(cascade = {CascadeType.ALL})
  private List<ProcessRecord> processRecords;

  public ProcessRecord appendProcessRecords( String opType, String opResult, String opName, String approverOpinion) {

    if(processRecords == null){
      processRecords = new ArrayList<>(4);
    }

    ProcessRecord record = new ProcessRecord()
      .setOpType(opType)
      .setOpResult(opResult)
      .setOpName(opName)
      .setApproverOpinion(approverOpinion)
      ;
    processRecords.add(record);

    return record;
  }

}
