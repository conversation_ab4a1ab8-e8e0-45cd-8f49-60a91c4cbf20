package blockChain.mapper;

import blockChain.dto.SubmitterDto;
import blockChain.dto.UserDto;
import blockChain.entities.Submitter;
import blockChain.entities.UserEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SubmitterDtoMapper {
    SubmitterDtoMapper INSTANCE = Mappers.getMapper(SubmitterDtoMapper.class);

    SubmitterDto entityToDto(Submitter submitter);

    List<SubmitterDto> entityToDto(List<Submitter> submitters);
}
