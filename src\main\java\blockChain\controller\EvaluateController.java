package blockChain.controller;

import blockChain.bean.Evaluate.EvaluateRequest;
import blockChain.bean.Evaluate.GetEvaluateRulesReturn;
import blockChain.bean.Evaluate.GetEvaluationRulesBean;
import blockChain.bean.Evaluate.GetSaveEvaluationBean;
import blockChain.config.GatewayAutoConfiguration;
import blockChain.config.SpringConfig;
import blockChain.entities.CopyrightManager;
import blockChain.entities.UserEntity;
import blockChain.facade.service.CopyrightManagerServiceFacade;
import blockChain.facade.service.EvaluationServiceFacade;
import blockChain.repository.EvaluationRepository;
import blockChain.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.message.SOAPHeaderElement;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.validation.Valid;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.*;

/**
 * Created by epcsoft on 2020/8/3.
 */
@Api("好差评")
@Slf4j
@RestController
@RequestMapping("evaluate")
@AllArgsConstructor
public class EvaluateController {
    private final CopyrightManagerServiceFacade copyrightManagerServiceFacade;
    private final UserService userService;
    private final EvaluationRepository evaluationRepository;
    private final EvaluationServiceFacade evaluationServiceFacade;
    private final SpringConfig config;

    @ApiOperation("获取结构化评价选项")
    @PostMapping("getEvaluationRules")
    public ResponseEntity<Map<String, Object>> getEvaluationRules(@Valid @RequestBody EvaluateRequest evaluateUrlRequest) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (evaluateUrlRequest.getRegistrationNum() != null) {
                CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(evaluateUrlRequest.getRegistrationNum());
                if (copyrightManager == null)
        {
          result.put("code", "500");
            result.put("message", "找不到此作品！");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        }
                if (evaluationRepository.countAllByManager(copyrightManager) > 0) {
                    result.put("code", "500");
                    result.put("message", "已经评价过了！");
                    return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
                }
            }
            if (evaluationServiceFacade.beforeRequest()) {
                result.put("code", "500");
                result.put("message", "好差评服务错误！请联系管理员！");
                return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
            }
            List<GetEvaluationRulesBean> getEvaluationRulesBeans = new ArrayList<>();
            String endpoint = config.getEvaluationRuleWsdl();
            Service service = new Service();
            Call call = (Call) service.createCall();
            SOAPHeaderElement head = new SOAPHeaderElement(config.getEvaluationNamespace(), "Authorization", "Basic " + GatewayAutoConfiguration.getToken());
      call.addHeader(head);
      call.setTargetEndpointAddress(endpoint);
      call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);
      call.addParameter("appKey",   org.apache.axis.encoding.XMLType.XSD_STRING,  javax.xml.rpc.ParameterMode.IN);// 接口的参数
      call.addParameter("sysNO",   org.apache.axis.encoding.XMLType.XSD_STRING,  javax.xml.rpc.ParameterMode.IN);// 接口的参数
      call.setOperationName("getEvaluationRules");// WSDL里面描述的接口名称   newSubmit     submit
      String appKey= GatewayAutoConfiguration.getAppKey();//公钥
      String sysNO=GatewayAutoConfiguration.getSysCode();
      String a=(String)call.invoke(new Object[] {appKey,sysNO});
      DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
      DocumentBuilder db = dbf.newDocumentBuilder();
      InputSource is = new InputSource(new StringReader(a));
      Document  document=db.parse(is);
      NodeList caseNodeList=document.getChildNodes().item(0).getChildNodes();
      for(int i=0;i<caseNodeList.getLength();i++)
      {
        Node dataNode=caseNodeList.item(i);
        if(dataNode.getNodeName().equals("data"))
        {
          NodeList ruleList=dataNode.getChildNodes().item(0).getChildNodes();
          for (int j = 0; j < ruleList.getLength(); j++)
          {
            GetEvaluationRulesBean getEvaluationRulesBean=new GetEvaluationRulesBean();
            Node node = ruleList.item(j);
            NodeList valueNodes=node.getChildNodes();
            for(int k=0;k<valueNodes.getLength();k++)
            {
              Node valueNode=valueNodes.item(k);
              try {
                //System.out.println(valueNode.getNodeName()+":"+valueNode.getTextContent());
                if (valueNode.getNodeName().equals("iid") )
                {
                  getEvaluationRulesBean.setIid(valueNode.getTextContent());
                }
                if (valueNode.getNodeName().equals("normCode") )
                {
                  getEvaluationRulesBean.setNormCode(valueNode.getTextContent());
                }
                if (valueNode.getNodeName().equals("normName") )
                {
                  getEvaluationRulesBean.setNormName(valueNode.getTextContent());
                }
                if (valueNode.getNodeName().equals("starLevel") )
                {
                  getEvaluationRulesBean.setStarLevel(valueNode.getTextContent());
                }
                if (valueNode.getNodeName().equals("usingStatus") )
                {
                  if(valueNode.getTextContent().equals("1"))
                  {
                    getEvaluationRulesBeans.add(getEvaluationRulesBean);
                  }
                }
              }
              catch (Exception e)
              {

              }
            }

          }
        }
      }
      GetEvaluateRulesReturn getEvaluateRulesReturn=new GetEvaluateRulesReturn();
      getEvaluateRulesReturn.setEvaluateionList(getEvaluationRulesBeans);
      result.put("code","200");
      result.put("evaluateionList", getEvaluateRulesReturn);
    }catch(Exception e){
      result.put("code", "500");
      result.put("message", "服务器内部错误！");
      log.error("error", e);
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
    }
    return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
  }


  @ApiOperation("请求好差评")
  @PostMapping("requestEvaluate")
  public ResponseEntity<Map<String, Object>> requestEvaluate(@Valid @RequestBody EvaluateRequest evaluateUrlRequest)
  {
    Map<String, Object> result = new HashMap<>();

    try
    {
      CopyrightManager copyrightManager= copyrightManagerServiceFacade.getById(evaluateUrlRequest.getRegistrationNum());
      if(copyrightManager==null)
      {
        result.put("code", "500");
        result.put("message", "找不到此作品！");
          return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
      }
        if (evaluationRepository.countAllByManager(copyrightManager) > 0) {
            result.put("code", "500");
            result.put("message", "已经评价过了！");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        }


        if (evaluationServiceFacade.beforeRequest()) {
            result.put("code", "500");
            result.put("message", "好差评服务错误！请联系管理员！");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        }

        Optional<UserEntity> user = userService.getByUserName(copyrightManager.getUserName());
        GetSaveEvaluationBean getSaveEvaluationBean = evaluationServiceFacade.getSaveEvaluationBean(evaluateUrlRequest, copyrightManager, user.get());
        result = evaluationServiceFacade.getSaveEvaluation(getSaveEvaluationBean);
        if (result.containsKey("code") && result.get("code").equals(200)) {
            evaluationServiceFacade.createEvaluation(evaluateUrlRequest, copyrightManager, user.get(), result);
        } else {
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    } catch (Exception e) {
        log.error("error", e);
        result.put("code", "500");
        result.put("message", "服务器内部错误！");
        result.put("errorMsg", e.getMessage());
        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
    }

      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
  }

//    @ApiOperation("获取单位事项简易事项列表信息接口")
//    @PostMapping("getServiceSimpleList")
//    public String getServiceSimpleList() {
//        try {
//            ApasInfoServiceImplService apasInfoServiceImplService = new ApasInfoServiceImplServiceLocator();
//            ApasInfoServiceImpl apasInfoService = apasInfoServiceImplService.getApasInfoServiceAsmx();
//            return apasInfoService.getServiceSimpleList("11350000003591037R", "123456", "100", "1", "1");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return "";
//    }

    @ApiOperation("自动评价")
    @PostMapping("autoEvaluation")
    public void autoEvaluation() {
        evaluationServiceFacade.sync();
    }
}
