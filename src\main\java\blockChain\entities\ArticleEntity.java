package blockChain.entities;

import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 行业消息
 *
 * <AUTHOR>
 * @date 2020/4/15 15:12
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tb_article")
@EntityListeners(AuditingEntityListener.class)
@Accessors(chain = true)
public class ArticleEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  /**
   * UUID
   * 唯一约束
   */
  @Column(unique = true, length = 36)
  @NonNull
  private String uuid;


  @Column(length = 500)
  private String title;

  @Lob
  private String content;

  @CreatedDate
  private LocalDateTime createTime;

  @LastModifiedDate
  private LocalDateTime updateTime;


  /**
   * 创建人
   */
  @ManyToOne
  private UserEntity creator;

}
