package blockChain.facade.service;

import blockChain.entities.CertificateEntity;
import blockChain.entities.CopyrightOwner;
import blockChain.entities.Digital;
import blockChain.service.CertificateService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lic.share.api.ShareService;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/22 16:54
 */
@Service
@AllArgsConstructor
public class CertificateServiceFacade {
  private CertificateService certificateService;
  private DigitalServiceFacade digitalServiceFacade;

  private final static Logger logger = LoggerFactory.getLogger(CertificateServiceFacade.class);

  public CertificateEntity getIndexByHolderCode(String idCard, String name, List<Integer> cardTypeList) {
      CertificateEntity entity = certificateService.findAllByCertificateHolderCode(idCard);
      if (!ObjectUtils.isEmpty(entity))
          return entity;
      CertificateEntity certificateEntity = new CertificateEntity();
      String cardTypeStr = "";
      if (cardTypeList != null && cardTypeList.size() > 0) {
//      for (Integer cardType : cardTypeList) {
//        if (CopyrightOwner.CopyCertificateValue.IDCARD.equals(cardType))
//          cardTypeStr += "||身份证";
//        else if (CopyrightOwner.CopyCertificateValue.RESIDENT.equals(cardType))
//          cardTypeStr += "||居住证";
//      }
//      if (cardTypeStr.startsWith("||"))
//        cardTypeStr = cardTypeStr.substring(2);
      switch (cardTypeList.get(0)) {
        case CopyrightOwner.CopyCertificateValue.IDCARD:
          cardTypeStr = "身份证";
          break;
        case CopyrightOwner.CopyCertificateValue.RESIDENT:
          cardTypeStr = "居住证";
          break;
        case CopyrightOwner.CopyCertificateValue.CODE:
          cardTypeStr = "营业执照";
          break;
        case CopyrightOwner.CopyCertificateValue.SOCIALORGZS:
          cardTypeStr = "社会团体法人登记证书";
          break;
        case CopyrightOwner.CopyCertificateValue.GOVERNMENTZS:
          cardTypeStr = "统一社会信用代码证书";
          cardTypeStr += "||居住证事业单位法人证书";
          break;
        case CopyrightOwner.CopyCertificateValue.INSTITUTIONZS:
          cardTypeStr = "事业单位法人证书";
          cardTypeStr += "||统一社会信用代码证书";
          break;
        default:
          break;
      }
    }
    if (cardTypeStr.isEmpty())
      cardTypeStr = "身份证||居住证";

    try {
      ShareService shareService = new ShareService();
      String s = shareService.getIndexByHolderCode(idCard, cardTypeStr);
      JSONObject result = JSONObject.parseObject(s);
      JSONObject headJson = result.getJSONObject("head");
      if (headJson.getInteger("status").equals(0)) {
        JSONArray dataList = result.getJSONObject("data").getJSONArray("dataList");
        if (dataList.size() == 0) {
          // return 证照不存在
          logger.info("【" + idCard + "】证照不存在");
          return certificateEntity;
        }
        boolean hasZM = false;
        for (Object json : dataList) {
          certificateEntity = JSON.parseObject(json.toString(), CertificateEntity.class);
           if (!"证照".equals(certificateEntity.getKZ_classification())) continue;
          if (!"edc".equals(certificateEntity.getFileFormat()) && !"ofd".equals(certificateEntity.getFileFormat())) continue;
          if (!StringUtils.isEmpty(name) && !name.equals(certificateEntity.getCertificateHolderName())) continue;//名称不一致

          hasZM = true;
          String countryStr = "";
          String[] areas = null;
          String certificateIssuingAuthorityName = certificateEntity.getCertificateIssuingAuthorityName();
          certificateIssuingAuthorityName = certificateIssuingAuthorityName.split("\\（")[0].split("\\(")[0];

          if (certificateIssuingAuthorityName.contains("公安局")) {
            areas = certificateIssuingAuthorityName.split("分局")[0].split("公安局");
          } else if (certificateIssuingAuthorityName.contains("工商行政管理局")) {
            areas = certificateIssuingAuthorityName.split("分局")[0].split("工商行政管理局");
          } else if (certificateIssuingAuthorityName.contains("市场监督管理局")) {
            areas = certificateIssuingAuthorityName.split("分局")[0].split("市场监督管理局");
          } else if (certificateIssuingAuthorityName.contains("委机构编制委员会办公室") && certificateIssuingAuthorityName.startsWith("中共")) {
            areas = certificateIssuingAuthorityName.split("中共")[1].split("委机构编制委员会办公室");
          } else if (certificateIssuingAuthorityName.contains("委机构编制委员会办公室") && certificateIssuingAuthorityName.startsWith("中国共产党")) {
            areas = certificateIssuingAuthorityName.split("中国共产党")[1].split("委机构编制委员会办公室");
          }

          if (areas == null) {
            // do nothing
          } else if (areas.length==1) {
            // TODO 字典表中追加特殊发证机构
            // case 1 福州市长乐区公安局 南平市建阳区公安局 福州市马尾区公安局 长乐市公安局
            // case 2 福鼎市公安局 安溪县公安局
            String[] areas2 = areas[0].split("市");
            if (areas2.length == 1) {
              countryStr = areas[0];
            } else {
              countryStr = areas2[1];
            }
            // 平潭县公安局=平潭试验区 福建省福州市下辖县，实验区=》福建省
            if ("平潭县公安局".equals(certificateIssuingAuthorityName)) {
              countryStr = "平潭综合实验区";
            }
          } else if (areas.length==2) {
            countryStr = areas[1].contains("区") ? areas[1] : areas[1]+"区";

            // 泉州市公安局台商投资区分局
            if ("泉州市公安局台商投资区分局".equals(certificateIssuingAuthorityName)) {
              countryStr = "泉州台商投资区";
            }
            // 漳州市公安局台商投资区分局=漳州市,龙海市
            // 漳州市公安局漳州开发区分局=漳州市,龙海市
            if ("漳州市公安局台商投资区分局".equals(certificateIssuingAuthorityName) || "漳州市公安局漳州开发区分局".equals(certificateIssuingAuthorityName)) {
              countryStr = "龙海区";
            }
            // 莆田市公安局湄洲湾北岸经济开发区分局
            if ("莆田市公安局湄洲湾北岸经济开发区分局".equals(certificateIssuingAuthorityName)) {
              countryStr = "秀屿区";
            }
          }
          if (countryStr.length()>0) {
            // dict get city
            if (countryStr.equals("长乐市")) countryStr="长乐区";
            List<Digital> digitals = digitalServiceFacade.getDictByDict_Name(countryStr);
            if (digitals.size()>0) {
              if (digitals.get(0).getLevel() == 4) {
                certificateEntity.setCertCountries(158);
                certificateEntity.setCertProvince(409);
                Digital city = digitalServiceFacade.getById(digitals.get(0).getPid());
                certificateEntity.setCertCounty(digitals.get(0).getId());// 区县id
                certificateEntity.setCertCity(city.getId());// 市id
                certificateEntity.setAreaNamesStr("中国,福建,"+ city.getDict_name() +"," + countryStr);
              } else if (digitals.get(0).getLevel() == 3 && digitals.get(0).isLeaf() == true){
                // level =3 县级市
                certificateEntity.setCertCountries(158);
                certificateEntity.setCertProvince(409);
                certificateEntity.setCertCity(digitals.get(0).getId());// 市id
                certificateEntity.setAreaNamesStr("中国,福建,"+ countryStr);
              }
            }
          }
          CertificateEntity certificateEntityExist = certificateService.findAllByCertificateHolderCode(certificateEntity.getCertificateHolderCode());
          if (certificateEntityExist != null)
            certificateEntity.setId(certificateEntityExist.getId());
          certificateService.saveOrUpdate(certificateEntity);
          break;
        }
        if (!hasZM) {
          logger.info("【" + idCard + "】证照不存在");
          return new CertificateEntity();
        }
      } else {
        logger.error("【" + idCard + "】证照获取失败：" + s);
      }
    } catch (Exception e) {
      logger.error("error: ", e);
    }
    return certificateEntity;
  }

  public CertificateEntity findById(Long id) {
    return certificateService.findById(id);
  }
}
