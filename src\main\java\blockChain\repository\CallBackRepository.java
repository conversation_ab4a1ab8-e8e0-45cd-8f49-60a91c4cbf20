package blockChain.repository;

import blockChain.entities.CallBackEntity;
import blockChain.entities.ProcessProgressEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/24 16:28
 */
@Repository
public interface CallBackRepository extends JpaRepository<CallBackEntity, Long>, QuerydslPredicateExecutor<CallBackEntity>,CallBackRepositoryCustom{

}
