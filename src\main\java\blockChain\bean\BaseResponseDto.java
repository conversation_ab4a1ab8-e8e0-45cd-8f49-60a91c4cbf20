package blockChain.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * 基本返回格式
 * 正常情况使用
 * <AUTHOR>
 * @date 2020/4/9 14:29
 */
@Getter
@Setter
@Accessors(chain = true)
public abstract class BaseResponseDto<T> {

  private int resultCode = 0;
  private String message = "操作成功";
  private T data;

  public static ResponseEntity<BaseResponseDto> ok(HttpStatus httpStatus) {
    return ok(httpStatus, new BaseResponseDto(){});
  }
  public static <T> ResponseEntity<BaseResponseDto<T>> ok(T dto) {
    return ResponseEntity.ok(new BaseResponseDto<T>(){}.setData(dto));
  }
  public static ResponseEntity<BaseResponseDto> ok(HttpStatus httpStatus, BaseResponseDto dto) {
    return ResponseEntity.status(httpStatus).body(dto);
  }
}
