package blockChain.controller;

import blockChain.bean.PageQuery;
import blockChain.bean.QueryParam;
import blockChain.bean.ResultCodeResponse;
import blockChain.dto.UserDto;
import blockChain.dto.user.UserPageResponse;
import blockChain.facade.service.UserServiceFacade;
import com.ctsi.ssdc.security.UserLoginValidator;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@Api("用户管理")
@Slf4j
@RestController
@RequestMapping("users")
@RequiredArgsConstructor
public class UserController {

    @Value("${ctsi.RSA-prikey:}")
    private String rsaPrikey = "";

    @Autowired
    private UserServiceFacade userServiceFacade;

    @Autowired(required = false)
    private UserLoginValidator userLoginValidator;

//  @ApiOperation("查找指定id用户")
//  @GetMapping("{id}")
//  public UserDto findUserById(@ApiParam("用户id") @PathVariable Integer id) {
//    return userServiceFacade.findUserById(id);
//  }

    @ApiOperation("登出")
    @PostMapping("logout")
    public ResponseEntity<Map<String, Object>> loginout() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "登出成功！");
        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
    }

//  @ApiOperation("登录验证")
//  @PostMapping("login")
//  public ResponseEntity<JwtToken> login(@Valid @RequestBody UserForm user) {
//
//    //进行DES解密
//        /*try {
//            user.setPassword(DESUtil.decryption(user.getPassword(),"desmscdd"));
//        } catch (Exception e1) {
//            // TODO Auto-generated catch block
//            e1.printStackTrace();
//        }*/
//    //String password = new BCryptPasswordEncoder().encode(user.getPassword());
//    //user.setPassword(password);
//    //boolean res = new BCryptPasswordEncoder().matches(user.getPassword(),password);
//
//    JwtResult jwtObject = userServiceFacade.identityAuthorize(user, userLoginValidator);
//    String jwt = jwtObject.getJwt();
//
//    HttpHeaders httpHeaders = new HttpHeaders();
//    String token = JWTConfigurer.AUTHORIZATION_BEARER + jwt;
//
//    httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, token);
//    //组装用户信息
//    CscpUserDetail userDetail = jwtObject.getUserDetail();
//    UserDto userDto = userServiceFacade.findUserById(userDetail.getId());
//    Map<String, Object> userMap = new HashMap<String, Object>() {{
//      put("id", userDetail.getId());
//      put("userName", userDetail.getUsername());
//      put("authorities", userDetail.getAuthorities());
//      put("visibleLevelCity", userDto.getVisibleLevelCity());
//      put("visibleLevelProvince", userDto.getVisibleLevelProvince());
//      put("visibleLevelCounty", userDto.getVisibleLevelCounty());
//    }};
//
//    return new ResponseEntity<>(new JwtToken(token, userMap), httpHeaders, HttpStatus.OK);
//  }

    @PostMapping(value = "query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResponseEntity<UserPageResponse> findPage(@RequestBody PageQuery<UserDto> pageQuery) {
        UserPageResponse response = null;
        if (userServiceFacade.isManager())
            response = userServiceFacade.findPage(pageQuery);
        return ResponseEntity.ok(response);
    }

    //  @PostMapping(value = "create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//  public ResultCodeResponse create(@RequestBody UserCreate userCreate) {
//    userServiceFacade.createUser(userCreate);
//    return ResultCodeResponse.successResponse();
//  }
//
    @PostMapping(value = "update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResultCodeResponse update(@RequestBody UserDto userDto) {
        userServiceFacade.updateRole(userDto);
        return ResultCodeResponse.successResponse();
    }
//
//  @PostMapping(value = "remove")
//  public ResultCodeResponse remove(@RequestBody UserRemove userRemove) {
//    userServiceFacade.removeBatchById(userRemove.getUserId());
//    return ResultCodeResponse.successResponse();
//  }
//
//  @ApiOperation("用户注册")
//  @PostMapping(value = "register")
//  public ResponseEntity<Map<String, Object>> register(@RequestBody @Validated UserUpdate userUpdate, ChangePasswordParam param) {
//    return userServiceFacade.saveBaseInfo(userUpdate);
//  }
//
//  @ApiOperation("忘记密码用户验证")
//  @PostMapping(value = "uservalidate")
//  public ResponseEntity<Map<String, Object>> userValidate(@Valid @RequestBody QueryParam queryParam) {
//    return userServiceFacade.userValidate(queryParam);
//  }
//
//  @ApiOperation("忘记密码邮箱验证码验证")
//  @PostMapping(value = "codevalidate")
//  public ResponseEntity<Map<String, Object>> codevalidate(@Valid @RequestBody QueryParam queryParam) {
//    return userServiceFacade.codevalidate(queryParam);
//  }
//
//  @ApiOperation("忘记密码修改密码")
//  @PostMapping(value = "changepassword")
//  public ResponseEntity<Map<String, Object>> changepassword(@Valid @RequestBody QueryParam queryParam) {
//    return userServiceFacade.changepassword(queryParam);
//  }
//
//  @ApiOperation("忘记密码邮件发送")
//  @PostMapping(value = "sendemailcode")
//  public ResponseEntity<Map<String, Object>> emailValidate(@Valid @RequestBody QueryParam queryParam) {
//    /*获取前端参数*/
//    String email = queryParam.getString("email");//获取邮箱
//    String username = queryParam.getString("username");//获取邮箱
//    /*验证邮箱并返回提示信息*/
//    Map<String, Object> result = new HashMap<>();//创建返回提示信息JSONOBJECT对象
//    /*验证邮箱*/
//    if (email != null) {
//      //根据用户名获取用户信息
//      UserDto user = userServiceFacade.getByUserName(username);
//      if (user != null) {
//        //验证输入的邮箱是否和注册的邮箱一致
//        if (email.equals(user.getEmail())) {
//          String randkey = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));// 产生随机激活码并加密
//          // 构造邮件内容
//          String emailsubject = "福建省版权局作品自愿登记系统密码重新设置邮件";// 构造主题
//          String emailcontent = getEmailContent(user.getUserName(), randkey);// 构造邮件正文
//
//          if (email != null && !"".equals(email)) {
//            /*邮件发送*/
//            Map<String, Object> param = new HashMap<String, Object>();
//            param.put("sendto", email);
//            param.put("title", emailsubject);
//            param.put("content", emailcontent);
//            HttpClientUtil.HttpConcent(JSONObject.toJSONString(param), "http://172.27.101.177:18081/sendmail");//发送邮件
//
//            /*更新数据库激活码*/
//            user.setRandKey(randkey);//设置加密激活码
//            userServiceFacade.updateUser(user);
//          }
//        } else {
//          result.put("message", "邮箱不正确");
//          return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
//        }
//      } else {
//        result.put("message", "用户不存在");
//        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
//      }
//    } else {
//      result.put("message", "输入的邮箱不能为空");
//      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
//    }
//    result.put("message", "发送验证码成功！");
//    return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
//  }

    @ApiOperation("设置首页")
    @PostMapping(value = "updateHomePage")
    private ResponseEntity<Map<String, Object>> updateHomePage(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();//创建返回提示信息JSONOBJECT对象
        return userServiceFacade.updateHomePage(queryParam);
    }

//  /**
//   * 构造邮件正文
//   */
//  public String getEmailContent(String username, String randkey) {
//    String first = "<h1>亲爱的" + username + "</h1></br>";
//    String second = "<h2>您的验证码为：" + randkey + "</h2></br>";
//    String end = "此邮件由系统发送，请勿回复。";
//    return first + second + end;
//  }
//
//  /**
//   * 解密
//   *
//   * @param ciphertext 密码的密文
//   * @return
//   */
//  private String decryptPassword(String ciphertext) {
//    String password = ciphertext;
//    try {
//      if (StringUtils.isNotBlank(rsaPrikey)) {
//        password = new String(
//          RSAUtil.decryptPri(Base64.decodeBase64(ciphertext), Base64.decodeBase64(rsaPrikey)));
//      }
//    } catch (Exception e) {
//      log.error("error", e);
//    }
//    return password;
//  }

    /**
     * Object to return as body in JWT Authentication.
     */
    static class JwtToken {

        private String token;
        private Map<String, Object> user;

        JwtToken(String token) {
      this.token = token;
    }

    JwtToken(String token, Map<String, Object> user) {
      this.token = token;
      this.user = user;
    }

    @JsonProperty("token")
    String getToken() {
      return token;
    }

    void setToken(String token) {
      this.token = token;
    }

    public Map<String, Object> getUser() {
      return user;
    }

    public void setUser(Map<String, Object> user) {
      this.user = user;
    }

    }
}
