package blockChain.annotation;

import org.springframework.core.annotation.AliasFor;
import org.springframework.format.annotation.DateTimeFormat;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2019/7/5 15:03
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
@DateTimeFormat
public @interface LocalDateFormat {
  @AliasFor(annotation = DateTimeFormat.class)
  String pattern() default "yyyy-MM-dd";
}
