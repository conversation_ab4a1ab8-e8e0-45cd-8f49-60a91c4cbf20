package blockChain.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 统计通用DTO
 *
 * <AUTHOR>
 * @date 2020/3/31 16:45
 */
@Data
@ApiModel(description = "统计通用DTO")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class StatisticDto {
  private String key;
  private String name;
  private Long amount;
  private String id;
  private String area;
  private Boolean isleaf;

  public StatisticDto(Integer key, String name, Long amount) {
    this.key = String.valueOf(key);
    this.name = name;
    this.amount = amount;
  }

  public StatisticDto(Integer key, String name, Long amount,Boolean isleaf) {
    this.key = String.valueOf(key);
    this.name = name;
    this.amount = amount;
    this.isleaf = isleaf;
  }

  public StatisticDto(String key, String name, Long amount) {
    this.key = key;
    this.name = name;
    this.amount = amount;
  }

  public StatisticDto(String id, String name, Long amount,Boolean isleaf) {
    this.id = id;
    this.name = name;
    this.amount = amount;
    this.isleaf = isleaf;
  }

  public StatisticDto(String id, String name, Long amount,String typeKey,Boolean isleaf) {
    this.id = id;
    if(typeKey.equals("name")){
      this.name = name;
    }else{
      this.area = name;
    }
    this.amount = amount;
    this.isleaf = isleaf;
  }

  public StatisticDto(String id, String key, String name, Long amount,Boolean isleaf) {
    this.id = id;
    this.key = key;
    this.name = name;
    this.amount = amount;
    this.isleaf = isleaf;
  }

  public StatisticDto(Integer key, Long amount) {
      this.key = String.valueOf(key);
      this.amount = amount;
  }
}
