package blockChain.controller;

import blockChain.bean.Constant;
import blockChain.bean.PageQuery;
import blockChain.bean.PageResponse;
import blockChain.bean.ResultCodeResponse;
import blockChain.constant.AuthoritiesConstant;
import blockChain.dto.RoleDto;
import blockChain.dto.role.RoleSave;
import blockChain.entities.RoleEntity;
import blockChain.exception.EntityNotFoundException;
import blockChain.facade.service.RoleServiceFacade;
import blockChain.facade.service.UserServiceFacade;
import blockChain.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019/12/20 11:22
 */
@Slf4j
@RestController
@RequestMapping("role")
@RequiredArgsConstructor
public class RoleController {

    private final RoleServiceFacade roleServiceFacade;

    private final UserServiceFacade userServiceFacade;

    private final RoleService roleService;

    @PostMapping("query")
    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.AuthorityManagement + "')")
    public PageResponse<RoleDto> findPage(@RequestBody PageQuery<RoleDto> pageQuery) {
        return roleServiceFacade.findPage(pageQuery);
    }

    @PostMapping("get")
    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.AuthorityManagement + "')")
    public RoleDto findRoleById(@RequestBody RoleDto roleDto) {
        return roleServiceFacade.findRoleById(roleDto.getId());
    }

    @PostMapping("create")
    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.AuthorityManagement + "')")
    public ResultCodeResponse create(@RequestBody RoleSave roleSave) {
        roleServiceFacade.create(roleSave);
        return ResultCodeResponse.successResponse();
    }

    @PostMapping("update")
    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.AuthorityManagement + "')")
    public ResultCodeResponse update(@RequestBody RoleSave roleSave) {
        roleServiceFacade.update(roleSave);
        return ResultCodeResponse.successResponse();
    }

    @PostMapping("remove")
    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.AuthorityManagement + "')")
    public ResultCodeResponse remove(@RequestBody RoleDto roleDto) {
        RoleEntity roleEntity = roleService.findById(roleDto.getId()).orElseThrow(() -> new EntityNotFoundException("该角色未找到"));
        if (userServiceFacade.existsByRoleId(roleDto.getId())) {
            return ResultCodeResponse.failResponse("存在用户是该角色，请变更所有存在该角色的用户后删除");
        }
        if (roleEntity.getIsNecessary() == Constant.BYTE_TRUE) {
            return ResultCodeResponse.failResponse("该角色是系统必须角色无法删除");
        }
        roleServiceFacade.remove(roleEntity);
        return ResultCodeResponse.successResponse();
    }
}
