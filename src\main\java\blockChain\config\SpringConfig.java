package blockChain.config;

import com.ctsi.ssdc.database.annotation.MapperByDataBaseScan;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableCaching
@Configuration
@EnableScheduling
@ComponentScan(basePackages = {"com.ctsi.*", "blockChain.*"})
@MapperByDataBaseScan(basePackages="com.ctsi.ssdc.admin.repository.*")
@EnableConfigurationProperties({LiquibaseProperties.class})
@EnableJpaAuditing
@Data
public class SpringConfig {

	@Value("${block.file.fileRoot}")
	private String fileRoot;

	public String getFileRoot() {
		return fileRoot;
	}

    @Value("${block.blockUrl.url}")
    private String blockUrl;

    public String getBlockUrl() {
        return blockUrl;
    }

    @Value("${block.images.imageRoot}")
    private String imageRoot;

    public String getImageRoot() {
        return imageRoot;
    }

    @Value("${block.frontUrl.url}")
    private String frontUrl;

    public String getFrontUrl() {return frontUrl;}

    @Value("${block.oldSystemUrl.url}")
    private String oldSystemUrl;

  @Value("${block.biyiServiceUrl.url}")
  private String biyiServiceUrl;

  public String getOldSystemUrl() {
      return oldSystemUrl;
  }

    @Value("${block.file.modelRoot}")
    private String modelRoot;

    public String getModelRoot() {
        return modelRoot;
    }

    @Value("${block.file.imgSize}")
    private Long imgSize;

    public Long getImgSize() {
        return imgSize;
    }

    @Value("${block.file.fileSize}")
    private Long fileSize;

    public Long getFileSize() {
        return fileSize;
    }

    @Value("${block.file.videoSize}")
    private Long videoSize;

    public Long getVideoSize() {
        return videoSize;
    }

    public String getBiyiServiceUrl() {
        return biyiServiceUrl;
    }

    @Value("${block.proxyIp}")
    private String proxyIp;

    @Value("${block.proxyPort}")
    private Integer proxyPort;

    public String getProxyIp() {
        return proxyIp;
    }

  public Integer getProxyPort() { return proxyPort; }

    @Value("${block.convergence.wsdl}")
    private String wsdl;

    public String getWsdl() {
        return wsdl;
    }

    @Value("${block.convergence.userName}")
    private String conUserName;

    public String getConUserName() {
        return conUserName;
    }

    @Value("${block.convergence.passWord}")
    private String conPassWord;

    public String getConPassWord() {
        return conPassWord;
    }

    @Value("${block.convergence.catalogId_manager}")
    private String catalogId_manager;

    public String getCatalogId_manager() {
        return catalogId_manager;
    }

    @Value("${block.convergence.catalogId_owner}")
    private String catalogId_owner;

    public String getCatalogId_owner() {
        return catalogId_owner;
    }

    @Value("${block.convergence.catalogId_author}")
    private String catalogId_author;

    @Value("${block.isExtranet}")
    private Boolean isExtranet;

    public Boolean getIsExtranet() {
        return isExtranet;
    }

    @Value("${block.autoSubmit}")
    private Boolean autoSubmit;

    public Boolean getAutoSubmit() {
      return autoSubmit;
    }

    @Value("${block.certificate.cerUrl}")
    private String certificateUrl;

    public String getCertificateUrl() {
      return certificateUrl;
    }

    @Value("${block.certificate.picUrl}")
    private String certificatePicUrl;

  public String getCertificatePicUrl() {
    return certificatePicUrl;
  }

    @Value("${block.certificate.documentUrl}")
    private String certificateDocUrl;

    public String getCertificateDocUrl() {
      return certificateDocUrl;
    }

    @Value("${block.similarScore}")
    private Integer similarScore;

    public Integer getSimilarScore() {
      return similarScore;
    }

//    @Value("${block.license.url}")
//    private String licenseUrl;
//
//    public String getLicenseUrl() {
//      return licenseUrl;
//    }
//
//    @Value("${block.license.guid}")
//    private String licenseGuid;
//
//    public String getLicenseGuid() {
//      return licenseGuid;
//    }
//
//    @Value("${block.license.surface}")
//    private String licenseSurface;
//
//    public String getLicenseSurface() {
//      return licenseSurface;
//    }


    @Value("${block.license.tokenUrl}")
    private String licenseTokenUrl;

    public String getLicenseTokenUrl() {
      return licenseTokenUrl;
    }


    @Value("${block.license.saveUrl}")
    private String licenseSaveUrl;

    public String getLicenseSaveUrl() {
      return licenseSaveUrl;
    }


    @Value("${block.license.accountId}")
    private String licenseAccountId;

    public String getLicenseAccountId() {
      return licenseAccountId;
    }


    @Value("${block.license.priKey}")
    private String licensePriKey;

    public String getLicensePriKey() {
        return licensePriKey;
    }

    @Value("${block.autoEvaluationDay}")
    private Integer autoEvaluationDay;

    public Integer getAutoEvaluationDay() {
        return autoEvaluationDay;
    }

    @Value("${block.evaluation.wsdl}")
    private String evaluationWsdl;

    public String getEvaluationWsdl() {
        return evaluationWsdl;
    }

    @Value("${block.evaluation.namespace}")
    private String evaluationNamespace;

    public String getEvaluationNamespace() {
        return evaluationNamespace;
    }

    @Value("${block.evaluation.serviceWsdl}")
    private String evaluationServiceWsdl;

    public String getEvaluationServiceWsdl() {
        return evaluationServiceWsdl;
    }

    @Value("${block.evaluation.ruleWsdl}")
    private String evaluationRuleWsdl;

    public String getEvaluationRuleWsdl() {
        return evaluationRuleWsdl;
    }

    @Value("${block.backend.oauth.tokenUrl}")
    private String backendOauthTokenUrl;

    @Value("${block.backend.oauth.clientId}")
    private String backendOauthClientId;

    @Value("${block.backend.oauth.clientSecret}")
    private String backendOauthClientSecret;

    @Value("${block.backend.projectIdGet.url}")
    private String backendProjectIdGetUrl;

    @Value("${block.backend.projectIdActive.url}")
    private String backendProjectIdActiveUrl;

    @Value("${block.backend.callbackUrl}")
    private String backendCallbackUrl;

    /**
     * 省办件办理信息推送url
     */
    @Value("${block.backend.startWorkflow.url}")
    private String backendStartWorkflow;

    /**
     * 办件推送 流程启动id
     */
    @Value("${block.backend.startWorkflow.baseInfoId}")
    private String baseInfoId;
    /**
     * 受理 流程启动id
     */
    @Value("${block.backend.startWorkflow.acceptId}")
    private String acceptId;
    /**
     * 补齐补正告知 流程启动id
     */
    @Value("${block.backend.startWorkflow.patchId}")
    private String patchId;
    /**
     * 补齐补正结束 流程启动id
     */
    @Value("${block.backend.startWorkflow.patchEndId}")
    private String patchEndId;
    /**
     * 办件环节数据 流程启动id
     */
    @Value("${block.backend.startWorkflow.nodeTransId}")
    private String nodeTransId;
    /**
     * 办结+证照信息 流程启动id
     */
    @Value("${block.backend.startWorkflow.transactId}")
    private String transactId;

    /**
     * 部门编码
     */
    @Value("${block.info.deptCode}")
    private String deptCode;

    /**
     * 服务编码/事项编码
     */
    @Value("${block.info.serviceCode}")
    private String serviceCode;

    /**
     * 基本编码
     */
    @Value("${block.info.baseCode}")
    private String baseCode;

    /**
     * 实施编码
     */
    @Value("${block.info.implementCode}")
    private String implementCode;

    /**
     * 业务办理项编码
     */
    @Value("${block.info.taskhandleitem}")
    private String taskhandleitem;

    @Value("${block.info.belongSystem}")
    private String belongSystem;

    @Value("${block.info.belongSystemRetry}")
    private String belongSystemRetry;

    /**
     * 以下闽政通相关配置
     */
    @Value("${block.mzt.appId}")
    private String appId;

    @Value("${block.mzt.appSecret}")
    private String appSecret;

    @Value("${block.mzt.url}")
    private String mztUrl;

    @Value("${block.mzt.appToken}")
    private String mztAppToken;

    @Value("${block.mzt.personalAuthorization}")
    private String mztPersonalAuthorization;

    @Value("${block.mzt.PAVerification}")
    private String mztPAVerification;

    private String mztAuthorize;

    @Value("${block.mzt.accessToken}")
    private String mztAccessToken;

    @Value("${block.mzt.userInfo}")
    private String mztUserInfo;

    @Value("${block.mzt.verify}")
    private String mztVerify;

    @Value("${block.mzt.logout}")
    private String mztLogout;

    @Value("${block.mzt.personAgencyQuery}")
    private String personAgencyQuery;

    @Value("${block.mzt.companyAgencyQuery}")
    private String companyAgencyQuery;

    @Value("${block.mzt.attnAgencyQuery}")
    private String attnAgencyQuery;

    /**
     * 以下中枢相关配置
     */
    @Value("${block.zhongshu.paasid}")
    private String zsPaasid;

    @Value("${block.zhongshu.token}")
    private String zsToken;

    @Value("${block.zhongshu.sm2_public_key}")
    private String zsSm2PublicKey;

    @Value("${block.zhongshu.modelKey}")
    private String zsModelKey;

    @Value("${block.zhongshu.url}")
    private String zsUrl;

    @Value("${block.zhongshu.getFormData}")
    private String zsGetFormData;

    @Value("${block.zhongshu.formDataReflux}")
    private String zsFormDataReflux;

    @Value("${block.backend.startWorkflow.systemNo}")
    private String systemNo;

    @Value("${block.backend.isOn}")
    private Boolean backendIsOn;

    @Value("${block.loginCodeValidity}")
    private Integer loginCodeValidity;

    @Value("${block.submitInterval}")
    private long submitInterval;

    @Value("${block.submitIntervalMax}")
    private long submitIntervalMax;
}
