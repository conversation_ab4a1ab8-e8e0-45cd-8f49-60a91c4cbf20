/**
 * WaiLianServiceImpl.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx;

public interface WaiLianServiceImpl extends java.rmi.Remote {
    public com.linewell.apas.wailian.monitor.SwapMonitor swapLog(java.lang.String xmlString, java.lang.String deptcode, java.lang.String wsspaction) throws java.rmi.RemoteException;
    public java.lang.String getCurTrafficControl(java.lang.String test) throws java.rmi.RemoteException;
    public java.lang.String releaseQueue(java.lang.String sn) throws java.rmi.RemoteException;
    public java.lang.String getProJid(java.lang.String deptcode, java.lang.String password, java.lang.String projectName) throws java.rmi.RemoteException;
    public java.lang.String apasserviceGetByOrgcode(java.lang.String orgcode) throws java.rmi.RemoteException;
    public java.lang.Object[] reToJson(java.lang.Object[][] rs) throws java.rmi.RemoteException;
    public java.lang.String apasserviceGetByOrgcodeAndLicenceName(java.lang.String orgcode, java.lang.String licenceName) throws java.rmi.RemoteException;
    public java.lang.String apasserviceGetByLicenceNam(java.lang.String licenceName) throws java.rmi.RemoteException;
    public java.lang.String newSubmit(java.lang.String deptcode, java.lang.String password, java.lang.String xmlString) throws java.rmi.RemoteException;
    public java.lang.String submit(java.lang.String deptcode, java.lang.String password, java.lang.String xmlString) throws java.rmi.RemoteException;
}
