package blockChain.mapper;

import blockChain.dto.ArticleDto;
import blockChain.entities.ArticleEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/15 15:44
 */
@Mapper(config = CommonConfig.class)
public interface ArticleDtoMapper {
  ArticleDtoMapper INSTANCE = Mappers.getMapper(ArticleDtoMapper.class);

  ArticleEntity toEntity(ArticleDto dto);

  @Mappings({
    @Mapping(target = "uuid", ignore = true),
    @Mapping(target = "createTime", ignore = true),
    @Mapping(target = "updateTime", ignore = true),
  })
  void update(ArticleDto dto,@MappingTarget ArticleEntity entity);

  ArticleDto toDto(ArticleEntity entity);

  List<ArticleDto> toDto(Iterable<ArticleEntity> list);
}
