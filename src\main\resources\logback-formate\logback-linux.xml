<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
  <property name="LOGS" value="/home/<USER>/copyright"/>
  <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} | %magenta(%15thread) | %highlight(%-5level) %logger{50} - %boldYellow(%msg)  %n</pattern>
    </encoder>
  </appender>
  <appender name="sysFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <FileNamePattern>
        ${LOGS}/sys/sys.%d{yyyy-MM-dd}.log
      </FileNamePattern>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} %c-%L[%t]%n%p: %m%n</pattern>
      <charset class="java.nio.charset.Charset">UTF-8</charset>
    </encoder>
  </appender>

  <appender name="debugFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <FileNamePattern>
        ${LOGS}/debug/debug.%d{yyyy-MM-dd}.log
      </FileNamePattern>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} %c-%L[%t]%n%p: %m%n</pattern>
      <charset class="java.nio.charset.Charset">UTF-8</charset>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>DEBUG</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>

  <appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <FileNamePattern>
        ${LOGS}/info/info.%d{yyyy-MM-dd}.log
      </FileNamePattern>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} %c-%L[%t]%n%p: %m%n</pattern>
      <charset class="java.nio.charset.Charset">UTF-8</charset>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>

  <appender name="warnFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <FileNamePattern>
        ${LOGS}/warn/warn.%d{yyyy-MM-dd}.log
      </FileNamePattern>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} %c-%L[%t]%n%p: %m%n</pattern>
      <charset class="java.nio.charset.Charset">UTF-8</charset>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>WARN</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>

  <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <FileNamePattern>
        ${LOGS}/error/error.%d{yyyy-MM-dd}.log
      </FileNamePattern>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} %c-%L[%t]%n%p: %m%n</pattern>
      <charset class="java.nio.charset.Charset">UTF-8</charset>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>



  <root level="debug">
    <Appender-Ref ref="console" level="all"/>
<!--    <Appender-Ref ref="sysFile" level="sys"/>-->
<!--    <Appender-Ref ref="debugFile" level="debug"/>-->
    <Appender-Ref ref="infoFile" level="info"/>
    <Appender-Ref ref="warnFile" level="warn"/>
    <Appender-Ref ref="errorFile" level="error"/>
  </root>
</configuration>
