package blockChain.controller;

import blockChain.config.SpringConfig;
import blockChain.facade.service.CertificateServiceFacade;
import blockChain.facade.service.DigitalServiceFacade;
import blockChain.facade.service.UserServiceFacade;
import com.ctsi.ssdc.security.UserLoginValidator;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/5/9 12:00
 */
@Api("对接第三方用户管理")
@Slf4j
@RestController
@RequestMapping("apiuser")
@RequiredArgsConstructor
public class LoginController {

  @Value("${ctsi.RSA-prikey:}")
  private String rsaPrikey = "";

  @Autowired
  private UserServiceFacade userServiceFacade;

  @Autowired(required = false)
  private UserLoginValidator userLoginValidator;

    @Autowired
    private DigitalServiceFacade digitalServiceFacade;
    @Autowired
    private CertificateServiceFacade certificateServiceFacade;

    private final SpringConfig config;
    private final static Logger logger = LoggerFactory.getLogger(LoginController.class);

//  @ApiOperation("查找指定id用户")
//  @GetMapping("{id}")
//  public UserDto findUserById(@ApiParam("用户id") @PathVariable Integer id) {
//    return userServiceFacade.findUserById(id);
//  }

//    @ApiOperation("验证是否已登录1")
//    @GetMapping("loginlogin")
//    public void loginlogin(HttpServletRequest request, HttpServletResponse response) {
//        response.setHeader("Access-Control-Allow-Credentials", "true");
//        String uri = config.getFrontUrl() + "api/qkl/apiuser/checkSession";
//        try {
//            response.sendRedirect("https://mztapp.fujian.gov.cn:8304/dataset/UnifiedController/checkLoginStatus.do" +
//                    "?checkbackurl=" + uri + "&callerCode=2c9bbe5d71ff3606017201801f3f75d72c9bbe5d71ff3606");
//        } catch (IOException e) {
//      log.error("error", e);
//    }
//  }
//
//    @ApiOperation("验证是否已登录2")
//    @GetMapping("checkSession")
//  public void checkSession(HttpServletRequest request,HttpServletResponse response){
//        // 跳转到 SSO 登录界面前，先判断自己本地 session 中是否存在用户信息，不存在则跳转到地址福建省统一身份认证平台
//    response.setHeader("Access-Control-Allow-Credentials", "true");
//    String uri = config.getFrontUrl()+"api/qkl/apiuser/loginReturn";
//    String loginflag = request.getParameter("loginflag");
//    try {
//      if (loginflag != null && "true".equals(loginflag)) {
//        String trustticket = request.getParameter("trustticket");
//        response.sendRedirect(config.getFrontUrl()+"api/qkl/apiuser/loginReturn?trustticket=" + trustticket);
//      } else {
//          // 首先用户访问福建省统一身份认证平台
//        response.sendRedirect("https://mztapp.fujian.gov.cn:8304/dataset/UnifiedController/goLogin.do?callerCode=2c9bbe5d71ff3606017201801f3f75d72c9bbe5d71ff3606" +
//          "&returnurl=" + uri);
//      }
//    }catch (Exception e){
//      log.error("error", e);
//    }
//  }
//
//    @ApiOperation("用户本系统登录验证3")
//    @GetMapping("loginReturn")
//  public void loginReturn(HttpServletRequest request, HttpServletResponse response) {
//    String trustticket = request.getParameter("trustticket");
//    UserEntity currentUser = userServiceFacade.updateProvinceUser(trustticket);
//
//    String uri ;
//    if (currentUser == null)
//      uri = config.getFrontUrl() + "indexPage";
//    else
//        uri = config.getFrontUrl() + "indexPage?userName=" + currentUser.getUserName() + "&code=" + currentUser.getRandKey();
//    try {
//      response.sendRedirect(uri);
//    } catch (IOException e) {
//      log.error("error", e);
//    }
//  }
//
//  @ApiOperation("获取用户登录信息")
//  @PostMapping("loginIn")
//  public ResponseEntity<UserController.JwtToken> loginIn(@Valid @RequestBody QueryParam queryParam,HttpServletRequest request) {
//      //获取登陆人真实ip
//      String ipAddr = CollectionTools.getIpAddr(request);
//
//      String userName = queryParam.getString("userName");
//      String randKey = queryParam.getString("code");
//
//      if (StringUtils.isEmpty(userName) || StringUtils.isEmpty(randKey)) {
//          throw new EntityNotFoundException("该用户登录已过期或未进行实名认证，请重新登录或登录闽政通APP进行实名认证后再进行登录。");
//      }
//      UserDto currentUser = userServiceFacade.getByUserName(userName);
//      if (StringUtils.isEmpty(currentUser.getCardId()) || !randKey.equals(currentUser.getRandKey()) || LocalDateTime.now().isAfter(currentUser.getRandKeyExpireTime())) {
//          throw new EntityNotFoundException("该用户登录已过期或未进行实名认证，请重新登录或登录闽政通APP进行实名认证后再进行登录。");
//      }
//      UserForm user = new UserForm();
//      user.setPassword("abc123456@");
//      user.setUsername(currentUser.getUserName());
//      user.setRememberme(0);
//      JwtResult jwtObject = userServiceFacade.identityAuthorize(user, userLoginValidator);
//      String jwt = jwtObject.getJwt();
//
//    HttpHeaders httpHeaders = new HttpHeaders();
//    String token = JWTConfigurer.AUTHORIZATION_BEARER + jwt;
//
//    httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, token);
//    //组装用户信息
//    CscpUserDetail userDetail = jwtObject.getUserDetail();
//    UserDto userDto = userServiceFacade.findUserById(userDetail.getId());
//    if (userDto.getIsWorkstation() != null && userDto.getIsWorkstation() == Constant.BYTE_TRUE)
//      userDto.setRoleName("福建省版权登记" + userDto.getRoleName());
//    //设置登录用户ip
//    userDto.setIp(ipAddr);
//    userServiceFacade.updateUser(userDto);
//    CopyrightOwnerDto copyrightOwnerDto = new CopyrightOwnerDto();
//    SubmitterDto submitterDto = new SubmitterDto();
//    AgentDto agentDto = new AgentDto();
//    //自动填充的著作权人和填报人信息
//    setOwner(copyrightOwnerDto,submitterDto,agentDto,userDto);
//    Map<String, Object> userMap = new HashMap<String, Object>() {{
//      put("id", userDetail.getId());
//      put("userName", userDetail.getUsername());
//      put("authorities", userDetail.getAuthorities());
//      put("visibleLevelCity", userDto.getVisibleLevelCity());
//      put("visibleLevelProvince", userDto.getVisibleLevelProvince());
//      put("visibleLevelCounty", userDto.getVisibleLevelCounty());
//      put("isProxyer",userDto.getIsProxyer());
//      put("realName",userDto.getRealName());
//      put("userMessage",userDto);
//      put("isManager",(userDto.getRoleId()==RoleEntity.ROLE_USER)?false:true);
//      put("owner",copyrightOwnerDto);
//      put("submitter",submitterDto);
//      put("homePageKey",userDto.getHomePageKey());
//    }};
//    //if(userDto.getIsProxyer()!=null && userDto.getIsProxyer()==2){
//      userMap.put("agent",agentDto);
//    //}
//
//      // 添加log
//      logger.info("用户登录："+userDto.getUserName());
//    return new ResponseEntity<>(new UserController.JwtToken(token, userMap), httpHeaders, HttpStatus.OK);
//  }
//
//  @ApiOperation("登出")
//  @GetMapping("logout")
//  public void loginout(HttpServletRequest request,HttpServletResponse response) {
//    String uri = config.getFrontUrl();
//    String returnUrl = "https://mztapp.fujian.gov.cn:8304/dataset/UnifiedController/goUserCenter.do?uitype=5&caller" +
//      "Code=2c9bbe5d71ff3606017201801f3f75d72c9bbe5d71ff3606&returnurl="+uri;
//    try {
//      response.sendRedirect(returnUrl);
//    } catch (IOException e) {
//      log.error("error", e);
//    }
//  }
//
//  private int parseIntSafe(String number)
//  {
//    try
//    {
//      return Integer.parseInt(number);
//    }
//    catch (Exception e)
//    {
//      log.error("error", e);
//    }
//    return 0;
//  }
//
//  private void setOwner(CopyrightOwnerDto owner, SubmitterDto submitter, AgentDto
//     agent,UserDto user){
//    owner.setCopyName(user.getRealName());
//    owner.setCopyCountries(user.getCountryName());
//    owner.setCopyProvince(user.getProvinceName());
//    owner.setCopyCity(user.getCityName());
//    owner.setCopyCounty(user.getCountyName());
//    owner.setCopyCategory(user.getUserId());
//    if(user.getIdentityKind()==1){
//      owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.PEOPLE);
//      owner.setCopyCertificate(user.getCardType());
//    }else{
//      if (user.getLegalPersionType() == null) {
//        owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.OTHERORG);
//        owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.OTHER2);
//      } else {
//        switch (user.getLegalPersionType()) {
//          case UserEntity.LegalPersionTypeValue.COMPANYPEOPLE:
//          case UserEntity.LegalPersionTypeValue.INDIVIDUAL:
//            owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.COMPANYPEOPLE);
//            owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.CODE);
//            break;
//          case UserEntity.LegalPersionTypeValue.SOCIALORG:
//            owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.SOCIALORG);
//            owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.SOCIALORGZS);
//            break;
//          case UserEntity.LegalPersionTypeValue.INSTITUTION:
//            if (user.getCardId().startsWith("11")) {
//              owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.GOVERNMENT);
//              owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.GOVERNMENTZS);
//            } else {// 事业单位 统一社会信用代码前2位：12
//              owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.INSTITUTION);
//              owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.INSTITUTIONZS);
//            }
//              break;
//            default:
//                owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.OTHERORG);
//                owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.OTHER2);
//                break;
//        }
//      }
////      owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.CODE);
//    }
//      owner.setCopyIdCard(user.getCardId());
//      // 证照调用去掉
////    CertificateEntity certificateEntity = new CertificateEntity();
////    if (config.getIsExtranet()) {
////      certificateEntity = certificateServiceFacade.getIndexByHolderCode(user.getCardId(), user.getRealName(), new ArrayList<Integer>(){{add(owner.getCopyCertificate());}});
////      if (StringUtils.isEmpty(certificateEntity.getId()) && owner.getCopyCertificate() == CopyrightOwner.CopyCertificateValue.IDCARD) {
////        certificateEntity = certificateServiceFacade.getIndexByHolderCode(user.getCardId(), user.getRealName(), new ArrayList<Integer>(){{add(CopyrightOwner.CopyCertificateValue.RESIDENT);}});
////        if (!StringUtils.isEmpty(certificateEntity.getId()))
////          owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.RESIDENT);
////      }
////    }
////    owner.setCopyCertificateZM(certificateEntity);
//      //设置著作权人地址字符串
//      if (user.getCountryName() != null && user.getProvinceName() != null && user.getCityName() != null &&
//              !user.getCountryName().isEmpty() && !user.getProvinceName().isEmpty() && !user.getCityName().isEmpty()) {
//          Digital country = digitalServiceFacade.getById(parseIntSafe(user.getCountryName()));
//          Digital province = digitalServiceFacade.getById(parseIntSafe(user.getProvinceName()));
//          Digital city = digitalServiceFacade.getById(parseIntSafe(user.getCityName()));
//          String copyAreaNamesStr = country.getDict_name() + "," + province.getDict_name() + "," + city.getDict_name();
//          if (user.getCountyName() != null && !user.getCountyName().isEmpty()) {
//              Digital county = digitalServiceFacade.getById(parseIntSafe(user.getCountyName()));
//              copyAreaNamesStr += "," + county.getDict_name();
//      }
//      owner.setCopyAreaNamesStr(copyAreaNamesStr);
//    }
//
//    submitter.setCopyrightName(user.getRealName());
//    submitter.setCopyrightAddress(user.getAddress());
//    submitter.setCopyrightPhone(user.getPhoneNum());
//    submitter.setCopyrightEmail(user.getEmail());
//
//    agent.setAgentName(user.getRealName());
//    agent.setAgentPhone(user.getPhoneNum());
//    agent.setAgentAddress(user.getAddress());
//    agent.setAgentEmail(user.getEmail());
//  }
//
//  @ApiOperation("获取用户登录信息")
//  @PostMapping("checkToken")
//  public ResponseEntity<Map<String,Object>> checkToken(@Valid @RequestBody QueryParam queryParam){
//    Integer id = queryParam.getInteger("id");
//    UserDto user = userServiceFacade.findUserById(id);
//    Map<String,Object> param = new HashMap<>();
//    Map<String,Object> resultMap = new HashMap<>();
//    param.put("INVOKESERVICE_CODE","033");
//    param.put("INVOKECALLER_CODE","2c9bbe5d71ff3606017201801f3f75d72c9bbe5d71ff3606");
//    param.put("USER_ID",user.getUuid());
//    param.put("USER_TOKEN",user.getCardImgf());
//    String json = JSON.toJSONString(param);
//    Map<String,Object> clientParam = new HashMap<String,Object>();
//    clientParam.put("POSTPARAM_JSON", json);
//    try {
//      if (config.getIsExtranet()) {
//        String url = "https://mztapp.fujian.gov.cn:8304/dataset/AppSerController/invokeservice.do";
//        String result = HttpUtil.sendPostParams(url, clientParam, config.getProxyIp(), config.getProxyPort());
//        if (result==null) {
//          resultMap.put("result", "success");
//        } else {
//          com.alibaba.fastjson.JSONObject object = JSON.parseObject(result);
//          String message = "";
//          if (object.containsKey("msg") && object.get("msg") != null) {
//            message = (String) object.get("msg");
//          }
//          if (object.containsKey("success") && object.get("success") != null) {
//            if (!object.getBoolean("success")) {
//              resultMap.put("result", "failed");
//              resultMap.put("message", message);
//            } else {
//              resultMap.put("result", "success");
//            }
//          }
//        }
//      } else {
//        resultMap.put("result", "success");
//      }
//    }catch (Exception e){
//      resultMap.put("result", "success");
//    }
//    return new ResponseEntity<Map<String,Object>>(resultMap,HttpStatus.OK);
//  }
}
