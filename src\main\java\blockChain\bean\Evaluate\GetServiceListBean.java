package blockChain.bean.Evaluate;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * Created by epcsoft on 2020/8/3.
 */

@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "Case")
// 控制JAXB 绑定类中属性和字段的
@Data
public class GetServiceListBean implements Serializable
{
  private static final long serialVersionUID = 1L;

  String sysNO;//	系统编号	String	是
  String creditCode;//	统一社会信用代码	String	是
  public String toXmlString() {
    return "<?xml version=\"1.0\" encoding=\"GB2312\"?>"
            + "<Case>"
            + "<sysNO>" +sysNO+ "</sysNO>"
            + "<creditCode>" +creditCode+ "</creditCode>"
            + "</Case>";
  }

}
