package blockChain.constant;

/**
 * <AUTHOR>
 * @date 2024/11/11
 */
public interface AuthoritiesConstant {
    /**
     * 管理员首页
     */
    String HomeManagement = "HomeManagement";
    /**
     * 工作站首页
     */
    String HomeWorkstation = "HomeWorkstation";
    /**
     * 我的作品
     */
    String MyCreation = "MyCreation";

    /**
     * 草稿箱
     */
    String DraftBox = "DraftBox";

    /**
     * 作品初审列表
     */
    String FirstReview = "FirstReview";

    /**
     * 作品复审
     */
    String SecondReview = "SecondReview";

    /**
     * 作品终审
     */
    String FinalReview = "FinalReview";

    /**
     * 作品上报国家局
     */
    String NationSubmit = "NationSubmit";

    /**
     * 证照信息提交
     */
    String LicenseInfoSubmit = "LicenseInfoSubmit";

    /**
     * 版保中心导出
     */
    String ReportCoypRight = "ReportCoypRight";

    /**
     * 作品撤销列表
     */
    String WorkRevoke = "WorkRevoke";

    /**
     * 材料修改列表
     */
    String MotifyList = "MotifyList";

    /**
     * 作品拒绝列表管理
     */
    String RejectList = "RejectList";

    /**
     * 证书生成
     */
    String Certifing = "Certifing";

    /**
     * 证书打印管理
     */
    String CertificatePrint = "CertificatePrint";


    // 数据统计^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    /**
     * 登记统计
     */
    String RegisterStatistics = "RegisterStatistics";

    /**
     * 用户统计
     */
    String UserStatistics = "UserStatistics";

    /**
     * 作品统计
     */
    String WorkStatistics = "WorkStatistics";

    /**
     * 报告下载
     */
    String ReportDownload = "ReportDownload";

    /**
     * 综合查询
     */
    String AdminView = "AdminView";

    /**
     * 开放查询
     */
    String OpenInquiry = "OpenInquiry";

    /**
     * 智能客服
     */
    String SmartCustomer = "SmartCustomer";

    /**
     * 管理员协会消息
     */
    String AssonewsManagement = "AssonewsManagement";

    /**
     * 用户协会消息
     */
    String Assonews = "Assonews";

    // 系统管理 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    /**
     * 个人资料
     */
    String PersonalCenter = "PersonalCenter";

    /**
     * 管理员站内信息
     */
    String AnnounceManagement = "AnnounceManagement";

    /**
     * 用户站内信息
     */
    String Announce = "Announce";

    /**
     * 用户管理
     */
    String UserManagement = "UserManagement";

    /**
     * 角色管理
     */
    String AuthorityManagement = "AuthorityManagement";

    /**
     * 敏感词管理
     */
    String SensitiveWordManagement = "SensitiveWordManagement";
}
