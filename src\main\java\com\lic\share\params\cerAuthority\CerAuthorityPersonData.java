package com.lic.share.params.cerAuthority;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @data 20200526
 **/
@Data
@Accessors(chain = true)
public class CerAuthorityPersonData {
    @NotNull(message = "name不能为空")
    //@ApiModelProperty(value = "被授权人")
    private String name;

    @NotNull(message = "name不能为空")
    //@ApiModelProperty(value = "被授权人代码")
    private String code;

    @NotNull(message = "name不能为空")
    //@ApiModelProperty(value = "被授权人代码类型")
    private String codeType;
}
