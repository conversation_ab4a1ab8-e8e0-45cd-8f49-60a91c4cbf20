package blockChain.entities;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name = "tb_copyrightmanager")
public class CopyrightManager implements Serializable {
    public static final int FIRST_REVIEW = 1; //初审中
    public static final int SECOND_REVIEW = 4; //复审中
    public static final int FINAL_REVIEW = 7; //终审中
    public static final int CERT_CREATE = 8; //证书生成中
    public static final int CERT_CREATED = 11; //证书已生成
    public static final int MODIFIED = 10; //材料修改
    public static final int FIRST_REVIEW_REJECT = 3; //初审拒绝
    public static final int SECOND_REVIEW_REJECT = 6; //复审拒绝
    public static final int FINAL_REVIEW_REJECT = 9; //终审拒绝
    public static final int CERT_CREATE_REJECT = 14; //证书生成拒绝
  public static final int UNSUBMIT = 2; //未提交
  public static final int REPORTING = 12; //操作执行中
  public static final int SUBMITTING = 13; //作品提交中

  public CopyrightManager(Long registrationNum){
    this.registrationNum = registrationNum;
  }

  @Transient
  private String uuid;

  @Transient
  private String syncStatus;

  // 在线提交其他数据的

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long registrationNum;// id


  /**
   * 是否可用，标识是否已经删除
   */
  private EnableStatusEnum enable = EnableStatusEnum.ENABLE;

  /**
   * 可用状态枚举
   */
  public enum EnableStatusEnum{
    ENABLE("enable"),
    DISABLE("disable");

    private String value;

    EnableStatusEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static EnableStatusEnum fromValue(String text) {
      for (EnableStatusEnum b : EnableStatusEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + text + "'");
    }
  }

  private BigInteger worksRegistrationNum;//进入流程的作品，点击保存将作品ID存储起来
  private String worksNum;//作品自增长的分类序列号
  private String productionName;// 作品名称
  /**
   * 关联用户表
   */
  private String userName;//作品提交的用户名称
  private String productionTypeDesc;// 作品创作说明
  private String productionTypeId;// 作品类别ID
  private String artWorksId;// 美术类作品类型
  private String opuseDesc;// 作品类别说明
  @JoinColumn(name = "authorIdCar_id")
  @ManyToOne(fetch = FetchType.EAGER)
  private UploadAttachment authorIdCar; //作者身份证件
  @JoinColumn(name = "copyIdCardZM_id")
  @ManyToOne(fetch = FetchType.EAGER)
  private UploadAttachment copyIdCardZM; //著作权人证件


  private String copyTRPermit;// 著作权人暂住证
  private int applyType;// 办理方式
  private String opusInditekind;// 作品创作性质ID

  private String otherOpusInditekind;//NEW ADD/作品创作性质，其他
  private String otherObtainMode;//NEW ADD/权利取得方式：其他获取方式
  private String copyrightCountries;//NEW ADD/著作权人地址
  private String   copyrightProvince;//NEW ADD/
  private String copyrightCity;//NEW ADD/
  private String   copyrightCounty;//NEW ADD/

  private String finishTime;// 完成时间
  private String finishAddress;// 作品完成地点
  private String completeCountries;// 作品完成国家
  private String completeProvince;// 作品完成身份
  private String completeCity;// 作品完成市
  private String completeCounty;// 作品完成县
  private String completeArea;// 作品完成详细地址
  private String completeAreaStr;// 作品完成详细地址文字
  private String finishAreaStr;//作品完成国家+省份+城市+县区文字
  private String firstAreaStr;//作品首次发表国家+省份+城市+县区文字

  private int publishState;// 发表未发表状态
  private String firstPublishTime;// 首次发表时间
  private String firstCountry;// 首次发表地点（国家）
  private String firstProvince;// 首次发表地点（省）
  private String firstCity;// 首次发表地点（市）
  private String firstCounty;// 首次发表地点（县）
  private String workPublishArea;//首次发表的详细地址
  private String workPublishAreaStr;//首次发表的详细地址文字
  private String workotherAreas;// 其他国家的具体地点
  private int rightOwnMode;// 权利归属方式
  private int obtainMode;// 权利取得方式
  @JoinColumn(name = "rightGuarantee_id")
  @ManyToOne(fetch = FetchType.EAGER)
  private UploadAttachment rightGuarantee; //权利保证书
  @JoinColumn(name = "agentBook_id")
  @ManyToOne(fetch = FetchType.EAGER)
  private UploadAttachment agentBook; //代理委托书

  @JoinColumn(name = "cooperationContract_id")
  @ManyToOne(fetch = FetchType.EAGER)
  private UploadAttachment cooperationContract; //合作作品合同
  @JoinColumn(name = "positionContract_id")
  @ManyToOne(fetch = FetchType.EAGER)
  private UploadAttachment positionContract; //职务作品合同
  //private String officeWork;// 职务作品合同
  // 委托作品合同
  private String entrustType;//
  @JoinColumn(name = "entrustContract_id")
  @ManyToOne(fetch = FetchType.EAGER)
  private UploadAttachment entrustContract; //委托作品合同
  @JoinColumn(name = "trusteeContract_id")
  @ManyToOne(fetch = FetchType.EAGER)
  private UploadAttachment trusteeContract; //受托合同书

  private int rightScope;// 权利拥有状况
  private String emedium;// 电子文档
  private String emediumCD;// 光盘
  private String emediumOther;// 其他
  // 证书领取方式
  private String emediumCertificate;// 电子证书
  private String pmediumCertificate;// 纸质证书
  private String pmediumCount;// 打印件(A4纸)1份

  private int fileType;//上传文件的类型，1是图片，2是文档,3视频

  private int status_type;// 状态
  private String refuseReason;// 拒绝理由
  private String certificateUrl;//证书生成的链接
  private String sampleUrl;//证书样本生成的链接
  private Integer inactiveType = InactiveTypeValue.NORMAL;//1代表没有撤销作品，2代表等待撤销的作品 3代表已撤销的作品

  private String certificateIdentifier; //电子证照标识

  /**
   * 1代表没有撤销作品，
   * 2代表等待撤销的作品
   * 3代表已撤销的作品
   */
  public interface InactiveTypeValue {
    Integer NORMAL = 1;
    Integer REVOKING = 2;
    Integer REVOKED = 3;
  }

  /**
   * 撤销作品的撤销证明
   */
  private String cancelTheProofUrl;
  private int restatus;//审批流程的上一个状态

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime registrationDate;// 登记时间

  private BigInteger allUpdateId;//获取当前保存插入ID
  private String stateType;//设置当点击提交之后跳到确认信息页，又返回修改数据需要进行修改数据库，不进行再插入数据
  @Transient
  private String starDate;//查询时间段的开始时间。
  @Transient
  private String endDate;//查询时间段的结束时间。

  private String rightScopeString; //权利拥有状况(文字)
  private String publishStatusString; //作品出版状态(文字)
  private String applyTypeString; //申请方式
  private String rightAttributionWay_String;// 权利归属方式(文字)
  private String rightWay_String;// 权利取得方式(文字)
  private String status_String;// 状态(文字)
  private String regNum; //证书生成后登记号
  private int focusWork;//作品的关注状态
  private int certificatePrint;//证书打印了就在列表上消失
  private BigInteger draftSaveId;//作品提交的时候先保存到草稿箱，确认提交在删除草稿箱数据
  private String intherFort;
  private int refuseRevokes;//1是上传撤销 ，2是拒绝撤销
  private int countDown; //倒计时时间
  private int isPrinted; //是否打印纸质证书
  private String pch; //数据汇聚操作的批次号
  private String snCode; //省网办申办号
  private int snStatus; //省网办进度
  // 0：默认，1：申请xml取得中，2：申请xml取得失败，3：申请中（申请xml取得成功），4：申请失败
  // 5：流转xml取得中(申请成功)，6：流转xml取得失败，7：流转中（流转xml取得成功），8：流转失败
  // 9：办结xml取得中（流转成功），10：办结xml取得失败，11：办结中（办结xml取得成功），12：办结失败，13：办结完了

  private String dictName;
  private int maskCount;
  private String onBlockToken;
    private String onBlockSampleToken;
    private String instructionsBox; //权利拥有状况其他内容
    private Integer isAllowedCopyrightExchange; //是否愿意委托版权交易
    private Boolean isAccusation; //是否疑似侵权
    private Boolean isAccusationReject; // 是否疑似侵权有被拒绝

    private String originalAuthorName;// 原作品作者名称
    private String originalProdName;// 原作品名称
    private String projectId;// 统一收件码

    //创建时间
    @CreatedDate
    private LocalDateTime createTime;
    //更新时间
    @LastModifiedDate
    private LocalDateTime updateTime;

    /**
     * 代理人
     */
    @JoinColumn(name = "agent_id")
    @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    private Agent agentList;

    /**
     * 作者
   */
  @JoinColumn(name = "copyright_id")
  @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.ALL}, fetch = FetchType.LAZY)
  private List<Author> authorList = new ArrayList<>();

  /**
   * 著作权人
   */
  @JoinColumn(name = "copyright_id")
  @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.ALL}, fetch = FetchType.EAGER)
  private List<CopyrightOwner> ownerList = new ArrayList<>();

  //好差评
  @JoinColumn(name = "evaluate_id")
  @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
  private EvaluateEntity evaluate;

  /**
   * 作品标签
   */
  @ManyToMany
  @JoinTable(name = "copyright_tag",  //用来指定中间表的名称
          //用于指定本表在中间表的字段名称，以及中间表依赖的是本表的哪个字段
          joinColumns = {@JoinColumn(name = "copyright_id", referencedColumnName = "registrationNum")},
          //用于指定对方表在中间表的字段名称，以及中间表依赖的是它的哪个字段
          inverseJoinColumns = {@JoinColumn(name = "tag_id", referencedColumnName = "id")}
  )
  private List<TagEntity> tags = new ArrayList<>();

  public List<CopyrightOwner> getOwnerList() {
    return ownerList;
  }

  @Deprecated
  public void setOwnerList(List<CopyrightOwner> ownerList) {
    this.ownerList = new ArrayList<>();
    for(CopyrightOwner owner:ownerList){
      this.appendOwner(owner);
    }
  }

  public void appendOwner(CopyrightOwner owner) {
    this.ownerList.add(owner);
    owner.setManager(this);
  }


  /**
   * 提交人
   */
  @JoinColumn(name = "submitter_id")
  @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
  private Submitter submitter;

  /**
   * 权利拥有表
   */
	/*@JoinColumn(name="haveright_id")
	@OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
	private HaverightEntity haveright;*/
  private String haverightIds;

  @Transient
  private List<Integer> haverightIdList = new ArrayList<>();
  @Transient
  private List<Long> uploadWorkRemove = new ArrayList<>();

  /**
   * 作品样本
   */
  @ManyToMany
  @JoinTable(name = "copyright_attachment",  //用来指定中间表的名称
    //用于指定本表在中间表的字段名称，以及中间表依赖的是本表的哪个字段
    joinColumns = {@JoinColumn(name = "copyright_id", referencedColumnName = "registrationNum")},
    //用于指定对方表在中间表的字段名称，以及中间表依赖的是它的哪个字段
    inverseJoinColumns = {@JoinColumn(name = "attachment_id", referencedColumnName = "id")}
  )
  private List<UploadAttachment> uploadWorks = new ArrayList<>();

  /**
   * 流程记录
   */
  @JoinColumn(name = "copyright_id")
  @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.ALL}, fetch = FetchType.LAZY)
  private List<ProcessRecord> flowRecord = new ArrayList<>();

  /**
   * 标星记录
   */
  @OneToOne(mappedBy = "manager", cascade = {CascadeType.REMOVE})
  private CopyrightManagerMarker marker;
  private Long workstationId; // 所属工作站

  @Transient
  private Long preId;//详情页上一条记录id

  @Transient
  private Long nextId;//详情页下一条记录id

  @Transient
  private String preName;//详情页上一条作品名称

  @Transient
  private String nextName;//详情页下一条作品名称

  @Transient
  private int preStatus;//详情页上一条作品状态

  @Transient
  private int nextStatus;//详情页下一条作品状态

  private String licenseStatus; //作品提交证照中心返回值，成功为作品登记号

  private int licenseState = 1; //作品提交证照中心状态，默认1 未提交

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime licenseTime; //作品提交证照中心完成时间

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime certificateCreateTime;

  @Transient
  private Map<String, Object> digitals = new HashMap<>(); //打印用数据字典

  public void setHaverightIds(String haverightIds) {
    if (haverightIds != null) {
      this.haverightIds = haverightIds;
      String[] temp = haverightIds.split(",");
      List<Integer> haverightIdList = new ArrayList<>();
      for (String str : temp) {
        haverightIdList.add(Integer.parseInt(str));
      }
      this.haverightIdList = haverightIdList;
    }
  }

  public void setHaverightIdList(List<Integer> haverightIdList) {
    if (haverightIdList != null) {
      this.haverightIdList = haverightIdList;
      String haverightIds = "";
        for (int i = 0; i < haverightIdList.size(); i++) {
            if (i == 0) {
                haverightIds = haverightIds + haverightIdList.get(i);
            } else {
                haverightIds = haverightIds + "," + haverightIdList.get(i);
            }
        }
        this.haverightIds = haverightIds;
    }
  }

    public interface SnStatus {
        // 没有电子证照
        int NO_CERT = 1;

        // 申请xml取得失败
        int APPLY_XML_FAILD = 2;

        // 申请中
        int APPLYING = 3;

        // 申请异常
        int APPLY_ERROR = 4;

        // 申请成功(流转xml取得中)
        int WAIT_LZ = 5;

        // 流转xml取得失败
        int LX_XML_FAILD = 6;

        // 流转中
        int LZ_ING = 7;

        // 流转调用失败
        int LZ_FAILD = 8;

        // 流转成功（办结xml取得中）
        int WAIT_BJ = 9;

        // 办结xml取得失败
        int BJ_XML_FAILD = 10;

        // 办结中
        int BJ_ING = 11;

        // 办结失败
        int BJ_FAILD = 12;

        // 办件成功
        int COMPLETE = 13;
    }

    public interface ApplyType {
        // 著作权人申请
        int OWN = 1;
        // 代理人申请
        int AGENT = 2;

    }

  public interface LicenseState {
    // 待上报
    int WAIT = 1;
    // 已上报
    int REPORTED = 2;
    // 正在上报中
    int REPORTING = 3;
    // 上报失败
    int FAILED = 4;
  }
}
