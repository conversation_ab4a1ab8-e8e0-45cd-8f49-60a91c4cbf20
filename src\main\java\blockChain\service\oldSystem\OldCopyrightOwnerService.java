package blockChain.service.oldSystem;

import blockChain.entities.oldSystem.OldAuthor;
import blockChain.entities.oldSystem.OldCopyrightOwner;
import blockChain.repository.oldSystem.OldAuthorRepository;
import blockChain.repository.oldSystem.OldCopyrightOwnerRepository;
import blockChain.service.BaseService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/9 12:08
 */
@Service
@AllArgsConstructor
public class OldCopyrightOwnerService implements BaseService<OldCopyrightOwnerRepository, OldCopyrightOwner, Integer> {
  private OldCopyrightOwnerRepository repository;

  @Override
  public OldCopyrightOwnerRepository getRepository() {
    return repository;
  }

}
