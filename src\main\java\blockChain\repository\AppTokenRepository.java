package blockChain.repository;

import blockChain.entities.AppTokenEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface AppTokenRepository extends BaseRepository<AppTokenEntity, Long> {
    @Query(value = "SELECT * FROM tb_app_token order by id desc limit 1", nativeQuery = true)
    AppTokenEntity findLastToken();
}
