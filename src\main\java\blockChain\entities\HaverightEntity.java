package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_haveright")
public class HaverightEntity {
    //权利拥有表
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	private String signatureBox;// 署名权
	private String publishedBox;// 发表权
	private String modifyBox;// 修改权
	private String protectBox;// 保护作品完整权
	private String copyBox;// 复制权
	private String issueBox;// 发行权
	private String rentBox;// 出租权
	private String exhibitionBox;// 展览权
	private String performanceBox;// 表演权
	private String screeningBox;// 放映权
	private String radioBox;// 广播权
	private String informationBox;// 信息网络传播权
	private String filmBox;// 摄制权
	private String adaptationBox;// 改编权
	private String translationBox;// 翻译权
	private String assemblyBox;// 汇编权
	private String otherBox;// 其他
	private String instructionsBox;// 其他说明

	//创建时间
	@CreatedDate
	private LocalDateTime createTime;
	//更新时间
	@LastModifiedDate
	private LocalDateTime updateTime;

}
