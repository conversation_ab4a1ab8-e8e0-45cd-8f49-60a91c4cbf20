package blockChain.service.oldSystem;

import blockChain.entities.oldSystem.OldSubmitter;
import blockChain.entities.oldSystem.OldUploadAttachment;
import blockChain.repository.oldSystem.OldSubmitterRepository;
import blockChain.repository.oldSystem.OldUploadAttachmentRepository;
import blockChain.service.BaseService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/9 12:08
 */
@Service
@AllArgsConstructor
public class OldUploadAttachmentService implements BaseService<OldUploadAttachmentRepository, OldUploadAttachment, Integer> {
  private OldUploadAttachmentRepository repository;

  @Override
  public OldUploadAttachmentRepository getRepository() {
    return repository;
  }

}
