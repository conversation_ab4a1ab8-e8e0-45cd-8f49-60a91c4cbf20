package blockChain.facade.service;

import blockChain.bean.PageResponse;
import blockChain.dto.NewUserAnalysisDto;
import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.service.UserService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR> <PERSON>
 * @date 2020/4/3 17:25
 */
@Service
@AllArgsConstructor
public class UserStatisticServiceFacade {

  private UserService service;

  public PageResponse<StatisticDto> getUser(CopyrightQueryStatisticGetParam queryParam) {
    Page<StatisticDto> userStatistic = service.getUserStatistic(queryParam);
    return PageResponse.of((long)queryParam.getPage(), (long)userStatistic.getSize(), userStatistic.getTotalElements(), userStatistic.getContent());
  }

  public NewUserAnalysisDto getNewUserAnalysis(CopyrightQueryStatisticGetParam queryParam) {
    NewUserAnalysisDto analysisDto = new NewUserAnalysisDto();

    LocalDateTime startTime = queryParam.getStartTime();
    LocalDateTime endTime = queryParam.getEndTime();

    Assert.notNull(startTime, "开始时间不能为空");
    Assert.notNull(endTime, "结束时间不能为空");

    long months = ChronoUnit.MONTHS.between(startTime, endTime) + 1;

    /// 时间段内新增用户
    Long countNewUser = service.countNewUser(startTime, endTime, queryParam.getArea());
    analysisDto.setNewUserCount(countNewUser);

    LocalDateTime lastEndTime = endTime.plusMonths(months * -1).with(TemporalAdjusters.lastDayOfMonth());
    LocalDateTime lastStartTime = startTime.plusMonths(months * -1).with(TemporalAdjusters.firstDayOfMonth());

    Long lastCycleCountNewUser = service.countNewUser(lastStartTime, lastEndTime, queryParam.getArea());
    // 环比增长率 =（本期数-上期数）/上期数×100%。
    analysisDto.setNewUserCountMOM( lastCycleCountNewUser == 0? 1: 1.0 * (countNewUser - lastCycleCountNewUser) / lastCycleCountNewUser);
    if(lastCycleCountNewUser == 0&& countNewUser == 0){
      analysisDto.setNewUserCountMOM( 0.0 );
    }

    Long userTotal = service.countAllByRegisterTimeBefore(endTime, queryParam.getArea());
    analysisDto.setUserTotal(userTotal);
    Long lastCycleCountTotalUser = service.countAllByRegisterTimeBefore(startTime, queryParam.getArea());

    analysisDto.setUserTotalMOM(lastCycleCountTotalUser == 0? 1: 1.0 * (userTotal - lastCycleCountTotalUser) / lastCycleCountTotalUser);
    if(lastCycleCountTotalUser == 0&& userTotal == 0){
      analysisDto.setUserTotalMOM( 0.0 );
    }

    return analysisDto;
  }

  public PageResponse<StatisticDto> getRecentUserGrowth(CopyrightQueryStatisticGetParam queryParam) {
    /*LocalDateTime startTime = queryParam.getStartTime();
    LocalDateTime endTime = queryParam.getEndTime();

    Assert.notNull(startTime, "开始时间不能为空");
    Assert.notNull(endTime, "结束时间不能为空");

    if (startTime.isAfter(endTime)) {
      Assert.notNull(endTime, "开始时间不能在结束时间之后");
    }

    // 将开始时间改为上上月最后一天
    startTime = startTime.plusMonths(-2L).with(TemporalAdjusters.lastDayOfMonth());
    long cyclical = ChronoUnit.MONTHS.between(startTime, endTime);

    List<StatisticDto> list = new ArrayList<>(Integer.parseInt(cyclical+""));

    for (long i = 0; i < cyclical; i++) {

      LocalDateTime cyclicalEndTime = startTime.plusMonths(1L);
      Long amount = service.countNewUser(startTime, cyclicalEndTime, queryParam.getArea());
      String key = cyclicalEndTime.format(DateTimeFormatter.ofPattern("yyyy-MM"));
      StatisticDto statisticDto = new StatisticDto(key, key, amount);
      list.add(statisticDto);

      startTime = startTime.plusMonths(1L);
    }
*/
    List<StatisticDto> historicalTimeLine = getHistoricalTimeLine(queryParam, (param) -> {
      Long amount = service.countNewUser(param.getStartTime(), param.getCyclicalEndTime(), queryParam.getArea());
      String key = param.getCyclicalEndTime().format(DateTimeFormatter.ofPattern(param.getDateFormat()));
      return new StatisticDto(key, key, amount);
    });


    return PageResponse.of((long)queryParam.getPage(), (long)historicalTimeLine.size(), (long)historicalTimeLine.size(), historicalTimeLine);
  }

  public PageResponse<StatisticDto> getGeographyOfUsers(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> list = service.getGeographyOfUsersInFujian(queryParam);
    list.add(service.getGeographyOfUsersNotInFujian(queryParam).setName("省外").setKey("省外"));
    return PageResponse.of((long)queryParam.getPage(), (long)list.size(), (long)list.size(), list);
  }

  /**
   * 历史用户总数
   * @param queryParam
   * @return
   */
  public PageResponse<StatisticDto> getHistoricalNumberOfUsers(CopyrightQueryStatisticGetParam queryParam) {

    List<StatisticDto> historicalTimeLine = getHistoricalTimeLine(queryParam, (param) -> {
      Long amount = service.countAllByRegisterTimeBefore(param.getCyclicalEndTime(), queryParam.getArea());
      String key = param.getCyclicalEndTime().format(DateTimeFormatter.ofPattern(param.getDateFormat()));
      return new StatisticDto(key, key, amount);
    });


    return PageResponse.of((long)queryParam.getPage(), (long)historicalTimeLine.size(), (long)historicalTimeLine.size(), historicalTimeLine);
  }

  /**
   * 用户数时间轴统计处理函数
   * @param queryParam
   * @param fun
   * @return
   */
  private List<StatisticDto> getHistoricalTimeLine(CopyrightQueryStatisticGetParam queryParam, Function<HistoricalTimeLineParam,StatisticDto> fun){

    DateTimeFormatter dfDate = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    DateTimeFormatter dfTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    LocalDateTime startTime = queryParam.getStartTime();
    LocalDateTime endTime = queryParam.getEndTime();

    Assert.notNull(startTime, "开始时间不能为空");
    Assert.notNull(endTime, "结束时间不能为空");

    Assert.isTrue(startTime.isBefore(endTime),"开始时间不能在结束时间之后");

    // 将开始时间改为上上月最后一天
    //startTime = startTime.plusMonths(-2L).with(TemporalAdjusters.lastDayOfMonth());
    startTime = startTime.with(TemporalAdjusters.firstDayOfMonth());
    long cyclical = 0;
    List<StatisticDto> list = new ArrayList<>();
    if (!queryParam.isYear()) {
      //获取月数差
      cyclical = ChronoUnit.MONTHS.between(startTime, endTime);
      for (long i = 0; i <= cyclical; i++) {

        LocalDateTime cyclicalEndDate = startTime.with(TemporalAdjusters.lastDayOfMonth());
        String cyclicalEndStr = cyclicalEndDate.format(dfDate) + " 23:59:59";
        LocalDateTime cyclicalEndTime = LocalDateTime.parse(cyclicalEndStr, dfTime);
        String dateFormat = "yyyy-MM";

        HistoricalTimeLineParam param = new HistoricalTimeLineParam(startTime, cyclicalEndTime, queryParam.getArea(), dateFormat);
        StatisticDto statisticDto =  fun.apply(param);
      /*Long amount = service.countNewUser(startTime, cyclicalEndTime, queryParam.getArea());
      String key = cyclicalEndTime.format(DateTimeFormatter.ofPattern("yyyy-MM"));
      new StatisticDto(key, key, amount);*/
        list.add(statisticDto);

        startTime = startTime.plusMonths(1L);
      }
    } else {
      //获取年数差
      cyclical = ChronoUnit.YEARS.between(startTime, endTime);
      for (long i = 0; i <= cyclical; i++) {

        LocalDateTime cyclicalEndDate = startTime.with(TemporalAdjusters.lastDayOfYear());
        String cyclicalEndStr = cyclicalEndDate.format(dfDate) + " 23:59:59";
        LocalDateTime cyclicalEndTime = LocalDateTime.parse(cyclicalEndStr, dfTime);
        String dateFormat = "yyyy";

        HistoricalTimeLineParam param = new HistoricalTimeLineParam(startTime, cyclicalEndTime, queryParam.getArea(), dateFormat);
        StatisticDto statisticDto = fun.apply(param);
        list.add(statisticDto);

        startTime = startTime.plusYears(1L);
      }
    }

    return list;

  }

  @Getter
  @Setter
  @AllArgsConstructor
  @NoArgsConstructor
  private static class HistoricalTimeLineParam{
    LocalDateTime startTime;
    LocalDateTime cyclicalEndTime;
    List<String> area;
    String dateFormat;
  }


}
