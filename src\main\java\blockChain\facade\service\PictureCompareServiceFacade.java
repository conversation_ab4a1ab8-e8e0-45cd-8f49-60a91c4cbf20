package blockChain.facade.service;

import blockChain.entities.Digital;
import blockChain.entities.pictureCompare.PictureCompareEntity;
import blockChain.service.DigitalService;
import blockChain.service.PictureCompareService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/22 16:54
 */
@Service
@AllArgsConstructor
public class PictureCompareServiceFacade {
    @Autowired
    private PictureCompareService pictureCompareService;

    @Transactional(rollbackFor = RuntimeException.class)
    public void save(PictureCompareEntity pictureCompareEntity){
        pictureCompareService.save(pictureCompareEntity);
    }

  @Transactional(rollbackFor = RuntimeException.class)
  public void removeByToken(String token){
    PictureCompareEntity entity = pictureCompareService.findByToken(token);
    pictureCompareService.deleteById(entity.getId());
  }

    @Transactional
    public PictureCompareEntity findByToken(String token){
        return pictureCompareService.findByToken(token);
    }
}
