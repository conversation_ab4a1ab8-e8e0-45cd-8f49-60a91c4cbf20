package blockChain.service;

import blockChain.entities.CopyrightManager;
import blockChain.entities.CopyrightOwner;
import blockChain.entities.DraftCopyrightManager;
import blockChain.repository.CopyrightManagerPredicates;
import blockChain.repository.DraftCopyrightManagerPredicates;
import blockChain.repository.DraftCopyrightManagerRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/2 17:20
 */
@Service
@AllArgsConstructor
public class DraftCopyrightManagerService {
    @Autowired
    private DraftCopyrightManagerRepository repository;

    @Transactional(rollbackFor = RuntimeException.class)
    public void saveOrUpdateCopyright(DraftCopyrightManager draftCopyrightManager){
      repository.save(draftCopyrightManager);
    }

    public DraftCopyrightManager getById(Long id){
        return repository.getByRegistrationNum(id);
    }

    public Page<DraftCopyrightManager> query(Long registrationNum, String productionName,
                                        String productionTypeId, Integer rightOwnMode,
                                        String startDate, String endDate,String userName, Pageable of){

        Page<DraftCopyrightManager> page = repository.findAll(DraftCopyrightManagerPredicates.digitalQuery(
                registrationNum,productionName,productionTypeId,rightOwnMode,startDate,endDate,userName),of);
        return page;
    }

    public void remove(DraftCopyrightManager draftCopyrightManager){ repository.delete(draftCopyrightManager);}
}
