package blockChain.controller.statistic;

import blockChain.bean.PageResponse;
import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.facade.service.CopyrightOwnerStatisticServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:00
 */
@Api("著作权所有者相关统计")
@Slf4j
@RestController
@RequestMapping("statistic/copyright_owner")
@AllArgsConstructor
public class CopyrightOwnerStatistic {

    private CopyrightOwnerStatisticServiceFacade service;

    @ApiOperation("获取公司排名")
    @PostMapping("companies/get")
    public ResponseEntity<PageResponse<StatisticDto>> getCompanies (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
      PageResponse<StatisticDto> companies = service.getCompanies(queryParam);
      return ResponseEntity.ok(companies);
    }

    @ApiOperation("获取代理人排名")
    @PostMapping("agent/get")
    public ResponseEntity<PageResponse<StatisticDto>> getAgent (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
      PageResponse<StatisticDto> agent = service.getAgent(queryParam);
      return ResponseEntity.ok(agent);
    }

    @ApiOperation("获取个人登记量/企业登记量排名")
    @PostMapping("manager/get")
    public ResponseEntity<PageResponse<StatisticDto>> getManager (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
      PageResponse<StatisticDto> agent = service.getManager(queryParam);
      return ResponseEntity.ok(agent);
    }

    @ApiOperation("作品类型统计")
    @PostMapping("production_types/get")
    public ResponseEntity<PageResponse<StatisticDto>> getProductionTypes (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
      PageResponse<StatisticDto> agent = service.getProductionTypes(queryParam);
      return ResponseEntity.ok(agent);
    }

    @ApiOperation("福建省内地区类型发表数量统计")
    @PostMapping("area_distributed/fujian/get")
    public ResponseEntity<PageResponse<StatisticDto>> getAreaDistributedFujian (@Valid @RequestBody CopyrightQueryStatisticGetParam queryParam){
      PageResponse<StatisticDto> agent = service.getAreaDistributedFujian(queryParam);
      return ResponseEntity.ok(agent);
    }

    @ApiOperation("福建省外地区类型发表数量统计")
    @PostMapping("area_distributed/get")
    public ResponseEntity<PageResponse<StatisticDto>> getAreaDistributed (){
      PageResponse<StatisticDto> agent = service.getAreaDistributed();
      return ResponseEntity.ok(agent);
    }

}
