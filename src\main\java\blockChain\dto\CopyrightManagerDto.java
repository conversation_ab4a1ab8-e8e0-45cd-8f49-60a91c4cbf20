package blockChain.dto;

import blockChain.entities.CopyrightManager;
import liquibase.util.StringUtils;
import lombok.Data;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> XXX
 * @date 2019/11/29.
 */

@Data
public class CopyrightManagerDto {
  private Long id;// 登记号


  /**
   * 是否可用，标识是否已经删除
   */
  private CopyrightManager.EnableStatusEnum enable;

  private BigInteger worksRegistrationNum;//进入流程的作品，点击保存将作品ID存储起来
  private String worksNum;//作品自增长的分类序列号
  private String productionName;// 作品名称
  private String userName;//作品提交的用户名称
  private String productionTypeDesc;// 作品创作说明
  private String productionTypeId;// 作品类别ID
  private String artWorksId;// 美术类作品类型
  private String opuseDesc;// 作品类别说明
  private UploadAttachmentDto authorIdCar;// 作者正面身份证
  private UploadAttachmentDto copyIdCardZM;// 著作权人正面身份证
  private String copyTRPermit;// 著作权人暂住证
  private int applyType;// 办理方式
  private String opusInditekind;// 作品创作性质ID
  private String otherOpusInditekind;//NEW ADD/作品创作性质，其他
  private String otherObtainMode;//NEW ADD/权利取得方式：其他获取方式
  private String copyrightCountries;//NEW ADD/著作权人地址
  private String   copyrightProvince;//NEW ADD/
  private String copyrightCity;//NEW ADD/
  private String   copyrightCounty;//NEW ADD/
  private String finishTime;// 完成时间
  private String finishAddress;// 作品完成地点
  private String completeCountries;// 作品完成国家
  private String completeProvince;// 作品完成身份
  private String completeCity;// 作品完成市
  private String completeCounty;// 作品完成县
  private String completeArea;// 作品完成详细地址
  private String completeAreaStr;// 作品完成详细地址文字
  private String finishAreaStr;//作品完成国家+省份+城市+县区文字
  private String firstAreaStr;//作品首次发表国家+省份+城市+县区文字

  private int publishState;// 发表未发表状态
  private String firstPublishTime;// 首次发表时间
  private String firstCountry;// 首次发表地点（国家）
  private String firstProvince;// 首次发表地点（省）
  private String firstCity;// 首次发表地点（市）
  private String firstCounty;// 首次发表地点（县）
  private String workPublishArea;//首次发表的详细地址
  private String workPublishAreaStr;//首次发表的详细地址文字
  private String workotherAreas;// 其他国家的具体地点
  private int rightOwnMode;// 权利归属方式
  private int obtainMode;// 权利取得方式
  private UploadAttachmentDto rightGuarantee;// 权利保证书
  private UploadAttachmentDto agentBook;// 代理委托书

  // 合作作品合同
  private UploadAttachmentDto cooperationContract;// 合作作品合同
  // 职务作品合同
  private UploadAttachmentDto positionContract;// 职务作品合同
  // 委托作品合同
  private String entrustType;//
  private UploadAttachmentDto entrustContract;// 委托作品合同
  private UploadAttachmentDto trusteeContract;//受托合同书

  private int rightScope;// 权利拥有状况
  private String emedium;// 电子文档
  private String emediumCD;// 光盘
  private String emediumOther;// 其他
  // 证书领取方式
  private String emediumCertificate;// 电子证书
  private String pmediumCertificate;// 纸质证书
  private String pmediumCount;// 打印件(A4纸)1份

  private int fileType;//上传文件的类型，0是图片，1是文档,2视频

  private int status_type;// 状态
  private String refuseReason;// 拒绝理由
  private String certificateUrl;//证书生成的链接
  private int inactiveType;//0代表没有撤销作品，1代表已撤销的作品
  private String cancelTheProofUrl;//撤销作品的撤销证明
  private int restatus;//审批流程的上一个状态
  private LocalDateTime registrationDate;// 登记时间

  private BigInteger allUpdateId;//获取当前保存插入ID
  private String stateType;//设置当点击提交之后跳到确认信息页，又返回修改数据需要进行修改数据库，不进行再插入数据
  private String starDate;//查询时间段的开始时间。
  private String endDate;//查询时间段的结束时间。

  private String rightScopeString; //权利拥有状况(文字)
  private String publishStatusString; //作品出版状态(文字)
  private String applyTypeString; //申请方式
  private String rightAttributionWay_String;// 权利归属方式(文字)
  private String rightWay_String;// 权利取得方式(文字)
  private String status_String;// 状态(文字)
  private String regNum; //证书生成后登记号
  private int focusWork;//作品的关注状态
  private int certificatePrint;//证书打印了就在列表上消失
  private BigInteger draftSaveId;//作品提交的时候先保存到草稿箱，确认提交在删除草稿箱数据
  private String intherFort;
  private int refuseRevokes;//1是上传撤销 ，2是拒绝撤销
  private int countDown; //倒计时时间
  private int isPrinted; //是否打印纸质证书

  private String dictName;
  private int maskCount;
  private String onBlockToken;
  private String instructionsBox; //权利拥有状况其他内容
  private Boolean isAccusation; //是否疑似侵权
  private Boolean isAccusationReject; // 是否疑似侵权有被拒绝

  private String originalAuthorName;// 原作品作者名称
  private String originalProdName;// 原作品名称
  private Long workstationId; // 所属工作站

  /**
   * 代理人
   */
  private AgentDto agentList;

  /**
   * 作者
   */
  private List<AuthorDto> authorList = null;

  /**
   * 著作权人
   */
  private List<CopyrightOwnerDto> ownerList = null;

  /**
   * 提交人
   */
  private SubmitterDto submitter;
  /**
   * 标星数据
   */
  private CopyrightManagerMarkerDto marker;


  //好差评1
  private EvaluateDto evaluate;

  private String certificateCreateTime;

  /*private HaverightDto haveright;*/
  private List<Integer> haverightIdList = null;

  /**
   * 作品样本
   */
  private List<UploadAttachmentDto> uploadWorks = null;

  /**
   * 作品标签
   */
  private List<TagDto> tags = null;

  /**
   * 流程记录
   */
  private List<ProcessRecordDto> flowRecord = null;

  private List<Long> uploadWorkRemove = null;

  private Long preId;//详情页上一条记录id
  private Long nextId;//详情页下一条记录id
  private String preName;//详情页上一条作品名称
  private String nextName;//详情页下一条作品名称
  private int preStatus;//详情页上一条作品状态
  private int nextStatus;//详情页下一条作品状态
  private String projectId;// 统一收件码
  private String snCode;

  private String licenseStatus; //作品提交证照中心状态
  private Map<String, Object> digitals = new HashMap<>(); //打印用数据字典
  private Integer isAllowedCopyrightExchange; //是否愿意委托版权交易

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public BigInteger getWorksRegistrationNum() {
    return worksRegistrationNum;
  }

  public void setWorksRegistrationNum(BigInteger worksRegistrationNum) {
    this.worksRegistrationNum = worksRegistrationNum;
  }

  public String getWorksNum() {
    return worksNum;
  }

  public void setWorksNum(String worksNum) {
    this.worksNum = worksNum;
  }

  public String getProductionName() {
    return productionName;
  }

  public void setProductionName(String productionName) {
    this.productionName = productionName;
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public String getProductionTypeDesc() {
    return productionTypeDesc;
  }

  public void setProductionTypeDesc(String productionTypeDesc) {
    this.productionTypeDesc = productionTypeDesc;
  }

  public String getProductionTypeId() {
    return productionTypeId;
  }

  public void setProductionTypeId(String productionTypeId) {
    this.productionTypeId = productionTypeId;
  }

  public String getArtWorksId() {
    return artWorksId;
  }

  public void setArtWorksId(String artWorksId) {
    this.artWorksId = artWorksId;
  }

  public String getOpuseDesc() {
    return opuseDesc;
  }

  public void setOpuseDesc(String opuseDesc) {
    this.opuseDesc = opuseDesc;
  }

  public UploadAttachmentDto getAuthorIdCar() {
    return authorIdCar;
  }

  public void setAuthorIdCar(UploadAttachmentDto authorIdCar) {
    this.authorIdCar = authorIdCar;
  }

  public UploadAttachmentDto getCopyIdCardZM() {
    return copyIdCardZM;
  }

  public void setCopyIdCardZM(UploadAttachmentDto copyIdCardZM) {
    this.copyIdCardZM = copyIdCardZM;
  }

  public String getCopyTRPermit() {
    return copyTRPermit;
  }

  public void setCopyTRPermit(String copyTRPermit) {
    this.copyTRPermit = copyTRPermit;
  }

  public int getApplyType() {
    return applyType;
  }

  public void setApplyType(int applyType) {
    this.applyType = applyType;
  }

  public String getOpusInditekind() {
    return opusInditekind;
  }

  public void setOpusInditekind(String opusInditekind) {
    this.opusInditekind = opusInditekind;
  }

  public String getFinishTime() {
    return finishTime;
  }

  public void setFinishTime(String finishTime) {
    this.finishTime = finishTime;
  }

  public String getFinishAddress() {
    return finishAddress;
  }

  public void setFinishAddress(String finishAddress) {
    this.finishAddress = finishAddress;
  }

  public String getCompleteCountries() {
    return completeCountries;
  }

  public void setCompleteCountries(String completeCountries) {
    this.completeCountries = completeCountries;
  }

  public String getCompleteProvince() {
    return completeProvince;
  }

  public void setCompleteProvince(String completeProvince) {
    this.completeProvince = completeProvince;
  }

  public String getCompleteCity() {
    return completeCity;
  }

  public void setCompleteCity(String completeCity) {
    this.completeCity = completeCity;
  }

  public String getCompleteArea() {
    return completeArea;
  }

  public void setCompleteArea(String completeArea) {
    this.completeArea = completeArea;
  }

  public int getPublishState() {
    return publishState;
  }

  public void setPublishState(int publishState) {
    this.publishState = publishState;
  }

  public String getFirstPublishTime() {
    return firstPublishTime;
  }

  public void setFirstPublishTime(String firstPublishTime) {
    this.firstPublishTime = firstPublishTime;
  }

  public String getFirstCountry() {
    return firstCountry;
  }

  public void setFirstCountry(String firstCountry) {
    this.firstCountry = firstCountry;
  }

  public String getFirstProvince() {
    return firstProvince;
  }

  public void setFirstProvince(String firstProvince) {
    this.firstProvince = firstProvince;
  }

  public String getFirstCity() {
    return firstCity;
  }

  public void setFirstCity(String firstCity) {
    this.firstCity = firstCity;
  }

  public String getWorkPublishArea() {
    return workPublishArea;
  }

  public void setWorkPublishArea(String workPublishArea) {
    this.workPublishArea = workPublishArea;
  }

  public String getWorkotherAreas() {
    return workotherAreas;
  }

  public void setWorkotherAreas(String workotherAreas) {
    this.workotherAreas = workotherAreas;
  }

  public int getRightOwnMode() {
    return rightOwnMode;
  }

  public void setRightOwnMode(int rightOwnMode) {
    this.rightOwnMode = rightOwnMode;
  }

  public int getObtainMode() {
    return obtainMode;
  }

  public void setObtainMode(int obtainMode) {
    this.obtainMode = obtainMode;
  }

  public UploadAttachmentDto getRightGuarantee() {
    return rightGuarantee;
  }

  public void setRightGuarantee(UploadAttachmentDto rightGuarantee) {
    this.rightGuarantee = rightGuarantee;
  }

  public UploadAttachmentDto getCooperationContract() {
    return cooperationContract;
  }

  public void setCooperationContract(UploadAttachmentDto cooperationContract) {
    this.cooperationContract = cooperationContract;
  }

  public String getEntrustType() {
    return entrustType;
  }

  public void setEntrustType(String entrustType) {
    this.entrustType = entrustType;
  }

  public UploadAttachmentDto getEntrustContract() {
    return entrustContract;
  }

  public void setEntrustContract(UploadAttachmentDto entrustContract) {
    this.entrustContract = entrustContract;
  }

  public UploadAttachmentDto getTrusteeContract() {
    return trusteeContract;
  }

  public void setTrusteeContract(UploadAttachmentDto trusteeContract) {
    this.trusteeContract = trusteeContract;
  }

  public int getRightScope() {
    return rightScope;
  }

  public void setRightScope(int rightScope) {
    this.rightScope = rightScope;
  }

  public String getEmedium() {
    return emedium;
  }

  public void setEmedium(String emedium) {
    this.emedium = emedium;
  }

  public String getEmediumCD() {
    return emediumCD;
  }

  public void setEmediumCD(String emediumCD) {
    this.emediumCD = emediumCD;
  }

  public String getEmediumOther() {
    return emediumOther;
  }

  public void setEmediumOther(String emediumOther) {
    this.emediumOther = emediumOther;
  }

  public String getEmediumCertificate() {
    return emediumCertificate;
  }

  public void setEmediumCertificate(String emediumCertificate) {
    this.emediumCertificate = emediumCertificate;
  }

  public String getPmediumCertificate() {
    return pmediumCertificate;
  }

  public void setPmediumCertificate(String pmediumCertificate) {
    this.pmediumCertificate = pmediumCertificate;
  }

  public String getPmediumCount() {
    return pmediumCount;
  }

  public void setPmediumCount(String pmediumCount) {
    this.pmediumCount = pmediumCount;
  }

  public int getFileType() {
    return fileType;
  }

  public void setFileType(int fileType) {
    this.fileType = fileType;
  }

  public int getStatus_type() {
    return status_type;
  }

  public void setStatus_type(int status_type) {
    this.status_type = status_type;
  }

  public String getRefuseReason() {
    return refuseReason;
  }

  public void setRefuseReason(String refuseReason) {
    this.refuseReason = refuseReason;
  }

  public String getCertificateUrl() {
    return certificateUrl;
  }

  public void setCertificateUrl(String certificateUrl) {
    this.certificateUrl = certificateUrl;
  }

  public int getInactiveType() {
    return inactiveType;
  }

  public void setInactiveType(int inactiveType) {
    this.inactiveType = inactiveType;
  }

  public String getCancelTheProofUrl() {
    return cancelTheProofUrl;
  }

  public void setCancelTheProofUrl(String cancelTheProofUrl) {
    this.cancelTheProofUrl = cancelTheProofUrl;
  }

  public int getRestatus() {
    return restatus;
  }

  public void setRestatus(int restatus) {
    this.restatus = restatus;
  }

  public LocalDateTime getRegistrationDate() {
    return registrationDate;
  }

  public void setRegistrationDate(LocalDateTime registrationDate) {
    this.registrationDate = registrationDate;
  }

  public BigInteger getAllUpdateId() {
    return allUpdateId;
  }

  public void setAllUpdateId(BigInteger allUpdateId) {
    this.allUpdateId = allUpdateId;
  }

  public String getStateType() {
    return stateType;
  }

  public void setStateType(String stateType) {
    this.stateType = stateType;
  }

  public String getStarDate() {
    return starDate;
  }

  public void setStarDate(String starDate) {
    this.starDate = starDate;
  }

  public String getEndDate() {
    return endDate;
  }

  public void setEndDate(String endDate) {
    this.endDate = endDate;
  }

  public String getRightScopeString() {
    return rightScopeString;
  }

  public void setRightScopeString(String rightScopeString) {
    this.rightScopeString = rightScopeString;
  }

  public String getPublishStatusString() {
    return publishStatusString;
  }

  public void setPublishStatusString(String publishStatusString) {
    this.publishStatusString = publishStatusString;
  }

  public String getApplyTypeString() {
    return applyTypeString;
  }

  public void setApplyTypeString(String applyTypeString) {
    this.applyTypeString = applyTypeString;
  }

  public String getRightAttributionWay_String() {
    return rightAttributionWay_String;
  }

  public void setRightAttributionWay_String(String rightAttributionWay_String) {
    this.rightAttributionWay_String = rightAttributionWay_String;
  }

  public String getRightWay_String() {
    return rightWay_String;
  }

  public void setRightWay_String(String rightWay_String) {
    this.rightWay_String = rightWay_String;
  }

  public String getStatus_String() {
    return status_String;
  }

  public void setStatus_String(String status_String) {
    this.status_String = status_String;
  }

  public String getRegNum() {
    return regNum;
  }

  public void setRegNum(String regNum) {
    this.regNum = regNum;
  }

  public int getFocusWork() {
    return focusWork;
  }

  public void setFocusWork(int focusWork) {
    this.focusWork = focusWork;
  }

  public int getCertificatePrint() {
    return certificatePrint;
  }

  public void setCertificatePrint(int certificatePrint) {
    this.certificatePrint = certificatePrint;
  }

  public BigInteger getDraftSaveId() {
    return draftSaveId;
  }

  public void setDraftSaveId(BigInteger draftSaveId) {
    this.draftSaveId = draftSaveId;
  }

  public String getIntherFort() {
    return intherFort;
  }

  public void setIntherFort(String intherFort) {
    this.intherFort = intherFort;
  }

  public int getRefuseRevokes() {
    return refuseRevokes;
  }

  public void setRefuseRevokes(int refuseRevokes) {
    this.refuseRevokes = refuseRevokes;
  }

  public int getCountDown() {
    return countDown;
  }

  public void setCountDown(int countDown) {
    this.countDown = countDown;
  }

  public String getDictName() {
    return dictName;
  }

  public void setDictName(String dictName) {
    this.dictName = dictName;
  }

  public int getMaskCount() {
    return maskCount;
  }

  public void setMaskCount(int maskCount) {
    this.maskCount = maskCount;
  }


  public String getOnBlockToken() {
    return onBlockToken;
  }

  public void setOnBlockToken(String onBlockToken) {
    this.onBlockToken = onBlockToken;
  }

  public AgentDto getAgentList() {
    return agentList;
  }

  public void setAgentList(AgentDto agentList) {
    this.agentList = agentList;
  }

  public List<AuthorDto> getAuthorList() {
    return authorList;
  }

  public void setAuthorList(List<AuthorDto> authorList) {
    this.authorList = authorList;
  }

  public List<CopyrightOwnerDto> getOwnerList() {
    return ownerList;
  }

  public void setOwnerList(List<CopyrightOwnerDto> ownerList) {
    this.ownerList = ownerList;
  }

  public SubmitterDto getSubmitter() {
    return submitter;
  }

  public void setSubmitter(SubmitterDto submitter) {
    this.submitter = submitter;
  }

    /*public HaverightDto getHaveright() {
        return haveright;
    }

    public void setHaveright(HaverightDto haveright) {
        this.haveright = haveright;
    }*/

  public List<UploadAttachmentDto> getUploadWorks() {
    return uploadWorks;
  }

  public void setUploadWorks(List<UploadAttachmentDto> uploadWorks) {
    this.uploadWorks = uploadWorks;
  }

  public List<ProcessRecordDto> getFlowRecord() {
    return flowRecord;
  }

  public void setFlowRecord(List<ProcessRecordDto> flowRecord) {
    this.flowRecord = flowRecord;
  }

  public List<Integer> getHaverightIdList() {
    return haverightIdList;
  }

  public void setHaverightIdList(List<Integer> haverightIdList) {
    this.haverightIdList = haverightIdList;
  }

  public UploadAttachmentDto getAgentBook() {
    return agentBook;
  }

  public void setAgentBook(UploadAttachmentDto agentBook) {
    this.agentBook = agentBook;
  }

  public UploadAttachmentDto getPositionContract() {
    return positionContract;
  }

  public void setPositionContract(UploadAttachmentDto positionContract) {
    this.positionContract = positionContract;
  }

  public String getInstructionsBox() {
    return instructionsBox;
  }

  public void setInstructionsBox(String instructionsBox) {
    this.instructionsBox = instructionsBox;
  }

  public String getCompleteCounty() {
    return completeCounty;
  }

  public void setCompleteCounty(String completeCounty) {
    this.completeCounty = completeCounty;
  }

  public String getFirstCounty() {
    return firstCounty;
  }

  public void setFirstCounty(String firstCounty) {
    this.firstCounty = firstCounty;
  }

  public List<Long> getUploadWorkRemove() {
    return uploadWorkRemove;
  }

  public void setUploadWorkRemove(List<Long> uploadWorkRemove) {
    this.uploadWorkRemove = uploadWorkRemove;
  }

  public Boolean getAccusation() {
    return isAccusation;
  }

  public void setAccusation(Boolean accusation) {
      isAccusation = accusation;
  }

    public List<TagDto> getTags() {
        return tags;
    }

    public void setTags(List<TagDto> tags) {
        this.tags = tags;
    }

    public String getCertificateCreateTime() {
        if (CopyrightManager.InactiveTypeValue.REVOKED.equals(inactiveType) && StringUtils.isNotEmpty(certificateCreateTime))
            return "作品登记已撤销";
        return certificateCreateTime;
    }
}
