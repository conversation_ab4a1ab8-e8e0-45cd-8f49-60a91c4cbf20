package blockChain.facade.service;

import blockChain.bean.PageQuery;
import blockChain.bean.PageResponse;
import blockChain.bean.ResultCode;
import blockChain.controller.exception.NotFountException;
import blockChain.dto.message.MessageDto;
import blockChain.dto.message.MessageDtoQueryParam;
import blockChain.entities.UserEntity;
import blockChain.entities.message.MessageEntity;
import blockChain.entities.message.MessageUserEntity;
import blockChain.entities.message.QMessageEntity;
import blockChain.exception.BaseException;
import blockChain.exception.EntityNotFoundException;
import blockChain.mapper.MessageMapper;
import blockChain.repository.MessagePredicates;
import blockChain.service.MessageService;
import blockChain.service.MessageUserService;
import blockChain.service.UserService;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.querydsl.QPageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/18 16:20
 */
@Service
@AllArgsConstructor
public class MessageServiceFacade {

  private final UserService userService;

  private final MessageService messageService;

  private final MessageUserService messageUserService;


  @Transactional
  public void sendMessage(MessageDto dto) {
    sendMessageBase(dto.getTitle(), dto.getContent(), dto.getToAll(), dto.getUserIds());
  }

  /**
   * 对指定用户发送消息
   * @param title
   * @param content
   * @param toAll
   * @param userIds
   */
  @Transactional
  public void sendMessageBase(String title, String content, Boolean toAll, List<Integer> userIds) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(0);
    Assert.notNull(currentUserId, "匿名用户不允许发送协会消息");
    Assert.hasText(title, "标题不能为空");
    Assert.hasText(content, "内容不能为空");
    Assert.isTrue(title.length()<=255, "标题长度不能超过255");
    Assert.isTrue(content.length()<=4096, "内容长度不能超过4096");

    UserEntity userEntity = userService.findById(currentUserId).orElseThrow(() -> new EntityNotFoundException("未找到该用户"));

    MessageEntity messageEntity = new MessageEntity()
      .setTitle(title)
      .setContent(content)
      .setCreator(userEntity)
      .setUuid(UUID.randomUUID().toString())
      .setToAll(toAll);

    messageService.save(messageEntity);

    List<UserEntity> userEntityList;
    if (Boolean.TRUE.equals(toAll)) {
      userEntityList = userService.findAll();
    } else {
      userEntityList = userService.findByIds(userIds);
    }

    List<MessageUserEntity> messageUserEntityList = userEntityList.parallelStream().map(user -> {
      MessageUserEntity messageUserEntity = new MessageUserEntity();
      messageUserEntity.setMessage(messageEntity)
        .setUuid(UUID.randomUUID().toString())
        .setUser(user)
        .setState(MessageUserEntity.MessageUserState.UNREAD);

      return messageUserEntity;
    }).collect(Collectors.toList());

    messageUserService.saveAll(messageUserEntityList);
  }

  @Transactional(readOnly = true)
  public PageResponse<MessageDto> findPage(MessageDtoQueryParam pageQuery) {
    QMessageEntity qMessageEntity = QMessageEntity.messageEntity;
    QPageRequest pageRequest = new QPageRequest(pageQuery.getPage() - 1, pageQuery.getPageSize(), qMessageEntity.createTime.desc(), qMessageEntity.id.desc());

    Page<MessageEntity> page = messageService.findAll(MessagePredicates.pageQueryByTitleAndByContentAndCreateTimeBetween(pageQuery.getTitle(), pageQuery.getContent(), pageQuery.getCreateTimeStart(), pageQuery.getCreateTimeEnd()), pageRequest);
    return PageResponse.of(page, MessageMapper.INSTANCE.toMessageDtoList(page.getContent()));
  }

  @Transactional(readOnly = true)
  public PageResponse<MessageDto> findPageByUserId(MessageDtoQueryParam param) {
    int userId = SecurityUtils.getCurrentUserId();

    Page<MessageDto> page = messageService.findPageByUserId(userId, param.getTitle(), param.getContent(), param.getState(), param.getCreateTimeStart(), param.getCreateTimeEnd(), param.getPageable());
    return PageResponse.of(page, page.getContent());
  }

  @Transactional
  public void readMessage(String uuid) {
    int currentUserId = SecurityUtils.getCurrentUserId();

    MessageUserEntity messageUserEntity = messageUserService.findByUserIdAndMessageUuid(currentUserId, uuid).orElseThrow(() -> new EntityNotFoundException("没有找到该消息"));
    if (messageUserEntity.getState() == MessageUserEntity.MessageUserState.READ) {
      throw new BaseException(ResultCode.RESULT_FAIL, "该消息已读！");
    }

    messageUserEntity.setState(MessageUserEntity.MessageUserState.READ);
  }

  @Transactional
  public void deleteMessage(String uuid) {
    MessageEntity messageEntity = messageService.findByUuid(uuid).orElseThrow(NotFountException::new);
    messageService.delete(messageEntity);
  }

  @Transactional(readOnly = true)
  public MessageDto getOneMessage(String uuid) {
    MessageEntity messageEntity = messageService.findByUuid(uuid).orElseThrow(NotFountException::new);
    MessageDto dto = MessageMapper.INSTANCE.toMessageDto(messageEntity);
    List<Integer> collect = messageEntity.getUsers().stream().map(u -> u.getUser().getUserId()).collect(Collectors.toList());
    dto.setUserIds(collect);
    return dto;
  }

  @Transactional
  public void updateMessage(MessageDto dto) {

    Assert.hasText(dto.getTitle(), "标题不能为空");
    Assert.hasText(dto.getContent(), "内容不能为空");
    Assert.isTrue(dto.getTitle().length()<=255, "标题长度不能超过255");
    Assert.isTrue(dto.getContent().length()<=4096, "内容长度不能超过4096");

    MessageEntity messageEntity = messageService.findByUuid(dto.getUuid()).orElseThrow(NotFountException::new);
    MessageMapper.INSTANCE.update(dto, messageEntity);
    List<Integer> newUserIds = dto.getUserIds();
    // 做合并操作
    // 找到已经存在的userIds
    List<Integer> existingIds;

    if (Boolean.TRUE.equals(dto.getToAll())) {
      existingIds = userService.getAllIds();
    } else {
      existingIds = messageEntity.getUsers().stream().map(mu -> mu.getUser().getUserId()).collect(Collectors.toList());
    }

    // 存在这两个差别
    // 不变的 (两个都存在)
    // 既然不变，这个代码就不用执行了 //List<Integer> unchangedIds = existingIds.stream().filter(i -> newUserIds.stream().anyMatch(oi -> oi.equals(i))).collect(Collectors.toList());
    // 要增加的(新传入的有，原来的不存在)
    List<Integer> appendIds = newUserIds.stream().filter(i -> existingIds.stream().noneMatch(ei -> ei.equals(i))).collect(Collectors.toList());
    // 要删除的 (原来的有，新传入的不存在)
    List<Integer> removeIds = newUserIds.stream().filter(i -> existingIds.stream().anyMatch(ei -> ei.equals(i))).collect(Collectors.toList());

    List<UserEntity> appendUsers = userService.findByIds(appendIds);

    List<MessageUserEntity> messageUserEntityList = appendUsers.parallelStream().map(user -> {
      MessageUserEntity messageUserEntity = new MessageUserEntity();
      messageUserEntity.setMessage(messageEntity)
        .setUuid(UUID.randomUUID().toString())
        .setUser(user)
        .setState(MessageUserEntity.MessageUserState.UNREAD);

      return messageUserEntity;
    }).collect(Collectors.toList());

    messageUserService.saveAll(messageUserEntityList);

    messageUserService.remove(messageEntity.getId(), removeIds);

  }

  @Transactional(readOnly = true)
  public Long countUnreadSize() {
    int userId = SecurityUtils.getCurrentUserId();
    return messageService.countUnreadSize(userId);
  }
}
