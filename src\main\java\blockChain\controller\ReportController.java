package blockChain.controller;

import blockChain.bean.LicenseSendInfo;
import blockChain.bean.QueryParam;
import blockChain.config.SpringConfig;
import blockChain.constant.AuthoritiesConstant;
import blockChain.dto.CopyrightOwnerDto;
import blockChain.dto.NationRevokeDTO;
import blockChain.dto.UserDto;
import blockChain.entities.*;
import blockChain.facade.service.*;
import blockChain.mapper.CopyrightManagerDtoMapper;
import blockChain.mapper.CopyrightOwnerDtoMapper;
import blockChain.mapper.DigitalDtoMapper;
import blockChain.nationalBean.*;
import blockChain.repository.CopyrightManagerRepository;
import blockChain.service.nationalservice.NationAddClient;
import blockChain.utils.HttpClientUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR> Jie
 * @date 2020/5/9 12:12
 */
@Api("数据上报")
@Slf4j
@RestController
@RequestMapping("report")
@RequiredArgsConstructor
public class ReportController {
  @Autowired
  private CopyrightManagerServiceFacade copyrightManagerServiceFacade;
  @Autowired
  private DigitalServiceFacade digitalServiceFacade;
  @Autowired
  private UserServiceFacade userServiceFacade;
  @Autowired
  private CopyrightManagerRepository copyrightManagerRepository;
  @Autowired
  private ReportServiceFacade reportServiceFacade;
  @Autowired
  private BackendServiceFacade backendServiceFacade;


  private final SpringConfig config;


    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.NationSubmit + "')")
    @ApiOperation("上报国家局列表查询")
    @PostMapping("certificateQuery")
    public ResponseEntity<Map<String, Object>> certificateQuery(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
            JSONArray array = queryParam.getJSONArray("status_type");
            List<Integer> statusList = new ArrayList<>();
            if (array != null && !array.isEmpty()) {
                statusList = JSONObject.parseArray(array.toJSONString(), Integer.class);
            }
            JSONArray arrayRegNum = queryParam.getJSONArray("regNum");
            List<Integer> regNums = new ArrayList<>();
            if (arrayRegNum != null && !arrayRegNum.isEmpty()) {
                regNums = JSONObject.parseArray(arrayRegNum.toJSONString(), Integer.class);
            }
            UserDto userDto = userServiceFacade.findUserById(queryParam.getInteger("UID"));
            if (userDto == null) {
                userDto = new UserDto();
            }

            Page<CopyrightManager> copyrightManagerPage = copyrightManagerServiceFacade.query(null,queryParam.getString("worksNum"), queryParam.getString("productionName"), queryParam.getString("productionTypeId"),
                    queryParam.getInteger("rightOwnMode"), queryParam.getString("startTime"), queryParam.getString("endTime"), statusList, null, queryParam.getString("agentName"), queryParam.getString("copyrightName"),
                    queryParam.getBoolean("isCertificate"),queryParam.getBoolean("isSubmit"),queryParam.getBoolean("isreport"),userDto.getVisibleLevelCity(),userDto.getVisibleLevelCounty(),new Long(queryParam.getPage()).intValue() - 1, new Long(queryParam.getPageSize()).intValue(),null,null
                    , queryParam.getString("certificateStartDate"), queryParam.getString("certificateEndDate"), regNums, null, 0, 0);
            List<CopyrightManager> list = copyrightManagerPage.getContent();
            result.put("listData", CopyrightManagerDtoMapper.INSTANCE.entityToDto(list));
            //设置分页项
            result.put("total", copyrightManagerPage.getTotalElements());
            result.put("page", queryParam.getPage());
            result.put("pageSize", queryParam.getPageSize());
            //获取数据字典
            //获取作品类型
            result.put("productionTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82)));
            //获取权利归属方式字典
            result.put("rightOwnModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(115)));
            //获取权利取得方式字典
            result.put("obtainModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(121)));
            //获取作品创作性质字典
            result.put("opusInditekindList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(126)));
            //获取著作权人类别字典
            result.put("manTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(134)));
            //获取著作权人证件类别字典
            result.put("manIdentityTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(141)));
            //获取国家字典
            result.put("CountryList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(157)));
            //获取省份字典
            //result.put("ProvinceList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(393)));
            //获取权力拥有状况字典
            result.put("rightScopeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(429)));
            //获取审批状态
            result.put("statusList", getApprovalType());
            result.put("message", "查询成功！");
        } catch (Exception e) {
            result.put("message", "查询失败！");
            result.put("errorCode", e.getLocalizedMessage());
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        } finally {
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        }
    }

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.ReportCoypRight + "')")
    @ApiOperation("上报版保中心列表查询")
    @PostMapping("reportQuery")
    public ResponseEntity<Map<String, Object>> reportQuery(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
            JSONArray array = queryParam.getJSONArray("status_type");
            List<Integer> statusList = new ArrayList<>();
            if (array != null && !array.isEmpty()) {
                statusList = JSONObject.parseArray(array.toJSONString(), Integer.class);
            }

            UserDto userDto = userServiceFacade.findUserById(queryParam.getInteger("UID"));
            if (userDto == null) {
                userDto = new UserDto();
            }

            Page<CopyrightManager> copyrightManagerPage = copyrightManagerServiceFacade.query(null,queryParam.getString("worksNum"), queryParam.getString("productionName"), queryParam.getString("productionTypeId"),
                    queryParam.getInteger("rightOwnMode"), queryParam.getString("startTime"), queryParam.getString("endTime"), statusList, null, queryParam.getString("agentName"), queryParam.getString("copyrightName"),
                    queryParam.getBoolean("isCertificate"),queryParam.getBoolean("isSubmit"),queryParam.getBoolean("isreport"),userDto.getVisibleLevelCity(),userDto.getVisibleLevelCounty(),new Long(queryParam.getPage()).intValue() - 1, new Long(queryParam.getPageSize()).intValue(),null,null
                    , queryParam.getString("certificateStartDate"), queryParam.getString("certificateEndDate"), null, null, 0 ,0);
            List<CopyrightManager> list = copyrightManagerPage.getContent();
            result.put("listData", CopyrightManagerDtoMapper.INSTANCE.entityToDto(list));
            //设置分页项
            result.put("total", copyrightManagerPage.getTotalElements());
            result.put("page", queryParam.getPage());
            result.put("pageSize", queryParam.getPageSize());
            //获取数据字典
            //获取作品类型
            result.put("productionTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82)));
            //获取权利归属方式字典
            result.put("rightOwnModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(115)));
            //获取权利取得方式字典
            result.put("obtainModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(121)));
            //获取作品创作性质字典
            result.put("opusInditekindList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(126)));
            //获取著作权人类别字典
            result.put("manTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(134)));
            //获取著作权人证件类别字典
            result.put("manIdentityTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(141)));
            //获取国家字典
            result.put("CountryList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(157)));
            //获取省份字典
            //result.put("ProvinceList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(393)));
            //获取权力拥有状况字典
            result.put("rightScopeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(429)));
            //获取审批状态
            result.put("statusList", getApprovalType());
            result.put("message", "查询成功！");
        } catch (Exception e) {
            result.put("message", "查询失败！");
            result.put("errorCode", e.getLocalizedMessage());
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        } finally {
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        }
    }

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.LicenseInfoSubmit + "')")
    @ApiOperation("上报证照中心列表查询")
    @PostMapping("licenseQuery")
    public ResponseEntity<Map<String, Object>> licenseQuery(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
//            JSONArray array = queryParam.getJSONArray("status_type");
            List<Integer> statusList = new ArrayList<>();
//            if (array != null && !array.isEmpty()) {
//                statusList = JSONObject.parseArray(array.toJSONString(), Integer.class);
//            }
          JSONArray arrayRegNum = queryParam.getJSONArray("regNum");
          List<Integer> regNums = new ArrayList<>();
          if (arrayRegNum != null && !arrayRegNum.isEmpty()) {
            regNums = JSONObject.parseArray(arrayRegNum.toJSONString(), Integer.class);
          }

          Optional<Integer> userId = SecurityUtils.getOptionalCurrentUserId();
          UserDto userDto = userServiceFacade.findUserById(userId.get());

          Page<CopyrightManager> copyrightManagerPage = copyrightManagerServiceFacade.reportQuery(queryParam.getString("worksNum"), queryParam.getString("productionName"),
                  queryParam.getString("productionTypeId"), queryParam.getInteger("rightOwnMode"),
                  queryParam.getString("startTime"), queryParam.getString("endTime"), queryParam.getString("agentName"), queryParam.getString("copyrightName"),
                  new Long(queryParam.getPage()).intValue() - 1, new Long(queryParam.getPageSize()).intValue(),
                  queryParam.getString("certificateStartDate"), queryParam.getString("certificateEndDate"), regNums);
          List<CopyrightManager> list = copyrightManagerPage.getContent();
          result.put("listData", CopyrightManagerDtoMapper.INSTANCE.entityToDto(list));
          //设置分页项
          result.put("total", copyrightManagerPage.getTotalElements());
          result.put("page", queryParam.getPage());
          result.put("pageSize", queryParam.getPageSize());
          //获取数据字典
          //获取作品类型
          result.put("productionTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82)));
          //获取权利归属方式字典
            result.put("rightOwnModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(115)));
            //获取权利取得方式字典
            result.put("obtainModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(121)));
            //获取作品创作性质字典
            result.put("opusInditekindList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(126)));
            //获取著作权人类别字典
            result.put("manTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(134)));
            //获取著作权人证件类别字典
            result.put("manIdentityTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(141)));
            //获取国家字典
            result.put("CountryList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(157)));
            //获取省份字典
            //result.put("ProvinceList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(393)));
            //获取权力拥有状况字典
            result.put("rightScopeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(429)));
            //获取审批状态
            result.put("statusList", getApprovalType());
            result.put("message", "查询成功！");
        } catch (Exception e) {
            result.put("message", "查询失败！");
            result.put("errorCode", e.getLocalizedMessage());
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
        } finally {
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        }
    }



  private List<Map<String, Object>> getApprovalType() {
    List<Map<String, Object>> result = new ArrayList<Map<String, Object>>() {{
      add(new HashMap<String, Object>() {{
        put("id", CopyrightManager.FINAL_REVIEW);
        put("name", "终审中");
      }});
      add(new HashMap<String, Object>() {{
        put("id", CopyrightManager.SECOND_REVIEW);
        put("name", "复审中");
      }});
      add(new HashMap<String, Object>() {{
        put("id", CopyrightManager.FIRST_REVIEW);
        put("name", "初审中");
      }});
      add(new HashMap<String, Object>() {{
        put("id", CopyrightManager.CERT_CREATE);
        put("name", "证书生成中");
      }});
      add(new HashMap<String, Object>() {{
        put("id", CopyrightManager.CERT_CREATED);
        put("name", "证书已生成");
      }});
      add(new HashMap<String, Object>() {{
        put("id", CopyrightManager.MODIFIED);
        put("name", "材料修改");
      }});
      add(new HashMap<String, Object>() {{
          put("id", CopyrightManager.CERT_CREATE_REJECT);
          put("name", "证书生成拒绝");
      }});
      add(new HashMap<String, Object>() {{
        put("id", CopyrightManager.FINAL_REVIEW_REJECT);
        put("name", "终审拒绝");
      }});
      add(new HashMap<String, Object>() {{
        put("id", CopyrightManager.SECOND_REVIEW_REJECT);
        put("name", "复审拒绝");
      }});
      add(new HashMap<String, Object>() {{
          put("id", CopyrightManager.FIRST_REVIEW_REJECT);
          put("name", "初审拒绝");
      }});
        add(new HashMap<String, Object>() {{
            put("id", CopyrightManager.UNSUBMIT);
            put("name", "未提交");
        }});
    }};
      return result;
  }

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.NationSubmit + "')")
    @ApiOperation("上报国家局注销")
    @PostMapping("certificateRevokePush")
    public ResponseEntity<Map<String, Object>> uptoNationRevoke(@Valid @RequestBody NationRevokeDTO nationRevokeDTO) {
        int result = 0;
        String resultStr = "";
        log.info("开始上报注销作品材料...");
        CloseableHttpClient client = HttpClientUtil.createHttpClient();
        String key = UUID.randomUUID().toString();
        try {
            CopyrightManageCancelBean crb = getCopyrightCancelBean(nationRevokeDTO.getRegistrationNum(), nationRevokeDTO.getReason());
            //log.info(crb.toString());
            resultStr = NationAddClient.Revoke(client,crb,key);
            org.json.JSONObject jsonObject=new org.json.JSONObject(resultStr);
            result=jsonObject.getInt("code");
            resultStr=jsonObject.getString("msg");
        } catch(Exception e) {
            log.error("error", e);
            resultStr=e.getMessage();
            result=-1;
        }
        if(result==200) {
            CopyrightManager cd = copyrightManagerServiceFacade.getById(nationRevokeDTO.getRegistrationNum().longValue());
            cd.setEnable(CopyrightManager.EnableStatusEnum.DISABLE);
            copyrightManagerRepository.save(cd);
        }
        if(client!=null){
            try {
                client.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                log.error("error", e);
            }
        }
        log.info("结束上报注销作品材料...");
        Map<String,Object> results = new HashMap<String,Object>();
        results.put("result", result);
        results.put("message", resultStr);
        return new ResponseEntity<Map<String, Object>>(results,result==200?HttpStatus.OK:HttpStatus.INTERNAL_SERVER_ERROR);
    }


  /**
   * 获取属性节点，参数对应属性KEY的值
   * @param alias
   * @param name
   * @param value
   * @return
   */
  public JSONObject getNodeAttr(String alias,String name, String value){
    JSONObject nodeAttr = new JSONObject();
    nodeAttr.put("@alias", alias);
    nodeAttr.put("@name", name);
    nodeAttr.put("@value", value);
    return	nodeAttr;
  }


  /**
   * surfaceData节点，证照照面信息
   * @param licenseInfo
   * @return SurfaceData节点的字节点内容
   */
  public JSONObject getSurfaceChildNode(LicenseSendInfo licenseInfo){
    JSONObject surfaceChildNode = new JSONObject();

    JSONObject baseNode = new JSONObject();//surfaceData节点->base节点
    JSONArray baseItemNodeAttr = new JSONArray();//surfaceData节点->base节点->item节点->item节点属性
    String nodeHead = licenseInfo.getNode().substring(0,2);
    if(!nodeHead.equals("35")) {
      baseItemNodeAttr.add(getNodeAttr("zzjgdm", "统一社会信用代码", licenseInfo.getNode()));
    }else{
      baseItemNodeAttr.add(getNodeAttr("sfzno", "身份证号码", licenseInfo.getNode()));
    }
    baseItemNodeAttr.add(getNodeAttr("bzdw","颁证单位","中共福建省委宣传部"));
    baseItemNodeAttr.add(getNodeAttr("bzsj","颁证时间",licenseInfo.getRegistrationDate().split(" ")[0].replace("-","")));
    baseItemNodeAttr.add(getNodeAttr("qymc","企业名称",licenseInfo.getAuthors()));
    baseItemNodeAttr.add(getNodeAttr("zzbh","登记号",licenseInfo.getWorksNum()));
    baseItemNodeAttr.add(getNodeAttr("yxqqs","有效期（起始）",licenseInfo.getRegistrationDate().split(" ")[0].replace("-","")));
    baseItemNodeAttr.add(getNodeAttr("yxqjz","有效期（截止）","00000000"));
    baseItemNodeAttr.add(getNodeAttr("zpzpmc","作品/制品名称",licenseInfo.getWorkName()));
    baseItemNodeAttr.add(getNodeAttr("zz","作者",licenseInfo.getAuthors()));
    baseItemNodeAttr.add(getNodeAttr("zplb","作品类别",licenseInfo.getWorkType()));
    baseItemNodeAttr.add(getNodeAttr("zzqr","著作权人",licenseInfo.getOwners()));
    baseItemNodeAttr.add(getNodeAttr("sccbzzrq","首次出版/制作日期",((licenseInfo.getFirstPublishTime()!=null && !licenseInfo.getFirstPublishTime().equals(""))?(licenseInfo.getFirstPublishTime().replace("-","")):"00000000")));
    baseItemNodeAttr.add(getNodeAttr("sqr","申请人","著作权人"));
    baseItemNodeAttr.add(getNodeAttr("shdw","审核单位","中共福建省委宣传部"));
    baseItemNodeAttr.add(getNodeAttr("czwcrq","创作完成日期",licenseInfo.getFinishTime().replace("-","")));
    baseNode.put("item",baseItemNodeAttr);
    surfaceChildNode.put("base", baseNode);

    return surfaceChildNode;
  }

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.LicenseInfoSubmit + "')")
    @ApiOperation("上报证照中心")
    @PostMapping("licenseSendInfoSubmit")
    public ResponseEntity<Map<String, Object>> licenseSendInfoSubmit(@Valid @RequestBody QueryParam queryParam) {
        List<Long> registrationNums = queryParam.getJSONArray("registrationNums").toJavaList(Long.class);
        Map<String, Object> result = new HashMap<>();

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity loginUser = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());
        try {
            if (registrationNums == null || registrationNums.isEmpty()) {
                result.put("message", "请选择作品！");
                return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
            }
            //创建请求线程
            reportServiceFacade.licenseSendInfoRun(registrationNums, loginUser);
            result.put("message", "开始提交证照中心，请耐心等待结果");
            Thread.sleep(3000);

            return new ResponseEntity<Map<String,Object>>(result,HttpStatus.OK);
        }catch (Exception e){
            log.error("error", e);
            result.put("message","提交证照中心程序启动失败");
            return new ResponseEntity<Map<String,Object>>(result,HttpStatus.INTERNAL_SERVER_ERROR);
        }
}

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.NationSubmit + "')")
    @ApiOperation("上报国家局")
    @PostMapping("certificatePush")
    public  ResponseEntity<Map<String,Object>> uptoNation(@Valid @RequestBody QueryParam queryParam) {
        List<BigInteger> registrationNums = queryParam.getJSONArray("registrationNums").toJavaList(BigInteger.class);
        Map<String,Object> result = new HashMap<>();
        Integer uid = queryParam.getInteger("UID");
        UserDto userDto = userServiceFacade.findUserById(uid);
        try {
            if (registrationNums == null || registrationNums.isEmpty()) {
                result.put("message","请选择作品！");
                return new ResponseEntity<Map<String,Object>>(result,HttpStatus.INTERNAL_SERVER_ERROR);
            }
            //创建请求线程
            reportServiceFacade.certificatePushRun(registrationNums,userDto.getRealName());
            result.put("message","开始上报国家局，请耐心等待结果");
            Thread.sleep(3000);
            return new ResponseEntity<Map<String,Object>>(result,HttpStatus.OK);
        }catch (Exception e){
            log.error("error", e);
            result.put("message","上报程序启动失败");
            return new ResponseEntity<Map<String,Object>>(result,HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

  public CopyrightManageCancelBean getCopyrightCancelBean(BigInteger registrationNum,String reason)
  {
    CopyrightBean cd=getCopyrightBean(registrationNum);
    CopyrightManageCancelBean copyrightManageCancelBean=new CopyrightManageCancelBean();
    copyrightManageCancelBean.setAgent(cd.getAgent());
    if(cd.getSubmitter().size()>0) {
      copyrightManageCancelBean.setSubmitter(cd.getSubmitter().get(0));
    }
    copyrightManageCancelBean.setUserName(cd.getUserName());
    CopyrightCancelBean copyrightCancelBean=new CopyrightCancelBean();
    copyrightCancelBean.setReason(reason);
    if(cd.getAgent()==null||cd.getAgent().getName()==null||cd.getAgent().getName().length()==0)
    {
      copyrightCancelBean.setApplyType(0);
    }
    else
    {
      copyrightCancelBean.setApplyType(1);
    }
    copyrightCancelBean.setCancelDate(Timestamp.from(Instant.now()));
    copyrightManageCancelBean.setCopyrightcancle(copyrightCancelBean);
    OrganizationBean organizationBean=new OrganizationBean();
    if(cd.getAuthor().size()>0) {
      organizationBean.setAuthor(cd.getAuthor().get(0).getName());
    }
    organizationBean.setFinishTime(cd.getCopyrightManage().getFinishTime());
    organizationBean.setFirstPublishTime(cd.getCopyrightManage().getFirstPublishTime());
    if(cd.getCopyrightOwner().size()>0) {
      organizationBean.setOwner(cd.getCopyrightOwner().get(0).getName());
      organizationBean.setProvince(cd.getCopyrightOwner().get(0).getProvince());
    }
    organizationBean.setProductionName(cd.getCopyrightManage().getProductionName());
    organizationBean.setProductionTypeId(cd.getCopyrightManage().getProductionTypeId());
    organizationBean.setRegDate(cd.getCopyrightManage().getRegistrationDate());
    organizationBean.setRegNum(cd.getCopyrightManage().getRegistrationNum());
    if(cd.getSubmitter().size()>0) {
      organizationBean.setSubmitter(cd.getSubmitter().get(0).getName());
    }
    copyrightManageCancelBean.setOrg(organizationBean);
    return  copyrightManageCancelBean;
  }

  // 将系统bean转换为国家接口对应bean
  public CopyrightBean getCopyrightBean(BigInteger registrationNum) {
    int i = 0;
    // 作品
    CopyrightManager cd = new CopyrightManager();
    cd = copyrightManagerServiceFacade.getById(registrationNum.longValue());
    // 首次发表时间类型转换
    Timestamp startime = new Timestamp(System.currentTimeMillis());
    try {
      startime = Timestamp.valueOf(cd.getFirstPublishTime() + " 00:00:00");
    } catch (Exception e) {
      // TODO Auto-generated catch block
    }
    // 权利拥有表类型转换
    CopyrightManager cp = new CopyrightManager();
    cp = copyrightManagerServiceFacade.getById(registrationNum.longValue());

    List<Integer> rightScope = cd.getHaverightIdList();
    String newRightScope = "";
    if(cd.getRightScope()!=2){
      List<Digital> digitals = digitalServiceFacade.getDictByPid(429);
      for(Digital digital:digitals){
        rightScope.add(digital.getSort());
      }
    }else{
      for(int j=0;j<rightScope.size();j++){
        rightScope.set(j,digitalServiceFacade.getById(rightScope.get(j)).getSort());
      }
    }
    for (i = 0; i < rightScope.size(); i++) {
      newRightScope = newRightScope + "," + rightScope.get(i);
    }
    newRightScope = newRightScope.replaceFirst(",", "");
    System.out.println(newRightScope);

    //权利归属方式处理
    int rightAttributionWay = 0;
    if(cd.getRightOwnMode()==1){
      rightAttributionWay=0;
    }else if(cd.getRightOwnMode()==2){
      rightAttributionWay=1;
    }else if(cd.getRightOwnMode()==3){
      rightAttributionWay=4;
    }else if(cd.getRightOwnMode()==4){
      rightAttributionWay=2;
    }else if(cd.getRightOwnMode()==5){
      rightAttributionWay=3;
    }
    //电子介质空值处理
    if(cd.getEmediumCD()==null||cd.getEmediumCD().isEmpty()){
      cd.setEmediumCD("");
    }
    if(cd.getEmediumOther()==null||cd.getEmediumOther().isEmpty()){
      cd.setEmediumOther("");
    }
    //作品完成国家/城市处理
    String finishCountry="";
    String finishCity="";
    String[] workAdress=cd.getCompleteAreaStr().split(" ");
    finishCountry=workAdress[0];
    if(workAdress.length>2){
      finishCity=workAdress[2];
    }else{
      finishCity=workAdress[1];
    }
    System.out.println(finishCountry+" and "+finishCity);
    String firstContry=null;
    String firstCity=null;
    try {
      firstContry= digitalServiceFacade.getById(Integer.parseInt(cd.getFirstCountry())).getDict_name();
      firstCity= digitalServiceFacade.getById(Integer.parseInt(cd.getFirstCity())).getDict_name();
    }
    catch (Exception e)
    {

    }
    // 与国家局Bean对接
    CopyrightManageBean copyrightManage = new CopyrightManageBean(
      cd.getProductionName(), cd.getWorksNum(),
      Date.from(cd.getCertificateCreateTime().atZone(ZoneId.systemDefault()).toInstant()),
      //Date.valueOf(cd.getCertificateCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-ddHH:mm:ss"))),
      digitalServiceFacade.getById(Integer.parseInt(cd.getProductionTypeId())).getSort(),
      cd.getOpuseDesc(), digitalServiceFacade.getById(Integer.parseInt(cd.getOpusInditekind())-1).getSort(),
      cd.getProductionTypeDesc(), Timestamp.valueOf(cd.getFinishTime()
      + " 00:00:00"), finishCountry,
      finishCity, (cd.getPublishState()-1), startime,
      firstContry ,firstCity,
      digitalServiceFacade.getById(cd.getObtainMode()).getSort(), "", rightAttributionWay, "",
      newRightScope, "", cd.getEmedium() + cd.getEmediumCD()
      + cd.getEmediumOther(), "1", "作品纸质介质", "1",
      cd.getApplyType()-1);
    // 作者
    List<Author> at = cd.getAuthorList();
    List<AuthorBean> authors = new ArrayList<AuthorBean>();
    for (i = 0; i < at.size(); i++) {
      AuthorBean author = new AuthorBean(at.get(i).getAuthorName(), at.get(i).getAuthorName());
      authors.add(author);
    }
    // 著作权人
    List<CopyrightOwner> coentity = cd.getOwnerList();
    List<CopyrightOwnerDto> co = CopyrightOwnerDtoMapper.INSTANCE.entityToDto(coentity);
    List<CopyrightOwnerBean> copyrightOwners = new ArrayList<CopyrightOwnerBean>();
    for (i = 0; i < co.size(); i++) {
      if(co.get(i).getCopySignature()==1){
        co.get(i).setCopySignature(0);
      }else if(co.get(i).getCopySignature()==3){
        co.get(i).setCopySignature(1);
      }
      CopyrightOwnerBean copyrightOwner = new CopyrightOwnerBean(co
        .get(i).getCopyName(), digitalServiceFacade.getById(co.get(i).getCopyCategory()).getSort(), digitalServiceFacade.getById(Integer.parseInt(co.get(i).getCopyCountries())).getSort(),
        digitalServiceFacade.getById(Integer.parseInt(co.get(i).getCopyProvince())).getSort(), co.get(i).getCopyCity(),digitalServiceFacade.getById(co.get(i).getCopyCertificate()).getSort(), co.get(i).getCopyIdCard(), co
        .get(i).getCopySignature(), co.get(i)
        .getCopySignatureName());
      copyrightOwners.add(copyrightOwner);
    }
    // 申请人
    List<SubmitterBean> submitters = new ArrayList<SubmitterBean>();
    Submitter temp = cd.getSubmitter();
    if(temp!=null)
    {
      SubmitterBean submitter = new SubmitterBean(temp.getCopyrightName(),
        temp.getCopyrightAddress(), temp.getCopyrightCode(),
        temp.getCopyrightOwner(), temp.getCopyrightTelephone(),
        temp.getCopyrightEmail(), temp.getCopyrightPhone(),
        temp.getCopyrightFax());
      submitters.add(submitter);
    }else{
      Agent otherTemp = cd.getAgentList();
      SubmitterBean submitter = new SubmitterBean(otherTemp.getAgentName(),
        otherTemp.getAgentAddress(), otherTemp.getAgentCode(),
        otherTemp.getAgentOwner(), otherTemp.getAgentTelephone(),
        otherTemp.getAgentEmail(), otherTemp.getAgentPhone(),
        otherTemp.getAgentFax());
      submitters.add(submitter);
    }
    // 代理人
    Agent agenttemp = (cd.getAgentList() == null)?new Agent():cd.getAgentList();
    AgentBean agent = new AgentBean(cd.getAgentBook()==null?"":cd.getAgentBook().getWorkName(), agenttemp.getAgentName(),
      agenttemp.getAgentAddress(), agenttemp.getAgentCode(), agenttemp.getAgentOwner(),
      agenttemp.getAgentTelephone(), agenttemp.getAgentEmail(), agenttemp.getAgentPhone(),
      agenttemp.getAgentFax());

    CopyrightBean crb = new CopyrightBean();
    crb.setAgent(agent);
    crb.setAuthor(authors);
    crb.setCopyrightManage(copyrightManage);
    crb.setCopyrightOwner(copyrightOwners);
    crb.setSubmitter(submitters);

        /*JSONObject json = JSONObject.fromObject(crb);
		System.out.println(json.toString());*/

    return crb;
  }

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.LicenseInfoSubmit + "')")
    @ApiOperation("数据汇聚（不开放前端页面）")
    @PostMapping("onportemp")
    public void onportemp() {
        copyrightManagerServiceFacade.onportemp();
    }

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.ReportCoypRight + "')")
    @ApiOperation("上报版保中心作品导出地址")
    @PostMapping("exportDataExcelUrl")
    public ResponseEntity<Map<String,Object>> exportDataExcelUrl(@Valid @RequestBody QueryParam queryParam) {
        Map<String,Object> result = new HashedMap();

        Integer taskId = queryParam.getInteger("taskId");
        if (taskId != null && taskId > 0) {
            TaskEntity taskEntity = reportServiceFacade.getTask(taskId);
            result.put("urls", taskEntity.getUrl());
        }

        return  new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.ReportCoypRight + "')")
    @ApiOperation("上报版保中心作品导出")
    @PostMapping("exportDataExcel")
    public ResponseEntity<Map<String,Object>> exportDataExcelNew(@Valid @RequestBody QueryParam queryParam) {
        Map<String,Object> result = new HashedMap();

        TaskEntity taskEntity = new TaskEntity();
        reportServiceFacade.saveTask(taskEntity);
        queryParam.put("taskId", taskEntity.getId());

        reportServiceFacade.exportDataExcelRun(queryParam);

        result.put("taskId", taskEntity.getId());
        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
    }

//    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.LicenseInfoSubmit + "')")
//    @ApiOperation("上报省网办办件-内部测试用-废弃，由统一平台上报")
//    @PostMapping("submitswb")
//    public void submitswb(@Valid @RequestBody QueryParam queryParam) {
//        try {
////      ApasInfoServiceImplService apasInfoServiceImplService=new ApasInfoServiceImplServiceLocator();
////      ApasInfoServiceImpl apasInfoService=apasInfoServiceImplService.getApasInfoServiceAsmx();
////      String xml=apasInfoService.getServiceListNew("11350000003591037R","123456","2000-01-01", "2022-03-07", "10","1","1");
////      String xml=apasInfoService.getServiceSimpleList("11350000003591037R","123456","150","1","1");
////      String configtxt=apasInfoService.getServiceNew("11350000003591037R","123456","0688C657DE0E5B693B6B85C36A09A1C4");
//
//      WaiLianServiceImplService waiLianServiceImplService=new WaiLianServiceImplServiceLocator();
//      WaiLianServiceImpl waiLianService= waiLianServiceImplService.getWaiLianServiceAsmx();
//      copyrightManagerServiceFacade.wailianPushSync(waiLianService);
//    }
//    catch (Exception e)
//    {
//      log.error("error", e);
//    }
//  }


    @ApiOperation("上报证照中心")
    @PostMapping("licenseSendInfoAutoSubmit")
    public void licenseSendInfoAutoSubmit(@Valid @RequestBody QueryParam queryParam) {
        reportServiceFacade.licenseSendInfoAutoSubmit();
    }
}
