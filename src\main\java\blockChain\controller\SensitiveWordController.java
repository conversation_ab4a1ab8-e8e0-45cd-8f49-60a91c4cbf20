package blockChain.controller;

import blockChain.bean.PageQuery;
import blockChain.bean.PageResponse;
import blockChain.bean.QueryParam;
import blockChain.bean.ResultCode;
import blockChain.dto.SensitiveWordDTO;
import blockChain.facade.service.SensitiveWordServiceFacade;
import blockChain.utils.ExcelUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/2
 */
@Slf4j
@RestController
@RequestMapping("sensitiveWord")
@RequiredArgsConstructor
public class SensitiveWordController {

    private final SensitiveWordServiceFacade sensitiveWordServiceFacade;

    @ApiOperation("导入敏感词列表")
    @PostMapping(value = "import")
    public ResponseEntity<Map<String, Object>> importSensitiveWord(@ApiParam(value = "导入敏感词列表", required = true) MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<SensitiveWordDTO> list = ExcelUtils.readExcelFileToDTO(file, SensitiveWordDTO.class);
            sensitiveWordServiceFacade.importSensitiveWord(list);
            result.put("message", "导入中，请稍后查询");
            result.put("resultCode", ResultCode.RESULT_SUCCESS);
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("message", "导入失败！\n原因：" + e.getMessage());
            result.put("resultCode", ResultCode.RESULT_FAIL);
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation("删除")
    @PostMapping(value = "delete")
    public ResponseEntity<Map<String, Object>> delete(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
            Integer id = queryParam.getInteger("id");
            sensitiveWordServiceFacade.delete(id);
            result.put("message", "成功删除！");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("message", "删除失败！\n原因：" + e.toString());
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation("查")
    @PostMapping(value = "query")
    public PageResponse<SensitiveWordDTO> query(@RequestBody PageQuery<SensitiveWordDTO> pageQuery) {
        return sensitiveWordServiceFacade.query(pageQuery);
    }
}
