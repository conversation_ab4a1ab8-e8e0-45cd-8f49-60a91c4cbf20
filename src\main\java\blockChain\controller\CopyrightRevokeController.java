package blockChain.controller;

import blockChain.bean.BaseResponseDto;
import blockChain.bean.PageResponse;
import blockChain.dto.CopyrightManagerDto;
import blockChain.dto.CopyrightRevokeDto;
import blockChain.facade.service.CopyrightRevokeServiceFacade;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 证书撤销
 * <AUTHOR>
 * @date 2020/4/17 9:53
 */
@Api("证书撤销")
@Slf4j
@RestController
@RequestMapping("copyright/revoke")
@AllArgsConstructor
public class CopyrightRevokeController {

  private CopyrightRevokeServiceFacade serviceFacade;

  @ApiOperation(value = "申请撤销", nickname = "createCopyrightRevoke", notes = "Creates a new instance of a `CopyrightRevokeEntity`.", tags={ "证书撤销", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("create")
  public ResponseEntity<BaseResponseDto> createCopyrightRevoke(@Valid @RequestBody CopyrightRevokeDto dto) {
    serviceFacade.revoke(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  @ApiOperation(value = "发起人取消申请撤销", nickname = "deleteCopyrightRevoke", notes = "", tags={ "证书撤销", })
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("remove")
  public ResponseEntity<BaseResponseDto> deleteCopyrightRevoke(@Valid @RequestBody CopyrightRevokeDto dto) {
    serviceFacade.deleteCopyrightRevoke(dto);
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }

  @ApiOperation(value = "处理撤销申请", nickname = "handleCopyrightRevoke", notes = "", tags={ "证书撤销", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("handle")
  public ResponseEntity<BaseResponseDto> handleCopyrightRevoke(@Valid @RequestBody CopyrightRevokeDto dto)
  {
    Integer userId = SecurityUtils.getCurrentUserId();
    serviceFacade.handleCopyrightRevoke(dto.getHandleType(), dto.getIdList(), dto.getRejectReason(),userId);
    return BaseResponseDto.ok(HttpStatus.OK);
  }

  @ApiOperation(value = "查询所有撤销申请（管理员）", nickname = "queryCopyrightRevoke", notes = "", tags={ "证书撤销", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("query")
  public ResponseEntity<PageResponse<CopyrightManagerDto>> queryCopyrightRevoke(@Valid @RequestBody CopyrightRevokeDto dto) {
    PageResponse<CopyrightManagerDto> page = serviceFacade.queryCopyrightRevoke(dto.getPageable(), dto);
    return ResponseEntity.ok(page);
  }

  @ApiOperation(value = "查询自己的撤销申请（用户）", nickname = "queryMineCopyrightRevoke", notes = "", tags={ "证书撤销", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("mine/query")
  public ResponseEntity<PageResponse<CopyrightManagerDto>> queryMineCopyrightRevoke(@Valid @RequestBody CopyrightRevokeDto dto) {
    PageResponse<CopyrightManagerDto> page = serviceFacade.queryMineCopyrightRevoke(dto.getPageable(), dto);
    return ResponseEntity.ok(page);
  }

}
