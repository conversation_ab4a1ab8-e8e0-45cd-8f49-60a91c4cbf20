<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
  <springProperty scope="context" name="LOGS" source="block.logs.filePath"/>
  <springProperty scope="context" name="FILE_PATH" source="ctsi.access.filePath"/>
  <springProperty scope="context" name="URL" source="ctsi.access.url"/>
  <springProperty scope="context" name="APP_Name" source="spring.application.name"/>
  <contextName>${APP_Name}</contextName>

  <appender name="sysFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <FileNamePattern>
        ${LOGS}/sys/${APP_Name}.%d{yyyy-MM-dd}.log
      </FileNamePattern>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} | %magenta(%15thread) | %-5level %logger{50} - %msg  %n</pattern>
      <charset class="java.nio.charset.Charset">UTF-8</charset>
    </encoder>
  </appender>

  <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <FileNamePattern>
        ${LOGS}/error/error.%d{yyyy-MM-dd}.log
      </FileNamePattern>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg  %n</pattern>
      <charset class="java.nio.charset.Charset">UTF-8</charset>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>

  <appender name="biyiFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <FileNamePattern>
        ${LOGS}/biyi/biyi.%d{yyyy-MM-dd}.log
      </FileNamePattern>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg  %n</pattern>
      <charset class="java.nio.charset.Charset">UTF-8</charset>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>

  <!-- 发送组件统计信息 -->
  <appender name="STAT_HTTP" class="com.ctsi.ssdc.access.HttpSenderAppender"></appender>
  <Logger name="httpSender" level="INFO">
    <appender-ref ref="STAT_HTTP"/>
  </Logger>
  <logger name="com.ctsi.ssdc.access" level="DEBUG">
    <appender-ref ref="biyiFile"/>
  </logger>


  <logger name="cn.ffcs.staff" level="INFO">
    <appender-ref ref="errorFile" />
  </logger>

  <root level="INFO">
    <appender-ref ref="sysFile"/>
  </root>
</configuration>
