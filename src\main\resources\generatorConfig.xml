<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
  PUBLIC "-//mybatis.org//DTD Ctsi MyBatis Generator Configuration 1.0//EN"
  "http://mybatis.org/dtd/ctsi-mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
  

  <context id="blockChain" targetRuntime="com.ctsi.biyi.generator.plugins.IntrospectedTableBiyiMyBatis3Impl"> 
	
	<property name="databaseDialect" value="mysql"/>
	
	<property name="dataAccess" value="sdjpa"/>
	
	<property name="useES" value="false"/>
	
    <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin" />
	
    <plugin type="com.ctsi.biyi.generator.plugins.SerializableBiyiPlugin" />

    <plugin type="com.ctsi.biyi.generator.plugins.MapperAnnotationBiyiPlugin" />

    <plugin type="com.ctsi.biyi.generator.plugins.ExampleBiyiPlugin" />

    <plugin type="com.ctsi.biyi.generator.plugins.ClassCommentPlugin" />
    
    <plugin type="com.ctsi.biyi.generator.plugins.ServicePlugin" > 
        <property name="importPackage" value="blockChain"/>  
        <property name="targetPackage" value="blockChain"/>  
        <property name="targetProject" value="src/main/java"/>  
        <property name="targetWebProject" value="..\blockChainweb"/>  
    </plugin>
    
    <plugin type="com.ctsi.biyi.generator.plugins.UnitSelectPlugin" > 
        <property name="importPackage" value="blockChain"/>  
        <property name="targetPackage" value="blockChain"/>  
        <property name="targetProject" value="src/main/java"/>  
    </plugin>
    
    <jdbcConnection driverClass="com.mysql.jdbc.Driver"
        connectionURL="********************************************************************************************"
        userId="root"
        password="123456">
    </jdbcConnection> 
    
    
    <javaTypeResolver >
      <property name="forceBigDecimals" value="false" />
    </javaTypeResolver>

    <javaModelGenerator targetPackage="blockChain.domain" targetProject="src/main/java">
      <property name="enableSubPackages" value="true" />
      <property name="trimStrings" value="true" />
    </javaModelGenerator>

    <sqlMapGenerator targetPackage="blockChain.xml"  targetProject="src/main/java">
      <property name="enableSubPackages" value="true" />
    </sqlMapGenerator>

    <javaClientGenerator type="ANNOTATEDMAPPER" targetPackage="blockChain.repository"  targetProject="src/main/java">
      <property name="enableSubPackages" value="true" />
    </javaClientGenerator>

    
    
    <table tableName="test_generator">
      <property name="useActualColumnNames" value="false"/>
      <!--<generatedKey column="id" sqlStatement="mysql" identity="true" />-->
    </table> 
    
    
  </context>
</generatorConfiguration>