package blockChain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 敏感词库(tb_sensitive_word)DTO
 *
 * <AUTHOR>
 * @since 2024-01-02 10:46:49
 */
@Data
public class SensitiveWordDTO {

    private Integer id;

    @ApiModelProperty("敏感词")
    private String sensitiveWord;

    private LocalDateTime createTime;
    //更新时间
    private LocalDateTime updateTime;
}

