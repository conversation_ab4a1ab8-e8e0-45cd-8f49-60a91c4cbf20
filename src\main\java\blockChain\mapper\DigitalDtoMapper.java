package blockChain.mapper;

import blockChain.dto.DigitalDto;
import blockChain.dto.UserDto;
import blockChain.entities.Digital;
import blockChain.entities.UserEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;

import java.util.List;

@Mapper
public interface DigitalDtoMapper {
    DigitalDtoMapper INSTANCE = Mappers.getMapper(DigitalDtoMapper.class);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "sort", source = "sort")
    @Mapping(target = "name", source = "dict_name")
    @Mapping(target = "parentId", source = "pid")
    DigitalDto entityToDto(Digital digital);

    List<DigitalDto> entityToDto(List<Digital> digitals);

    List<DigitalDto> digitalPageToDto(Page<Digital> pageable);
}
