package blockChain.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 功能描述：时间工具类
 */
public class DateUtils {
    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    private static final String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static boolean isDate(String dateStr) {
        try {
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isDateTime(String dateStr) {
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_FORMAT));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean dateIsFuture(String dateStr) {
        try {
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT));
            if (date.isAfter(LocalDate.now())) return false;
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static int dateCompare(String dateStr1, String dateStr2) {
        try {
            LocalDate date = LocalDate.parse(dateStr1, DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT));
            LocalDate date2 = LocalDate.parse(dateStr2, DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT));
            return date.compareTo(date2);
        } catch (Exception e) {
            return 0;
        }
    }
}
