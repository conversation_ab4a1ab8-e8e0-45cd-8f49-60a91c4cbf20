package blockChain.dto.ebus;

import lombok.Data;

/**
 * 用户企业信息
 */
@Data
public class CompanyInfo {
    private String searchValue; // 搜索值
    private String createBy; // 创建人
    private String createTime; // 创建时间
    private String updateBy; // 修改人
    private String updateTime; // 修改时间
    private String remark; // 备注
    private String params; // 下一次认证的认证源参数
    private String id; // 企业id
    private String name; // 企业名称
    private String certificateType; // 证件类型
    private String socialCreditCode; // 证件号码（社会统一信用代码）
    private String address; // 地址
    private String establishmentDate; // 成立日期
    private String contact; // 联系人
    private String maskContact; // 脱敏联系人
    private String contactCertificateType; // 联系人证件类型
    private String contactCertificateNo; // 联系人证件号
    private String phone; // 手机号
    private String maskPhone; // 脱敏手机号
    private String email; // 邮箱
    private String legalRepresentative; // 法定代表人
    private String legalRepresentativeUserId; // 法定代表人账号Id
    private String legalType; // 法人类型
    private String delFlag; // 删除标识
    private String state; // 状态（此字段描述可能需要进一步明确）
    private String maskSocialCreditCode; // 脱敏公司证件号
    private String maskContactCertificateNo; // 脱敏联系人证件号
    private String legalName; // 法人名称（注意：这个字段与legalRepresentative可能重复，需要确认）
    private String legalPhone; // 法人手机号
    private String legalCard; // 法人身份证号码
}
