package blockChain.facade.service;

import blockChain.bean.BaseInfo;
import blockChain.config.SpringConfig;
import blockChain.dto.UserDto;
import blockChain.entities.*;
import blockChain.exception.EntityNotFoundException;
import blockChain.mapper.UserDtoMapper;
import blockChain.service.*;
import blockChain.utils.HttpClientUtils;
import blockChain.utils.HttpUtil;
import blockChain.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import lombok.AllArgsConstructor;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/8/20
 */
@Service
@AllArgsConstructor
public class BackendServiceFacade {
    private static final Logger log = LoggerFactory.getLogger(BackendServiceFacade.class);

    private final SpringConfig config;
    private final UserServiceFacade userServiceFacade;
    private final ApprovalWorkFlowService approvalWorkFlowService;
    private final ProcessProgressService processProgressService;
    private final ProcessRecordService processRecordService;
    private final CopyrightManagerService copyrightManagerService;
    private AuthorService authorService;
    private static final int PAGE_SIZE = 500;

    public String getAccessToken(UserEntity userEntity) {
        String accessToken = null;
        Map<String, Object> param = new HashMap<>();
        param.put("grant_type", "client_credentials");
        param.put("client_id", config.getBackendOauthClientId());
        param.put("client_secret", config.getBackendOauthClientSecret());
        try {
            String url = config.getBackendOauthTokenUrl();
            String result = HttpUtil.sendPostParams(url, param);
            if (result != null) {
                JSONObject object = JSON.parseObject(result);
                if (object.containsKey("error") && object.get("error") != null) {
                    log.info("getAccessToken Error: " + result);
                } else if (object.containsKey("access_token") && object.get("access_token") != null) {
                    accessToken = (String) object.get("access_token");
                    userEntity.setAccessToken(accessToken);

                    if (object.containsKey("expires_in") && object.get("expires_in") != null)
                        userEntity.setAccessTokenExpireTime(LocalDateTime.now().plusSeconds(object.getInteger("expires_in") - 10));
                    userServiceFacade.save(userEntity);
                }
            }
        } catch (Exception e) {
            log.error("getAccessToken Error: " + e.getMessage());
        }
        return accessToken;
    }

    public String getProjectId(UserEntity userEntity, String accessToken) {
        String projectId = null;
//        if (StringUtils.isEmpty(userEntity.getCityName())) {
//            log.error("ProjectIdGet Error: 用户所属区域不存在");
//            return null;
//        }
//        Digital city = dictService.getById(Integer.valueOf(userEntity.getCityName())).orElseThrow(() -> new EntityNotFoundException("用户所属城市未找到"));

        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + accessToken);
            Map<String, String> bodys = new HashMap<>();
            bodys.put("systemName", "fjszpzydj");
            bodys.put("deptCode", config.getDeptCode());
            bodys.put("serviceCode", config.getServiceCode());
            bodys.put("applyFrom", "1");
            bodys.put("areaCode", "350000000000");
            bodys.put("status", "1");

            log.info("ProjectIdGet bodys:{}", bodys);
            HttpResponse httpResponse = HttpClientUtils.doPost(config.getBackendProjectIdGetUrl(), headers, bodys);
            HttpEntity entity = httpResponse.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity, "UTF-8");
                JSONObject object = JSON.parseObject(result);
                if (object.containsKey("flag") && (Boolean) object.get("flag")) {
//                    log.info("ProjectIdGet :" + result);
                    if (object.containsKey("data") && object.get("data") != null)
                        projectId = (String) object.get("data");
                } else {
                    log.error("ProjectIdGet Error :" + result);
                }
            }
        } catch (Exception e) {
            log.error("ProjectIdGet Error: " + e.getMessage());
        }
        return projectId;
    }

    /**
     * 统一收件码激活
     */
    public Boolean projectIdActive(UserEntity userEntity, String projectId) {
        log.info("统一收件码激活开始：{}", projectId);
        String accessToken = userEntity.getAccessToken();
        if (StringUtils.isEmpty(accessToken) || userEntity.getAccessTokenExpireTime().isAfter(LocalDateTime.now()))
            accessToken = getAccessToken(userEntity);

        try {
            // 激活失败再获取一次token
            for (int i = 0; i < 2; i++) {
                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", "Bearer " + accessToken);
                Map<String, String> bodys = new HashMap<>();
                bodys.put("projectId", projectId);
                bodys.put("status", "1");

                HttpResponse httpResponse = HttpClientUtils.doPost(config.getBackendProjectIdActiveUrl(), headers, bodys);
                HttpEntity entity = httpResponse.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity, "UTF-8");
                    JSONObject object = JSON.parseObject(result);
                    if (object.containsKey("flag") && (Boolean) object.get("flag")) {
                        log.info("统一收件码激活成功 :" + result);
                        return true;
                    } else {
                        log.info("统一收件码激活失败 :" + result);
                        accessToken = getAccessToken(userEntity);
                        continue;
                    }
                }
            }
        } catch (Exception e) {
            log.error("统一收件码激活失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 办件信息推送
     */
    public String startWorkflow(CopyrightManager manager, UserEntity userEntity, int type, String cause, Boolean isRetry) {
        String workFlowId = null;
        try {
            String accessToken = userEntity.getAccessToken();
            if (StringUtils.isEmpty(accessToken) || userEntity.getAccessTokenExpireTime().isBefore(LocalDateTime.now()))
                accessToken = getAccessToken(userEntity);

            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + accessToken);
            Map<String, String> bodys = new HashMap<>();

            // get parameters
            JSONObject parameters = new JSONObject();
            switch (type) {
                case 1: // 办件推送
                    bodys.put("id", config.getBaseInfoId()); // 流程启动id（服务总线提供）
                    BaseInfo baseInfo = getBaseInfo(manager, userEntity);
                    parameters.put("baseInfoXml", getBaseInfoXml(baseInfo, isRetry));
//                    parameters.put("attrXml","");
//                    parameters.put("formXml","");
//                    parameters.put("apasPostXml","");
//                    parameters.put("billInfoXml","");
                    break;
                case 2: // 受理
                    bodys.put("id", config.getAcceptId()); // 流程启动id（服务总线提供）
                    parameters.put("acceptInfoXml", getAcceptInfoXml(manager, isRetry));
                    parameters.put("attrXml", "");
                    parameters.put("billInfoXml", "");
                    break;
                case 3: // 办件环节
                    bodys.put("id", config.getNodeTransId()); // 流程启动id（服务总线提供）
                    parameters.put("nodeTransXml", getProcessInfoXml(manager, userEntity, isRetry));
                    parameters.put("attrXml", "");
                    break;
                case 4: // 办结+证照
                    bodys.put("id", config.getTransactId()); // 流程启动id（服务总线提供）
                    parameters.put("transactXml", getEndInfoXml(manager, userEntity, cause, isRetry));
                    if (!cause.equals("办结")) {
                        parameters.put("zzXml", "");
                    } else {
                        parameters.put("zzXml", getCertificateInfoXml(manager));
                    }

                    parameters.put("attrXml", "");
                    parameters.put("billInfoXml", "");
                    break;
                case 10: // 补齐补正告知
                    bodys.put("id", config.getPatchId()); // 流程启动id（服务总线提供）
                    parameters.put("patchXml", getCorrectInfoXml(manager, userEntity, cause, isRetry));
                    parameters.put("patchAttrXml", ""); // todo
                    parameters.put("billInfoXml", "");
                    break;
                case 9: // 补齐补正结束
                    bodys.put("id", config.getPatchEndId()); // 流程启动id（服务总线提供）
                    parameters.put("patchEndXml", getCorrectEndInfoXml(manager, userEntity, isRetry));
                    parameters.put("attrXml", ""); // todo
                    parameters.put("systemNo", config.getSystemNo());
                    break;
                default:
                    return "";
            }
            parameters.put("projid", manager.getSnCode());

            bodys.put("parameters", parameters.toJSONString());

            log.info("【信息推送】 bodys:{}", bodys);
            HttpResponse httpResponse = HttpClientUtils.doPost(config.getBackendStartWorkflow(), headers, bodys);
            HttpEntity entity = httpResponse.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity, "UTF-8");
                JSONObject object = JSON.parseObject(result);
                log.info("回调返回值object:{}",object);
                if (object.containsKey("code") && object.get("code") != null && (Integer) object.get("code") == 1) {
                    if (object.containsKey("data") && object.get("data") != null)
                        workFlowId = (String) object.get("data");
                        // 保存流程信息
                    ProcessProgressEntity progress = new ProcessProgressEntity();
                    progress.setType(type);
                    progress.setState(ProcessProgressEntity.State.UNFINISHED);
                    progress.setProcessId(workFlowId);
                    progress.setSnCode(manager.getSnCode());
                    progress.setProjectId(manager.getProjectId());
                    progress.setRegistrationNum(manager.getRegistrationNum());
                    if(type == ProcessProgressEntity.Type.COMPLETE || type == ProcessProgressEntity.Type.PROCESS) {
                        progress.setUuid(UUID.randomUUID().toString());
                    }else if(type == ProcessProgressEntity.Type.END) {
                        progress.setUuid(manager.getUuid());
                    }
                    processProgressService.saveData(progress);
                } else {
                    log.info("【startWorkflow】 Error :" + result);
                }
            }
        } catch (Exception e) {
            log.error("【startWorkflow】 Error: " + e.getMessage());
//            StackTraceElement[] stackTraceElements = e.getStackTrace();
//            for (StackTraceElement stackTrace : stackTraceElements) {
//                log.error(stackTrace.toString());
//            }
        }

        return workFlowId;
    }

    /**
     * 申报基本信息 TODO
     * 首次提交/撤销申请
     */
    private BaseInfo getBaseInfo(CopyrightManager manager, UserEntity userEntity) {
        BaseInfo baseInfo = new BaseInfo();
//        baseInfo.setIssue("");

        baseInfo.setRelativeBusiId(manager.getProjectId());

        baseInfo.setProjId(manager.getSnCode());

        String randomPwd = String.valueOf((int) ((Math.random() * 900000) + 100000));
        baseInfo.setProjpwd(randomPwd);
        baseInfo.setProjectName(manager.getProductionName());
        baseInfo.setApplyName(manager.getOwnerList().get(0).getCopyName()); // 申报者 第一著作权人
        baseInfo.setApplyCardType(baseInfo.copyCertificateToCardType(manager.getOwnerList().get(0).getCopyCertificate()));   // 申报者证件类型
        baseInfo.setApplyCardNumber(manager.getOwnerList().get(0).getCopyIdCard());
        if (manager.getApplyType() == CopyrightManager.ApplyType.OWN) {
            baseInfo.setContactman(baseInfo.getApplyName());
            baseInfo.setContactmanCardType(baseInfo.getApplyCardType());
            baseInfo.setContactmanCardNumber(baseInfo.getApplyCardNumber());
        } else {
            baseInfo.setContactman(manager.getAgentList().getAgentName());
            UserDto userDto;
            try {
                userDto = userServiceFacade.getByUserName(manager.getUserName());
                if (!Objects.equals(userDto.getRealName(), manager.getAgentList().getAgentName())) {
                    List<UserEntity> userEntities = userServiceFacade.findByRealName(manager.getAgentList().getAgentName());
                    if (ObjectUtils.isEmpty(userDto)) {
                        throw new RuntimeException("代理人信息找不到");
                    }
                    if (userEntities.size() > 1) {
                        Optional<UserEntity> optionalUser = userEntities.stream().max(Comparator.comparing(UserEntity::getRegisterTime));
                        if (optionalUser.isPresent()) {
                            userDto = UserDtoMapper.INSTANCE.entityToDto(optionalUser.get());
                        }
                    } else {
                        userDto = UserDtoMapper.INSTANCE.entityToDto(userEntities.get(0));
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException("代理人信息找不到");
            }
            if (userDto.getIdentityKind() == 1) {
                baseInfo.setContactmanCardType(baseInfo.copyCertificateToCardType(userDto.getCardType()));
            } else {
                baseInfo.setContactmanCardType(baseInfo.legalPersionTypeToCardType(userDto.getLegalPersionType()));
            }
            baseInfo.setContactmanCardNumber(userDto.getCardId());
        }
        baseInfo.setTelphone(manager.getSubmitter().getCopyrightPhone());
        baseInfo.setAddress(manager.getOwnerList().get(0).getCopyAreaNamesStr().replace(",", ""));
        baseInfo.setSsOrgCode(config.getDeptCode());
        baseInfo.setReceiveUserId(manager.getUserName());
        baseInfo.setReceiveName(userEntity.getRealName());
        LocalDateTime now = manager.getRegistrationDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formatted = now.format(formatter);
        baseInfo.setReceiveTime(formatted);
        baseInfo.setCreateTime(formatted);

        baseInfo.setSyncStatus(manager.getSyncStatus());
        return baseInfo;
    }

    private String getBaseInfoXml(BaseInfo baseInfo, Boolean isRetry) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formatted = now.format(formatter);
        String xml = "<?xml version=\"1.0\" encoding=\"gb2312\"?><RECORD><CALLINFO>";
        xml += "      <CALLER>福建省作品自愿登记系统</CALLER>";
        try {
            xml += "      <CALLTIME>" + formatted + "</CALLTIME>";
            xml += "      <CALLBACK_URL>" + config.getBackendCallbackUrl() + "</CALLBACK_URL>";
//        xml += "      <ISSUE>" + baseInfo.getIssue() + "</ISSUE>";
            xml += "  </CALLINFO>";
            xml += "  <RELATIVE_BUSI_ID>" + baseInfo.getRelativeBusiId() + "</RELATIVE_BUSI_ID>";
            xml += "  <PROJID>" + baseInfo.getProjId() + "</PROJID>";
            xml += "  <PROJPWD>" + baseInfo.getProjpwd() + "</PROJPWD>";
            xml += "  <IS_MANUBRIUM>" + baseInfo.getIsManubrium() + "</IS_MANUBRIUM>";
            xml += "  <BASE_CODE>" + config.getBaseCode() + "</BASE_CODE>";
            xml += "  <IMPLEMENT_CODE>" + config.getImplementCode() + "</IMPLEMENT_CODE>";
            xml += "  <TASKHANDLEITEM>" + config.getTaskhandleitem() + "</TASKHANDLEITEM>";
            xml += "  <SERVICECODE>" + config.getServiceCode() + "</SERVICECODE>";
            xml += "  <SERVICE_DEPTID>" + config.getDeptCode() + "</SERVICE_DEPTID>";
//        xml += "  <BUS_MODE/>";
//        xml += "  <BUS_MODE_DESC/>";
//        xml += "  <SERVICEVERSION></SERVICEVERSION>";
            xml += "  <SERVICENAME>" + baseInfo.getServiceName() + "</SERVICENAME>";
            xml += "  <PROJECTNAME>" + baseInfo.getProjectName() + "</PROJECTNAME>";
            xml += "  <INFOTYPE>" + baseInfo.getInfoType() + "</INFOTYPE>";
            xml += "  <APPLY_TYPE>" + baseInfo.getApplyType() + "</APPLY_TYPE>";
            xml += "  <BUS_TYPE>" + baseInfo.getBusType() + "</BUS_TYPE>";
            xml += "  <REL_BUS_ID/>";
            xml += "  <APPLYNAME>" + baseInfo.getApplyName() + "</APPLYNAME>";
            xml += "  <APPLY_CARDTYPE>" + baseInfo.getApplyCardType() + "</APPLY_CARDTYPE>";
            xml += "  <APPLY_CARDNUMBER>" + baseInfo.getApplyCardNumber() + "</APPLY_CARDNUMBER>";
            xml += "  <CONTACTMAN>" + baseInfo.getContactman() + "</CONTACTMAN>";
            xml += "  <CONTACTMAN_CARDTYPE>" + baseInfo.getContactmanCardType() + "</CONTACTMAN_CARDTYPE>";
            xml += "  <CONTACTMAN_CARDNUMBER>" + baseInfo.getContactmanCardNumber() + "</CONTACTMAN_CARDNUMBER>";
            xml += "  <TELPHONE>" + baseInfo.getTelphone() + "</TELPHONE>";
//        xml += "  <MOBILE>" + baseInfo.getCaller() + "</MOBILE>";
//        xml += "  <POSTCODE>" + baseInfo.getCaller() + "</POSTCODE>";
            xml += "  <ADDRESS>" + baseInfo.getAddress() + "</ADDRESS>";
//        xml += "  <LEGALMAN>" + baseInfo.getCaller() + "</LEGALMAN>";
            xml += "  <DEPTID>" + config.getDeptCode() + "</DEPTID>";
            xml += "  <DEPTNAME>" + baseInfo.getDeptName() + "</DEPTNAME>";
            xml += "  <APPLYFROM>" + baseInfo.getApplyFrom() + "</APPLYFROM>";
            xml += "  <APPROVE_TYPE>" + baseInfo.getApproveType() + "</APPROVE_TYPE>";
            xml += "  <APPLY_PROPERTIY>" + baseInfo.getApplyPropertiy() + "</APPLY_PROPERTIY>";
            xml += "  <RECEIVETIME>" + baseInfo.getReceiveTime() + "</RECEIVETIME>";
//        xml += "  <BELONGTO>" + baseInfo.getCaller() + "</BELONGTO>";
            xml += "  <AREACODE>" + baseInfo.getAreaCode() + "</AREACODE>";
            xml += "  <DATASTATE>" + baseInfo.getDataState() + "</DATASTATE>";
            if (!isRetry) {
                xml += "  <BELONGSYSTEM>" + config.getBelongSystem() + "</BELONGSYSTEM>";
            } else {
                xml += "  <BELONGSYSTEM>" + config.getBelongSystemRetry() + "</BELONGSYSTEM>";
            }
            xml += "  <EXTEND/>";
            xml += "  <DATAVERSION>" + baseInfo.getDataVersion() + "</DATAVERSION>";
            xml += "  <SYNC_STATUS>" + baseInfo.getSyncStatus() + "</SYNC_STATUS>";
            xml += "  <RECEIVE_USEID>" + baseInfo.getReceiveUserId() + "</RECEIVE_USEID>";
            xml += "  <RECEIVE_NAME>" + baseInfo.getReceiveName() + "</RECEIVE_NAME>";
            xml += "  <CREATE_TIME>" + baseInfo.getCreateTime() + "</CREATE_TIME>";
            xml += "  <SS_ORGCODE>" + baseInfo.getSsOrgCode() + "</SS_ORGCODE>";
        } catch (Exception e) {
            log.info("【startWorkflow】 error:" + e.getMessage());
            e.printStackTrace();
        }
        xml += "  <MEMO/>";
//        xml += "  <SITE_UNID>" + baseInfo.getCaller() + "</SITE_UNID>";
//        xml += "  <PHYSICAL_UNID>" + baseInfo.getCaller() + "</PHYSICAL_UNID>";
//        xml += "  <TAGS>" + baseInfo.getCaller() + "</TAGS>";
//        xml += "  <APPLICABLE_AREA_CODE>" + baseInfo.getCaller() + "</APPLICABLE_AREA_CODE>";
        xml += "</RECORD>";
        return xml;
    }

    /**
     * 受理信息（受理）
     * 首次提交/撤销申请
     */
    public String getAcceptInfoXml(CopyrightManager manager, Boolean isRetry) {
        LocalDateTime promiseEtime = addWorkdays(manager.getRegistrationDate(), 15);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String promiseEtimeStr = promiseEtime.format(formatter);


        LocalDateTime registrationDate = manager.getRegistrationDate();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String createTime = registrationDate.format(dateTimeFormatter);

        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter currentFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime;
        if (!isRetry) {
            currentTime = now.format(currentFormatter);
        } else {
            // 如果重试，受理时间 取最后一次提交时间后的第一个审核通过时间，若为终审或证书生成取审核时间和提交时间中间值
            ProcessRecord processRecord = processRecordService.getByCopyrightId(manager.getRegistrationNum()).stream()
                    .filter(m -> m.getTime().isAfter(registrationDate))
                    .min(Comparator.comparing(ProcessRecord::getTime))
                    .orElseThrow(() -> new EntityNotFoundException("作品" + manager.getRegistrationNum() + "未做受理"));
            if (processRecord.getOpResult().equals("终审通过") || processRecord.getOpResult().equals("证书生成成功")) {
                // 取受理时间和申请时间的中间值
                Duration duration = Duration.between(registrationDate, processRecord.getTime());
                long halfDurationSeconds = duration.toMillis() / 2 / 1000; // 转换为秒;

                // 获取中间时间点
                currentTime = registrationDate.plusSeconds(halfDurationSeconds).format(currentFormatter);
            } else {
                currentTime = processRecord.getTime().format(currentFormatter);
            }
        }
        String xml = "<?xml version=\"1.0\" encoding=\"gb2312\"?><RECORD>";
        xml += "  <PROJID>" + manager.getSnCode() + "</PROJID>";
        xml += "  <TYSJM>" + manager.getProjectId() + "</TYSJM>";
        xml += "  <ACCEPT_MAN>版权处</ACCEPT_MAN>";
        xml += "  <HANDER_DEPTNAME>版权管理处</HANDER_DEPTNAME>";
        xml += "  <HANDER_DEPTID>" + config.getDeptCode() + "</HANDER_DEPTID>";
        xml += "  <AREACODE>350000</AREACODE>";
        xml += "  <ACCEPT_TIME>" + currentTime + "</ACCEPT_TIME>";
        xml += "  <PROMISEVALUE>15</PROMISEVALUE>";
        xml += "  <PROMISETYPE>工作日</PROMISETYPE>";
        xml += "  <PROMISE_ETIME>" + promiseEtimeStr + "</PROMISE_ETIME>";
        if (!isRetry) {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystem() + "</BELONGSYSTEM>";
        } else {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystemRetry() + "</BELONGSYSTEM>";
        }
        xml += "  <DATAVERSION>1</DATAVERSION>";
        String syncStatus = manager.getSyncStatus();
        if (ObjectUtils.isEmpty(syncStatus)) syncStatus = "I";
        xml += "  <SYNC_STATUS>" + syncStatus + "</SYNC_STATUS>";
        xml += "  <CREATE_TIME>" + createTime + "</CREATE_TIME>";
//        xml += "  <ACCEPT_USER_ID>受理人id</ACCEPT_USER_ID>";
        xml += "</RECORD>";
        return xml;
    }

    /***
     * 补齐补正告知信息
     */
    public String getCorrectInfoXml(CopyrightManager manager, UserEntity user, String cause, Boolean isRetry) {
        LocalDateTime registrationDate = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String patchDate = registrationDate.format(dateTimeFormatter);

        LocalDateTime date = manager.getRegistrationDate();
        DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String createTime = date.format(dd);

        String xml = "<?xml version=\"1.0\" encoding=\"gb2312\"?><RECORD>";
        xml += "<UNID>" + UUID.randomUUID() + "</UNID>";
        xml += "<TYSJM>" + manager.getProjectId() + "</TYSJM>";
        xml += "<PROJID>" + manager.getSnCode() + "</PROJID>";
        xml += "<PATCH_PHASE>" + ProcessRecord.APPROVAL_NAME.get(manager.getStatus_type()) + "</<PATCH_PHASE>";
        xml += "<PATCH_DATE>" + patchDate + "</PATCH_DATE>";
        xml += "<PATCH_REASON>" + cause + "</PATCH_REASON>";
        xml += "<PATCH_TIME_LIMIT>" + 30 + "</PATCH_TIME_LIMIT>";
        xml += "<PATCH_TIME_LIMIT_UNIT>" + "天" + "</PATCH_TIME_LIMIT_UNIT>";
        xml += "<HANDLE_USERUNID>" + user.getUserId() + "</HANDLE_USERUNID>";
        xml += "<HANDLE_USERNAME>" + user.getUserName() + "</HANDLE_USERNAME>";
        xml += "<HANDER_DEPTNAME>" + "版权登记处" + "</HANDER_DEPTNAME>";
        xml += "<HANDER_DEPTID>" + config.getDeptCode() + "</HANDER_DEPTID>";
        xml += "<AREACODE>" + 350000 + "<AREACODE>";
        xml += "<MEMO>" + "材料修改" + "</MEMO>";
        if (!isRetry) {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystem() + "</BELONGSYSTEM>";
        } else {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystemRetry() + "</BELONGSYSTEM>";
        }
        xml += "<SYNC_STATUS>" + "I" + "</SYNC_STATUS>"; // TODO
        xml += "<CREATE_TIME>" + createTime + "</CREATE_TIME>";
        xml += "<EXTEND>材料修改</EXTEND>";
        xml += "<DATAVERSION>1</DATAVERSION>";
        xml += "</RECORD>";
        return xml;
    }


    /**
     * 补齐补正结束信息
     */
    public String getCorrectEndInfoXml(CopyrightManager manager, UserEntity user, Boolean isRetry) {
        LocalDateTime registrationDate = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String callTime = registrationDate.format(dateTimeFormatter);


        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String endTime = now.format(dd);

        LocalDateTime localDateTime = manager.getRegistrationDate();
        DateTimeFormatter ccc = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String createTime = localDateTime.format(ccc);


        String xml = "<?xml version=\"1.0\" encoding=\"gb2312\"?><RECORD><CALLINFO>";
        xml += "<CALLER>福建省作品自愿登记系统</CALLER>";
        xml += "<CALLTIME>" + callTime + "</CALLTIME>";
        xml += "<CALLBACK_URL>" + config.getBackendCallbackUrl() + "</CALLBACK_URL>";
        xml += "</CALLINFO>";
        xml += "<UNID>" + UUID.randomUUID() + "</UNID>";
        xml += "<PROJID>" + manager.getSnCode() + "</PROJID>";
        xml += "<TYSJM>" + manager.getProjectId() + "</TYSJM>";
        xml += "<PATCH_UNID>" + manager.getUuid() + "</PATCH_UNID>";
        xml += "<END_DATE>" + endTime + "</END_DATE>";
        xml += "<PATCH_RESULT>" + "补齐补正结束" + "</PATCH_RESULT>";
        xml += "<PATCH_OPINION>" + "补齐补正结束" + "</PATCH_OPINION>";
        xml += "<HANDLE_USERUNID>" + user.getUserId() + "</HANDLE_USERUNID>";
        xml += "<HANDLE_USERNAME>" + user.getUserName() + "</HANDLE_USERNAME>";
        xml += "<HANDER_DEPTNAME>" + "版权登记处" + "</HANDER_DEPTNAME>";
        xml += "<HANDER_DEPTID>" + config.getDeptCode() + "</HANDER_DEPTID>";
        xml += "<AREACODE>" + 350000 + "<AREACODE>";
        xml += "<MEMO>备注</MEMO>";
        if (!isRetry) {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystem() + "</BELONGSYSTEM>";
        } else {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystemRetry() + "</BELONGSYSTEM>";
        }
        xml += "<SYNC_STATUS>I</SYNC_STATUS>"; // TODO
        xml += "<CREATE_TIME>" + createTime + "</CREATE_TIME>";
        xml += "<EXTEND>补齐补正结束</EXTEND>";
        xml += "<DATAVERSION>1</DATAVERSION>";
        xml += "</RECORD>";
        return xml;
    }

    /**
     * 环节办理
     */
    public String getProcessInfoXml(CopyrightManager manager, UserEntity user, Boolean isRetry) {
        DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        String createTime = now.format(dd);
        LocalDateTime startDateTime = now.minusMinutes(10);
        String startTime = startDateTime.format(dd);
        String endTime = createTime;

        if (isRetry) {
            ProcessRecord processRecord = processRecordService.getByCopyrightId(manager.getRegistrationNum()).stream().filter(m -> Objects.equals(m.getOpResult(), "终审通过"))
                    .max(Comparator.comparing(ProcessRecord::getTime)).orElseThrow(() -> new EntityNotFoundException("作品" + manager.getRegistrationNum() + "未做终审受理"));
            startTime = processRecord.getTime().minusMinutes(10).format(dd);
            endTime = processRecord.getTime().format(dd);
        }

        String xml = "<?xml version=\"1.0\" encoding=\"gb2312\"?><RECORDS><RECORD>";
        xml += "<UNID>" + UUID.randomUUID() + "</UNID>";
        xml += "<TYSJM>" + manager.getProjectId() + "</TYSJM>";
        xml += "<PROJID>" + manager.getSnCode() + "</PROJID>";
        xml += "<ACTION>" + "通过" + "</ACTION>";
        xml += "<NODE_NAME>" + "终审" + "</NODE_NAME>";
        xml += "<NAME>" + user.getUserName() + "</NAME>";
        xml += "<HANDER_DEPTNAME>" + "版权登记处" + "</HANDER_DEPTNAME>";
        xml += "<HANDER_DEPTID>" + config.getDeptCode() + "</HANDER_DEPTID>";
        xml += "<AREACODE>" + 350000 + "</AREACODE>";
        xml += "<OPINION>" + "通过" + "</OPINION>";
        xml += "<START_TIME>" + startTime + "</START_TIME>";
        xml += "<END_TIME>" + endTime + "</END_TIME>";
//        xml += "<REMARK>备注</REMARK>";
        xml += "<CREATE_TIME>" + createTime + "</CREATE_TIME>";
        if (!isRetry) {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystem() + "</BELONGSYSTEM>";
        } else {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystemRetry() + "</BELONGSYSTEM>";
        }
        xml += "<NEXT_NODE_NAME>" + "证书生成" + "</NEXT_NODE_NAME>";
        xml += "<PHASE_NAME>" + "审批阶段" + "</PHASE_NAME>";
        xml += "<PHASE_CODE>03</PHASE_CODE>";
//        xml += "<EXTEND>备用字段</EXTEND>";
        xml += "<DATAVERSION>" + 1 + "</DATAVERSION>";
        String syncStatus = manager.getSyncStatus();
        if (ObjectUtils.isEmpty(syncStatus)) syncStatus = "I";
        xml += "<SYNC_STATUS>" + syncStatus + "</SYNC_STATUS>";
//        xml += "<USER_ID>审批人id</USER_ID>";
        xml += "</RECORD></RECORDS>";
        return xml;
    }

    /**
     * 办结信息定义
     */
    public String getEndInfoXml(CopyrightManager manager, UserEntity user, String cause, Boolean isRetry) {


        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter ddd = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String createTime = now.format(ddd);

        String xml = "<?xml version=\"1.0\" encoding=\"gb2312\"?><RECORD>";
        xml += "<TYSJM>" + manager.getProjectId() + "</TYSJM>";
        xml += "<PROJID>" + manager.getSnCode() + "</PROJID>";
        xml += "<TRANSACT_USER>" + user.getUserName() + "</TRANSACT_USER>";
        xml += "<HANDER_DEPTNAME>" + "版权登记处" + "</HANDER_DEPTNAME>";
        xml += "<HANDER_DEPTID>" + config.getDeptCode() + "</HANDER_DEPTID>";
        xml += "<AREACODE>" + 350000 + "</AREACODE>";
        if (cause.equals("办结") && manager.getInactiveType().equals(CopyrightManager.InactiveTypeValue.NORMAL)) {
            // LocalDateTime licenseTime = manager.getLicenseTime();
            LocalDateTime certificateCreateTime = manager.getCertificateCreateTime();
            DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String transactTime = certificateCreateTime.format(dd);
            xml += "<TRANSACT_TIME>" + transactTime + "</TRANSACT_TIME>";
            xml += "<TRANSACT_RESULT>" + cause + "</TRANSACT_RESULT>";
            xml += "<TRANSACT_DESCRIBE>" + "准予许可" + "</TRANSACT_DESCRIBE>";
        } else {
            xml += "<TRANSACT_TIME>" + createTime + "</TRANSACT_TIME>";
            xml += "<TRANSACT_RESULT>" + cause + "</TRANSACT_RESULT>";
            xml += "<TRANSACT_DESCRIBE>" + "不予许可" + "</TRANSACT_DESCRIBE>";
        }

//        xml += "<RESULT_CODE>结果编号</RESULT_CODE>";
        if (!isRetry) {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystem() + "</BELONGSYSTEM>";
        } else {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystemRetry() + "</BELONGSYSTEM>";
        }
        String syncStatus = manager.getSyncStatus();
        if (ObjectUtils.isEmpty(syncStatus)) syncStatus = "I";
        xml += "<SYNC_STATUS>" + syncStatus + "</SYNC_STATUS>";
        xml += "<CREATE_TIME>" + createTime + "</CREATE_TIME>";
        xml += "<DATAVERSION>" + 1 + "</DATAVERSION>";
//        xml += "<OPENWAY>公开方式</OPENWAY>";
//        xml += " <IS_PRODUCED_LICENSE>是否生成证照</IS_PRODUCED_LICENSE>";
//        xml += "<NO_PRODUCED_REASON>未生成电子证照原因</NO_PRODUCED_REASON>";
//        xml += "<EXTEND>扩展信息</EXTEND>";
        xml += "</RECORD>";
        return xml;
    }


    /**
     * 证照信息定义
     */
    public String getCertificateInfoXml(CopyrightManager manager) {
        LocalDateTime certificateCreateTime = manager.getCertificateCreateTime();
        DateTimeFormatter dd = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String certificateCreateTimes = certificateCreateTime.format(dd);
        List<Author> author = authorService.getAuthorByCopyrightId(manager.getRegistrationNum());

        String xml = "<?xml version=\"1.0\" encoding=\"gb2312\"?>";
        xml += "<RECORDS><RECORD>";
        xml += "<Info>";
        xml += "<zzmc>"+ "作品登记证书" +"</czz>";
        xml += "<zzbh>"+ manager.getWorksNum() +"</zzbh>";
        xml += "<zzid>"+ manager.getCertificateIdentifier() +"</zzid>";
        xml += "<czz>"+ manager.getOwnerList().get(0).getCopyName() +"</czz>";
        xml += "<czzbm>"+ manager.getOwnerList().get(0).getCopyIdCard() +"</czzbm>";
        xml += "</Info>";
        xml += "<FormInfo name=\"作品登记证书\">";
        xml += "<Item name=\"worksnum\" name_cn=\"登记号\">"+ manager.getWorksNum() +"</Item>";
        xml += "<Item name=\"productionname\" name_cn=\"作品（制品名称）\">"+ manager.getProductionName() +"</Item>";
        xml += "<Item name=\"productiontypeid\" name_cn=\"作品类别\">" + manager.getProductionTypeId() + "</Item>";
        xml += "<Item name=\"author_name\" name_cn=\"作者\">" + author.get(0).getAuthorName() + "</Item>";
        xml += "<Item name=\"copy_name\" name_cn=\"著作权人\">" + manager.getOwnerList().get(0).getCopyName() + "</Item>";
        xml += "<Item name=\"finishtime\" name_cn=\"创作完成日期\">"+ manager.getFinishTime() +"</Item>";
        xml += "<Item name=\"firstpublishtime\" name_cn=\"首次出版（制作日期）\">"+ manager.getFirstPublishTime() +"</Item>";
        xml += "<Item name=\"certificate_create_time\" name_cn=\"登记日期\"> " + certificateCreateTimes + "</Item>";
//        xml += "<AutoTable name=\"autotable_01\">";
//        xml += "<AutoRow>";
//        xml += "<Item name=\"username\" name_cn=\"用户名\">值</Item>";
//        xml += "<Item name=\"字段名2\" name_cn=\"字段中文名2\">值</Item>";
//        xml += "<Item name=\"字段名3\" name_cn=\"字段中文名3\">值</Item>";
//        xml += "</AutoRow>";
//        xml += "</AutoTable>";
        xml += "</AutoDiv>";
        xml += "</FormInfo></RECORD></RECORDS>";
        return xml;
    }


    public String getAttrXml(CopyrightManager manager, Boolean isRetry) {
        String xml = "<?xml version=\"1.0\" encoding=\"gb2312\"?>";
        xml += "<RECORDS><RECORD>";
        xml += "<UNID>" + UUID.randomUUID() + "</UNID>";
        xml += "<PROJID>" + manager.getSnCode() + "</PROJID>";
        xml += "<ATTRNAME>材料名称</ATTRNAME>";
        xml += "<ATTRID>材料标识</ATTRID>";
//        xml += "<SORTID>材料序号</SORTID>";
        xml += "<TAKETYPE>收取方式</TAKETYPE>";
        xml += "<ISTAKE>是否收取</ISTAKE>";
//        xml += "<AMOUNT>收取数量</AMOUNT>";
        xml += "<TAKETIME>收取时间</TAKETIME>";
//        xml += "<MEMO>备注</MEMO>";
        if (!isRetry) {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystem() + "</BELONGSYSTEM>";
        } else {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystemRetry() + "</BELONGSYSTEM>";
        }
        xml += "<AREACODE>" + 35000 + "</AREACODE>";
//        xml += "<EXTEND>备用字段</EXTEND>";
        xml += "<DATAVERSION>" + 1 + "</DATAVERSION>";
        xml += "<SYNC_STATUS>" + "I" + "</SYNC_STATUS>"; // TODO
        xml += "<CREATE_TIME>数据产生时间</CREATE_TIME>";
        xml += "<ATTRID>材料标识</ATTRID>";
//        xml += "<SERVICE_ID>事项（目录）标识</SERVICE_ID>";
        xml += "<IS_NEED>材料必要性</IS_NEED>";
//        xml += "< DIRECTORY_ID>材料目录ID </ DIRECTORY_ID >";
        xml += "<FILES><FILEINFO>";
//        xml += "<FILENAME>附件名称</FILENAME>";
        xml += "<FILEUNID>附件ID</FILEUNID>";
//        xml += "<FILEURL>附件存储路径</FILEURL>";
//        xml += "<FILEPWD>提取密码</FILEPWD>";
//        xml += "<REF_CODE>事项材料编码</REF_CODE>";
//        xml += "<LICENSE_APP_ID>电子证照应用标识</LICENSE_APP_ID>";
//        xml += "<LICENSE_SHARE_ID>电子证照共享标识</LICENSE_SHARE_ID>";
//        xml += "<CERTIFICATE_TYPE_CODE>证照类型代码</CERTIFICATE_TYPE_CODE>";
//        xml += "<CERTIFICATE_HOLDER_CODE>持证者证件代码</CERTIFICATE_HOLDER_CODE>";
//        xml += "<LICENSE_CORRECTION_RECEIPT>电子证照纠错回执单号</LICENSE_CORRECTION_RECEIPT>";
//        xml += "<LICENSE_NOT_USE_COND>配置电子证照而未使用原因</LICENSE_NOT_USE_COND>";
//        xml += "<LICENSE_NOT_USE_DETAIL>配置电子证照而未使用原因是其他时的详细理由</LICENSE_NOT_USE_DETAIL>";
        xml += "</FILEINFO></FILES>";
        xml += "</RECORD></RECORDS>";
        return xml;
    }

    public String getPatchAttrXml(CopyrightManager manager, Boolean isRetry) {
        String xml = "<?xml version=\"1.0\" encoding=\"gb2312\"?>";
        xml += "<RECORDS><RECORD>";
        xml += "<UNID>" + UUID.randomUUID() + "</UNID>";
        xml += "<TYSJM>" + manager.getProjectId() + "</TYSJM>";
        xml += "<PROJID>" + manager.getSnCode() + "</PROJID>";
        xml += "<PATCH_UNID>补件告知信息的唯一标识</PATCH_UNID>";
        xml += "<ATTR_NAME>材料名称</ATTR_NAME>";
        xml += "<ATTR_UNID>材料标识</ATTR_UNID>";
        xml += "<CREATE_TIME>数据产生时间</CREATE_TIME>";
//        xml += "<MEMO>备注</MEMO>";
        if (!isRetry) {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystem() + "</BELONGSYSTEM>";
        } else {
            xml += "  <BELONGSYSTEM>" + config.getBelongSystemRetry() + "</BELONGSYSTEM>";
        }
        xml += "<AREACODE>" + 35000 + "</AREACODE>";
//        xml += "<EXTEND>备用字段</EXTEND>";
        xml += "<DATAVERSION>" + 1 + "</DATAVERSION>";
        xml += "<SYNC_STATUS>" + "I" + "</SYNC_STATUS>"; //TODO
        xml += "</RECORD></RECORDS>";
        return xml;
    }

    @Transactional
    public void saveApprovalWorkFlow(Integer state, String approverResult, UserEntity loginUser, CopyrightManager copyrightManager) {
        ApprovalWorkFlowEntity approvalWorkFlowEntity = new ApprovalWorkFlowEntity();
        approvalWorkFlowEntity.setCreator(loginUser);
        approvalWorkFlowEntity.setState(state);
        approvalWorkFlowEntity.setApprovalResult(approverResult);
        approvalWorkFlowEntity.setManager(copyrightManager);
        approvalWorkFlowEntity.setResolveState(ApprovalWorkFlowEntity.ResolveState.TODO);
        approvalWorkFlowService.save(approvalWorkFlowEntity);
    }

    @Async("doSomethingExecutor")
    public void approvalWorkFlowScheduling() {
        while (true) {
            try {
                List<ApprovalWorkFlowEntity> entities = approvalWorkFlowService.findAllResolve(ApprovalWorkFlowEntity.ResolveState.TODO, PAGE_SIZE);
                if (entities.size() > 0) {
                    LocalDateTime beginTime = LocalDateTime.now();
                    log.info("采一次审批待推送开始,{}条, {}", entities.size(), beginTime);
                    for (ApprovalWorkFlowEntity approvalWorkFlowEntity : entities) {
                        approvalWorkFlow(approvalWorkFlowEntity.getState(), approvalWorkFlowEntity.getApprovalResult(), approvalWorkFlowEntity.getCreator(), approvalWorkFlowEntity.getManager(), false);
                        approvalWorkFlowEntity.setResolveState(ApprovalWorkFlowEntity.ResolveState.END);
                    }
                    approvalWorkFlowService.saveAll(entities);
                    LocalDateTime endTime = LocalDateTime.now();
                    log.info("采一次审批待推送结束,{}条, 耗时", entities.size(), Duration.between(beginTime, endTime).getSeconds());
                } else {
                    Thread.sleep(60000);
                }
            } catch (Exception e) {
                log.error("采一次审批待推送失败: " + e);
            }
        }
    }

    public void approvalWorkFlow(Integer state, String approverResult, UserEntity loginUser, CopyrightManager copyrightManager, Boolean isRetry) {
        log.info("流程同步开始，版权作品:{},当前状态:{}, 审批结果{}", copyrightManager.getRegistrationNum(), state, approverResult);

        if (approverResult.equals("pass")) {
            try {
                /*log.info("作品审批开始(补齐补正结束),copyrightManager:{}",copyrightManager);
                if(copyrightManager.getRestatus() == CopyrightManager.MODIFIED){
                    //  判断是否是补正通知 且状态为为结束 （多条查看最后一条）
                    List<ProcessProgressEntity> progressEntities = processProgressService.findByRegistrationNumAndTypeAndState(copyrightManager.getRegistrationNum(), ProcessProgressEntity.Type.COMPLETE,ProcessProgressEntity.State.UNFINISHED);
                    Optional<ProcessProgressEntity> progressEntity = progressEntities.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime));
                    progressEntity.ifPresent(entity -> {
                        copyrightManager.setUuid(entity.getUuid());
                        backendServiceFacade.startWorkflow(copyrightManager,loginUser,ProcessProgressEntity.Type.END,null);
                    });
                }
                log.info("作品审批结束(补齐补正结束)");*/

                // 判断办件的上一个流程
                List<ProcessProgressEntity> entities = processProgressService.findBySnCodeAndTypeAndState(copyrightManager.getSnCode(), ProcessProgressEntity.Type.ACCEPT, ProcessProgressEntity.State.FINISHED);
                Optional<ProcessProgressEntity> entitiesOption = entities.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime));
                if (entitiesOption.isPresent()) {

                    /*// 如果是材料修改重新推送(1,0)，做受理
                    List<ProcessProgressEntity> pushEntities = processProgressService.findBySnCode(copyrightManager.getSnCode());
                    log.info("作品复审终审集合长度:{}", pushEntities.size());
                    Optional<ProcessProgressEntity> pushEntitiesOption = pushEntities.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime));
                    if (pushEntitiesOption.isPresent()) {
                        ProcessProgressEntity progress = pushEntitiesOption.get();
                        // 如果这个作品的最后一个状态 为（1,0）做受理
                        if (progress.getType() == 1 && progress.getState() == 0) {
                            log.info("作品复审终审开始(受理环节)");
                            try {
                                startWorkflow(copyrightManager, loginUser, ProcessProgressEntity.Type.ACCEPT, null);
                            } catch (Exception e) {
                                log.debug("环节办理信息失败：{}", e.getMessage());
                            }
                            log.info("作品复审终审结束(受理环节)");
                        } else {
                            // 如果这个作品的最后一个状态已经是受理，做办结
                            log.info("作品复审终审开始(办件环节)");
                            try {
                                startWorkflow(copyrightManager, loginUser, ProcessProgressEntity.Type.PROCESS, null);
                            } catch (Exception e) {
                                log.error("环节办理信息失败：{}", e.getMessage());
                            }
                            log.info("作品复审终审结束(办件环节)");
                        }
                    }*/


                    try {
                        log.info("作品终审开始(办件环节)");
                        startWorkflow(copyrightManager, loginUser, ProcessProgressEntity.Type.PROCESS, null, isRetry);
                        log.info("作品终审结束(办件环节)");
                    }catch (Exception e){
                        log.debug("环节办理信息失败：{}", e.getMessage());
                    }
                }

            } catch (Exception e) {
                log.debug("审核通过出错：{}", e.getMessage());
            }

            switch (state) {
                case CopyrightManager.FIRST_REVIEW:
                    // 受理
                    List<ProcessProgressEntity> processEntitys = processProgressService.findBySnCodeAndTypeAndState(copyrightManager.getSnCode(), ProcessProgressEntity.Type.PUSH, ProcessProgressEntity.State.FINISHED);
                    Optional<ProcessProgressEntity> progressOption = processEntitys.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime)); // 最后一次办件推送成功
                    progressOption.ifPresent((processProgress) -> {
                        // 只受理一次 TODO 如有补齐补正，另议
                        List<ProcessProgressEntity> progress = processProgressService.findBySnCodeAndTypeAndState(copyrightManager.getSnCode(), ProcessProgressEntity.Type.ACCEPT, ProcessProgressEntity.State.FINISHED);
                        if (progress.size() == 0) {
                            log.info("作品初审开始(受理)");
                            try {
                                startWorkflow(copyrightManager, loginUser, ProcessProgressEntity.Type.ACCEPT, null, isRetry);
                            } catch (Exception e) {
                                log.debug("审核通过后受理失败：{}", e.getMessage());
                            }
                            log.info("作品初审结束(受理)");
                        }
                    });
                    break;
                default:
                    break;
            }
        } else if (approverResult.equals("reject")) {
            log.info("作品审批开始-拒绝(办结+证照),copyrightManager:{}", copyrightManager);
            // 调用办结
            try {
                List<ProcessProgressEntity> processEntitys = processProgressService.findBySnCodeAndTypeAndState(copyrightManager.getSnCode(), ProcessProgressEntity.Type.PUSH, ProcessProgressEntity.State.FINISHED);
                Optional<ProcessProgressEntity> progressOption = processEntitys.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime));
                progressOption.ifPresent((processProgress) -> {
                    startWorkflow(copyrightManager, loginUser, ProcessProgressEntity.Type.FINISH, "退件", isRetry);
                });
            } catch (Exception e) {
                log.debug("审核通过后受理失败：{}", e.getMessage());
            }
            log.info("作品审批结束-拒绝(办结+证照)");
        } else if (approverResult.equals("modify")) {
            /*ProcessProgressEntity progressEntity = processProgressService.findByRegistrationNumAndStateAndType(copyrightManager.getRegistrationNum(),ProcessProgressEntity.State.FINISHED, ProcessProgressEntity.Type.PUSH);
            if(progressEntity != null){
                ProcessProgressEntity progress = processProgressService.findByRegistrationNumAndStateAndType(copyrightManager.getRegistrationNum(), ProcessProgressEntity.State.UNFINISHED, ProcessProgressEntity.Type.ACCEPT);
                if(progress == null){
                    log.info("作品审批开始(受理)");
                    try {
                        backendServiceFacade.startWorkflow(copyrightManager,loginUser,ProcessProgressEntity.Type.ACCEPT,null);
                    }catch (Exception e){
                        log.debug("审核通过后受理失败：{}", e.getMessage());
                    }
                    log.info("作品审批结束(受理)");
                }
            }*/
           /*
           log.info("作品审批修改开始(补齐补正告知),copyrightManager:{}",copyrightManager);
            try {
                backendServiceFacade.startWorkflow(copyrightManager,loginUser,ProcessProgressEntity.Type.COMPLETE, approverResult);
            }catch (Exception e){
                log.debug("修改材料出错：{}", e.getMessage());
            }
            log.info("作品审批修改结束(补齐补正告知)");*/

        } else if (approverResult.equals("rollback")) {
        }
        log.info("流程同步结束，版权作品:{},当前状态:{}, 审批结果{}", copyrightManager.getRegistrationNum(), state, approverResult);
    }

    @Transactional
    public void deleteApprovalWorkFlow() {
        approvalWorkFlowService.deleteByResolveStateAndCreateTimeBefore(ApprovalWorkFlowEntity.ResolveState.END, LocalDateTime.now().minusMonths(6));
    }

    public LocalDateTime addWorkdays(LocalDateTime startDateTime, int workdays) {
        LocalDate currentDate = startDateTime.toLocalDate();
        int addedDays = 0;

        while (addedDays < workdays) {
            // 如果当前日期不是周末（周六或周日）
            if (currentDate.getDayOfWeek() != DayOfWeek.SATURDAY && currentDate.getDayOfWeek() != DayOfWeek.SUNDAY) {
                addedDays++; // 增加工作日计数
            }

            // 无论如何都要移动到下一天
            currentDate = currentDate.plusDays(1);
        }

        // 返回计算后的日期（已经加上了指定数量的工作日）
        return currentDate.atTime(LocalTime.MAX);
    }

    @Async("doSomethingExecutor")
    public void startWorkflowRetry(String startDate, String endDate, int type, LocalDateTime startTime, LocalDateTime endTime, int size, Integer statusType) {
        String uuid = UUID.randomUUID().toString();
        log.info("办件信息推送（重推测试用）异步开始, uuid={}：", uuid);
        int perNum = 500;
        int num = 0;
        boolean flag = true;
        int snStatus = 0;
        switch (type) {
            case 2:
                snStatus = 16;
                break;
            case 3:
                snStatus = 18;
                break;
            case 4:
                snStatus = 20;
                break;
            default:
                break;
        }
        while (flag) {
            LocalDateTime now = LocalDateTime.now();
//            int now = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
//            // 自动提交, {8:00-20:00}点之间，暂停自动推送
//            if (startHour > endHour)
//                if (now >= endHour && now < startHour) {
//                    flag = false;
//                    continue;
//                }

            // 手动提交，开始小时 小于 结束小时
//            if (startHour <= endHour)
            if (now.isBefore(startTime) || now.isAfter(endTime)) {
                flag = false;
                continue;
            }

            BooleanBuilder builder = new BooleanBuilder();
            builder.and(QCopyrightManager.copyrightManager.registrationDate.goe(LocalDate.parse(startDate).atStartOfDay()));
            builder.and(QCopyrightManager.copyrightManager.registrationDate.loe(LocalDate.parse(endDate).atTime(LocalTime.MAX)));
            builder.and(QCopyrightManager.copyrightManager.projectId.isNotEmpty());
            builder.and(QCopyrightManager.copyrightManager.snStatus.eq(snStatus));
            if (null != statusType) {
                builder.and(QCopyrightManager.copyrightManager.status_type.eq(statusType));
            }
            builder.and(QCopyrightManager.copyrightManager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE));

            List<CopyrightManager> managerList = Lists.newArrayList(copyrightManagerService.findAll(builder, PageRequest.of(0, perNum)));
            log.info("办件信息推送（重推测试用）异步, uuid={}, 查询到符合条件的作品数量:{}", uuid, managerList.size());
            for (CopyrightManager manager : managerList) {
                UserDto userDto = userServiceFacade.getByUserName(manager.getUserName());
                UserEntity userEntity = userServiceFacade.findById(userDto.getUserId());
                manager.setSyncStatus("U");

                if (type == ProcessProgressEntity.Type.PUSH) {
                    String accessToken = getAccessToken(userEntity);
                    if (StringUtils.isEmpty(accessToken)) {
                        manager.setSnStatus(15);// retry fail
                        continue;
                    }
                    manager.setProjectId(getProjectId(userEntity, accessToken));
                }

                String cause = "";
                if (type == ProcessProgressEntity.Type.FINISH) {
                    cause = "办结";
                }
                log.info("办件信息推送（重推测试用）异步, uuid={}, 开始处理作品:{}", uuid, manager.getRegistrationNum());
                String workflowId = startWorkflow(manager, userEntity, type, cause, true);
                if (workflowId != null) {
                    switch (type) {
                        case 1:
                            manager.setSnStatus(16);
                            break;
                        case 2:
                            manager.setSnStatus(18);
                            break;
                        case 3:
                            manager.setSnStatus(20);
                            break;
                        case 4:
                            manager.setSnStatus(22);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (type) {
                        case 1:
                            manager.setSnStatus(15);
                            break;
                        case 2:
                            manager.setSnStatus(17);
                            break;
                        case 3:
                            manager.setSnStatus(19);
                            break;
                        case 4:
                            manager.setSnStatus(21);
                            break;
                        default:
                            break;
                    }
                }
                copyrightManagerService.saveOrUpdateCopyright(manager);
            }

            num += perNum;

            if (num >= size || CollectionUtils.isEmpty(managerList)) {
                flag = false;
                continue;
            }

            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        log.info("办件信息推送（重推测试用）异步结束, uuid={}：", uuid);
    }
}
