package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_attachments")
public class UploadAttachment {

	// 用户提交作品图片表
	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;// 作品图片ID
	private String workUrl;  // 文件保存路径
	private Long size;  // 文件大小
	private String workName;//作品名称
	private int fileType;//文件类型
    private String userName;

	//创建时间
	@CreatedDate
	private LocalDateTime createTime;
	//更新时间
	@LastModifiedDate
	private LocalDateTime updateTime;
}
