package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 作品著作权登记申请Bean
 */
@XmlRootElement
public class CopyrightBean {
	//代理人
	private AgentBean agent;
	//作者
	private List<AuthorBean> author;
	//作品信息
	private CopyrightManageBean copyrightManage;
	//著作权人
	private List<CopyrightOwnerBean> copyrightOwner;
	//提交者
	private List<SubmitterBean> submitter;
	//用户名
	private String userName;


	public AgentBean getAgent() {
		return agent;
	}
	public void setAgent(AgentBean agent) {
		this.agent = agent;
	}
	public List<AuthorBean> getAuthor() {
		return author;
	}
	public void setAuthor(List<AuthorBean> author) {
		this.author = author;
	}
	public CopyrightManageBean getCopyrightManage() {
		return copyrightManage;
	}
	public void setCopyrightManage(CopyrightManageBean copyrightManage) {
		this.copyrightManage = copyrightManage;
	}
	public List<CopyrightOwnerBean> getCopyrightOwner() {
		return copyrightOwner;
	}
	public void setCopyrightOwner(List<CopyrightOwnerBean> copyrightOwner) {
		this.copyrightOwner = copyrightOwner;
	}
	public List<SubmitterBean> getSubmitter() {
		return submitter;
	}
	public void setSubmitter(List<SubmitterBean> submitter) {
		this.submitter = submitter;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
}
