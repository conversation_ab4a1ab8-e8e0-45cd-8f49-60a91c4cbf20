package blockChain.service.nationalservice;

import blockChain.nationalBean.*;
import blockChain.utils.MD5Util;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.Consts;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.*;

public class NationAddClient {
	//读取配置文件
	//private static Properties prop = ControllerUtils.readProperties("/dict.properties");
	// 用户名
	private static String USER_ID = "apifjbq";
	// 密码
	private static String PASSWORD = "1q2w3e4r";
	// 接口地址
	private static String URLSUBMIT = "https://219.141.187.82:8443/NCSS2Service/rest/copyrightAdd/submission.api";
  private static String URLREVOKE = "https://219.141.187.82:8443/NCSS2Service/rest/copyrightAdd/revoke.api";

  private static String genToken(String key)
  {

    String userId = USER_ID;
    String password = PASSWORD;
    password = MD5Util.md5(password);
    // token信息Map
    Map<String, String> tokenMap = null;
    // 作品著作权登记信息
    //CopyrightBean crb = TestAddClient.getCopyrightBean(registrationNum);
    // 拼接认证字符串--用户名 + key（登记号） + 密码（MD5加密后的密码）
    String sign = userId + key + password;
    // 再次执行MD5加密得出最终sign
    sign = MD5Util.md5(sign);
    // 创建tokenMap对象并组装所有参数
    tokenMap = new HashMap<String, String>();
    tokenMap.put("userId", userId);
    tokenMap.put("key", key);
    tokenMap.put("sign", sign);

    return  JSONObject.toJSONString(tokenMap);
  }
  /**
   * 撤销正式数据到国家接口
   */
  public static String Revoke(CloseableHttpClient client, CopyrightManageCancelBean crb, String key)
    throws ClientProtocolException, IOException
  {
    // 添加代理
		/*HttpHost proxy = new HttpHost("*************", 7777);
		RequestConfig config = RequestConfig.custom().setProxy(proxy).build();*/

    HttpPost httpPost = new HttpPost(URLREVOKE);
    //httpPost.setConfig(config);

    List<NameValuePair> formParams = new ArrayList<NameValuePair>();
    crb.setUserName(USER_ID);
    formParams.add(new BasicNameValuePair("info", JSONObject
      .toJSONString(crb)));
    formParams.add(new BasicNameValuePair("token",genToken(key)) );

    UrlEncodedFormEntity entity = new UrlEncodedFormEntity(formParams,
      Consts.UTF_8);
    httpPost.setEntity(entity);

    // long startTime = System.currentTimeMillis();
    CloseableHttpResponse response = client.execute(httpPost);
    // long endTime = System.currentTimeMillis();

    InputStream is = response.getEntity().getContent();

    StringBuffer sb = new StringBuffer();

    byte[] b = new byte[1024];

    while ((is.read(b, 0, 1024)) != -1) {
      sb.append(new String(b, Consts.UTF_8));
    }

    int result = response.getStatusLine().getStatusCode();
    System.out.println(result);

    System.out.println(sb);

    is.close();

    response.close();
    return sb.toString();

  }


	/**
	 * 提交正式数据到国家接口
	 */
	public static  Map<String,Object> Submit(CloseableHttpClient client, CopyrightBean crb, String key)
			throws ClientProtocolException, IOException
  {
		//CloseableHttpClient client = NationAddClient.createHttpClient();
    HttpPost httpPost = new HttpPost(URLSUBMIT);
    //httpPost.setConfig(config);

    List<NameValuePair> formParams = new ArrayList<>();

		crb.setUserName(USER_ID);
		formParams.add(new BasicNameValuePair("info", JSONObject
				.toJSONString(crb)));
		formParams.add(new BasicNameValuePair("token",genToken(key)));

		UrlEncodedFormEntity entity = new UrlEncodedFormEntity(formParams,
				Consts.UTF_8);
		httpPost.setEntity(entity);

		// long startTime = System.currentTimeMillis();
		CloseableHttpResponse response = client.execute(httpPost);
		// long endTime = System.currentTimeMillis();

		InputStream is = response.getEntity().getContent();

		StringBuffer sb = new StringBuffer();

		byte[] b = new byte[4096];
    int readsize=0;
    readsize=is.read(b, 0, 4096);
    if(readsize>0)
    {
      sb.append(new String(b, 0, readsize));
    }
		int result = response.getStatusLine().getStatusCode();
		System.out.println(result);

		System.out.println(sb);

		is.close();

		response.close();
    Map<String,Object> results = new HashMap<String,Object>();
    results.put("result",result);
    results.put("message",sb.toString());
    results.put("name",crb.getCopyrightManage().getProductionName());
    try
    {
      JSONObject jsonObject= JSON.parseObject(sb.toString());
      results.put("message",jsonObject.get("msg").toString());
      results.put("name",jsonObject.get("registerNum").toString());
    }
    catch (Exception e)
    {

    }
		return results;
	}


}
