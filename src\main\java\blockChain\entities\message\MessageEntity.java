package blockChain.entities.message;

import blockChain.entities.UserEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/18 15:39
 */
@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tb_message")
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
public class MessageEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  /**
   * UID
   * 唯一约束
   */
  @Column(unique = true, length = 36)
  @NonNull
  private String uuid;

  /**
   * 消息标题
   */
  private String title;

  /**
   * 消息内容
   */
  @Column(length = 4096)
  private String content;

  /**
   * 是否发送给所有人
   * 发送给所有人的时候也需要保存接收消息列表
   */
  private Boolean toAll;

  /**
   * 创建时间
   */
  @CreatedDate
  private LocalDateTime createTime;

  /**
   * 更新时间
   */
  @LastModifiedDate
  private LocalDateTime updateTime;

  /**
   * 创建人
   */
  @ManyToOne(cascade = {CascadeType.DETACH})
  private UserEntity creator;

  @OneToMany(fetch = FetchType.LAZY, cascade = {CascadeType.REMOVE})
  @JoinColumn(name="message_id")
  private List<MessageUserEntity> users;
}
