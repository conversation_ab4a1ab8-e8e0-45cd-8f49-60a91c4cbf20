package blockChain.service;

import blockChain.entities.message.QuestionAnswerMessageEntity;
import blockChain.repository.QuestionAnswerMessageEntityRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/13 9:27
 */
@Service
@AllArgsConstructor
public class QuestionAnswerMessageService implements BaseService<QuestionAnswerMessageEntityRepository, QuestionAnswerMessageEntity, Long> {

  private QuestionAnswerMessageEntityRepository repository;
  @Override
  public QuestionAnswerMessageEntityRepository getRepository() {
    return repository;
  }

  public Optional<QuestionAnswerMessageEntity> findByUuid(String uuid) {
    return repository.findByUuid(uuid);
  }
}
