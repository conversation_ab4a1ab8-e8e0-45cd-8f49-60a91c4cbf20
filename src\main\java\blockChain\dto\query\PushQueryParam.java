package blockChain.dto.query;

import lombok.Data;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2022/05/2022/5/24
 */
@Data
public class PushQueryParam {

  /**
   * 统计的月
   */
  LocalDate month;

  /**
   * 统计的年
   */
  LocalDate year;
  private String startDate;
  private String endDate;

  public Boolean isYear(){
    if(startDate == null || endDate == null){
      return true;
    }
    String[] splitStartDate = startDate.split("-");
    String[] splitEndDate = endDate.split("-");
    if(splitStartDate.length < 2 && splitEndDate.length < 2){
      return true;
    }
    return false;
  }

  public LocalDateTime getStartTime() {
    if (startDate == null) {
      return null;
    }
    String[] splitDate = startDate.split("-");
    int month = 1;
    if(splitDate.length >= 2){
      month = Integer.parseInt(splitDate[1]);
    }
    LocalDate localDate = LocalDate.of(Integer.parseInt(splitDate[0]), month, 1);
    LocalDate nowDate = LocalDate.now();
    Assert.isTrue(localDate.isBefore(nowDate),"开始时间不能在当前时间之后");
    return LocalDateTime.of(localDate, LocalTime.MIN);
  }

  public LocalDateTime getEndTime() {
    if (endDate == null) {
      return null;
    }
    String[] splitDate = endDate.split("-");
    int month = 12;
    if(splitDate.length >= 2){
      month = Integer.parseInt(splitDate[1]);
    }
    int dayOfMonth = 30;
    int year = Integer.parseInt(splitDate[0]);
    switch (month) {
      case 1:
      case 3:
      case 5:
      case 7:
      case 8:
      case 10:
      case 12:
        dayOfMonth = 31;
        break;
      case 2:
        boolean leapYear = LocalDate.of(year, month, 1).isLeapYear();
        if(leapYear){
          dayOfMonth = 29;
        }else{
          dayOfMonth = 28;
        }
    }
    LocalDate localDate = LocalDate.of(year, month, dayOfMonth);
    LocalDate nowDate = LocalDate.now();
    if(localDate.isAfter(nowDate)) return LocalDateTime.of(nowDate, LocalTime.MAX);
    return LocalDateTime.of(localDate, LocalTime.MAX);
  }


  /**
   * 202201、2201
   * @return
   */
  public String getStart(int yearLen) {
    if (startDate == null) {
      return null;
    }
    String start;
    String[] splitDate = startDate.split("-");
    LocalDate nowDate = LocalDate.now();
    int month = 1;
    if(splitDate.length >= 2){
      month = Integer.parseInt(splitDate[1]);
      start = splitDate[0].substring(4-yearLen)+splitDate[1];
    } else {
      start = splitDate[0].substring(4-yearLen)+"";
    }
    LocalDate localDate = LocalDate.of(Integer.parseInt(splitDate[0]), month, 1);
    Assert.isTrue(localDate.isBefore(nowDate),"开始时间不能在当前时间之后");
    return start;
  }

  public String getEnd(int yearLen) {
    if (endDate == null) {
      return null;
    }
    String end;
    String[] splitDate = endDate.split("-");
    if(splitDate.length >= 2){
      end = splitDate[0].substring(4-yearLen) + splitDate[1];
    } else {
      end = splitDate[0].substring(4-yearLen)+"";
    }
    if (getStart(yearLen)!=null) {
      Assert.isTrue(Integer.valueOf(end) >= Integer.valueOf(getStart(yearLen)),"结束时间不能在开始时间之前");
    }
    return end;
  }

}
