package blockChain.repository;

import blockChain.entities.CallBackEntity;
import blockChain.entities.ProcessProgressEntity;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;

import static blockChain.entities.QProcessProgressEntity.processProgressEntity;

/**
 * <AUTHOR>
 * @date 2021/6/24 16:28
 */
public class CallBackRepositoryCustomImpl extends QuerydslRepositorySupport implements CallBackRepositoryCustom {


    public CallBackRepositoryCustomImpl() {
        super(CallBackEntity.class);
    }

    @Override
    public long updateData(String processId, Integer status) {
        return update(processProgressEntity)
                .set(processProgressEntity.state, status)
                .where(processProgressEntity.processId.eq(processId))
                .execute();
    }

}
