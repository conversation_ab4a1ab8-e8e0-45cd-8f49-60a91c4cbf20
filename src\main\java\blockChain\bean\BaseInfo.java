package blockChain.bean;

import blockChain.entities.CopyrightOwner;
import blockChain.entities.UserEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/21
 */
@Data
public class BaseInfo {
    // callinfo
//    /**
//     * 统一受理系统的系统名称，如**统一受理系统
//     */
//    private String caller = "福建省作品自愿登记系统";
//    /**
//     * 接口的调用发送时间
//     */
//    private LocalDateTime calltime;
//    /**
//     * 统一受理系统数据反馈接收地址
//     */
//    private String callbackUrl;
//    /**
//     * 分发码
//     */
//    private String issue;

    // infoDetail
    /**
     * 统一收件码
     */
    private String relativeBusiId;
    /**
     * 申报号
     */
    private String projId;
    /**
     * 查询密码
     */
    private String projpwd;
    /**
     * 是否是在垂管系统中运行的事项(2=使用省级垂管系统的事项) 0:否, 1:是
     */
    private byte isManubrium = 0;
    /**
     //     * 事项基本编码 非必须
     //     */
//    private String baseCode = "000739001000";
//    /**
//     * 事项实施编码 非必须
//     */
//    private String implementCode = "11350000003591037R2000739001000";
//    /**
//     * 业务办理项编码 非必须
//     */
//    private String taskhandleitem = "11350000003591037R200073900100001";
//    /**
//     * 权力事项编码
//     */
//    private String servicecode = "003591504GF05462";
//    /**
//     * 事项终审部门编码
//     */
//    private String serviceDeptid = "11350000003591037R";
    /**
     * 权力事项名称
     */
    private String serviceName = "作品自愿登记";
    /**
     * 申请项目的具体名称。如：关于XXX的交通建设工程施工许可 常以“关于XX申报事项”出现
     */
    private String projectName;
    /**
     * 办件类型 1:即办件, 2:承诺件, 3:其他
     */
    private byte infoType = 2;
    /**
     * 业务类型 整型类型，0:普通办件, 1:业务协同, 2:多级联动",
     */
    private byte busType = 0;
    /**
     * 申请人类型 0-个人，1-企业，2-非企业 TODO
     * 1:自然人, 2:法人, 9:其他
     */
    private byte applyType = 0;
    /**
     * 申报者名称
     */
    private String applyName;
    /**
     * 申报者证件类型
     * 统一社会信用代码证	001	法人
     * 营业执照: 820	法人
     * 其他法人或其他组织有效证件: 099	法人
     * 居民身份证:	111	个人
     * 居住证:	154	个人
     * 台湾居民来往大陆通行证:	511	个人
     * 港澳居民来往内地通行证:	516	个人
     * 其他:	999	个人
     */
    private String applyCardType;

    public String copyCertificateToCardType(int copyCertificate) {
        switch (copyCertificate) {
            case CopyrightOwner.CopyCertificateValue.CODE:
                return "820";
            case CopyrightOwner.CopyCertificateValue.GOVERNMENTZS:
            case CopyrightOwner.CopyCertificateValue.SOCIALORGZS:
            case CopyrightOwner.CopyCertificateValue.INSTITUTIONZS:
            case CopyrightOwner.CopyCertificateValue.OTHER2:
                return "099";
            case CopyrightOwner.CopyCertificateValue.IDCARD:
                return "111";
            case CopyrightOwner.CopyCertificateValue.RESIDENT:
                return "154";
            case CopyrightOwner.CopyCertificateValue.TWTXZ:
                return "511";
            case CopyrightOwner.CopyCertificateValue.GATTXZ:
                return "516";
            case CopyrightOwner.CopyCertificateValue.OTHER:
            case CopyrightOwner.CopyCertificateValue.PASSPORT:
                return "999";
            default:
                return "";
        }
    }

    public String legalPersionTypeToCardType(String legalPersionType) {
        if (legalPersionType == null) {
            return "099";
        } else {
            switch (legalPersionType) {
                case UserEntity.LegalPersionTypeValue.COMPANYPEOPLE:
                case UserEntity.LegalPersionTypeValue.INDIVIDUAL:
                    return "001";
                default:
                    return "099";
            }
        }
    }

    /**
     * 申报者证件号码
     */
    private String applyCardNumber;
    /**
     * 联系人/代理人姓名 如果无代理人，联系人就是申报者
     */
    private String contactman;
    /**
     * 联系人/代理人证件类型 提供的有效证件名称，包括身份证、组织机构代码证等详见10.3证件类型
     */
    private String contactmanCardType;
    /**
     * 联系人/代理人证件号码 提供的有效证件的识别号。如身份证号码
     */
    private String contactmanCardNumber;
    /**
     * 联系人手机号码 申报者的联系号码
     */
    private String telphone;
    /**
     * 通讯地址 申报者的联系地址
     */
    private String address;
//    /**
//     * 收件部门编码 收件部门标识
//     */
//    private String deptId = "11350000003591037R";
    /**
     * 收件部门名称
     */
    private String deptName = "版权管理处";
    /**
     * 实施机构组织机构代码
     */
    private String ssOrgCode = "";
    /**
     * 收件人id 创建用户标识
     */
    private String receiveUserId;
    /**
     * 创建用户名称
     */
    private String receiveName;
    /**
     * 申报来源，0:窗口申报, 1:网上申报, 10:APP申报, 11:政务一体机"
     */
    private byte applyFrom = 1;
    /**
     * 审批类型 普通办件:01
     */
    private String approveType = "01";
    /**
     * 项目性质 其他:99
     */
    private String applyPropertiy = "01";
    /**
     * 申报时间
     */
//    private LocalDateTime receiveTime;
    private String receiveTime;
    /**
     * 收件部门所属行政区划编码
     */
    private String areaCode = "350000";
    /**
     * 数据状态 标识办件是否为有效件，默认是有效。0=作废1=有效。
     */
    private String dataState = "1";
    /**
     * 所属系统 用于区分不同业务系统报送的数据，标识由省级平台分配。（无标识时直接传对应系统系统中文名称）
     */
    private String belongSystem = "福建省作品自愿登记系统";
    /**
     * 数据产生时间
     */
    private String createTime;
    /**
     * 同步状态 插入：I，更新：U，删除：D，已同步：S。
     */
    private String syncStatus = "I";
    /**
     * 版本号 默认值=1，如果有信息变更，则版本号递增。
     */
    private byte dataVersion = 1;
    /**
     * 数据来源渠道 41：省直自建系统（PC端）
     */
    private byte dataFrom = 41;
    /**
     * 办件是否需回流到地市 模式2对接的厅局系统需要，是否将该办件回流到地市。Y:回流，N:不回流。
     */
    private byte province_back = 'N';
}
