package blockChain.repository;

import blockChain.entities.TagEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/05/2022/5/7
 */
@Repository
public interface TagRepository extends JpaRepository<TagEntity, Integer>, QuerydslPredicateExecutor<TagEntity> {

    List<TagEntity> findAllByIdIn(List<Integer> ids);
    List<TagEntity> findAllByName(String name);
}
