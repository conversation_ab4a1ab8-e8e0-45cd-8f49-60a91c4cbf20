package blockChain.dto;

import blockChain.entities.PaperCertificateEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/16 15:21
 */
@Data
@Accessors(chain = true)
public class PaperCertificateDto {
  /**
   * UUID
   * 唯一约束
   */
  @JsonProperty("id")
  private String uuid;

  @JsonProperty("idList")
  private List<String> uuids;

  private Long copyrightId;

  /**
   * 申请理由
   */
  private String reason;

  /**
   * 驳回理由
   */
  @JsonProperty("approverOpinion")
  private String rejectReason;

  /**
   * 申请人
   */
  private String creatorName;

  /**
   * 物流编号
   */
  private String logisticsNum;

  /**
   * 物流公司代码
   */
  private String logisticsComNum;

  /**
   * 物流公司中文描述
   */
  private String logisticsComCn;

  /**
   * 物流状态
   */
  private PaperCertificateEntity.StatusEnum status;

  /**
   * 作品登记号
   */
  private String worksNum;
  /**
   * 作品名称
   */
  private String productionName;


  /**
   * 申请时间
   */
  private LocalDateTime createTime;


  private CopyrightManagerDto copyright;

  private List<ProcessRecordDto> processRecords;

}
