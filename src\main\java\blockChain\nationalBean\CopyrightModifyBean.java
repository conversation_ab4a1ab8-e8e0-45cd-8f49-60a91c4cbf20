package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement
public class CopyrightModifyBean {
	//变更理由Bean对象
	private ChangeReasonBean changeReason;
	//变更项Bean对象集合
	private List<CopyrightChangeItemBean> copyrightChangeItem;
	//提交者Bean对象
	private SubmitterBean submitter;
	//代理人Bean对象
	private AgentBean agent;
	//登记机构报送Bean
	private OrganizationBean org;
	//用户名
	private String userName;

	public ChangeReasonBean getChangeReason() {
		return changeReason;
	}
	public void setChangeReason(ChangeReasonBean changeReason) {
		this.changeReason = changeReason;
	}
	public List<CopyrightChangeItemBean> getCopyrightChangeItem() {
		return copyrightChangeItem;
	}
	public void setCopyrightChangeItem(
			List<CopyrightChangeItemBean> copyrightChangeItem) {
		this.copyrightChangeItem = copyrightChangeItem;
	}
	public SubmitterBean getSubmitter() {
		return submitter;
	}
	public void setSubmitter(SubmitterBean submitter) {
		this.submitter = submitter;
	}
	public AgentBean getAgent() {
		return agent;
	}
	public void setAgent(AgentBean agent) {
		this.agent = agent;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public OrganizationBean getOrg() {
		return org;
	}
	public void setOrg(OrganizationBean org) {
		this.org = org;
	}
	@Override
	public String toString() {
		return "CopyrightModifyBean [changeReason=" + changeReason
				+ ", copyrightChangeItem=" + copyrightChangeItem + ", submitter="
				+ submitter + ", agent=" + agent + ", org=" + org + ", userName="
				+ userName +"]";
	}


}
