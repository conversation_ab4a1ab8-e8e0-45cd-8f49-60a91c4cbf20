/**
 * WsSmsServiceServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package blockChain.utils.umpay.mascloud.mgw.service.webservice.server;

public class WsSmsServiceServiceLocator extends org.apache.axis.client.Service implements WsSmsServiceService {

    public WsSmsServiceServiceLocator() {
    }


    public WsSmsServiceServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public WsSmsServiceServiceLocator(String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for WsSmsServicePort
    private String WsSmsServicePort_address = "http://*************:1999/smsservice";

    public String getWsSmsServicePortAddress() {
        return WsSmsServicePort_address;
    }

    // The WSDD service name defaults to the port name.
    private String WsSmsServicePortWSDDServiceName = "WsSmsServicePort";

    public String getWsSmsServicePortWSDDServiceName() {
        return WsSmsServicePortWSDDServiceName;
    }

    public void setWsSmsServicePortWSDDServiceName(String name) {
        WsSmsServicePortWSDDServiceName = name;
    }

    public WsSmsService getWsSmsServicePort() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(WsSmsServicePort_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getWsSmsServicePort(endpoint);
    }

    public WsSmsService getWsSmsServicePort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            WsSmsServicePortBindingStub _stub = new WsSmsServicePortBindingStub(portAddress, this);
            _stub.setPortName(getWsSmsServicePortWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setWsSmsServicePortEndpointAddress(String address) {
        WsSmsServicePort_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (WsSmsService.class.isAssignableFrom(serviceEndpointInterface)) {
                WsSmsServicePortBindingStub _stub = new WsSmsServicePortBindingStub(new java.net.URL(WsSmsServicePort_address), this);
                _stub.setPortName(getWsSmsServicePortWSDDServiceName());
                return _stub;
            }
        }
        catch (Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        String inputPortName = portName.getLocalPart();
        if ("WsSmsServicePort".equals(inputPortName)) {
            return getWsSmsServicePort();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://server.webservice.service.mgw.mascloud.umpay.com/", "WsSmsServiceService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://server.webservice.service.mgw.mascloud.umpay.com/", "WsSmsServicePort"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(String portName, String address) throws javax.xml.rpc.ServiceException {

if ("WsSmsServicePort".equals(portName)) {
            setWsSmsServicePortEndpointAddress(address);
        }
        else
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
