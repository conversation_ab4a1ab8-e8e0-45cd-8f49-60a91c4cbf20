package blockChain.config;


import blockChain.facade.service.BackendServiceFacade;
import lombok.AllArgsConstructor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ApplicationApprovalWorkFlowImpl implements ApplicationRunner {

    private final BackendServiceFacade facade;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        facade.approvalWorkFlowScheduling();
    }
}
