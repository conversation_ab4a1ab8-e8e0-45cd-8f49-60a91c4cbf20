package blockChain.bean;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

@Data
@AllArgsConstructor
@ApiModel(description = "分页查询结果")
public class PageResponse<T> {
    private long page;

    private long pageSize;

    private long total;

    private List<T> listData;

    public static <T> PageResponse<T> of(Page<T> page) {
        if (page == null) {
            return null;
        }
        return new PageResponse<>(page.getNumber() + 1, page.getSize(), page.getTotalElements(), page.getContent());
    }

    public static <T> PageResponse<T> of(Page page, List<T> listData) {
        if (page == null) {
            return null;
        }
        return new PageResponse<>(page.getNumber() + 1, page.getSize(), page.getTotalElements(), listData);
    }

    public static <T> PageResponse<T> of(Long page, Long size, Long total, List<T> listData) {
        if (page == null) {
            return null;
        }
        return new PageResponse<>(page, size, total, listData);
    }
}
