package blockChain.controller;


import blockChain.bean.AuthenticationBean;
import blockChain.bean.IDCheckRawXML;
import blockChain.bean.QueryEntInfoBean;
import blockChain.dto.LicenseVerifyDTO;
import blockChain.utils.SM4Utils;
import blockChain.utils.XMLUtil;
import com.alibaba.fastjson.JSONObject;
//import com.sun.xml.ws.client.BindingProviderProperties;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.tempuri.*;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.InputSource;

import javax.validation.Valid;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.ws.BindingProvider;
import java.io.StringReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by epcsoft on 2020/8/13.
 */
@Api("身份校验")
@Slf4j
@RestController
@RequestMapping("verify")
@RequiredArgsConstructor
public class LisenceCheckController
{
  private static String LISENCE_ACCOUNT="bqglxt_hjjkdy";
  private static String LISENCE_PASSWD="bqglxt@0630";
  private String sm4key=null;




  private String takelogin()
  {
    try{
      Authentication authentication=new Authentication();
      AuthenticationSoap weatherWebServiceSoap = authentication.getAuthenticationSoap();
      Map<String, Object> requestContext = ((BindingProvider)weatherWebServiceSoap).getRequestContext();
      //requestContext.put(BindingProviderProperties.REQUEST_TIMEOUT, 3000); // Timeout in millis
      //requestContext.put(BindingProviderProperties.CONNECT_TIMEOUT, 1000); // Timeout in millis
      if(sm4key==null||sm4key.length()==0)
      {
        String a = weatherWebServiceSoap.getSecretKey(LISENCE_ACCOUNT, LISENCE_PASSWD);
        if (a.contains("ERR")) {
          return a;
        }
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        DocumentBuilder db = dbf.newDocumentBuilder();
        InputSource is = new InputSource(new StringReader(a));
        Document document = db.parse(is);
        Node node = document.getChildNodes().item(0);
        sm4key = node.getTextContent();
      }
      AuthenticationBean authenticationBean=new AuthenticationBean();
      authenticationBean.setUsername(LISENCE_ACCOUNT);
      authenticationBean.setPassword(LISENCE_PASSWD);
      authenticationBean.setLogintime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
      String xmlstr=XMLUtil.convertToXml(authenticationBean);
      SM4Utils sm4 = new SM4Utils();
      sm4.secretKey = sm4key;
      sm4.hexString = false;
      String requestMsg= sm4.encryptData_ECB(xmlstr);
      String retdataEnc=weatherWebServiceSoap.loginByAccount(requestMsg);
      if (retdataEnc.contains("ERR")) {
        return retdataEnc;
      }
      String retdataDec=sm4.decryptData_ECB(retdataEnc);
      InputSource is = new InputSource(new StringReader(retdataDec));
      DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
      DocumentBuilder db = dbf.newDocumentBuilder();
      Document document=db.parse(is);
      Node node=document.getChildNodes().item(0);
      String guid=node.getTextContent();
      return guid;
    }catch(Exception e){
      log.error("error", e);
    }
    return "";
  }


  @ApiOperation("身份核验")
  @PostMapping("lisenceVerify")
  public ResponseEntity<Map<String,Object>> lisenceVerify(@Valid @RequestBody LicenseVerifyDTO licenseVerifyDTO)
  {
      Map<String, Object> result = new HashMap<>();
      /*
      String guid=takelogin();
      if(guid.contains("ERR")) {
        result.put("code", "500");
        result.put("message", guid);
        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
//        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
      }
      if(licenseVerifyDTO.getCopyCertificateString().contains("身份证"))
      {
        try {
          IDCheckRawXML idCheckRawXML = new IDCheckRawXML();
          String rawxml = idCheckRawXML.getXML(guid, licenseVerifyDTO.getCopyIdCard(), licenseVerifyDTO.getCopyName());
          Population population = new Population();
          PopulationSoap populationSoap = population.getPopulationSoap();
          Map<String, Object> requestContext = ((BindingProvider)populationSoap).getRequestContext();
          requestContext.put(BindingProviderProperties.REQUEST_TIMEOUT, 3000); // Timeout in millis
          requestContext.put(BindingProviderProperties.CONNECT_TIMEOUT, 1000); // Timeout in millis
          SM4Utils sm4 = new SM4Utils();
          sm4.secretKey = sm4key;
          sm4.hexString = false;
          String requestMsg = sm4.encryptData_ECB(rawxml);
          String retdataEnc = populationSoap.idCheck(requestMsg);
          if (retdataEnc.contains("ERR")) {
            result.put("code", "500");
            result.put("message", retdataEnc);
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
            //return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
          }


          String retdataDec = sm4.decryptData_ECB(retdataEnc);
          InputSource is = new InputSource(new StringReader(retdataDec));
          DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
          DocumentBuilder db = dbf.newDocumentBuilder();
          Document document = db.parse(is);
          Node node = document.getChildNodes().item(0).getChildNodes().item(0);
          int martchcount=0;
          for(int r=0;r<node.getChildNodes().getLength();r++)
          {
            Node childNode=node.getChildNodes().item(r);
            if(childNode.getNodeName().equals("公民身份号码-匹配度"))
            {
              if(childNode.getTextContent().equals("一致"))
              {
                martchcount++;
              }
            }
            else if(childNode.getNodeName().equals("姓名-匹配度"))
            {
              if(childNode.getTextContent().equals("一致"))
              {
                martchcount++;
              }
            }
          }
          if(martchcount!=2)
          {
            result.put("code", "500");
            result.put("message", "姓名或名称与证件号码不匹配！");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
          }
        }
        catch (Exception e)
        {
          e.printStackTrace();
          result.put("code", "200");
          result.put("message", "国家局接口异常，开放时限：工作日8:00-18:00\n"+e.getMessage());
          return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
          ///return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
      }
      else
      {
        String retdataEnc="";
        try {
          CegnGSZJQueryData cegnFGWQueryData=new CegnGSZJQueryData();
          CegnGSZJQueryDataSoap cegnFGWQueryDataSoap=cegnFGWQueryData.getCegnGSZJQueryDataSoap();
          Map<String, Object> requestContext = ((BindingProvider)cegnFGWQueryDataSoap).getRequestContext();
          requestContext.put(BindingProviderProperties.REQUEST_TIMEOUT, 3000); // Timeout in millis
          requestContext.put(BindingProviderProperties.CONNECT_TIMEOUT, 1000); // Timeout in millis
          QueryEntInfoBean queryTyshxydmBean=new QueryEntInfoBean();
          queryTyshxydmBean.setGuid(guid);
          queryTyshxydmBean.setEntname(licenseVerifyDTO.getCopyName());
          queryTyshxydmBean.setUniscid(licenseVerifyDTO.getCopyIdCard());
          String rawxml=XMLUtil.convertToXml(queryTyshxydmBean);
          SM4Utils sm4 = new SM4Utils();
          sm4.secretKey = sm4key;
          sm4.hexString = false;
          String requestMsg = sm4.encryptData_ECB(rawxml);
          retdataEnc = cegnFGWQueryDataSoap.queryEntInfo(requestMsg);
          if (retdataEnc.contains("ERR"))
          {
            result.put("code", "500");
            result.put("message", retdataEnc);
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
            //return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
          }

          String retdataDec = sm4.decryptData_ECB(retdataEnc);
          QueryEntInfoBean object = (QueryEntInfoBean)JSONObject.parseObject(retdataDec.replace("[","").replace("]",""),QueryEntInfoBean.class);

          //blockChain.bean.QueryTyshxydmResponse queryTyshxydmResponse=(blockChain.bean.QueryTyshxydmResponse) XMLUtil.convertXmlFileToObject(blockChain.bean.QueryTyshxydmResponse.class,retdataDec);
          if(!object.getEntname().equals(queryTyshxydmBean.getEntname())||!object.getUniscid().equals(queryTyshxydmBean.getUniscid()))
          {
            result.put("code", "500");
            result.put("message", "企业名称和统一社会信用代码不匹配！");
            return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
          }
        }
        catch (Exception e)
        {
          e.printStackTrace();
          result.put("code", "500");
          result.put("message", e.getMessage());
          return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
          // /return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
      }
      */
      result.put("code", "200");
      result.put("message", "匹配成功！");
      return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
  }


}
