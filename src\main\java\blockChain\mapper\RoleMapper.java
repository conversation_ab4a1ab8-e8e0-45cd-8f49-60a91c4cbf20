package blockChain.mapper;

import blockChain.dto.RoleDto;
import blockChain.dto.role.RoleSave;
import blockChain.entities.RoleEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/18 10:28
 */
@Mapper(config = CommonConfig.class, uses = {MenuMapper.class})
public interface RoleMapper {

    RoleMapper INSTANCE = Mappers.getMapper(RoleMapper.class);

    @Mapping(source = "menus", target = "authorities")
    RoleDto toRoleDto(RoleEntity role);

    List<RoleDto> toRoleDtoList(List<RoleEntity> roleList);

    RoleEntity fromRoleSave(RoleSave roleSave);

    void updateRoleEntity(RoleSave roleSave, @MappingTarget RoleEntity roleEntity);
}
