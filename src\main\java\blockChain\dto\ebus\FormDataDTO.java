package blockChain.dto.ebus;

import blockChain.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Data
public class FormDataDTO {

    @ApiModelProperty("中枢表单用数ID")
    private Long id;

    @ApiModelProperty("证件号码")
    private String certificateNumber;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("国籍")
    @JsonIgnore
    private String countries;

    @ApiModelProperty("省份")
    @JsonIgnore
    private String province;// 省份

    @ApiModelProperty("城市")
    @JsonIgnore
    private String city;// 所在地级市

    @ApiModelProperty("区县")
    @JsonIgnore
    private String county;// 所在区县

    private String areaNamesStr;

    private List<String> areaNames = new ArrayList<>();

    @ApiModelProperty("国籍编码")
    private Integer certCountries;

    @ApiModelProperty("省份编码")
    private Integer certProvince;

    @ApiModelProperty("城市编码")
    private Integer certCity;

    @ApiModelProperty("区县编码")
    private Integer certCounty;

    @ApiModelProperty("联系地址")
    private String fullAddress;

    public String getAreaNamesStr() {
//        String areaNamesStr = "";
        if (StringUtils.isEmpty(countries))
            return areaNamesStr;
        this.areaNamesStr = countries;

        if (StringUtils.isEmpty(province))
            return this.areaNamesStr;
        this.areaNamesStr += "," + province;

        if (StringUtils.isEmpty(city))
            return this.areaNamesStr;
        this.areaNamesStr += "," + city;

        if (StringUtils.isEmpty(county))
            return this.areaNamesStr;
        this.areaNamesStr += "," + county;
        return this.areaNamesStr;
    }

    public List<String> getAreaNames() {
        if (this.areaNamesStr != null) {
            return Arrays.asList(this.areaNamesStr.split(","));
        } else {
            return new ArrayList<>();
        }
    }

}
