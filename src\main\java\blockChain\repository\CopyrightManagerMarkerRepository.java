package blockChain.repository;

import blockChain.entities.CopyrightManagerMarker;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/14 14:40
 */
public interface CopyrightManagerMarkerRepository extends BaseRepository<CopyrightManagerMarker, Long> {
    Optional<CopyrightManagerMarker> findByUuid(String uuid);

    @Query(value = "SELECT * FROM tb_copyrightmanager_marker where manager_id =?1", nativeQuery = true)
    List<CopyrightManagerMarker> findByManagerId(Long registrationNum);
}
