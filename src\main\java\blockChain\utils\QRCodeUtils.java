package blockChain.utils;

import com.ctsi.ssdc.util.MatrixToImageWriter;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

public class QRCodeUtils {
	   //定义黑色值
	   private static final int BLACK = 0xFF000000;
	   //定义白色值
	   private static final int WHITE = 0xFFFFFDF0;

	   //将图片转化为流
	   public static BufferedImage toBufferedImage(BitMatrix matrix) {
	     int width = matrix.getWidth();
	     int height = matrix.getHeight();
	     BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
	     for (int x = 0; x < width; x++) {
	       for (int y = 0; y < height; y++) {
	         image.setRGB(x, y, matrix.get(x, y) ? BLACK : WHITE);
	       }
	     }
	     return image;
	   }

	   //
	   public static void writeToFile(BitMatrix matrix, String format, File file)
	       throws IOException {
	     BufferedImage image = toBufferedImage(matrix);
	     if (!ImageIO.write(image, format, file)) {
	       throw new IOException("Could not write an image of format " + format + " to " + file);
	     }
	   }

	   //
	   public static void writeToStream(BitMatrix matrix, String format, OutputStream stream)
	       throws IOException {
	     BufferedImage image = toBufferedImage(matrix);
	     if (!ImageIO.write(image, format, stream)) {
	       throw new IOException("Could not write an image of format " + format);
	     }
	   }

	   //生成二维码
	   public static String drawQRCode(String content,String path, String name){

		   try {
		           /*String content = "这是测试xing二维码生成";
//		           String path = "D:/tt";
		           String path = "D:/java/apache-tomcat-7.0.47/webapps/wmsDemo/QRCode";*/
               MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
               Map hints = new HashMap();
               //内容所使用编码
               hints.put(EncodeHintType.CHARACTER_SET, "gb2312");
               BitMatrix bitMatrix = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, 416, 416, hints);
               //生成二维码
               if (StringUtils.isEmpty(name)) {
                   name = "QRCode.jpg";
               }
               File outputFile = new File(path);
               if (!outputFile.exists()) {
                   outputFile.mkdirs();
               }
               MatrixToImageWriter.writeToFile(bitMatrix, "jpg", new File(path, name));
               path = path + "/" + name;
               return path;
           } catch (Exception e) {
		       e.printStackTrace();
		       return "";
		   }
		       }

}
