package blockChain.utils;

/**
 * Created by chenwr on 2019/2/25
 */
public class UserPasswordHelper {

    private static String RANDOM_PASSWORD_SEED = "abcdefghijkmnpqrstuvwxyz123456789";

    private static int PASSWORD_MIN_LEN = 8;

    /**
     * 判断密码是否为弱密码
     *
     * @param password 待检查密码
     * @return true 弱密码，false 强密码
     */
    public static boolean isWeakPassword(String password) {
        //密码为空，不包含数字、字母 或者 长度不在[8, 20]之间。视为弱密码
        if (password == null || !password.matches("(?=.*[0-9]).*")
            || !password.matches("(?=.*[a-zA-Z]).*")
            || !(password.length() >= 8) || !(password.length() <= 20)) {
            return true;
        }
        return false;
    }

    /**
     * 获取随机密码种子
     */
    public static String getRandomPasswordSeed() {
        return RANDOM_PASSWORD_SEED;
    }

    /**
     * 获取随机密码长度
     */
    public static int getPasswordMinLen() {
        return PASSWORD_MIN_LEN;
    }
}
