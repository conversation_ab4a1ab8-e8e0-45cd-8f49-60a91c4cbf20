package blockChain.exception;

import blockChain.bean.ResultCode;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 * @date 2019/12/19 16:10
 */
@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class EntityNotFoundException extends BaseException {

    public EntityNotFoundException() {
        this("资源未找到");
    }

    public EntityNotFoundException(String message) {
        super(ResultCode.RESULT_FAIL, message);
    }
}
