# blockChain

出版社区块链项目

## 框架帮助

### [querydsl](https://lufficc.com/blog/spring-boot-jpa-querydsl)
### [mapstruct实体映射](https://blog.csdn.net/u012373815/article/details/88367456)
> [mapstruct.org](http://mapstruct.org/)

## 环境配置

### IDEA

#### 代码模板

如图所示位置，添加如下标记：

```java
/**
 * 
 * <AUTHOR>
 * @date ${DATE} ${TIME}
 */
```

![1560505438478](_img/1560505438478.png)

#### 方法重写设置

如图所示`Settings > Edit > Code Style > Java`

![1562211417980](_img/1562211417980.png)

请注意IDEA中的Mavan配置是否正确：

![1562211609958](_img/1562211609958.png)

### Maven

> 添加比翼私有库

>  在maven配置文件`setting.xml`的`servers`节点添加
```xml
    <server>
      <id>ctbiyi</id>  
      <username>nexus</username>  
      <password>FWSs5*j8YeTqp3DD</password>  
    </server>
```

> 在maven配置文件`setting.xml`的`profiles`节点添加
```xml
<profile>
  <id>ctbiyi</id>
  <repositories>
      <repository>
          <id>ctbiyi</id>
          <name>local private nexus release</name>
          <url>http://repo.ctbiyi.com:6442/repository/maven-public/</url>
      </repository>
  </repositories>
  <pluginRepositories>
      <pluginRepository>
          <id>ctbiyi</id>
          <name>Team Nexus Repository</name>
          <url>http://repo.ctbiyi.com:6442/repository/maven-public/</url>
      </pluginRepository>
  </pluginRepositories>
</profile>
```

>  `IDEA` `maven` 视图激活比翼私有库

![1558337063249](_img/1558337063249.png)



## 运行项目

### idea

创建Maven 运行配置，所有参数留空即可

![1558333810382](_img/1558333810382.png)

本地开发配置需要更改时，可以编辑`src\main\resources\config\application-local.yml`配置文件

## 比翼共享研发社区使用说明

1. 提问请访问 [https://qa.ctbiyi.com](https://qa.ctbiyi.com/)

2. 比翼共享研发社区地址: [https://www.ctbiyi.com](https://www.ctbiyi.com/)

3. **帮助平台** [https://help.ctbiyi.com](https://help.ctbiyi.com/)

4. 代码库：<https://gitlab.ctbiyi.com/>

5. 比翼平台账号申请：<https://www.ctbiyi.com/#/registe> 