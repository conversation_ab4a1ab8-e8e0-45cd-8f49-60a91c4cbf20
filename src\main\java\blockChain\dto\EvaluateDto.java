package blockChain.dto;

import lombok.Data;

/**
 * Created by epcsoft on 2020/8/6.
 */
@Data
public class EvaluateDto
{

  private Long id;

  private Long copyright_id;

  private String worksNum;

  private String certificateCreateTime;

  private String realName;

  //价渠道（pc端=1，移动服务端=2，二维码=3，政务大厅平板电脑=4，政务大厅自助终端=5，电话=6，短信=7），填数字
  private Integer pf;

  //整体满意度（1-5）,（值为1和2时，评价详情和文字评价至少一项必填）
  private Integer alternate;

  //评价详情（勾选的评价详情编号，勾选多个用逗号隔开。例：501,503,504）
  private String appraisald;

  //评价详情勾选数量
  private Integer appraisaldnum;

  //文字评价
  private String writingevalua;

  //评价时间，格式：yyyy-MM-dd HH:mm:ss
  private String assessTime;

  //回执
  private String data;
}
