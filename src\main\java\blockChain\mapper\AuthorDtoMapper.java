package blockChain.mapper;

import blockChain.dto.AuthorDto;
import blockChain.dto.UserDto;
import blockChain.entities.Author;
import blockChain.entities.UserEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AuthorDtoMapper {
    AuthorDtoMapper INSTANCE = Mappers.getMapper(AuthorDtoMapper.class);

    AuthorDto entityToDto(Author author);

    List<AuthorDto> entityToDto(List<Author> authors);
}
