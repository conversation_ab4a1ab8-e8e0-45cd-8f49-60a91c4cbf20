
package blockChain.mapper;

import blockChain.dto.DraftCopyrightManagerDto;
import blockChain.entities.CopyrightManager;
import blockChain.entities.DraftCopyrightManager;
import blockChain.mapper.transform.CopyrightManagerTransform;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/12/3 10:32
 */

@Mapper(uses={CopyrightManagerTransform.class})
public interface DraftCopyrightManagerDtoMapper {
    DraftCopyrightManagerDtoMapper INSTANCE = Mappers.getMapper(DraftCopyrightManagerDtoMapper.class);

    @Mapping(target = "id", source = "registrationNum")
    @Mapping(target = "haverightIdList", source = "haverightIds")
    DraftCopyrightManagerDto entityToDto(DraftCopyrightManager draftCopyrightManager);

    List<DraftCopyrightManagerDto> entityToDto(List<DraftCopyrightManager> draftCopyrightManager);

}

