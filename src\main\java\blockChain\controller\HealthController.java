package blockChain.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/26
 */
@Api("最多采一次-健康状态检测")
@Slf4j
@RestController
@RequestMapping("health")
@RequiredArgsConstructor
public class HealthController {
    @ApiOperation("服务健康状态检测")
    @GetMapping()
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("status", "UP");
        return new ResponseEntity<>(resultMap, HttpStatus.OK);
    }
}
