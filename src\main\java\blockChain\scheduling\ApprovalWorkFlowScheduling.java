package blockChain.scheduling;

import blockChain.facade.service.BackendServiceFacade;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;

/**
 * <AUTHOR>
 * @date 2024/10/11
 */
@Component
@AllArgsConstructor
@Slf4j
@Async
public class ApprovalWorkFlowScheduling {

    private final BackendServiceFacade facade;

    /**
     * 每日凌晨2点对审批采一次轮询已完成的项目删除
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Transactional
    public void deleteApprovalWorkFlow() {
        facade.deleteApprovalWorkFlow();
    }
}
