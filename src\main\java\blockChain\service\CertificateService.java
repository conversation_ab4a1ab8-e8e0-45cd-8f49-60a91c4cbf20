package blockChain.service;

import blockChain.entities.CertificateEntity;
import blockChain.exception.EntityNotFoundException;
import blockChain.repository.CertificateRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class CertificateService {

    @Autowired
    private CertificateRepository certificateRepository;

    public void saveOrUpdate(CertificateEntity certificateEntity){
        certificateRepository.save(certificateEntity);
    }

    public CertificateEntity findAllByCertificateHolderCode(String certificateHolderCode){
        List<CertificateEntity> certificateEntities = certificateRepository.findAllByCertificateHolderCode(certificateHolderCode);
        if (certificateEntities.size()>0) return certificateEntities.get(0);
        return null;
    }

    public CertificateEntity findById(Long id) {
      return certificateRepository.findById(id).orElseThrow(() -> new EntityNotFoundException("电子证照不存在，请重新获取"));
    }
}
