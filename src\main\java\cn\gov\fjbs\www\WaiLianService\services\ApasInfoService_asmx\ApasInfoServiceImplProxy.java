package cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx;

public class ApasInfoServiceImplProxy implements cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceImpl {
  private String _endpoint = null;
  private cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceImpl apasInfoServiceImpl = null;
  
  public ApasInfoServiceImplProxy() {
    _initApasInfoServiceImplProxy();
  }
  
  public ApasInfoServiceImplProxy(String endpoint) {
    _endpoint = endpoint;
    _initApasInfoServiceImplProxy();
  }
  
  private void _initApasInfoServiceImplProxy() {
    try {
      apasInfoServiceImpl = (new cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceImplServiceLocator()).getApasInfoServiceAsmx();
      if (apasInfoServiceImpl != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)apasInfoServiceImpl)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)apasInfoServiceImpl)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (apasInfoServiceImpl != null)
      ((javax.xml.rpc.Stub)apasInfoServiceImpl)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceImpl getApasInfoServiceImpl() {
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl;
  }
  
  public java.lang.String getCrudInfoListNew(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String count, java.lang.String action, java.lang.String times, java.lang.String sortsign) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getCrudInfoListNew(deptcode, password, starttime, endtime, count, action, times, sortsign);
  }
  
  public java.lang.String getServiceListNew(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String count, java.lang.String sortsign, java.lang.String times) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceListNew(deptcode, password, starttime, endtime, count, sortsign, times);
  }
  
  public java.lang.String getServiceList(java.lang.String deptcode, java.lang.String password) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceList(deptcode, password);
  }
  
  public java.lang.String getObjectToStr(java.lang.Object obj) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getObjectToStr(obj);
  }
  
  public java.lang.String getServiceSimpleList(java.lang.String deptcode, java.lang.String password, java.lang.String count, java.lang.String sortsign, java.lang.String times) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceSimpleList(deptcode, password, count, sortsign, times);
  }
  
  public java.lang.String getServiceSimpleListByChange(java.lang.String deptcode, java.lang.String password, java.lang.String count, java.lang.String sortsign, java.lang.String times, java.lang.String start_time, java.lang.String end_time) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceSimpleListByChange(deptcode, password, count, sortsign, times, start_time, end_time);
  }
  
  public java.lang.String getServiceNew(java.lang.String deptcode, java.lang.String password, java.lang.String serviceunid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceNew(deptcode, password, serviceunid);
  }
  
  public java.lang.String getServiceFileListNew(java.lang.String deptcode, java.lang.String password, java.lang.String materialunid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceFileListNew(deptcode, password, materialunid);
  }
  
  public java.lang.String getInfoListNew(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String handlestatesing, java.lang.String count, java.lang.String sortsign, java.lang.String times) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getInfoListNew(deptcode, password, starttime, endtime, handlestatesing, count, sortsign, times);
  }
  
  public boolean voidTime2(java.lang.String starttime, java.lang.String endtime) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.voidTime2(starttime, endtime);
  }
  
  public void getApasInfoXml(java.lang.Object sb, com.linewell.apas.info.ApasInfo apasinfo) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    apasInfoServiceImpl.getApasInfoXml(sb, apasinfo);
  }
  
  public java.lang.String getInfoListByMonitor(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String handlestatesing, java.lang.String count, java.lang.String sortsign, java.lang.String times) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getInfoListByMonitor(deptcode, password, starttime, endtime, handlestatesing, count, sortsign, times);
  }
  
  public java.lang.String getInfoCountNew(java.lang.String deptcode, java.lang.String password) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getInfoCountNew(deptcode, password);
  }
  
  public java.lang.String getInfoByProjid(java.lang.String deptcode, java.lang.String password, java.lang.String projid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getInfoByProjid(deptcode, password, projid);
  }
  
  public java.lang.String getInfoByProjidNew(java.lang.String projid, java.lang.String projpwd) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getInfoByProjidNew(projid, projpwd);
  }
  
  public java.lang.String deleteFavorite(java.lang.String unid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.deleteFavorite(unid);
  }
  
  public java.lang.String getOpinionList(java.lang.String deptcode, java.lang.String password, java.lang.String projid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getOpinionList(deptcode, password, projid);
  }
  
  public java.lang.String getAllCityServiceList(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String count, java.lang.String sortsign, java.lang.String times, java.lang.String cityDeptCode) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getAllCityServiceList(deptcode, password, starttime, endtime, count, sortsign, times, cityDeptCode);
  }
  
  public java.lang.String getAllCityInfoListNew(java.lang.String deptcode, java.lang.String password, java.lang.String starttime, java.lang.String endtime, java.lang.String handlestatesing, java.lang.String count, java.lang.String sortsign, java.lang.String times, java.lang.String cityDeptCode) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getAllCityInfoListNew(deptcode, password, starttime, endtime, handlestatesing, count, sortsign, times, cityDeptCode);
  }
  
  public java.lang.String validatesParam(java.lang.String deptcode, java.lang.String password, java.lang.String size, java.lang.String current) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.validatesParam(deptcode, password, size, current);
  }
  
  public java.lang.String getServiceListByCreditCode(java.lang.String deptcode, java.lang.String password, java.lang.String size, java.lang.String current, java.lang.String creditCode) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceListByCreditCode(deptcode, password, size, current, creditCode);
  }
  
  public java.lang.String getServiceListByCreditCodeAndCondition(java.lang.String deptcode, java.lang.String password, java.lang.String size, java.lang.String current, java.lang.String creditCode, java.lang.String condition) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceListByCreditCodeAndCondition(deptcode, password, size, current, creditCode, condition);
  }
  
  public java.lang.String getServiceListByChange(java.lang.String deptCode, java.lang.String passWord, java.lang.String size, java.lang.String current, java.lang.String creditCode, java.lang.String start_time, java.lang.String end_time) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceListByChange(deptCode, passWord, size, current, creditCode, start_time, end_time);
  }
  
  public java.lang.String voidTime(java.lang.String btime, java.lang.String etime) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.voidTime(btime, etime);
  }
  
  public java.lang.String getServiceListByChangeAndCondition(java.lang.String deptCode, java.lang.String passWord, java.lang.String size, java.lang.String current, java.lang.String creditCode, java.lang.String start_time, java.lang.String end_time, java.lang.String condition) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceListByChangeAndCondition(deptCode, passWord, size, current, creditCode, start_time, end_time, condition);
  }
  
  public java.lang.String getTradesDeptCode(java.lang.String deptcode, java.lang.String password, java.lang.String trades) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getTradesDeptCode(deptcode, password, trades);
  }
  
  public java.lang.String getServiceListByEngineering(java.lang.String citycode, java.lang.String password, java.lang.String size, java.lang.String current, java.lang.String isInit) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceListByEngineering(citycode, password, size, current, isInit);
  }
  
  public java.lang.String getServiceDetailByServiceUnid(java.lang.String deptCode, java.lang.String passWord, java.lang.String serviceUnid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceDetailByServiceUnid(deptCode, passWord, serviceUnid);
  }
  
  public java.lang.String getSNbyRowId(java.lang.String deptcode, java.lang.String password, java.lang.String rowid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getSNbyRowId(deptcode, password, rowid);
  }
  
  public boolean getTureTime() throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getTureTime();
  }
  
  public java.lang.String getServiceListByChangeforzj(java.lang.String size, java.lang.String current, java.lang.String creditCode, java.lang.String start_time, java.lang.String end_time) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceListByChangeforzj(size, current, creditCode, start_time, end_time);
  }
  
  public java.lang.String geDeptByAll(java.lang.String areaCode) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.geDeptByAll(areaCode);
  }
  
  public java.lang.String getServiceListByCreditCodeforzj(java.lang.String size, java.lang.String current, java.lang.String creditCode) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceListByCreditCodeforzj(size, current, creditCode);
  }
  
  public java.lang.String getServiceDetailByServiceUnidforzj(java.lang.String serviceUnid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceDetailByServiceUnidforzj(serviceUnid);
  }
  
  public java.lang.String getServiceCataRelationListNew(java.lang.String deptcode, java.lang.String password, java.lang.String serviceunid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceCataRelationListNew(deptcode, password, serviceunid);
  }
  
  public java.lang.String getDirectoryList(java.lang.String deptcode, java.lang.String password) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getDirectoryList(deptcode, password);
  }
  
  public java.lang.String getPromisesSerivce() throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getPromisesSerivce();
  }
  
  public java.lang.String getPromisesSerivce(java.lang.String deptCode, java.lang.String size, java.lang.String current, java.lang.String token, java.lang.String serviceName, java.lang.String infoprojid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getPromisesSerivce(deptCode, size, current, token, serviceName, infoprojid);
  }
  
  public boolean checkToken(java.lang.String detpcode, java.lang.String token) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.checkToken(detpcode, token);
  }
  
  public java.lang.String getJGSerivce(java.lang.String deptCode, java.lang.String type, java.lang.String size, java.lang.String current, java.lang.String token, java.lang.String serviceName, java.lang.String infoprojid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getJGSerivce(deptCode, type, size, current, token, serviceName, infoprojid);
  }
  
  public java.lang.String getJGService(java.lang.String type) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getJGService(type);
  }
  
  public java.lang.String getPromisesSerivceByTime(java.lang.String date, java.lang.String size, java.lang.String current, java.lang.String token, java.lang.String serviceName, java.lang.String infoprojid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getPromisesSerivceByTime(date, size, current, token, serviceName, infoprojid);
  }
  
  public java.lang.String getJGServiceByTime(java.lang.String date, java.lang.String type, java.lang.String size, java.lang.String current, java.lang.String token, java.lang.String serviceName, java.lang.String infoprojid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getJGServiceByTime(date, type, size, current, token, serviceName, infoprojid);
  }
  
  public java.lang.String getServiceBydirUnid(java.lang.String dirUnid, java.lang.String deptCode) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceBydirUnid(dirUnid, deptCode);
  }
  
  public java.lang.String getDirectoryByDeptCode(java.lang.String deptcode) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getDirectoryByDeptCode(deptcode);
  }
  
  public java.lang.String getServiceByInfoprojid(java.lang.String infoprojid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getServiceByInfoprojid(infoprojid);
  }
  
  public java.lang.String getdirBydirUnid(java.lang.String dirUnid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    return apasInfoServiceImpl.getdirBydirUnid(dirUnid);
  }
  
  public void downFileByUnid(java.lang.String attrUnid) throws java.rmi.RemoteException{
    if (apasInfoServiceImpl == null)
      _initApasInfoServiceImplProxy();
    apasInfoServiceImpl.downFileByUnid(attrUnid);
  }
  
  
}