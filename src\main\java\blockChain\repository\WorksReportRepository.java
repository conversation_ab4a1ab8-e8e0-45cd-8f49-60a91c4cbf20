package blockChain.repository;

import blockChain.entities.WorksReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2019/11/29 14:44
 */
@Repository
public interface WorksReportRepository extends JpaRepository<WorksReport, Integer>, QuerydslPredicateExecutor<WorksReport> {
    public WorksReport getById(Long id);
}
