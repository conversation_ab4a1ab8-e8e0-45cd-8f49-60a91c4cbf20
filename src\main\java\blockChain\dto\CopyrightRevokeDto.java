package blockChain.dto;

import blockChain.bean.BaseQueryParam;
import blockChain.dto.message.AlertMessageDto;
import blockChain.entities.CopyrightManager;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.models.auth.In;
import lombok.Data;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/17 16:35
 */
@Data
public class CopyrightRevokeDto extends BaseQueryParam {
  @JsonProperty("id")
  private Long registrationNum;// id
  /**
   * normal 代表没有撤销作品
   * revoked 代表已撤销的作品
   * revoking 撤销中
   */
  private InactiveTypeEnum inactiveType;
  private String cancelTheProofUrl;//撤销作品的撤销证明
  /**
   * 撤销作品的撤销证明File Id
   */
  private Long cancelTheProofFileId;

  /**
   * 撤销状态枚举
   */
  public enum InactiveTypeEnum {
    /**
     * 未撤销
     */
    NORMAL("normal"),
    /**
     * 撤销中
     */
    REVOKING("revoking"),
    /**
     * 已撤销
     */
    REVOKED("revoked");

    private String value;

    InactiveTypeEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static InactiveTypeEnum fromValue(String text) {
      for (InactiveTypeEnum b : InactiveTypeEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + text + "'");
    }

    /**
     * 转换 CopyrightManager.InactiveTypeValue
     * @return
     */
    public Integer convertToInactiveTypeValue(){
      switch (this) {
        case NORMAL:
          return CopyrightManager.InactiveTypeValue.NORMAL;
        case REVOKED:
          return CopyrightManager.InactiveTypeValue.REVOKED;
        case REVOKING:
          return CopyrightManager.InactiveTypeValue.REVOKING;
      }
      throw new IllegalArgumentException("InactiveTypeEnum can not convert");
    }

    public static Optional<InactiveTypeEnum> convertToInactiveTypeEnum(Integer val){
      if(val == null){
        return Optional.empty();
      }
      switch (val) {
        case 1:
          return Optional.of(InactiveTypeEnum.NORMAL);
        case 3:
          return Optional.of(InactiveTypeEnum.REVOKED);
        case 2:
          return Optional.of(InactiveTypeEnum.REVOKING);
      }
      throw new IllegalArgumentException("InactiveType can not convert");
    }


  }

  /**
   * 处理状态
   * accepted 接受
   * revoked 拒绝
   */
  private HandleTypeEnum handleType;
  /**
   * 拒绝原因
   */
  private String rejectReason;
  public enum HandleTypeEnum {
    /**
     * 接受
     */
    ACCEPTED("accepted"),
    /**
     * 拒绝
     */
    REVOKED("revoked");

    private String value;

    HandleTypeEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static InactiveTypeEnum fromValue(String text) {
      for (InactiveTypeEnum b : InactiveTypeEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + text + "'");
    }
  }



  // =============================以下为查询条件======================

  /**
   * 作品自增长的分类序列号
   */
  private String worksNum;//

  /**
   * 作品名称
   */
  private String productionName;//
  private String productionTypeId;// 作品类别ID
  private Integer rightOwnMode;// 权利归属方式
  /**
   * 代理人
   */
  private String agentName;//
  /**
   * 著作权人
   */
  private String copyrightName;//

  //当前查询用户市级权限
  private Integer visibleLevelCity;

  //当前查询用户县级权限
  private Integer visibleLevelCounty;

/*
  private LocalDateTime createTimeStart;
  private LocalDateTime createTimeEnd;*/


}
