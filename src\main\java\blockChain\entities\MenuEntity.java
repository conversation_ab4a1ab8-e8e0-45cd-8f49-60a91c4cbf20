package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name="tb_menulink")
@EntityListeners(AuditingEntityListener.class)
public class MenuEntity {

    public static final String DEFAULT_MENU = "Home";

	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

    /**
     * 菜单名称
     */
	private String menuName;

    /**
     * 父菜单id
     */
	private String parentId;

    /**
     * 菜单权限标识
     */
	private String menuUrl;

    /**
     * 菜单分类
     */
	private Integer sort;

    /**
     * 菜单图标
     */
	private String icon;
}
