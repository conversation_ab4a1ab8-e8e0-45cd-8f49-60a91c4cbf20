package blockChain.facade.service;

import blockChain.config.SpringConfig;
import blockChain.dto.DataRefluxDTO;
import blockChain.dto.ebus.*;
import blockChain.entities.*;
import blockChain.exception.EntityNotFoundException;
import blockChain.mapper.FormDataMapper;
import blockChain.service.AppTokenService;
import blockChain.service.FormDataService;
import blockChain.service.UserService;
import blockChain.utils.AesEncryptUtils;
import blockChain.utils.AlgorithmUtil;
import blockChain.utils.HttpClientUtils;
import blockChain.utils.SignatureUtils;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.jcajce.provider.digest.SM3;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@AllArgsConstructor
public class OutSideServiceFacade {
    private static final String sm4Key = "E39A92F08A855B815CE1C91E5059A3E1";

    private final SpringConfig config;

    private final FormDataService formDataService;
    private final AppTokenService appTokenService;
    private final DigitalServiceFacade digitalServiceFacade;
    private final UserService userService;

    public FormDataDTO getFormDataDetail(FormNumberQueryParam param, UserEntity userEntity) {
        FormDataEntity entity;
        List<FormDataEntity> dataEntities = formDataService.findOne(param.getName(), param.getCertificateNumber(), param.getIdCardType());
        LocalDateTime startOfToDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        if (!dataEntities.isEmpty() && dataEntities.get(0).getUpdateTime().isAfter(startOfToDay)) {
            entity = dataEntities.get(0);
        } else {
            String appToken;
            String comm_userVerifyCode;
            AppTokenEntity appTokenEntity = appTokenService.findLastToken();
            if (!ObjectUtils.isEmpty(appTokenEntity) && appTokenEntity.getExpirationTime().isAfter(LocalDateTime.now())) {
                appToken = appTokenEntity.getAppToken();
            } else {
                appToken = getAppToken();
            }

            if (userEntity.getIdentityKind() == 1) {
                param.setComm_userCheckIdCardType(String.valueOf(userEntity.getCardType()));
                param.setComm_userCheckIdCard(userEntity.getCardId());
                param.setComm_userCheckName(userEntity.getRealName());
            } else {
                if (StringUtils.hasText(userEntity.getJbrName())
                        && StringUtils.hasText(userEntity.getJbrCardId())
                        && userEntity.getJbrCardType() != null) {
                    param.setComm_userCheckIdCardType(String.valueOf(userEntity.getJbrCardType()));
                    param.setComm_userCheckIdCard(userEntity.getJbrCardId());
                    param.setComm_userCheckName(userEntity.getJbrName());
                } else if (StringUtils.hasText(userEntity.getLegalPersionName()) && StringUtils.hasText(userEntity.getLegalPersionIdcard())) {
                    param.setComm_userCheckIdCardType(String.valueOf(CopyrightOwner.CopyCertificateValue.IDCARD));
                    param.setComm_userCheckIdCard(userEntity.getLegalPersionName());
                    param.setComm_userCheckName(userEntity.getLegalPersionIdcard());
                } else {
                    log.error("闽政通返回信息不完整" + userEntity.getUserName());
                    return null;
                }
            }
            comm_userVerifyCode = personalAuthorization(
                    param.getComm_userCheckName(), param.getComm_userCheckIdCard(), param.getComm_userCheckIdCardType(), appToken);

            if (comm_userVerifyCode == null) return null;
            param.setComm_userVerifyCode(comm_userVerifyCode);

            getAuthInfo(param, userEntity);
            if (param.isNeedAuth() && StringUtils.isEmpty(param.getAuthCode())) {
                log.info("最多采一次，表单用数获取 未授权：{},{}->{},{}", param.getName(), param.getCertificateNumber(), userEntity.getRealName(), userEntity.getCardId());
                return null;
            }

            entity = getFormData(param, userEntity);
        }

        FormDataDTO retDTO = FormDataMapper.INSTANCE.toDTO(entity);
        log.info("最多采一次，表单用数获取，返回用户信息：" + retDTO);
        return retDTO;
    }

    public FormDataEntity getFormData(FormNumberQueryParam param, UserEntity userEntity) {
        try {
            Map<String, String> headers = getZsHeader();

            Map<String, String> params = new HashMap<>();
            if (param.getUserType().equals("1")) {
                // userType为1时，办理人的姓名和证件号码通过comm_userCheckName、comm_userCheckIdCard、idCardType3个参数传递
                params.put("userType", param.getUserType());
            } else {
                params.put("userType", "2");
                params.put("name", param.getName());
                params.put("certificateNumber", param.getCertificateNumber());
            }
            params.put("comm_userCheckIdCard", param.getComm_userCheckIdCard());
            params.put("comm_userCheckName", param.getComm_userCheckName());
            params.put("idCardType", param.getComm_userCheckIdCardType()); //证件类型
            params.put("projectId", param.getProjectId());

            params.put("modelKey", config.getZsModelKey());
            params.put("subItemCode", config.getServiceCode());
            params.put("dataFrom", "1");

            params.put("comm_ip", "**************");
            params.put("comm_mac", "0c:da:41:1d:18:ef");
            params.put("comm_queryCause", "作品登记查询用户信息"); // 查询企业注册信息
            params.put("comm_queryResume", "因作品登记需要, 需查询用户信息"); // 因业务需要，需查询某公司的注册信息
            params.put("comm_itemBaseDirectoryCode", "35103903300000");
            params.put("comm_itemCodeName", "作品自愿登记");
            params.put("comm_userVerifyCode", param.getComm_userVerifyCode());
            params.put("jc_user_name", param.getComm_userCheckName()); // login user
            params.put("jc_user_id_card", param.getComm_userCheckIdCard());
            params.put("jc_user_rylx", "002");
            params.put("jc_user_terminal_ip", userEntity.getIp());
            params.put("jc_user_mobile", userEntity.getPhoneNum());
            params.put("comm_netType", "1");
            params.put("comm_userVerifyAccessToken", userEntity.getOauthAccessToken());
            if (param.isNeedAuth()) {
                params.put("authType", param.getAuthType());
                params.put("authCode", param.getAuthCode());
            }

            log.info("getFormData param={}", params);
            // 1.sm4密钥
            // String sm4Key = AlgorithmUtil.generateSm4PrivateKey();
            // 2.请求数据sm4加密
            String content = new ObjectMapper().writeValueAsString(params);
            // System.out.println("使用SM4加密前的数据：" + content);
            String cipherText = AlgorithmUtil.encryptBySm4AndBase64(content, sm4Key);
            // System.out.println("使用SM4加密后的数据：" + cipherText);

//            log.info("getFormData cipherText param={}", cipherText);

            HttpClientUtils.HttpResponse httpResponse = HttpClientUtils.httpPost(config.getZsUrl() + config.getZsGetFormData(), cipherText, headers, null);
            log.info("【最多采一次】表单用数获取返回值：{}", httpResponse);
            if (httpResponse.getStatusCode() == 200) {
                String bodyData = httpResponse.getBody();
                JSONObject object = JSON.parseObject(bodyData);
                if (object.containsKey("code") && (Integer) object.get("code") == 0) {
                    String data = object.getString("data");
                    return formDataDeal(data, param, userEntity);
                }
            }
        } catch (Exception e) {
            log.error("【最多采一次】表单用数获取失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 表单数据处理
     */
    private FormDataEntity formDataDeal(String data, FormNumberQueryParam param, UserEntity userEntity) throws Exception {
        String plainText = AlgorithmUtil.decryptByBase64AndSm4(data, sm4Key);
        // plainText = "{\"subItemCode\":\"35073900100000\",\"dictType\":\"2\",\"fieldData\":[{\"standardFieldCode\":\"99999901520\",\"standardFieldName\":\"作品名称\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901521\",\"standardFieldName\":\"作品类别\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldValue\":\"陆升（福建）集团有限公司\",\"standardFieldCode\":\"50000059454\",\"standardFieldName\":\"申请人\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901522\",\"standardFieldName\":\"著作权人类别\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldValue\":\"其他有效身份证件\",\"standardFieldCode\":\"99999990081\",\"standardFieldName\":\"证件类型\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldValue\":\"S0229080D\",\"standardFieldCode\":\"99999901202\",\"standardFieldName\":\"证件号码\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldValue\":\"新加坡\",\"standardFieldCode\":\"99999901525\",\"standardFieldName\":\"著作权人国籍\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldValue\":\"福建省\",\"standardFieldCode\":\"99999901526\",\"standardFieldName\":\"著作权人所在省份\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldValue\":\"泉州市\",\"standardFieldCode\":\"99999901527\",\"standardFieldName\":\"著作权人所在城市\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldValue\":\"德化县\",\"standardFieldCode\":\"99999991572\",\"standardFieldName\":\"所属区县\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901528\",\"standardFieldName\":\"著作权人署名情况\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901529\",\"standardFieldName\":\"作品署名\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901530\",\"standardFieldName\":\"创作/制作完成日期\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901531\",\"standardFieldName\":\"创作/制作完成地点\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901532\",\"standardFieldName\":\"作品首次发表日期\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901533\",\"standardFieldName\":\"作品发表地点国家\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901534\",\"standardFieldName\":\"作品发表地点城市\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999990197\",\"standardFieldName\":\"联系电话\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999993229\",\"standardFieldName\":\"联系地址\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldValue\":\"362500\",\"standardFieldCode\":\"99999990042\",\"standardFieldName\":\"邮政编码\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999993426\",\"standardFieldName\":\"联系人\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999990011\",\"standardFieldName\":\"手机号码\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999990031\",\"standardFieldName\":\"电子邮箱\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999995007\",\"standardFieldName\":\"联系传真\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901535\",\"standardFieldName\":\"作品著作权登记代理申请人\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldValue\":\"0595-23557909\",\"standardFieldCode\":\"99999901536\",\"standardFieldName\":\"作品著作权登记被委托人联系电话\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901537\",\"standardFieldName\":\"作品著作权登记被委托人联系地址\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901538\",\"standardFieldName\":\"作品著作权登记被委托人邮政编码\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901539\",\"standardFieldName\":\"作品著作权登记被委托人联系人\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901540\",\"standardFieldName\":\"作品著作权登记被委托人联系人手机号码\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901541\",\"standardFieldName\":\"作品著作权登记被委托人电子邮箱\",\"standardFieldBody\":\"CJJS103\"},{\"standardFieldCode\":\"99999901542\",\"standardFieldName\":\"作品著作权登记被委托人联系传真\",\"standardFieldBody\":\"CJJS103\"}],\"formData\":[]}";
        JSONObject result = JSON.parseObject(plainText);
        log.info("【最多采一次】表单用数解密：{}", result);

        JSONArray array = result.getJSONArray("fieldData");
        FormDataEntity entity = new FormDataEntity();
        for (int i = 0; i < array.size(); i++) {
            JSONObject item = array.getJSONObject(i);
            String code = item.getString("standardFieldCode");
            String value = item.getString("standardFieldValue");
            if (ObjectUtils.isEmpty(value)) continue;

            Digital countries;
            Digital area;
            switch (code) {
                case "50000059454": // 申请人
                    entity.setApplyName(value);
                    break;
                case "99999990004": // 姓名
                    entity.setName(value);
                    break;
                case "99999901525": // 著作权人国籍
                    entity.setCountries(value);
                    area = digitalServiceFacade.getDictByDict_Name(value)
                            .stream()
                            .filter(digital -> digital.getLevel() == 1)
                            .findFirst().orElse(new Digital());
                    if (ObjectUtils.isEmpty(area)) break;
                    entity.setCertCountries(area.getId());
                    break;
                case "99999901526": // 著作权人所在省份
                    area = digitalServiceFacade.getDictByDict_Name(value.replace("省", ""))
                            .stream()
                            .filter(digital -> digital.getCode().equals("pn"))
                            .findFirst().orElse(new Digital());
                    if (ObjectUtils.isEmpty(area)) break;
                    entity.setProvince(area.getDict_name());
                    entity.setCertProvince(area.getId());

                    if (!StringUtils.isEmpty(entity.getCountries())) break;
                    countries = digitalServiceFacade.getById(area.getPid());
                    entity.setCountries(countries.getDict_name());
                    entity.setCertCountries(countries.getId());
                    break;
                case "99999901527": // 著作权人所在城市
                    entity.setCity(value);
                    area = digitalServiceFacade.getDictByDict_Name(value)
                            .stream()
                            .filter(digital -> digital.getLevel() == 3 && !digital.isLeaf())
                            .findFirst().orElse(new Digital());

                    if (ObjectUtils.isEmpty(area)) break;
                    entity.setCertCity(area.getId());

                    if (!StringUtils.isEmpty(entity.getProvince())) break;
                    Digital province = digitalServiceFacade.getById(area.getPid());
                    entity.setProvince(province.getDict_name());
                    entity.setCertProvince(province.getId());

                    if (!StringUtils.isEmpty(entity.getCountries())) break;
                    countries = digitalServiceFacade.getById(province.getPid());
                    entity.setCountries(countries.getDict_name());
                    entity.setCertCountries(countries.getId());
                    break;
                case "99999991572": // 所属区县
                    area = digitalServiceFacade.getDictByDict_Name(value)
                            .stream()
                            .filter(digital -> digital.getLevel() == 4
                                    || (digital.getLevel() == 3 && digital.isLeaf()))
                            .findFirst().orElse(new Digital());

                    if (ObjectUtils.isEmpty(area)) break;
                    entity.setCounty(area.getDict_name());
                    entity.setCertCounty(area.getId());
                    break;
                case "99999993229": // 联系地址
                    entity.setFullAddress(value);
                    break;
                default:
                    break;
            }
        }
        entity.setCertificateNumber(param.getCertificateNumber());

        log.info("【最多采一次】表单用数取得entity:{}", entity);
        if (StringUtils.isEmpty(entity.getProvince())) return null;

        List<FormDataEntity> entities = formDataService.findData(entity.getApplyName(), entity.getName(), entity.getCertificateNumber(), entity.getCountries(), entity.getProvince(), entity.getCity(), entity.getCounty(), entity.getFullAddress());
        if (entities.isEmpty()) {
            entity.setFieldData(array.toJSONString());
            entity.setUserType(param.getUserType());
            entity.setIdCardType(param.getIdCardType());
            entity.setCreateBy(userEntity.getUserId());
            formDataService.save(entity);
            return entity;
        } else {
            FormDataEntity existEntity = entities.get(entities.size() - 1);
            existEntity.setUpdateTime(LocalDateTime.now());
            existEntity.setUpdateBy(userEntity.getUserId());
            formDataService.save(existEntity);
            return existEntity;
        }
    }

    public void dataReflux(CopyrightManager manager) {
        log.info("表单数据回流,作品id：{}", manager.getRegistrationNum());
        try {
            List<CopyrightOwner> owners = manager.getOwnerList();
            for (CopyrightOwner owner : owners) {
                if (Objects.equals(owner.getProjectId(), manager.getProjectId()) && owner.getFormDataId() != null && owner.getFormDataId() != 0L) {
                    DataRefluxDTO dto = new DataRefluxDTO();
                    dto.setName(owner.getCopyName());
                    dto.setCertificateNumber(owner.getCopyIdCard());
                    dto.setSubjectType(owner.getCopyCategory() == CopyrightOwner.CopyCategoryValue.PEOPLE ? 1 : 2);
                    dataReflux(dto);
                }
            }
            List<Author> authors = manager.getAuthorList();
            for (Author author : authors) {
                if (Objects.equals(author.getProjectId(), manager.getProjectId()) && author.getFormDataId() != null && author.getFormDataId() != 0L) {
                    DataRefluxDTO dto = new DataRefluxDTO();
                    dto.setName(author.getAuthorName());
                    dto.setCertificateNumber(author.getAuthorIdCard());
                    dto.setSubjectType(author.getAuthorCategory() == CopyrightOwner.CopyCategoryValue.PEOPLE ? 1 : 2);
                    dataReflux(dto);
                }
            }
        } catch (Exception e) {
            log.error("表单数据回流失败：作品id={}, 原因={}", manager.getRegistrationNum(), e.getLocalizedMessage());
//            StackTraceElement[] stackTraceElements = e.getStackTrace();
//            for (StackTraceElement stackTrace : stackTraceElements) {
//                log.error(stackTrace.toString());
//            }
        }
    }

    /**
     * 表单数据回流
     */
    public Boolean dataReflux(DataRefluxDTO dto) {
        log.info("表单数据回流开始：{}", dto.getName());
        try {
            Map<String, String> headers = getZsHeader();
            headers.put("modelKey", config.getZsModelKey());

            Map<String, String> params = new HashMap<>();
            params.put("modelKey", config.getZsModelKey());
            params.put("name", dto.getName());
            params.put("certificateNumber", dto.getCertificateNumber());
            params.put("subItemCode", config.getServiceCode());
            params.put("subjectType", dto.getSubjectType().toString());

            // 1.sm4密钥
            // String sm4Key = AlgorithmUtil.generateSm4PrivateKey();

            // 2.请求数据sm4加密
            String content = new ObjectMapper().writeValueAsString(params);
            String cipherText = AlgorithmUtil.encryptBySm4AndBase64(content, sm4Key);
            System.out.println("表单数据回流 请求体：" + cipherText);
            HttpClientUtils.HttpResponse httpResponse = HttpClientUtils.httpPost(config.getZsUrl() + config.getZsFormDataReflux(), cipherText, headers, null);
            if (httpResponse.getStatusCode() == 200) {
                String bodyData = httpResponse.getBody();
                JSONObject object = JSON.parseObject(bodyData);
                if (object.containsKey("code") && (Integer) object.get("code") == 0) {
                    log.info("表单数据回流成功：{}", dto.getName());
                    return true;
                }
            }
            log.info("表单数据回流失败：{}, 原因：{}", dto.getName(), httpResponse);
        } catch (Exception e) {
            log.error("表单数据回流失败：{}, 原因：{}", dto.getName(), e.getMessage());
        }
        return false;
    }

    public Map<String, String> getZsHeader() {
        //该方法会根据时间戳，随机数，paadId,paasToken生成签名，并且放入request-header中
        Map<String, String> headers;
        try {
            headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        // 3.对sm4密钥进行sm2加密
        String encryptSM4Key = AlgorithmUtil.encryptBySm2AndBase64(sm4Key, config.getZsSm2PublicKey());
//        System.out.println("使用SM2加密后的SM4密钥：" + encryptSM4Key);
        headers.put("X-TB-KeyInfo", encryptSM4Key);
        log.info("zsHeader: {}", headers);
        return headers;
    }

    /**
     * 数字身份管理服务平台公共请求头
     */
    public Map<String, String> getHeader(Map<String, String> headers, JSONObject params) {
//        Map<String, String> headers = new HashMap<>();
        // appId+appSecret+timestamp+randomNumber+每个接口中的含有 body 请求体敏感字段

        String appId = config.getAppId();
        String appSecret = config.getAppSecret();
        long timestamp = System.currentTimeMillis() / 1000;
        String randomNumber = RandomUtil.randomString(16);
        StringBuilder sb = new StringBuilder();
        sb.append(appId).append(appSecret).append(timestamp).append(randomNumber);
        if (!StringUtils.isEmpty(params.getString("name"))) sb.append(" ").append(params.getString("name"));
        if (!StringUtils.isEmpty(params.getString(("card")))) sb.append(params.getString("card"));

        if (!StringUtils.isEmpty(params.getString(("authorizerName")))) sb.append(params.getString("authorizerName"));
        if (!StringUtils.isEmpty(params.getString("authorizerIdCard"))) sb.append(params.getString("authorizerIdCard"));

        if (!StringUtils.isEmpty(params.getString(("authorizerCompanyName"))))
            sb.append(params.getString("authorizerCompanyName"));
        if (!StringUtils.isEmpty(params.getString("authorizerCompanySCCode")))
            sb.append(params.getString("authorizerCompanySCCode"));

        if (!StringUtils.isEmpty(params.getString("authorizedIdCard"))) sb.append(params.getString("authorizedIdCard"));
        if (!StringUtils.isEmpty(params.getString("authorizedName"))) sb.append(params.getString("authorizedName"));

        log.info("数字身份管理服务平台公共请求头 加密前字符串：{}", sb);
        MessageDigest digest = new SM3.Digest();
        // 2. 计算消息摘要
        byte[] hash = digest.digest(sb.toString().getBytes(StandardCharsets.UTF_8));

        // 3. 将结果转换为十六进制字符串
        String hexString = Hex.toHexString(hash);
        log.info("数字身份管理服务平台公共请求头 加密后字符串：{}", hexString);

        // 4. 输出加密后的十六进制字符串
        headers.put("encSign", hexString);
        headers.put("timestamp", String.valueOf(timestamp));
        headers.put("randomNumber", randomNumber);
        return headers;
    }

    /**
     * 获取应用令牌
     */
    public String getAppToken() {
        try {
            log.info("闽政通-获取应用令牌开始");
//            Map<String, String> headers = getHeader("", "");
            Map<String, String> headers;
            try {
                headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
            } catch (Exception e) {
                e.printStackTrace();
                log.error("闽政通-获取应用令牌失败: " + e.getMessage());
                return null;
            }

            JSONObject params = new JSONObject();
            params.put("appId", config.getAppId());
            params.put("appSecret", config.getAppSecret());

            HttpResponse httpResponse = HttpClientUtils.post(config.getMztUrl() + config.getMztAppToken(), params.toJSONString(), headers, null);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                String result = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
                JSONObject object = JSON.parseObject(result);

                if (object.containsKey("code") && (Integer) object.get("code") == 200) {
                    JSONObject data = object.getJSONObject("data");
                    String appToken = data.getString("app_token");
                    Integer expiresIn = data.getInteger("expires_in");// 令牌有效时长（分钟）

                    AppTokenEntity appTokenEntity = new AppTokenEntity();
                    appTokenEntity.setAppToken(appToken);
                    appTokenEntity.setExpirationTime(LocalDateTime.now().plusMinutes(expiresIn));
                    appTokenService.save(appTokenEntity);

                    log.info("闽政通-获取应用令牌成功 :" + appToken);
                    return appToken;
                }
            } else {
                log.error("闽政通-获取应用令牌失败2: " + httpResponse);
            }
        } catch (Exception e) {
            log.error("闽政通-获取应用令牌失败3: " + e.getMessage());
        }
        return null;
    }

    /**
     * 身份凭证生成
     */
    public String personalAuthorization(String name, String card, String idCardType, String appToken) {
        log.info("闽政通-身份凭证生成开始, name={}, card={}", name, card);
//        Map<String, String> headers = getHeader(name, card);
        Map<String, String> headers;
        try {
            headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("闽政通-身份凭证生成失败: " + e.getMessage());
            return null;
        }
        headers.put("app-token", appToken);

        JSONObject idcardinfo = new JSONObject();
        idcardinfo.put("name", name);
        // 证件类型（选择实体证必填） 111：身份证 414：普通护照 516：港澳居民往来内地通行证 511：台湾居民往来内地通行证
        idcardinfo.put("idCardType", idCardType);
        idcardinfo.put("idCard", card);

        JSONObject credential = new JSONObject();
        credential.put("credentialtype", 1); // 1:实体证
        credential.put("idcardinfo", idcardinfo);

        JSONObject params = new JSONObject();
        params.put("businessId", String.valueOf(0));
        params.put("idCardType", idCardType);
        params.put("idCardno", card);
        params.put("name", name);
        params.put("credentials", credential);

        try {
            HttpClientUtils.HttpResponse httpResponse = HttpClientUtils.httpPost(config.getMztUrl() + config.getMztPersonalAuthorization(), params.toJSONString(), headers, null);
            log.info("闽政通-身份凭证生成返回值：{}", httpResponse);
            if (httpResponse.getStatusCode() == 200) {
                String bodyData = httpResponse.getBody();
                JSONObject object = JSON.parseObject(bodyData);
                if (object.containsKey("identitycertificate")) {
                    log.info("闽政通-身份凭证生成成功");
                    return object.getString("identitycertificate");
                }
            }
            log.error("闽政通-身份凭证生成失败, name={}, card={}, 原因={}", name, card, httpResponse);
        } catch (Exception e) {
            log.error("闽政通-身份凭证生成失败, name={}, card={}, 原因={}", name, card, e.getMessage());
//            e.printStackTrace();
        }
        return null;
    }

    /**
     * 身份凭证核验
     */
    public String PAVerification(String identityCertificate, String appToken) {
//        Map<String, String> headers = getHeader("", "");
        Map<String, String> headers;
        try {
            headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("闽政通-身份凭证核验失败: " + e.getMessage());
            return null;
        }
        headers.put("app-token", appToken);

        JSONObject params = new JSONObject();
        params.put("identityCertificate", identityCertificate);
        params.put("businessId", 0);

        try {
            HttpClientUtils.HttpResponse httpResponse = HttpClientUtils.httpPost(config.getMztUrl() + config.getMztPAVerification(), params.toJSONString(), headers, null);
            if (httpResponse.getStatusCode() == 200) {
                String bodyData = httpResponse.getBody();
                JSONObject object = JSON.parseObject(bodyData);
                if (object.containsKey("idCard")) {
                    return object.toJSONString();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public FormDataEntity findFormDataById(Long formDataId) {
        return formDataService.findById(formDataId).orElseThrow(() -> new EntityNotFoundException("中枢表单不存在，请重新获取"));
    }

    /**
     * 第三方获取 Access Token
     */
    public JSONObject getAccessToken(String appToken, String code, String refreshToken, String grantType) {
        try {
            Map<String, String> headers;
            try {
                headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
            } catch (Exception e) {
                e.printStackTrace();
                log.error("闽政通-获取Access Token失败: " + e.getMessage());
                return null;
            }
            headers.put("App-Token", appToken);
            headers.put("Accept-Certificate", config.getAppId());

            JSONObject params = new JSONObject();
            params.put("grant_type", grantType);
            if (!StringUtils.isEmpty(code))
                params.put("code", code);
            if (!StringUtils.isEmpty(refreshToken))
                params.put("refresh_token", refreshToken);
            params.put("redirect_uri", config.getFrontUrl() + "indexPage");

            log.info("闽政通-获取Access Token params：" + params);
            HttpResponse httpResponse = HttpClientUtils.post(config.getMztUrl() + config.getMztAccessToken(), params.toJSONString(), headers, null);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                String result = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
                JSONObject object = JSON.parseObject(result);
                log.info("闽政通-获取Access Token object：" + object);
                if (object.containsKey("code") && object.getInteger("code") == 200) {
                    return object.getJSONObject("data");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("闽政通-获取Access Token失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 第三方获取用户信息
     */
    public UserInfoData getUserInfo(String appToken, String accessToken) {
        LinkedHashMap<String, String> headers;
        try {
            headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("闽政通-第三方获取用户信息失败: " + e.getMessage());
            return null;
        }
        headers.put("App-Token", appToken);
        headers.put("Accept-Certificate", config.getAppId());
        headers.put("Authorization", accessToken);

//        log.info("闽政通-第三方获取用户信息headers: {}", headers);
//        log.info("闽政通-第三方获取用户信息url: {}", config.getMztUrl() + config.getMztUserInfo());
        try {
            HttpResponse httpResponse = HttpClientUtils.get(config.getMztUrl() + config.getMztUserInfo(), null, headers);
            String uuid = UUID.randomUUID().toString();
//            log.info(uuid + "闽政通-第三方获取用户信息 response：{}", httpResponse);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                String result = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
                JSONObject object = JSON.parseObject(result);
                log.info("闽政通-第三方获取用户信息 详细 : " + object);
                if (!object.containsKey("code") || object.getInteger("code") != 200) {
                    return null;
                }
                JSONObject dataObject = object.getJSONObject("data");
                UserInfoData userInfoData = new UserInfoData();
                UserInfo userInfo = new UserInfo();
                if (dataObject.containsKey("userInfoStr")) {
                    String plainText = AesEncryptUtils.decrypt(dataObject.getString("userInfoStr"));
                    JSONObject userInfoJson = JSON.parseObject(plainText);
                    log.info(uuid + "闽政通-第三方获取用户信息userInfoStr解密后信息：{}", userInfoJson);
                    JSONObject data = userInfoJson.getJSONObject("data");
                    if (!ObjectUtils.isEmpty(data)) {
                        String userInfoStr = data.getString("userInfo"); // 个人扩展信息
                        if (!StringUtils.isEmpty(userInfoStr)) {
                            userInfo = JSON.parseObject(userInfoStr, UserInfo.class);
                        }
                        JSONObject userCustomize = data.getJSONObject("userCustomize"); // 用户企业扩展信息
                        if (!ObjectUtils.isEmpty(userCustomize) && StringUtils.hasText(userCustomize.getString("customizeValues"))) {
                            CustomizeValues customizeValues = JSON.parseObject(userCustomize.getString("customizeValues"), CustomizeValues.class);
                            userInfoData.setCustomizeValues(customizeValues);
                        }
                    }
                }
                if (dataObject.containsKey("companyInfo") && StringUtils.hasText(dataObject.getString("companyInfo"))) {
                    CompanyInfo companyInfo = JSON.parseObject(dataObject.getString("companyInfo"), CompanyInfo.class);
                    userInfoData.setCompanyInfo(companyInfo);
                }

                if (dataObject.containsKey("comUser") && StringUtils.hasText(dataObject.getString("comUser"))) {
                    JSONObject comUserStr = dataObject.getJSONObject("comUser"); // 用户企业关联信息
                    if (!ObjectUtils.isEmpty(comUserStr)) {
                        userInfo.setId(comUserStr.getString("id"));
                        userInfo.setFjUserId(comUserStr.getString("fjUserId"));
                    }
                }
                userInfoData.setUserInfo(userInfo);
                log.info(uuid + "闽政通-第三方获取用户信息DTO：{}", userInfoData);
                return userInfoData;
            } else {
                log.info("闽政通-第三方获取用户信息失败: " + httpResponse);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("闽政通-第三方获取用户信息失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 验证用户令牌
     */
    @Transactional
    public Map<String, Object> verify(UserEntity user) {
        Map<String, Object> resultMap = new HashMap<>();
        String appToken = getAppToken2();
        if (ObjectUtils.isEmpty(user.getOauthAccessTokenExpireTime()) || ObjectUtils.isEmpty(user.getOauthAccessToken()) || ObjectUtils.isEmpty(user.getOauthRefreshToken())) {
            resultMap.put("message", "该用户登录已过期或未进行实名认证，请重新登录或登录闽政通APP进行实名认证后再进行登录。");
            resultMap.put("result", "failed");
            return resultMap;
        }

        if (user.getOauthAccessTokenExpireTime().isAfter(LocalDateTime.now())
                && user.getOauthAccessTokenExpireTime().minusMinutes(10).isBefore(LocalDateTime.now())) {
            JSONObject data = getAccessToken(appToken, null, user.getOauthRefreshToken(), "refresh_token");
            if (data != null && data.containsKey("access_token") && data.containsKey("refresh_token") && data.containsKey("expires_in")) {
                String accessToken = data.getString("access_token");
                String refreshToken = data.getString("refresh_token");
                Integer expiresIn = data.getInteger("expires_in");
                user.setOauthAccessToken(accessToken);
                user.setOauthRefreshToken(refreshToken);
                user.setOauthAccessTokenExpireTime(LocalDateTime.now().plusMinutes(expiresIn - 10));
                userService.save(user);
            }
        }

        LinkedHashMap<String, String> headers;
        try {
            headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("闽政通-验证用户令牌失败: " + e.getMessage());
            resultMap.put("result", "failed");
            return resultMap;
        }
        headers.put("App-Token", appToken);
        headers.put("Authorization", user.getOauthAccessToken());
        headers.put("Accept-Certificate", config.getAppId());

        HttpResponse httpResponse = null;
        HttpEntity entity = null;
        JSONObject object = null;
        try {
            httpResponse = HttpClientUtils.get(config.getMztUrl() + config.getMztVerify(), null, headers);
            entity = httpResponse.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity, "UTF-8");
                object = JSON.parseObject(result);
                if (object.containsKey("code") && object.getInteger("code") == 200) {
//                    log.info("闽政通-验证用户令牌 返回值：" + object);
                    resultMap.put("result", "success");
                } else {
                    log.error("闽政通-验证用户令牌 返回值：" + object);
                    resultMap.put("result", "failed");
                    resultMap.put("message", object.getString("msg"));
                }
            }
        } catch (Exception e) {
            log.error("闽政通-验证用户令牌 headers: {}", headers);
            log.error("闽政通-验证用户令牌 返回值: httpResponse={}, entity={}, object={}", httpResponse, entity, object);
            log.error("闽政通-验证用户令牌失败: {}", e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "failed");
        }
        return resultMap;
    }

    /**
     * 用户退出登录
     */
    public void logout(UserEntity user) {
        if (StringUtils.isEmpty(user.getOauthAccessToken())) return;

        String appToken = getAppToken2();
        LinkedHashMap<String, String> headers;
        try {
            headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("闽政通-用户退出登录失败: " + e.getMessage());
            return;
        }

        headers.put("App-Token", appToken);
        headers.put("Authorization", user.getOauthAccessToken());
        headers.put("Accept-Certificate", config.getAppId());
        log.info("闽政通-用户退出登录 headers: {}", headers);

        JSONObject params = new JSONObject();
        params.put("appId", config.getAppId());
        log.info("闽政通-用户退出登录 params: {}", params);

        try {
            HttpResponse httpResponse = HttpClientUtils.post(config.getMztUrl() + config.getMztLogout(), params.toJSONString(), headers, null);
            log.info("闽政通-用户退出登录 返回值" + httpResponse);
            HttpEntity entity = httpResponse.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity, "UTF-8");
                JSONObject object = JSON.parseObject(result);
                log.info("闽政通-用户退出登录 object" + object);
                if (object.containsKey("code") && object.getInteger("code") == 200) {
                    user.setOauthAccessTokenExpireTime(LocalDateTime.now().minusSeconds(1));
                    userService.save(user);
                    return;
                } else {
                    log.error("闽政通-用户退出登录失败: " + httpResponse);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("闽政通-用户退出登录失败: " + e.getMessage());
        }
        return;
    }

    private String getAppToken2() {
        String appToken;
        AppTokenEntity appTokenEntity = appTokenService.findLastToken();
        if (!ObjectUtils.isEmpty(appTokenEntity) && appTokenEntity.getExpirationTime().isAfter(LocalDateTime.now())) {
            appToken = appTokenEntity.getAppToken();
        } else {
            appToken = getAppToken();
        }
        return appToken;
    }

    /**
     * 获取授权类型和授权凭证码
     */
    private void getAuthInfo(FormNumberQueryParam param, UserEntity userEntity) {
        if (userEntity.getIdentityKind() == 1) {
            if (param.getName().equals(userEntity.getRealName()) && param.getCertificateNumber().equals(userEntity.getCardId())) {
                param.setNeedAuth(false);
                return;
            } else if (param.getUserType().equals("1")) {
                param.setAuthType("1");
            } else if (param.getUserType().equals("2")) {
                param.setAuthType("3");
            }
        } else {
            if (param.getName().equals(userEntity.getRealName()) && param.getCertificateNumber().equals(userEntity.getCardId())) {
                param.setNeedAuth(false);
            } else if (param.getUserType().equals("1")) {
                param.setAuthType("2");
            }
        }

        if (StringUtils.isEmpty(param.getAuthType())) return;

        param.setAuthCode(getAuthCode(param, userEntity));
    }

    private String getAuthCode(FormNumberQueryParam param, UserEntity userEntity) {
        JSONObject params;
        String url;
        JSONObject object;
        switch (param.getAuthType()) {
            case "1":
                // 授权类型：1-核验本人授权他人服务
                params = personAgencyQueryParams(param);
                url = config.getPersonAgencyQuery();
                object = agencyQuery(params, url);
                if (!ObjectUtils.isEmpty(object)) {
                    JSONObject data = object.getJSONObject("data");
                    return data.getString("authCode");
                }
                break;
            case "2":
                String authCode = null;
                // 授权类型：3-核验企业授权经办人服务
                params = attnAgencyQueryParams(param, userEntity);
                url = config.getAttnAgencyQuery();
                object = agencyQuery(params, url);
                if (!ObjectUtils.isEmpty(object)) {
                    JSONObject data = object.getJSONObject("data");
                    authCode = data.getString("authCode");
                }

                if (!StringUtils.isEmpty(authCode)) {
                    params = new JSONObject();
                    // 授权类型：2-核验本人授权企业服务
                    params = companyAgencyQueryParams(param, userEntity);
                    url = config.getCompanyAgencyQuery();
                    object = agencyQuery(params, url);
                    if (!ObjectUtils.isEmpty(object)) {
                        JSONArray data = object.getJSONArray("data");
                        return authCode + ',' + data.getJSONObject(0).getString("authCode");
                    }
                }
                break;
            case "3":
                // 授权类型：3-核验企业授权经办人服务
                params = attnAgencyQueryParams(param, userEntity);
                url = config.getAttnAgencyQuery();
                object = agencyQuery(params, url);
                if (!ObjectUtils.isEmpty(object)) {
                    JSONObject data = object.getJSONObject("data");
                    return data.getString("authCode");
                }
                break;
        }
        return null;
    }

    /**
     * 代办授权查询服务
     */
    private JSONObject agencyQuery(JSONObject params, String url) {
        String appToken = getAppToken2();

        LinkedHashMap<String, String> headers;
        try {
            headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
        } catch (Exception e) {
            log.error("最多采一次-核验授权服务 失败: " + e.getMessage());
            return null;
        }
        getHeader(headers, params);
        headers.put("app-Token", appToken);

        try {
            log.info("最多采一次-核验授权服务失败 参数: " + params.toJSONString());
            HttpResponse httpResponse = HttpClientUtils.post(config.getMztUrl() + url, params.toJSONString(), headers, null);
            HttpEntity entity = httpResponse.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity, "UTF-8");
                JSONObject object = JSON.parseObject(result);
                log.info("最多采一次-核验授权服务失败 结果: " + result);
                if (object.containsKey("code") && object.getInteger("code") == 200) {
                    return object;
                }
            }
        } catch (Exception e) {
            log.error("最多采一次-核验授权服务失败 失败: " + e.getMessage());
        }
        return null;
    }

//    /**
//     * 核验本人授权他人服务
//     */
//    private String personAgencyQuery(FormNumberQueryParam param) {
//
//        String appToken = getAppToken2();
//
//        JSONObject params = new JSONObject();
//        params.put("authorizerIdCardType", param.getIdCardType());
//        params.put("authorizerIdCard", param.getCertificateNumber());
//        params.put("authorizerName", param.getName());
//        params.put("authorizedIdCardType", param.getComm_userCheckIdCardType());
//        params.put("authorizedIdCard", param.getComm_userCheckIdCard());  //
//        params.put("authorizedName", param.getComm_userCheckName());    // 被授权者姓名
//        params.put("matterCode", config.getServiceCode());
//
//        LinkedHashMap<String, String> headers;
//        try {
//            headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
//        } catch (Exception e) {
//            log.error("闽政通-核验本人授权他人服务失败: " + e.getMessage());
//            return null;
//        }
//        getHeader(headers, params);
//        headers.put("app-Token", appToken);
//
//        try {
//            log.info("闽政通-核验本人授权他人服务: " + params.toJSONString());
//            HttpResponse httpResponse = HttpClientUtils.post(config.getMztUrl() + config.getPersonAgencyQuery(), params.toJSONString(), headers, null);
//            HttpEntity entity = httpResponse.getEntity();
//            if (entity != null) {
//                String result = EntityUtils.toString(entity, "UTF-8");
//                JSONObject object = JSON.parseObject(result);
//                log.info("闽政通-核验本人授权他人服务结果: " + result);
//                if (object.containsKey("code") && object.getInteger("code") == 200) {
//                    JSONObject data = object.getJSONObject("data");
//                    return data.getString("authCode");
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("闽政通-核验本人授权他人服务失败: " + e.getMessage());
//        }
//        return null;
//    }
//
//    /**
//     * 核验本人授权企业服务
//     */
//    private String companyAgencyQuery(FormNumberQueryParam param, UserEntity userEntity) {
//        String appToken = getAppToken2();
//
//        JSONObject params = new JSONObject();
//        params.put("authorizerIdCardType", param.getIdCardType());
//        params.put("authorizerIdCard", param.getCertificateNumber());
//        params.put("authorizerName", param.getName());
////        params.put("authorizedIdCardType", param.getComm_userCheckIdCardType());
//        params.put("authorizedCompanySCCode", userEntity.getCardId());  //
//        params.put("authorizedCompanyName", userEntity.getRealName());    // 被授权企业名称
//        params.put("matterCode", config.getServiceCode());
//
//        LinkedHashMap<String, String> headers;
//        try {
//            headers = SignatureUtils.computeSignatureHeaders(config.getZsPaasid(), config.getZsToken());
//        } catch (Exception e) {
//            log.error("闽政通-核验本人授权企业服务失败: " + e.getMessage());
//            return null;
//        }
//        getHeader(headers, params);
//        headers.put("app-Token", appToken);
//
//        try {
//            log.info("闽政通-核验本人授权企业服务: " + params.toJSONString());
//            HttpResponse httpResponse = HttpClientUtils.post(config.getMztUrl() + config.getPersonAgencyQuery(), params.toJSONString(), headers, null);
//            HttpEntity entity = httpResponse.getEntity();
//            if (entity != null) {
//                String result = EntityUtils.toString(entity, "UTF-8");
//                JSONObject object = JSON.parseObject(result);
//                log.info("闽政通-核验本人授权企业服务结果: " + result);
//                if (object.containsKey("code") && object.getInteger("code") == 200) {
//                    JSONArray data = object.getJSONArray("data");
//                    return data.getJSONObject(0).getString("authCode");
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("闽政通-核验本人授权他人服务失败: " + e.getMessage());
//        }
//        return null;
//    }

    private JSONObject personAgencyQueryParams(FormNumberQueryParam param) {
        JSONObject params = new JSONObject();
        params.put("authorizerIdCardType", param.getIdCardType());
        params.put("authorizerIdCard", param.getCertificateNumber());
        params.put("authorizerName", param.getName());
        params.put("authorizedIdCardType", param.getComm_userCheckIdCardType());
        params.put("authorizedIdCard", param.getComm_userCheckIdCard());  //
        params.put("authorizedName", param.getComm_userCheckName());    // 被授权者姓名
        params.put("matterCode", config.getServiceCode());
        return params;
    }

    /**
     * 个人授权企业
     * 自然人授权指定企业授权代办关系查询
     */
    private JSONObject companyAgencyQueryParams(FormNumberQueryParam param, UserEntity userEntity) {
        JSONObject params = new JSONObject();
        params.put("authorizerIdCardType", param.getIdCardType());
        params.put("authorizerIdCard", param.getCertificateNumber());
        params.put("authorizerName", param.getName());
//        params.put("authorizedIdCardType", param.getComm_userCheckIdCardType());
        params.put("authorizedCompanySCCode", userEntity.getCardId());  //
        params.put("authorizedCompanyName", userEntity.getRealName());    // 被授权企业名称
        params.put("matterCode", config.getServiceCode());
        params.put("pageSize", 1);
        params.put("pageNum", 100);
        return params;
    }

    /**
     * 企业授权个人 （企业法人授权指定代办人授权关系查询）
     */
    private JSONObject attnAgencyQueryParams(FormNumberQueryParam param, UserEntity userEntity) {
        JSONObject params = new JSONObject();
//        params.put("authorizerIdCardType", param.getIdCardType());
        params.put("authorizerCompanySCCode", userEntity.getCardId());
        params.put("authorizerCompanyName", userEntity.getRealName());
        params.put("authorizedIdCardType", param.getComm_userCheckIdCardType());
        params.put("authorizedIdCard", param.getComm_userCheckIdCard());  //
        params.put("authorizedName", param.getComm_userCheckName());    // 被授权者姓名
        params.put("matterCode", config.getServiceCode());
        return params;
    }
}
