package blockChain.config;


import blockChain.facade.service.CopyrightManagerServiceFacade;
import lombok.AllArgsConstructor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ApplicationRunnerBlockChainUpImpl implements ApplicationRunner {

  private CopyrightManagerServiceFacade facade;

  @Override
  public void run(ApplicationArguments args) throws Exception {
    facade.blockChainUpScheduling();
  }
}
