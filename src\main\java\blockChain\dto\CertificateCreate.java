package blockChain.dto;

import java.util.List;

public class CertificateCreate {
	// 证书生成用

	private String mainPicStr; // 源证书路径
	private List<String> picsUrl; //图片路径集合
	private List<Integer> picsXY; //图片打印位置范围
	private List<String> Strings; //文字信息
	private List<Integer> copyownerTPointXY; //文字信息打印位置范围
	private String resultUrl; //最终证书生成路径
	private String font_family; //字体
	//private int font_size; //字号(px)
	private List<Integer> font_size;
	
	
	public String getMainPicStr() {
		return mainPicStr;
	}
	public void setMainPicStr(String mainPicStr) {
		this.mainPicStr = mainPicStr;
	}
	public List<String> getPicsUrl() {
		return picsUrl;
	}
	public void setPicsUrl(List<String> picsUrl) {
		this.picsUrl = picsUrl;
	}
	public List<String> getStrings() {
		return Strings;
	}
	public void setStrings(List<String> strings) {
		Strings = strings;
	}
	public String getResultUrl() {
		return resultUrl;
	}
	public void setResultUrl(String resultUrl) {
		this.resultUrl = resultUrl;
	}
	public List<Integer> getPicsXY() {
		return picsXY;
	}
	public void setPicsXY(List<Integer> picsXY) {
		this.picsXY = picsXY;
	}
	public List<Integer> getCopyownerTPointXY() {
		return copyownerTPointXY;
	}
	public void setCopyownerTPointXY(List<Integer> copyownerTPointXY) {
		this.copyownerTPointXY = copyownerTPointXY;
	}
	public String getFont_family() {
		return font_family;
	}
	public void setFont_family(String font_family) {
		this.font_family = font_family;
	}
	public List<Integer> getFont_size() {
		return font_size;
	}
	public void setFont_size(List<Integer> font_size) {
		this.font_size = font_size;
	}
}


