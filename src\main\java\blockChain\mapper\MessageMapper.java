package blockChain.mapper;

import blockChain.dto.message.MessageDto;
import blockChain.entities.message.MessageEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/18 17:21
 */
@Mapper(config = CommonConfig.class)
public interface MessageMapper {

  MessageMapper INSTANCE = Mappers.getMapper(MessageMapper.class);

  MessageDto toMessageDto(MessageEntity messageEntity);

  List<MessageDto> toMessageDtoList(List<MessageEntity> messageEntityList);

  @Mappings({
    @Mapping(target = "id", ignore = true),
    @Mapping(target = "uuid", ignore = true),
    @Mapping(target = "creator", ignore = true),
    @Mapping(target = "createTime", ignore = true),
    @Mapping(target = "updateTime", ignore = true),
    @Mapping(target = "users", ignore = true),
  })
  void update(MessageDto dto, @MappingTarget MessageEntity messageEntity);
}
