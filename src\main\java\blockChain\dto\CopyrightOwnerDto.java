package blockChain.dto;

import blockChain.entities.CertificateEntity;
import blockChain.entities.UploadAttachment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

@Data
public class CopyrightOwnerDto {
    private Long id;
    private String copyName;// 著作权人姓名
    private String copyCountries;// 国籍
    private String copyProvince;// 省份
    private String copyCity;// 所在城市
    private String copyCounty;// 所在区县
    private int copyCategory;// 著作权人类别
    private String copyCategorys;//著作权人类别
    private int copyCertificate;// 证件类型
    private String copyCertificates;// 证件类型
    private byte isMinority;// 是否少数民族 (1是)
    private String copyIdCard;// 证件号
    private int copySignature;// 署名情况
    private String copySignatureName;// 别名
    private int copyClassification;//分别是草稿箱中保存的数据，还是作品提交的数据

    private String copy_typeString; //著作权人类别(文字)
    private String copy_idTypeString; //证件类型(文字)
    private String copySignature_String; //署名情况(文字)

    private List<String> copyAreaNames = null;
    private String copyAreaNamesStr;
    private Boolean noDeleteUpdate;
    private int seq;
    private CertificateEntity copyCertificateZM; //著作权人电子证照
    private UploadAttachment copyIdCardZM; //著作权人证件
    @JsonIgnore
    private Long formDataId; // 中枢表单用数ID
    private String projectId; // 统一收件码


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCopyName() {
        return copyName;
    }

    public void setCopyName(String copyName) {
        this.copyName = copyName;
    }

    public String getCopyCountries() {
        return copyCountries;
    }

    public void setCopyCountries(String copyCountries) {
        this.copyCountries = copyCountries;
    }

    public String getCopyProvince() {
        return copyProvince;
    }

    public void setCopyProvince(String copyProvince) {
        this.copyProvince = copyProvince;
    }

    public String getCopyCity() {
        return copyCity;
    }

    public void setCopyCity(String copyCity) {
        this.copyCity = copyCity;
    }

    public String getCopyCounty() {
        return copyCounty;
    }

    public void setCopyCounty(String copyCounty) {
        this.copyCounty = copyCounty;
    }

    public int getCopyCategory() {
        return copyCategory;
    }

    public void setCopyCategory(int copyCategory) {
        this.copyCategory = copyCategory;
    }

    public String getCopyCategorys() {
        return copyCategorys;
    }

    public void setCopyCategorys(String copyCategorys) {
        this.copyCategorys = copyCategorys;
    }

    public int getCopyCertificate() {
        return copyCertificate;
    }

    public void setCopyCertificate(int copyCertificate) {
        this.copyCertificate = copyCertificate;
    }

    public String getCopyCertificates() {
        return copyCertificates;
    }

    public void setCopyCertificates(String copyCertificates) {
        this.copyCertificates = copyCertificates;
    }

    public String getCopyIdCard() {
        return copyIdCard;
    }

    public void setCopyIdCard(String copyIdCard) {
        this.copyIdCard = copyIdCard;
    }

    public int getCopySignature() {
        return copySignature;
    }

    public void setCopySignature(int copySignature) {
        this.copySignature = copySignature;
    }

    public String getCopySignatureName() {
        return copySignatureName;
    }

    public void setCopySignatureName(String copySignatureName) {
        this.copySignatureName = copySignatureName;
    }

    public int getCopyClassification() {
        return copyClassification;
    }

    public void setCopyClassification(int copyClassification) {
        this.copyClassification = copyClassification;
    }

    public String getCopy_typeString() {
        return copy_typeString;
    }

    public void setCopy_typeString(String copy_typeString) {
        this.copy_typeString = copy_typeString;
    }

    public String getCopy_idTypeString() {
        return copy_idTypeString;
    }

    public void setCopy_idTypeString(String copy_idTypeString) {
        this.copy_idTypeString = copy_idTypeString;
    }

    public String getCopySignature_String() {
        return copySignature_String;
    }

    public void setCopySignature_String(String copySignature_String) {
        this.copySignature_String = copySignature_String;
    }

    public String getCopyAreaNamesStr() {
        return copyAreaNamesStr;
    }

    public void setCopyAreaNamesStr(String copyAreaNamesStr) {
        this.copyAreaNamesStr = copyAreaNamesStr;
    }

    public List<String> getCopyAreaNames() {
        if(this.copyAreaNamesStr!=null) {
            return Arrays.asList(this.copyAreaNamesStr.split(","));
        }else{
            return null;
        }
    }

    public void setCopyAreaNames(List<String> copyAreaNames) {
      this.copyAreaNames = copyAreaNames;
      if (copyAreaNames != null) {
        String str = "";
        for (int i = 0; i < copyAreaNames.size(); i++) {
          if (i == 0) {
            str = str + copyAreaNames.get(i);
          } else {
            str = str + "," + copyAreaNames.get(i);
          }
        }
        this.copyAreaNamesStr = str;
      }
    }

  public Boolean getNoDeleteUpdate() {
    return noDeleteUpdate;
  }

  public void setNoDeleteUpdate(Boolean noDeleteUpdate) {
    this.noDeleteUpdate = noDeleteUpdate;
  }

  public int getSeq() {
    return seq;
  }

  public void setSeq(int seq) {
    this.seq = seq;
  }

  public CertificateEntity getCopyCertificateZM() {
    return copyCertificateZM;
  }

  public void setCopyCertificateZM(CertificateEntity copyCertificateZM) {
    this.copyCertificateZM = copyCertificateZM;
  }

  public UploadAttachment getCopyIdCardZM() {
    return copyIdCardZM;
  }

  public void setCopyIdCardZM(UploadAttachment copyIdCardZM) {
    this.copyIdCardZM = copyIdCardZM;
  }
}
