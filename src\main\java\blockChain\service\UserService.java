package blockChain.service;

import blockChain.bean.Constant;
import blockChain.config.ApplicationRuntimeProperties;
import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.entities.QUserEntity;
import blockChain.entities.UserEntity;
import blockChain.repository.UserPredicates;
import blockChain.repository.UserRepository;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class UserService {

    private final UserRepository repository;
    private final ApplicationRuntimeProperties properties;

    /**
     * 根据Username查找
     *
     * @param username
     * @return
     */
    public Optional<UserEntity> getByUserName(String username) {
        return repository.findAllByUserName(username).stream().findFirst();
    }

    public Optional<UserEntity> getByUuid(String uuid) {
    return repository.getByUuid(uuid);
  }

    public Optional<UserEntity> findById(Integer id) {
        return repository.findById(id);
    }

    public UserEntity save(UserEntity entity) {
        return repository.save(entity);
    }

    public Page<UserEntity> findPage(Predicate predicate, Pageable pageable) {
        return repository.findAll(predicate, pageable);
    }

    public List<UserEntity> findAll() {
        return repository.findAll();
    }

  public List<UserEntity> findAll(BooleanBuilder builder) {
    return repository.findAll(builder);
  }

    public List<UserEntity> findByUserIdIn(List<Integer> userIds) {
        QUserEntity qUserEntity = QUserEntity.userEntity;
        return repository.findAll(qUserEntity.userId.in(userIds));
    }

    public void removeBatchById(List<Integer> userIds) {
        repository.removeBatchById(userIds);
    }

    public boolean existsByRoleId(long roleId) {
        QUserEntity qUserEntity = QUserEntity.userEntity;
        return repository.exists(qUserEntity.role.id.eq(roleId));
    }

  public Page<StatisticDto> getUserStatistic(CopyrightQueryStatisticGetParam queryParam) {
    Long count = repository.countUserStatisticInCopyrightManager(queryParam.getPageable(), UserPredicates.statisticByManagerProductionTypesAndManagerRegistrationDateAndUserArea(queryParam));
    List<StatisticDto> list = repository.getUserStatisticInCopyrightManager(queryParam.getPageable(), UserPredicates.statisticByManagerProductionTypesAndManagerRegistrationDateAndUserArea(queryParam));
    return new <StatisticDto>PageImpl(list, queryParam.getPageable(), count);
  }

  public Long countNewUser(LocalDateTime begin, LocalDateTime end, List<String> area) {
    Assert.notNull(begin, "开始时间不能为空");
    Assert.notNull(end, "结束时间不能为空");
    return repository.count(UserPredicates.statisticRegistrationDateAndUserArea(begin, end, area));
  }

  /**
   * 统计最迟时间前所有用户
   * @param lastDateTime
   * @param area
   * @return
   */
  public Long countAllByRegisterTimeBefore(LocalDateTime lastDateTime, List<String> area) {
    Assert.notNull(lastDateTime, "最迟注册时间不能为空");
    return repository.count(UserPredicates.statisticRegistrationDateBeforeAndUserArea(lastDateTime, area));
  }

  public Long count() {
    return repository.count();
  }

  /**
   * 按照地市统计用户数量，省内
   * @param queryParam
   * @return
   */
  public List<StatisticDto> getGeographyOfUsersInFujian(CopyrightQueryStatisticGetParam queryParam) {
    return repository.countGeographyOfUsers(UserPredicates.provincePidAndRegistrationDateBefore(properties.getProvinceFujianDigitalDefinePid(),queryParam.getEndTime()));
  }

  public StatisticDto getGeographyOfUsersNotInFujian(CopyrightQueryStatisticGetParam queryParam) {
    return repository.countGeographyOfUsersCountAll(UserPredicates.neProvincePidAndRegistrationDateBefore(properties.getProvinceFujianDigitalDefinePid(),queryParam.getEndTime()));
  }

  public List<UserEntity> findByIds(List<Integer> userIds) {
    return repository.findAll(QUserEntity.userEntity.userId.in(userIds));
  }

  public List<UserEntity> findByWorkstation() {
    return repository.findAll(QUserEntity.userEntity.role.isWorkstation.eq(Constant.BYTE_TRUE));
  }

  public List<Integer> getAllIds() {

      return repository.getAllIds(new BooleanBuilder());
  }

    public List<UserEntity> findByCardId(String cardId) {
        return repository.findByCardId(cardId);
    }

    public void updateHomePage(Integer userId, String homePageKey) {
        repository.updateHomePage(userId, homePageKey);
    }

    public List<UserEntity> findByRealName(String realName) {
        return repository.findByRealName(realName);
    }
}
