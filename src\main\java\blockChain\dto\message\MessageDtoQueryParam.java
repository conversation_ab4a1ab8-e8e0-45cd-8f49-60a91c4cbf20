package blockChain.dto.message;

import blockChain.bean.BaseQueryParam;
import blockChain.entities.message.MessageUserEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/13 11:11
 */
@Data
public class MessageDtoQueryParam extends BaseQueryParam {


  /**
   * 消息标题
   */
  @ApiModelProperty("消息标题")
  private String title;

  /**
   * 消息内容
   */
  private String content;

  /**
   * 创建时间
   */
  private LocalDateTime createTimeStart;
  /**
   * 创建时间
   */
  private LocalDateTime createTimeEnd;

  /**
   * 创建者ID
   */
  private Integer creatorId;

  /**
   * 创建者用户名
   */
  private String creatorName;

  /**
   * 状态 {@link MessageUserEntity.MessageUserState}
   */
  private Byte state;

  /**
   * 已读时间
   */
  private LocalDateTime confirmTime;


  /**
   *用户IDs
   */
  private List<Integer> userIds;


  /**
   * 是否发送给所有人
   */
  private Boolean toAll;
}
