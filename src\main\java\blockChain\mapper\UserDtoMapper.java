package blockChain.mapper;

import blockChain.dto.UserDto;
import blockChain.dto.user.UserCreate;
import blockChain.dto.user.UserUpdate;
import blockChain.entities.UserEntity;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import java.util.List;

@Mapper(config = CommonConfig.class)
public interface UserDtoMapper {

  UserDtoMapper INSTANCE = Mappers.getMapper(UserDtoMapper.class);

  @Mappings({
    @Mapping(source = "role.id", target = "roleId"),
    @Mapping(source = "role.isWorkstation", target = "isWorkstation"),
    @Mapping(source = "role.roleName", target = "roleName"),
    @Mapping(source = "visibleLevelProvince.id", target = "visibleLevelProvince"),
    @Mapping(source = "visibleLevelCity.id", target = "visibleLevelCity"),
    @Mapping(source = "visibleLevelCounty.id", target = "visibleLevelCounty"),
  })
  UserDto entityToDto(UserEntity user);

  @Mappings({
    @Mapping(target = "visibleLevelProvince", ignore = true),
    @Mapping(target = "visibleLevelCity", ignore = true),
    @Mapping(target = "visibleLevelCounty", ignore = true),
  })
  UserEntity fromUserCreate(UserCreate userCreate);

  List<UserDto> toUserDtoList(List<UserEntity> userEntityList);

  @Mappings({
    @Mapping(target = "userId", ignore = true),
    @Mapping(target = "visibleLevelProvince", ignore = true),
    @Mapping(target = "visibleLevelCity", ignore = true),
    @Mapping(target = "visibleLevelCounty", ignore = true),
  })
  void updateUserEntity(UserDto userDto, @MappingTarget UserEntity userEntity);

  void updateUserEntity(UserUpdate userUpdate, @MappingTarget UserEntity userEntity);
}
