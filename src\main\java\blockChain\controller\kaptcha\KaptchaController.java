package blockChain.controller.kaptcha;

import blockChain.dto.KaptchaDto;
import blockChain.utils.KaptchaUtil;
import com.google.code.kaptcha.Producer;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.axis.encoding.Base64;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;

@Api("kaptcha")
@Slf4j
@RestController
@RequestMapping("kaptcha")
@RequiredArgsConstructor
public class KaptchaController {

  /**
   * 验证码工具
   */
  private final Producer defaultKaptcha;

	@PostMapping(value = "defaultKaptcha", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public Map<String,Object> defaultKaptcha(HttpServletRequest request, HttpServletResponse response) throws Exception{
    byte[] captcha = null;
    ByteArrayOutputStream out = new ByteArrayOutputStream();
    String base64 = null;
    Map<String,Object> result = new HashMap<>();

    try {
      // 将生成的验证码保存在session中
      KaptchaDto kaptchaDto = KaptchaUtil.getContext();
      String createText = kaptchaDto.getContext();
      BufferedImage bi = kaptchaDto.getImage();
      //String createText = defaultKaptcha.createText();
      request.getSession().setAttribute("rightCode", createText);
      //BufferedImage bi = defaultKaptcha.createImage(createText);
      ImageIO.write(bi, "jpg", out);
      base64 = Base64.encode(out.toByteArray());
    } catch (Exception e) {
      response.sendError(HttpServletResponse.SC_NOT_FOUND);
      return result;
    }
    //captcha = base64.getBytes();
    result.put("base64Code",base64);
    response.setHeader("Cache-Control", "no-store");
    response.setHeader("Pragma", "no-cache");
    response.setDateHeader("Expires", 0);
    response.setContentType("application/json");
    /*ServletOutputStream sout = response.getOutputStream();
    sout.write(captcha);
    sout.flush();
    sout.close();*/
    return result;
	}
}
