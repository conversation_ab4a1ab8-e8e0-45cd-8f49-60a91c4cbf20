package blockChain.dto;

import blockChain.bean.BaseQueryParam;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 新用户统计DTO
 * <AUTHOR>
 * @date 2020/4/7 15:01
 */
@Setter
@Getter
@ApiModel(description = "新用户统计DTO")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class NewUserAnalysisDto extends BaseQueryParam {
  /**
   * 新增用户数
   */
  Long newUserCount;
  /**
   * 新增用户数 - 同比
   */
  @Deprecated
  Double newUserCountYOY;
  /**
   * month-on-month
   * 新增用户数 - 环比
   */
  Double newUserCountMOM;


  /**
   * 总用户数
   */
  Long userTotal;
  /**
   * month-on-month
   * 总用户数 - 环比
   * 总用户数 - 统计周期前总数
   */
  Double userTotalMOM;


}
