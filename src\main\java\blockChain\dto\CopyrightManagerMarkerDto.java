package blockChain.dto;


import blockChain.bean.BaseQueryParam;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/14 14:21
 */
@Data
public class CopyrightManagerMarkerDto extends BaseQueryParam {


  /**
   * UID
   * 唯一约束
   */
  @JsonProperty("id")
  private String uuid;

  /**
   * 作品ID
   */
  private Long registrationNum;

  /**
   * 标星注释
   */
  private String content;

  /**
   * 作品标签id列表
   */
  private List<Integer> tagIds;

  /**
   * 作品标签id列表
   */
  private List<String> tagStrs;
}
