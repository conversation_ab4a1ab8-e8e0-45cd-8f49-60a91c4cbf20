package blockChain.entities.message;

import blockChain.dto.message.AlertMessageDto;
import blockChain.entities.UserEntity;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 系统公告(强制弹窗)
 *
 * <AUTHOR>
 * @date 2020/4/9 11:27
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tb_alert_message")
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
public class AlertMessage {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  /**
   * UUID
   * 唯一约束
   */
  @Column(unique = true, length = 36)
  @NonNull
  private String uuid;

  /**
   * 消息标题
   */
  private String title;

  /**
   * 消息内容
   */
  @Column(length = 4096)
  private String content;

  /**
   * 通知截至时间
   */
  private LocalDateTime deadline;

  /**
   * 状态
   */
  private AlertMessageDto.StatusEnum status = AlertMessageDto.StatusEnum.DISABLE;

  @CreatedDate
  private LocalDateTime createTime;

  @LastModifiedDate
  private LocalDateTime updateTime;

  /**
   * 创建人
   */
  @ManyToOne
  private UserEntity creator;

}
