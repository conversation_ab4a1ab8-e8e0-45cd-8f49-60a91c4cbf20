package blockChain.utils;

public class DataDecodeUtil {
	/**
     * 数据信息解密
     * @param trustticket  票据
     * @param date  时间毫秒值
     * @param msg   已加密数据信息字符串
     * @return
     * @throws Exception
     */
    public String dataDecode(String trustticket,String date,String msg) throws Exception {
        //组装加密私钥trustticket+公钥+时间
        String md5 = MD5Util.md5(trustticket  + date);
        return new SM4().decode(msg,md5 );
    }
}
