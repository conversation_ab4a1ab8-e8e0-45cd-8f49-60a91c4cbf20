package blockChain.Thread;

import blockChain.bean.QueryParam;
import blockChain.config.BeanContext;
import blockChain.entities.CopyrightManager;
import blockChain.entities.threads.CertificateCreateEntity;
import blockChain.facade.service.CopyrightManagerServiceFacade;
import blockChain.facade.service.Thread.CertificateCreateServiceFacade;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public class CertificateCreateThread extends Thread {
  private QueryParam queryParam;
  private HttpServletRequest request;
  private HttpServletResponse response;
  private String userName;
  private CopyrightManagerServiceFacade copyrightManagerServiceFacade;
  private CertificateCreateServiceFacade certificateCreateServiceFacade;

  /*public CertificateCreateThread(QueryParam queryParam, HttpServletRequest request, HttpServletResponse response,String userName){
    this.queryParam = queryParam;
    this.request = request;
    this.response = response;
    this.userName = userName;
  }*/

  public void certificateCreate(QueryParam queryParam, HttpServletRequest request, HttpServletResponse response,String userName){
    this.queryParam = queryParam;
    this.request = request;
    this.response = response;
    this.userName = userName;
    run();
  }

  @Async("doSomethingExecutor")
  public void run(){
    this.copyrightManagerServiceFacade = BeanContext.getApplicationContext().getBean(CopyrightManagerServiceFacade.class);
    this.certificateCreateServiceFacade = BeanContext.getApplicationContext().getBean(CertificateCreateServiceFacade.class);
    JSONArray idArray = queryParam.getJSONArray("idList");
    if (idArray != null && !idArray.isEmpty())
    {
      List<Long> idList = JSONObject.parseArray(idArray.toJSONString(), Long.class);
      System.out.println("添加了"+idList.size()+"个生成证书任务！");
      copyrightManagerServiceFacade.updateStateByIds(CopyrightManager.REPORTING,idList);
    }/*else{
      result.put("message","请选择证书！");
      return new ResponseEntity<Map<String,Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
    }*/
    /*
    LocalDateTime start = LocalDateTime.now();
    ResponseEntity<Map<String, Object>> result1 = copyrightManagerServiceFacade.certificateCreate(queryParam,request,response);
    CertificateCreateEntity certificateCreateEntity = new CertificateCreateEntity();
    certificateCreateEntity.setResult(result1.getBody().get("message").toString()+";"+result1.getBody().get("erroeMsg"));
    LocalDateTime finish = LocalDateTime.now();
    certificateCreateEntity.setCreateTime(start);
    certificateCreateEntity.setFinishTime(finish);
    certificateCreateEntity.setUserName(userName);

    certificateCreateServiceFacade.save(certificateCreateEntity);
    */
  }
}
