package blockChain.controller.statistic;

import blockChain.bean.QueryParam;
import blockChain.dto.StatementMonthlyDto;
import blockChain.dto.query.HomeQueryParam;
import blockChain.dto.query.StatementQueryParam;
import blockChain.facade.service.CopyrightManagerServiceFacade;
import blockChain.facade.service.MessageServiceFacade;
import blockChain.facade.service.StatementServiceFacade;
import blockChain.utils.DocumentHandlerUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Chan
 * @date 2020/4/16 10:01
 */
@Api("首页")
@Slf4j
@RestController
@RequestMapping("homePage")
@AllArgsConstructor
public class HomePageDataController {

  private CopyrightManagerServiceFacade serviceFacade;

  private MessageServiceFacade messageServiceFacade;

  @ApiOperation(value = "审核中作品数统计", notes = "用户审核中作品数统计", tags = { "审核中" })
  @PostMapping("dataCount")
  public ResponseEntity<Map<String,Object>> dataCount (@Valid @RequestBody HomeQueryParam param){
    Map<String,Object> result = serviceFacade.dataCount(param);
    return ResponseEntity.ok(result);
  }

  @ApiOperation(value = "获取未读消息数", notes = "获取未读消息数", tags = { "首页消息" })
  @PostMapping("personal/unread")
  public ResponseEntity<Map<String,Object>> countUnreadSize (@Valid @RequestBody HomeQueryParam param){
    Map<String, Object> result = new HashMap<>();
    Boolean isManager = param.getIsManager();
    if(isManager) {
      result = serviceFacade.getUnreadForManager(param);
    }else{
      Long size = messageServiceFacade.countUnreadSize();
      result.put("size",size);
    }
    return ResponseEntity.ok(result);
  }

}
