package blockChain.service.oldSystem;

import blockChain.entities.ArticleEntity;
import blockChain.entities.oldSystem.OldAgent;
import blockChain.repository.ArticleRepository;
import blockChain.repository.oldSystem.OldAgentRepository;
import blockChain.service.BaseService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/9 12:08
 */
@Service
@AllArgsConstructor
public class OldAgentService implements BaseService<OldAgentRepository, OldAgent, Integer> {
  private OldAgentRepository repository;

  @Override
  public OldAgentRepository getRepository() {
    return repository;
  }

}
