package blockChain.mapper;

import blockChain.dto.MenuDto;
import blockChain.entities.MenuEntity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/12/23 10:19
 */
@Mapper(config = CommonConfig.class)
public interface MenuMapper {

    MenuMapper INSTANCE = Mappers.getMapper(MenuMapper.class);

    MenuDto toMenuDto(MenuEntity menu);

    List<MenuDto> toMenuDtoList(List<MenuEntity> menuList);

    Set<MenuDto> toMenuDtoSet(Set<MenuEntity> menuSet);

    void updateMenuEntity(MenuDto menuDto, @MappingTarget MenuEntity menu);
}
