package blockChain.service;

import blockChain.config.ApplicationRuntimeProperties;
import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.entities.CopyrightOwner;
import blockChain.entities.Digital;
import blockChain.repository.BaseRepository;
import blockChain.repository.CopyrightOwnerPredicates;
import blockChain.repository.CopyrightOwnerRepository;
import blockChain.repository.DigitalPredicates;
import blockChain.utils.CollectionTools;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:25
 */
@Service
public class CopyrightOwnerService implements BaseService {

  private final CopyrightOwnerRepository repository;
  private final DigitalService digitalService;
  private final ApplicationRuntimeProperties runtimeProperties;
  private final CopyrightOwnerRepository secReepository;

  public CopyrightOwnerService(CopyrightOwnerRepository repository,CopyrightOwnerRepository secReepository,
                               DigitalService digitalService,ApplicationRuntimeProperties runtimeProperties){
    this.repository = repository;
    this.secReepository = secReepository;
    this.digitalService = digitalService;
    this.runtimeProperties = runtimeProperties;
  }

  @Override
  public BaseRepository getRepository() {
    return repository;
  }



  /**
   * 获取公司统计
   * @param of
   * @param queryParam
   * @return
   */
  public Page<StatisticDto> getCompanies(PageRequest of, CopyrightQueryStatisticGetParam queryParam) {
    List<Digital> zzqrlb = CollectionTools.toList(digitalService.getDictByCode(runtimeProperties.getOwnerTypeDigitalDefineCode(), 0));
    List<Integer> zzqrlbs = zzqrlb.stream().filter(z -> !"自然人".equals(z.getDict_name())).map(Digital::getId).collect(Collectors.toList());
    List<StatisticDto> companies = secReepository.getCompanies(of, CopyrightOwnerPredicates.copyrightOwnerStatisticPredicates(queryParam, zzqrlbs));
    Long total = secReepository.countCompanies(CopyrightOwnerPredicates.copyrightOwnerStatisticPredicates(queryParam, zzqrlbs));
    Page<StatisticDto> page = new PageImpl<>(companies, of, total);
    return page;
  }

  /**
   * 获取代理人统计
   * @param queryParam
   * @return
   */
  public List<StatisticDto> getAgent(CopyrightQueryStatisticGetParam queryParam) {
    List<StatisticDto> agent = secReepository.getAgent(queryParam.getPageSize(), CopyrightOwnerPredicates.copyrightAgentStatisticPredicates(queryParam));
    return agent;
  }

  /**
   * 获取所有者排行
   * @param queryParam
   * @return
   */
  public List<StatisticDto> getOwnerStatistic(CopyrightQueryStatisticGetParam queryParam) {
    List<Digital> zzqrlb = CollectionTools.toList(digitalService.getDictByCode(runtimeProperties.getOwnerTypeDigitalDefineCode(), 0));
    List<StatisticDto> manager;
    if (CopyrightQueryStatisticGetParam.OwnerEnum.COMPANY.equals(queryParam.getOwnerType())) {
      List<Integer> companies = zzqrlb.stream().filter(z -> !"自然人".equals(z.getDict_name())).map(Digital::getId).collect(Collectors.toList());
      manager = secReepository.getOwner(100, CopyrightOwnerPredicates.copyrightOwnerStatisticPredicates(queryParam, companies ));
    } else if (CopyrightQueryStatisticGetParam.OwnerEnum.PERSONAL.equals(queryParam.getOwnerType())) {
      List<Integer> companies = zzqrlb.stream().filter(z -> "自然人".equals(z.getDict_name())).map(Digital::getId).collect(Collectors.toList());
      manager = secReepository.getOwner(100, CopyrightOwnerPredicates.copyrightOwnerStatisticPredicates(queryParam, companies));
    }else {
      manager = new ArrayList<>(1);
    }
    return manager;
  }

  /**
   * 获取作品分类统计
   * @return
   */
  public List<StatisticDto> getProductionTypes() {
    return secReepository.getProductionTypes(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProductTypeDigitalDefineCode(), 0));
  }
  /**
   * 获取作品分类统计
   * @return
   */
  public List<StatisticDto> getProductionTypes(Predicate predicate) {
    return secReepository.getProductionTypes(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProductTypeDigitalDefineCode(), 0), predicate);
  }

  /**
   * 获取福建作品分类统计
   * @return
   */
  public List<StatisticDto> getProductionTypeByFujian(Predicate predicate) {
    return secReepository.getProductionTypesByFujian(/*DigitalPredicates.digitalQueryByCode(runtimeProperties.getProductTypeDigitalDefineCode(), 0), */predicate);
  }


  /**
   * 福建省各个地市数量分布情况
   * @return
   */
  public List<StatisticDto> getAreaDistributedForFujian() {
    return secReepository.getAreaDistributedForFujian(DigitalPredicates.digitalQueryByPid(runtimeProperties.getProvinceFujianDigitalDefinePid(), 0));
  }
  /**
   * 福建省各个地市数量分布情况
   * @param predicate 附加查询条件
   * @return
   */
  public List<StatisticDto> getAreaDistributedForFujian(Predicate predicate) {
    return secReepository.getAreaDistributedForFujian(DigitalPredicates.digitalQueryByPid(runtimeProperties.getProvinceFujianDigitalDefinePid(), 0), predicate);
  }

  public StatisticDto getAreaDistributed(Predicate ... predicate) {
    return secReepository.getAreaDistributedCountry(predicate);
  }

  public StatisticDto getAreaDistributedProvince(Predicate ... predicate) {
    return secReepository.getAreaDistributedProvince(predicate);
  }
  public StatisticDto getAreaDistributedCity(Predicate ... predicate) {
    return secReepository.getAreaDistributedCity(predicate);
  }

  public List<CopyrightOwner> getOwnerFilled(Predicate ... predicate) {
    return repository.getOwnerFilled(predicate);
  }

  /**
   * 获取作品分类统计总数
   */
  public long countBy(Predicate ... predicate){
    return secReepository.countBy(predicate);
  }

  /**
   * 按月/年获取作品分类统计总数
   */
  public List<StatisticDto> countListBy(Predicate predicate, Boolean isYear){
    return repository.countListBy(predicate, isYear);
  }

  /**
   * 根据作品id获取著作权人
   */
  public List<CopyrightOwner> getByCopyrightId(Long copyrightId){return repository.getByCopyrightId(copyrightId);}

  /**
   * 根据作品id获取作者
   */
  //public List<Author> getAuthorByCopyrightId(Long copyrightId){return repository.getAuthorByCopyrightId(copyrightId);}
}

