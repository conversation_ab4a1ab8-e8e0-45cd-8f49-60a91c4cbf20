package blockChain.service;

import blockChain.controller.exception.VerificationFailedException;
import blockChain.entities.MenuEntity;
import blockChain.entities.UserEntity;
import com.ctsi.ssdc.security.CscpUserDetail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 自定义 UserDetailsService
 *
 * <AUTHOR>
 * @date 2019/7/3 10:30
 */
@Slf4j
@Service
@AllArgsConstructor
public class IdentityUserDetailsService implements UserDetailsService {

    private final UserService userService;

    /**
     * @param name 使用空格拼接姓名和Identity信息
     * @return
     * @throws UsernameNotFoundException
     */
    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String name) throws UsernameNotFoundException {
        UserEntity csUser = userService.getByUserName(name).orElseThrow(() -> new UsernameNotFoundException("User  " + name + " was not found"));
        Set<MenuEntity> identities = csUser.getRole().getMenus();
        if (identities == null || identities.size() < 1) {
            throw new VerificationFailedException("用户无权限进入系统");
        }
        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();

        for (MenuEntity identity : identities) {
            grantedAuthorities.add(new SimpleGrantedAuthority(identity.getMenuUrl()));
        }
        return new CscpUserDetail(csUser.getUserId(), name, csUser.getPassword(), grantedAuthorities);
    }
}
