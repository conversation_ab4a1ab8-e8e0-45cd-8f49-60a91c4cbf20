package blockChain.utils;

import com.fgi.egbase.security.*;
import com.fgi.egbase.security.openssl.PemSM2PrivateKey;
import com.fgi.egbase.security.openssl.PemSM2PublicKey;
import com.sunfirestudio.common.encoding.EncodingHelper;
import com.sunfirestudio.common.encoding.UTF8Base64Encoding;
import com.sunfirestudio.common.security.CryptographyException;
import com.sunfirestudio.common.security.InvalidSignatureException;
import java.security.*;

public final class SmCall {

    public static void funtiontest()
    {
      try {
        String strPrikey="", strPubkey;
        strPrikey="-----BEGIN EC PARAMETERS-----\n" +
          "BggqgRzPVQGCLQ==\n" +
          "-----END EC PARAMETERS-----\n" +
          "-----BEGIN EC PRIVATE KEY-----\n" +
          "MHcCAQEEIC4RVUf4yn1EHWiS0cNZghAh8eNcw49sPRV0y1NC9y0hoAoGCCqBHM9V\n" +
          "AYItoUQDQgAEGTDtNywjoy7vmln71oR28MNiqVKoc6yPoL1Em7CbVbLxC+dX+QPF\n" +
          "DaDXdlrseNqtO0Fnn4a6wLL0756iirywPQ==\n" +
          "-----END EC PRIVATE KEY-----\n";
        strPubkey="-----BEGIN PUBLIC KEY-----\n" +
          "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEGTDtNywjoy7vmln71oR28MNiqVKo\n" +
          "c6yPoL1Em7CbVbLxC+dX+QPFDaDXdlrseNqtO0Fnn4a6wLL0756iirywPQ==\n" +
          "-----END PUBLIC KEY-----\n";
        System.out.println("私钥：" + strPrikey);
        System.out.println("公钥：" + strPubkey);
        String encstr = createSM2Encryptor("dengfubiao是傻逼", strPubkey);
        System.out.println("密文：" + encstr);
        String decstr = createSM2Decrypt(encstr, strPrikey);
        System.out.println("原文：" + decstr);
      }
      catch (Exception e)
      {
        e.printStackTrace();
      }
    }
    /**
     * 用bouncycastle的SM2签名
     *
     * @param text
     * @return
     * @throws SignatureException
     */
    public static String createSM2Signature(String text, String priKey) throws SignatureException {
        PemSM2PrivateKey privateKey = new PemSM2PrivateKey(priKey);
        EncodingHelper encoding = UTF8Base64Encoding.getInstance();
        SM2Signer signer = new SM2Signer(privateKey.getValue(), encoding);
        return signer.sign(text);
    }


    /**
     * 用bouncycastle的SM2验签
     *
     * @param text
     * @return
     * @throws InvalidSignatureException
     */
    public static void createSM2SigVerifier(String text, String sig,String pubKey) throws InvalidSignatureException {
        PemSM2PublicKey publicKey = new PemSM2PublicKey(pubKey);
        EncodingHelper encoding = UTF8Base64Encoding.getInstance();
        SM2SigVerifier Verifier = new SM2SigVerifier(publicKey.getValue(), encoding);
        Verifier.verify(text, sig);
    }


    /**
     * 用bouncycastle的SM2加密
     *
     * @param text
     * @param publicKey
     * @return
     * @throws CryptographyException
     */
    public static String createSM2Encryptor(String text, String publicKey) {
        try {
            EncodingHelper encoding = UTF8Base64Encoding.getInstance();
            PemSM2PublicKey pemSM2PublicKey = new PemSM2PublicKey(publicKey);
            SM2Encryptor target = new SM2Encryptor(pemSM2PublicKey.getValue(), encoding);
            return target.encrypt(text);
        } catch (CryptographyException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 用bouncycastle的SM2解密
     *
     * @param text
     * @param priKey
     * @return
     * @throws CryptographyException
     */
    public static String createSM2Decrypt(String text, String priKey) throws CryptographyException {
        EncodingHelper encoding = UTF8Base64Encoding.getInstance();
        PemSM2PrivateKey pemSM2PrivateKey = new PemSM2PrivateKey(priKey);
        SM2Decryptor target = new SM2Decryptor(pemSM2PrivateKey.getValue(), encoding);
        return target.decrypt(text);
    }
    /**
     * 用bouncycastle的SM3摘要
     *
     * @param text
     * @return
     * @throws CryptographyException
     */
    public static String createSM3Digest(String text) {
        EncodingHelper encoding = UTF8Base64Encoding.getInstance();
        SM3 target = new SM3(encoding);
        return target.generateDigest(text);
    }
    /**
     *
     * @param text
     * @param key
     * @return
     */
    public static String createSM3HMAC(String text,String key) throws InvalidKeyException {
        EncodingHelper encoding = UTF8Base64Encoding.getInstance();
        SM3 target = new SM3(encoding);
        return target.generateHMAC(text, key);
    }
}

