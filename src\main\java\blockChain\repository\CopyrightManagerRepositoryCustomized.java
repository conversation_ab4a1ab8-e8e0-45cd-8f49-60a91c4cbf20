package blockChain.repository;

import blockChain.dto.CopyrightManageExportDto;
import blockChain.dto.StatisticDto;
import blockChain.entities.CopyrightManager;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9 9:21
 */
public interface CopyrightManagerRepositoryCustomized {
  /**
   * 福建省各个地市数量分布情况
   * @param predicate
   * @return
   */
  List<StatisticDto> getAreaDistributedForFujian(Predicate ... predicate);

  /**
   * 国家发布统计
   * @param predicate
   * @return
   */
  StatisticDto getAreaDistributedCountry(Predicate ... predicate);

  /**
   * 省份发布统计
   * @param predicate
   * @return
   */
  StatisticDto getAreaDistributedProvince(Predicate ... predicate);

  /**
   * 城市分布统计
   * @param predicate
   * @return
   */
  List<StatisticDto> getAreaDistributedCity(Predicate digitalpredicate,Predicate managerperdicate,Predicate ... predicate);

  /**
   * 城市分布统计(省辖行政管理区(属市级))
   * @param predicate
   * @return
   */
  List<StatisticDto> getAreaDistributedCity(Integer pid,Predicate managerperdicate,Predicate ... predicate);

  /**
   * 县区分布统计
   * @param predicate
   * @return
   */
  List<StatisticDto> getAreaDistributedCounty(Predicate digitalpredicate,Predicate managerperdicate,Predicate ... predicate);

  /**
   * 县区分布统计
   * @param predicate
   * @return
   */
  List<StatisticDto> getAreaDistributedCounty(Integer id,Predicate managerperdicate,Predicate ... predicate);

  long updateCountDown();

//  void updateLicenseStatus();

  long countBy(Predicate predicate);

  void updateStateByIds(Integer state,List<Long> ids);

  List<CopyrightManageExportDto> getExportList(String worksNum, String productionName,
                                               List<String> productionTypeIds, Integer rightOwnMode,
                                               String startDate, String endDate, List<Integer> status, String userName, String agentName, String copyrightName,
                                               Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level, Integer evaluatemin, Integer evaluatemax,
                                               String certificateStartDate,String certificateEndDate);
  /**
   * 按月/年统计
   * @param predicate
   * @param isYear
   * @return
   */
  List<StatisticDto> countListBy(Predicate predicate, Boolean isYear, Boolean isWorkstation);

  /**
   * 重复作品数
   * @param predicate
   * @return
   */
  long countSameBy(Predicate predicate);

  List<CopyrightManager> getAllSWBSB(PageRequest of, Predicate predicate);

  /**
   * 各工作站统计作品总数列表
   * @param predicate
   * @return
   */
  List<StatisticDto> countWorkstationList(Predicate predicate);

  /**
   * 其他工作站统计作品总数
   * @param predicate
   * @return
   */
  long countWorkstationOther(Predicate predicate);

  /**
   * 工作站统计作品总数
   * @param predicate
   * @return
   */
  long countWorkstation(Predicate predicate);
}
