/**
 * SwapMonitor.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.linewell.apas.wailian.monitor;

public class SwapMonitor  implements java.io.Serializable {
    private java.lang.Object dataxml;

    private java.lang.String endswap;

    private java.lang.String fromcode;

    private java.lang.String fromip;

    private java.lang.String itemcode;

    private java.lang.String projcode;

    private java.lang.String returnMsg;

    private java.lang.String rowGuid;

    private java.lang.String swaptime;

    private java.lang.String tocode;

    private java.lang.String unid;

    public SwapMonitor() {
    }

    public SwapMonitor(
           java.lang.Object dataxml,
           java.lang.String endswap,
           java.lang.String fromcode,
           java.lang.String fromip,
           java.lang.String itemcode,
           java.lang.String projcode,
           java.lang.String returnMsg,
           java.lang.String rowGuid,
           java.lang.String swaptime,
           java.lang.String tocode,
           java.lang.String unid) {
           this.dataxml = dataxml;
           this.endswap = endswap;
           this.fromcode = fromcode;
           this.fromip = fromip;
           this.itemcode = itemcode;
           this.projcode = projcode;
           this.returnMsg = returnMsg;
           this.rowGuid = rowGuid;
           this.swaptime = swaptime;
           this.tocode = tocode;
           this.unid = unid;
    }


    /**
     * Gets the dataxml value for this SwapMonitor.
     * 
     * @return dataxml
     */
    public java.lang.Object getDataxml() {
        return dataxml;
    }


    /**
     * Sets the dataxml value for this SwapMonitor.
     * 
     * @param dataxml
     */
    public void setDataxml(java.lang.Object dataxml) {
        this.dataxml = dataxml;
    }


    /**
     * Gets the endswap value for this SwapMonitor.
     * 
     * @return endswap
     */
    public java.lang.String getEndswap() {
        return endswap;
    }


    /**
     * Sets the endswap value for this SwapMonitor.
     * 
     * @param endswap
     */
    public void setEndswap(java.lang.String endswap) {
        this.endswap = endswap;
    }


    /**
     * Gets the fromcode value for this SwapMonitor.
     * 
     * @return fromcode
     */
    public java.lang.String getFromcode() {
        return fromcode;
    }


    /**
     * Sets the fromcode value for this SwapMonitor.
     * 
     * @param fromcode
     */
    public void setFromcode(java.lang.String fromcode) {
        this.fromcode = fromcode;
    }


    /**
     * Gets the fromip value for this SwapMonitor.
     * 
     * @return fromip
     */
    public java.lang.String getFromip() {
        return fromip;
    }


    /**
     * Sets the fromip value for this SwapMonitor.
     * 
     * @param fromip
     */
    public void setFromip(java.lang.String fromip) {
        this.fromip = fromip;
    }


    /**
     * Gets the itemcode value for this SwapMonitor.
     * 
     * @return itemcode
     */
    public java.lang.String getItemcode() {
        return itemcode;
    }


    /**
     * Sets the itemcode value for this SwapMonitor.
     * 
     * @param itemcode
     */
    public void setItemcode(java.lang.String itemcode) {
        this.itemcode = itemcode;
    }


    /**
     * Gets the projcode value for this SwapMonitor.
     * 
     * @return projcode
     */
    public java.lang.String getProjcode() {
        return projcode;
    }


    /**
     * Sets the projcode value for this SwapMonitor.
     * 
     * @param projcode
     */
    public void setProjcode(java.lang.String projcode) {
        this.projcode = projcode;
    }


    /**
     * Gets the returnMsg value for this SwapMonitor.
     * 
     * @return returnMsg
     */
    public java.lang.String getReturnMsg() {
        return returnMsg;
    }


    /**
     * Sets the returnMsg value for this SwapMonitor.
     * 
     * @param returnMsg
     */
    public void setReturnMsg(java.lang.String returnMsg) {
        this.returnMsg = returnMsg;
    }


    /**
     * Gets the rowGuid value for this SwapMonitor.
     * 
     * @return rowGuid
     */
    public java.lang.String getRowGuid() {
        return rowGuid;
    }


    /**
     * Sets the rowGuid value for this SwapMonitor.
     * 
     * @param rowGuid
     */
    public void setRowGuid(java.lang.String rowGuid) {
        this.rowGuid = rowGuid;
    }


    /**
     * Gets the swaptime value for this SwapMonitor.
     * 
     * @return swaptime
     */
    public java.lang.String getSwaptime() {
        return swaptime;
    }


    /**
     * Sets the swaptime value for this SwapMonitor.
     * 
     * @param swaptime
     */
    public void setSwaptime(java.lang.String swaptime) {
        this.swaptime = swaptime;
    }


    /**
     * Gets the tocode value for this SwapMonitor.
     * 
     * @return tocode
     */
    public java.lang.String getTocode() {
        return tocode;
    }


    /**
     * Sets the tocode value for this SwapMonitor.
     * 
     * @param tocode
     */
    public void setTocode(java.lang.String tocode) {
        this.tocode = tocode;
    }


    /**
     * Gets the unid value for this SwapMonitor.
     * 
     * @return unid
     */
    public java.lang.String getUnid() {
        return unid;
    }


    /**
     * Sets the unid value for this SwapMonitor.
     * 
     * @param unid
     */
    public void setUnid(java.lang.String unid) {
        this.unid = unid;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof SwapMonitor)) return false;
        SwapMonitor other = (SwapMonitor) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.dataxml==null && other.getDataxml()==null) || 
             (this.dataxml!=null &&
              this.dataxml.equals(other.getDataxml()))) &&
            ((this.endswap==null && other.getEndswap()==null) || 
             (this.endswap!=null &&
              this.endswap.equals(other.getEndswap()))) &&
            ((this.fromcode==null && other.getFromcode()==null) || 
             (this.fromcode!=null &&
              this.fromcode.equals(other.getFromcode()))) &&
            ((this.fromip==null && other.getFromip()==null) || 
             (this.fromip!=null &&
              this.fromip.equals(other.getFromip()))) &&
            ((this.itemcode==null && other.getItemcode()==null) || 
             (this.itemcode!=null &&
              this.itemcode.equals(other.getItemcode()))) &&
            ((this.projcode==null && other.getProjcode()==null) || 
             (this.projcode!=null &&
              this.projcode.equals(other.getProjcode()))) &&
            ((this.returnMsg==null && other.getReturnMsg()==null) || 
             (this.returnMsg!=null &&
              this.returnMsg.equals(other.getReturnMsg()))) &&
            ((this.rowGuid==null && other.getRowGuid()==null) || 
             (this.rowGuid!=null &&
              this.rowGuid.equals(other.getRowGuid()))) &&
            ((this.swaptime==null && other.getSwaptime()==null) || 
             (this.swaptime!=null &&
              this.swaptime.equals(other.getSwaptime()))) &&
            ((this.tocode==null && other.getTocode()==null) || 
             (this.tocode!=null &&
              this.tocode.equals(other.getTocode()))) &&
            ((this.unid==null && other.getUnid()==null) || 
             (this.unid!=null &&
              this.unid.equals(other.getUnid())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getDataxml() != null) {
            _hashCode += getDataxml().hashCode();
        }
        if (getEndswap() != null) {
            _hashCode += getEndswap().hashCode();
        }
        if (getFromcode() != null) {
            _hashCode += getFromcode().hashCode();
        }
        if (getFromip() != null) {
            _hashCode += getFromip().hashCode();
        }
        if (getItemcode() != null) {
            _hashCode += getItemcode().hashCode();
        }
        if (getProjcode() != null) {
            _hashCode += getProjcode().hashCode();
        }
        if (getReturnMsg() != null) {
            _hashCode += getReturnMsg().hashCode();
        }
        if (getRowGuid() != null) {
            _hashCode += getRowGuid().hashCode();
        }
        if (getSwaptime() != null) {
            _hashCode += getSwaptime().hashCode();
        }
        if (getTocode() != null) {
            _hashCode += getTocode().hashCode();
        }
        if (getUnid() != null) {
            _hashCode += getUnid().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(SwapMonitor.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://monitor.wailian.apas.linewell.com", "SwapMonitor"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dataxml");
        elemField.setXmlName(new javax.xml.namespace.QName("", "dataxml"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "anyType"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("endswap");
        elemField.setXmlName(new javax.xml.namespace.QName("", "endswap"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("fromcode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "fromcode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("fromip");
        elemField.setXmlName(new javax.xml.namespace.QName("", "fromip"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("itemcode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "itemcode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("projcode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "projcode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("returnMsg");
        elemField.setXmlName(new javax.xml.namespace.QName("", "returnMsg"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowGuid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "rowGuid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("swaptime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "swaptime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tocode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "tocode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unid");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
