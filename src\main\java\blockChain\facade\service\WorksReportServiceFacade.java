
package blockChain.facade.service;


import blockChain.bean.QueryParam;
import blockChain.config.SpringConfig;
import blockChain.entities.*;
import blockChain.service.DraftCopyrightManagerService;
import blockChain.service.UploadAttachmentService;
import blockChain.service.UserService;
import blockChain.service.WorksReportService;
import blockChain.utils.HttpUtil;
import blockChain.utils.ImageUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2019/12/3 9:45
 */

@Service
@AllArgsConstructor
public class WorksReportServiceFacade {
    @Autowired
    private WorksReportService worksReportService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserServiceFacade userServiceFacade;
    @Autowired
    private UploadAttachmentService uploadAttachmentService;

    private final SpringConfig config;

    @Transactional(rollbackFor = RuntimeException.class)
    public WorksReport getById(Long id){
        return worksReportService.getById(id);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void createWorksReport(WorksReport worksReport){
        /*if(worksReport.getId()==null){
            CopyrightManager copyrightManager = worksReport.getCopyrightManager();
            worksReport.setCopyrightManager(null);
            worksReportService.saveOrUpdateWorksReport(worksReport);
            worksReport.setCopyrightManager(copyrightManager);
            worksReportService.saveOrUpdateWorksReport(worksReport);
        }else {*/
            worksReportService.saveOrUpdateWorksReport(worksReport);
        /*}*/
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public Page<WorksReport> query(String worksNum, String productionName,
                                   String productionTypeId, String startDate, String endDate,
                                   String certificateStartDate, String certificateEndDate, List<Integer> status, String userName, Integer page, Integer size){
        Sort sort = new Sort(Sort.Direction.DESC, "createTime");
        Pageable of = PageRequest.of(page,size,sort);
        return worksReportService.query(worksNum,productionName,productionTypeId,startDate,endDate,certificateStartDate,certificateEndDate,status,userName,of);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void remove(WorksReport worksReport){ worksReportService.remove(worksReport);}

    @Transactional(rollbackFor = RuntimeException.class)
    public void approval(QueryParam queryParam, HttpServletRequest request){
        try {
            JSONArray idArray = queryParam.getJSONArray("idList");
            if(idArray!=null && !idArray.isEmpty()) {
                List<Long> idList = JSONObject.parseArray(idArray.toJSONString(),Long.class);
                for(Long id:idList) {
                    //Long id = queryParam.getLong("id");
                    String approverResult = queryParam.getString("approverResult");
                    Integer uid = queryParam.getInteger("UID");
                    Integer state = null;
                    Long fileId = queryParam.getLong("tortReport");
                    //设置流程记录
                    ProcessRecord processRecord = new ProcessRecord();
                    processRecord.setOpType("审批");
                    processRecord.setOpResult("审批"+ProcessRecord.APPROVAL_RESULT.get(approverResult));
                    processRecord.setApproverOpinion(queryParam.getString("approverOpinion"));
                    WorksReport worksReport = worksReportService.getById(id);
                    if (worksReport == null) {
                        throw new RuntimeException();
                    }
                    if(approverResult.equals("pass")){
                        state = WorksReport.PASSED;
                    }else if(approverResult.equals("reject")){
                        state = WorksReport.MODIFIED;
                    }else{
                        state=WorksReport.REVIEWING;
                    }
                    //获取当前操作人
                    processRecord.setOpName(userServiceFacade.findUserById(uid).getRealName());

                    worksReport.setReportStatus(state);
                    worksReport.getFlowRecord().add(processRecord);
                    if(fileId!=null) {
                        UploadAttachment uploadAttachment = new UploadAttachment();
                        uploadAttachment.setId(fileId);
                        worksReport.setTortReport(uploadAttachment);
                    }
                    worksReportService.saveOrUpdateWorksReport(worksReport);

                    //举报报告上链
                    if(approverResult.equals("pass")) {
                        UploadAttachment temp = uploadAttachmentService.getById(fileId);
                        String onBlockChain = reportBlockChain(temp.getWorkUrl(), request);
                        worksReport.setOnBlockChain(onBlockChain);
                        worksReportService.saveOrUpdateWorksReport(worksReport);
                    }
                }
                /*result.put("message", "操作成功！");
                return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);*/
            }else{
                throw new RuntimeException();
            }
        }catch (Exception e){
            throw new RuntimeException();
        }
    }

    private String reportBlockChain(String url,HttpServletRequest request) throws RuntimeException
    {
        String postUrl = config.getBlockUrl();
        //String contextPath = "File/";
        String fileUrl = System.getProperty("user.dir")+ File.separator +url;
        try {
            String HexStr = ImageUtil.ImageToHex(fileUrl);
            Map<String, Object> param = new HashMap<>();
            param.put("jsonrpc", "2.0");
            param.put("method", "personal_unlockAccount");
            param.put("params", new ArrayList<Object>() {{
                add("******************************************");
                add("123456");
                add(120);
            }});
            param.put("id", 1);
            String unlockParamStr = (new JSONObject(param)).toJSONString();
            String getUnlockResult = HttpUtil.requestPost(postUrl, unlockParamStr);
            JSONObject unlockResult = JSONObject.parseObject(getUnlockResult);
            if (unlockResult.containsKey("result") && unlockResult.getBooleanValue("result")) {
                //上链
                param.clear();
                param.put("jsonrpc", "2.0");
                param.put("method", "eth_sendTransaction");
                param.put("params", new ArrayList<Object>() {{
                    add(new HashMap<String, Object>() {{
                        put("from", "******************************************");
                        put("to", "******************************************");
                        put("value", "0x100000");
                        put("data", HexStr);
                    }});
                }});
                param.put("id", 1);
                String paramStr = (new JSONObject(param)).toJSONString();
                String getOnBlockResult = HttpUtil.requestPost(postUrl, paramStr);
                JSONObject onBlockResult = JSONObject.parseObject(getOnBlockResult);
                if (onBlockResult.containsKey("result")) {
                    return onBlockResult.getString("result");
                }else{
                    throw new RuntimeException();
                }
            } else {
                throw new RuntimeException();
            }
        }catch (Exception e){
            throw new RuntimeException();
        }
    }
}

