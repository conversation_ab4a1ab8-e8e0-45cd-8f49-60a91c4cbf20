# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# ===================================================================
logging:
  config: classpath:logback-formate/logback-linux.xml
  level:
    ROOT: debug
    com.ctsi.sampleapp: debug
    org.hibernate: ERROR
    org.hibernate.ejb.HibernatePersistence: ERROR
    org.hibernate.SQL: ERROR
    org.hibernate.type.descriptor.sql.BasicBinder: ERROR
    com.querydsl.jpa.impl.JPAQuery: ERROR
    com.zdww.biyi.component.sdk.log.LogFile: ERROR
spring:
  profiles:
    active: dev
    include: swagger

  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL57Dialect
  liquibase:
    enabled: true
    change-log: classpath:/db/changelog/master.xml
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************
    username: root
    password: Qwerty10000
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      poolName: DatebookHikariCP1
      #最大连接池数
      maximumPoolSize: 20
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
      idleTimeout: 120000
      connectionTimeout: 300000
      leakDetectionThreshold: 300000
  #国际化
  messages:
    basename: i18n/messages
    encoding: UTF-8
server:
  port: 19000
  servlet:
    context-path: /api/qkl

block:
  file:
    fileRoot: media/nas/copyright_newupload
    modelRoot: media/nas/
  images:
    imageRoot: certimages
  logs:
    filePath: /media/log
  blockUrl:
    url: http://**************:8091/
  frontUrl:
    url: http://copyright.fjxuanchuan.cn/
  oldSystemUrl:
    url: http://**************:8081/CopyRight/
  biyiServiceUrl:
    url: http://127.0.0.1:8099/api/qkl/test/
  convergence:
    wsdl: http://**************:809/Convergence/webservice/ConvergenceService?wsdl
    userName: hjpt_bqbkxt
    passWord: hjpt@321
    catalogId_manager: WEB736
    catalogId_owner: WEB735
  proxyIp: none
  proxyPort: 0
  isExtranet: true
  autoSubmit: true
  certificate:
    cerUrl: http://**************:18080/processBuildCertificate
    picUrl: http://**************:18080/processFixTakeStamp
    documentUrl: http://**************:18888/docTakeStamp
  similarScore: 3
  #  证照提交
  license:
    url: http://**************:90/services/DataCollect
    guid: ********-B96B51EB9423BACE2CAF-60
    surface: sendSurfaceAndAutoSeal
    tokenUrl: http://**************:8040/license-api-release/certificate/oauth/token
    saveUrl: http://**************:8040/license-api-release/certificate/v1/saveCertificateInfo
    accountId: fjsbqj
    priKey: FHrFA8XpCBkSr0pWiG+/a/pcJcEVVfycb1i1fqKHx4M=
  autoEvaluationDay: 3
  backend:
    isOn: true
    startWorkflow:
      acceptId: f304fe826129165ba71ac5729f0ffc11
      patchId: 2df35919cd253833a9fa16348fc6cfb3
      patchEndId: dac9e1a3494012c6626184173a580e4f
      transactId: ea640650f2dfe86aac47d3e8f59a7f38
      nodeTransId: de74efec1a71ce0cf96e584445cfc03c
      baseInfoId: bd2b834d947d8ec628118e8f1d060a95
    projectIdGet:
      url: https://fwzx.fjzwt.cn:28063/bcsp/backend/adapter/swbxx/unifyAddress/projectIdGet
    projectIdActive:
      url: https://fwzx.fjzwt.cn:28063/bcsp/backend/adapter/swbxx/unifyAddress/projectIdActive
  mzt:
    url: https://allinone-gateway.e-govt.cn:9080
    appId: 843f5010-644d-479a-9b41-ef06d8d3e637
    appSecret: 77fd6f0d47f94c738fe92ae3a7bcef90
    appToken: /ebus/ability_tysfrz_gzcszsf/outside/oauth2/getAppToken
    personalAuthorization: /ebus/ability_sfjszsfglfwpt_sqdbfw/prod-api/outside/user/personalAuthorizationv2
    PAVerification: /ebus/ability_sfjszsfglfwpt_sqdbfw/prod-api/outside/user/PAVerificationv2
    accessToken: /ebus/ability_tysfrz_gzcszsf/outside/oauth2/token
    userInfo: /ebus/ability_tysfrz_gzcszsf/outside/oauth2/getUserInfo
    verify: /ebus/ability_tysfrz_gzcszsf/outside/oauth2/verify
    logout: /ebus/ability_tysfrz_gzcszsf/auth_applogout_iam
    personAgencyQuery: /ebus/ability_sfjszsfglfwpt_sqdbfw/prod-api/mc-personal-auth/outside/agency/personAgencyQuery
    companyAgencyQuery: /ebus/ability_sfjszsfglfwpt_sqdbfw/prod-api/mc-personal-auth/outside/agency/companyAgencyQuery
    attnAgencyQuery: /ebus/ability_sfjszsfglfwpt_sqdbfw/prod-api/mc-personal-auth/outside/agency/attnAgencyQuery
ctsi:
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization,Link,X-Total-Count"
    allow-credentials: true
    max-age: 1800
  log:
    operation-log:
      enable: true
    login-log:
      enable: false
  jwtfilter:
    enable: true
  component-statics:
    log-path: /home/<USER>/logs
  proxyIp: none
  proxyPort: 0
