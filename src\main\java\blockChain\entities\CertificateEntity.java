package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_certificate")
public class CertificateEntity {

	// 电子证照信息表
	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;// 电子证照ID

    /**
     * 电子证照标识
     * eg: 004155341-3502032018122850114-410727199802207325
     * 1.2.156.3005.2.11100000000013127D001.5dd2830477b21486d85d644e.9UXYF1YE1XFV6PBT4N1D.001.L
     */
    private String certificateIdentifier;

    /**
     * 证照类型代码
     * eg: 11100000000013127D050(居住证)
     * 11100000000013127D001（中华人民共和国居民身份证）
     */
    private String certificateTypeCode;

    /**
     * 证照类型名称
     * eg: 居住证/中华人民共和国居民身份证
     */
    private String certificateTypeName;

    /**
     * 证照所属行政区划
     * eg: 350104
     */
    private String certificateAreaCode;

    /**
     * 数据来源，由三位数字组成，
     * eg: 135
     */
    private String dataSource;

    /**
     * 证照编号
     * eg: 3502032018122850114(居住证编号)
     * （身份证号码）
     */
    private String certificateNumber;

    /**
     * 证照名称
     * eg: 福建省居住证/中华人民共和国居民身份证
     */
    private String certificateName;

    /**
     * 持证主体
     * eg: 用户名
     */
    private String certificateHolderName;

    /**
     * 持证主体代码
     * eg: 身份证号
     */
    private String certificateHolderCode;

    /**
     * 持证主体代码类型代码
     * eg: 公民身份号码:111; 其他自然人有效证件代码:999
     */
    private String certificateHolderTypeCode;

    /**
     * 持证主体代码类型名称
     * eg: 公民身份号码
     */
    private String certificateHolderTypeName;

    /**
     * 证照颁发机构
     * eg: 福州市公安局仓山分局
     */
    private String certificateIssuingAuthorityName;

    /**
     * 证照颁发机构代码
     * eg: 11350100003606753D
     */
    private String certificateIssuingAuthorityCode;

    /**
     * 证照颁发日期
     * eg: 2019-09-24
     */
    private LocalDate certificateIssuedDate;

    /**
     * 证照有效期起始日期
     * eg: 2019-09-24
     */
    private LocalDate certificateEffectiveDate;

    /**
     * 证照有效期截止日期
     * eg: 2021-09-24
     */
    private String certificateExpiringDate;

    /**
     * 电子证照文件格式：edc()、ofd（）（如果是省外证照数据，则为空）
     * eg: 公民身份号码
     */
    private String fileFormat;

    /**
     * 分类:证照、批文（如果是省外证照数据，则为空）
     */
    private String KZ_classification;

    /**
     * 安全共享等级:低、中、高
     */
    private String KZ_securityClass;
    //创建时间
	@CreatedDate
	private LocalDateTime createTime;
	//更新时间
	@LastModifiedDate
	private LocalDateTime updateTime;

    @Transient
    private List<String> areaNames = new ArrayList<>();
    private String areaNamesStr;
    private int certCountries;// 国籍
    private int certProvince;// 省份
    private int certCity;// 所在地级市
    private int certCounty;// 所在区县

    public List<String> getAreaNames() {
      if (this.areaNamesStr != null) {
        return Arrays.asList(this.areaNamesStr.split(","));
      } else {
        return new ArrayList<>();
      }
    }

    public void setAreaNames(List<String> areaNames) {
      this.areaNames = areaNames;
      if (areaNames != null) {
        String str = "";
        for (int i = 0; i < areaNames.size(); i++) {
          if (i == 0) {
            str = str + areaNames.get(i);
          } else {
            str = str + "," + areaNames.get(i);
          }
        }
        this.areaNamesStr = str;
      }
    }
}
