package blockChain.controller;

import blockChain.bean.BaseResponseDto;
import blockChain.bean.PageResponse;
import blockChain.dto.message.AlertMessageDto;
import blockChain.dto.message.AlertMessageDtoQueryParam;
import blockChain.facade.service.AlertMessageServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Chan
 * @date 2020/4/9 14:15
 */
@Api("系统公告(强制弹窗类)")
@Slf4j
@RestController
@RequestMapping("message/alert")
@AllArgsConstructor
public class AlertMessageController {

  private AlertMessageServiceFacade serviceFacade;

  //@PreAuthorize("hasAnyAuthority('"+ AuthoritiesConstants.MEMBER_CREATE+"')")
  @ApiOperation(value = "创建", nickname = "createAlertMessage", notes = "Creates a new instance of a `AlertMessage`.", tags={ "系统公告(强制弹窗) (管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("create")
  public ResponseEntity<BaseResponseDto> createAlertMessage(@Valid @RequestBody AlertMessageDto dto) {
    serviceFacade.create(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  //@PreAuthorize("hasAnyAuthority('"+ AuthoritiesConstants.MEMBER_CREATE+"')")
  @ApiOperation(value = "删除", nickname = "deleteAlertMessage", notes = "删除的时候只要传入UUID即可", tags={ "系统公告(强制弹窗) (管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("remove")
  public ResponseEntity<BaseResponseDto> deleteAlertMessage(@Valid @RequestBody AlertMessageDto dto) {
    serviceFacade.delete(dto.getUuid());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }


  @ApiOperation(value = "修改", nickname = "updateAlertMessage", notes = " 使用UUID标识唯一消息", tags={ "系统公告(强制弹窗) (管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 202, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("update")
  public ResponseEntity<BaseResponseDto> updateAlertMessage(@Valid @RequestBody AlertMessageDto dto) {
    serviceFacade.update(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }


  @ApiOperation(value = "查询单个", nickname = "getOneAlertMessage", notes = " 使用UUID标识唯一消息", tags={ "系统公告(强制弹窗) (管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("getOne")
  public ResponseEntity<AlertMessageDto> getOneAlertMessage(@Valid @RequestBody AlertMessageDto dto) {
    Assert.notNull(dto.getUuid(), "UUID不能为空");
    AlertMessageDto one = serviceFacade.getOne(dto.getUuid());
    return ResponseEntity.ok(one);
  }

  @ApiOperation(value = "query", nickname = "queryAlertMessage", notes = "query", tags={ "系统公告(强制弹窗) (管理)", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("query")
  public ResponseEntity<PageResponse<AlertMessageDto>> queryAlertMessage(@Valid @RequestBody AlertMessageDtoQueryParam dto) {
    PageResponse<AlertMessageDto> page = serviceFacade.queryAlertMessage(dto);
    return ResponseEntity.ok(page);
  }

  @ApiOperation(value = "getAlert", nickname = "queryAlertMessage", notes = "登录系统时，获取当前一条公告", tags={ "系统公告(强制弹窗)(用户读取)", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("getAlert")
  public ResponseEntity<Map<String, Object>> getAlertMessage() {
    Map<String, Object> result = new HashMap<>();
    List<AlertMessageDto> dtos = serviceFacade.getAlertMessage();
    result.put("infoListArr", dtos);
    return new ResponseEntity<>(result, HttpStatus.OK);
  }

  @ApiOperation(value = "获取当前消息列表", nickname = "currentMessage", notes = "获取当前消息列表", tags={ "系统公告(强制弹窗) (用户读取)", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("current")
  public ResponseEntity<PageResponse<AlertMessageDto>> currentMessage(@Valid @RequestBody AlertMessageDtoQueryParam dto) {
    PageResponse<AlertMessageDto> page = serviceFacade.currentMessage(dto);
    return ResponseEntity.ok(page);
  }
}
