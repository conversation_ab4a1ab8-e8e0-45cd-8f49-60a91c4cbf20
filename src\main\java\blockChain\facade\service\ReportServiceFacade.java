package blockChain.facade.service;

import blockChain.bean.LicenseSendInfo;
import blockChain.bean.QueryParam;
import blockChain.config.SpringConfig;
import blockChain.dto.CopyrightManageExportDto;
import blockChain.dto.CopyrightOwnerDto;
import blockChain.dto.DigitalDto;
import blockChain.dto.UserDto;
import blockChain.entities.*;
import blockChain.entities.threads.CertificateCreateEntity;
import blockChain.mapper.CopyrightOwnerDtoMapper;
import blockChain.mapper.DigitalDtoMapper;
import blockChain.nationalBean.*;
import blockChain.service.*;
import blockChain.service.Thread.CertificateCreateService;
import blockChain.service.nationalservice.NationAddClient;
import blockChain.utils.HttpClientUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.linewell.License;
import jxl.CellView;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Colour;
import jxl.format.UnderlineStyle;
import jxl.write.Label;
import jxl.write.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;

/**
 * <AUTHOR> Jie
 * @date 2019/11/22 16:54
 */
@Service
@AllArgsConstructor
@Slf4j
public class ReportServiceFacade {
  private CopyrightManagerService copyrightManagerService;
  private CopyrightManagerServiceFacade copyrightManagerServiceFacade;
  private DigitalServiceFacade digitalServiceFacade;
  private CertificateCreateService certificateCreateService;
  private CopyrightOwnerService copyrightOwnerService;
  private AuthorService authorService;
  private TaskService taskService;
  private UserServiceFacade userServiceFacade;
  private BackendServiceFacade backendServiceFacade;
  private final static Logger logger = LoggerFactory.getLogger(ReportServiceFacade.class);

  private final SpringConfig config;;

  @Autowired
  private ProcessProgressService processProgressService;

  //应用系统名称
  private static String APPNAME = "福建省版权局作品自愿登记系统";

  //上报国家局线程
  @Async("doSomethingExecutor")
  public void certificatePushRun(List<BigInteger> registrationNums,String userName)
  {
    System.out.print("开始上报作品材料...：\n");
    for(BigInteger registrationNum:registrationNums) {
      CopyrightManager manager = copyrightManagerService.getById(registrationNum.longValue());
      manager.setRegNum("REPORTING");
      copyrightManagerService.saveOrUpdateCopyright(manager);
    }
    LocalDateTime start = LocalDateTime.now();
    ResponseEntity<Map<String, Object>> result1 = certificatePush(registrationNums);
    CertificateCreateEntity certificateCreateEntity = new CertificateCreateEntity();
    certificateCreateEntity.setResult(result1.getBody().get("message").toString()+";"+result1.getBody().get("erroeMsg"));
    LocalDateTime finish = LocalDateTime.now();
    certificateCreateEntity.setCreateTime(start);
    certificateCreateEntity.setFinishTime(finish);
    certificateCreateEntity.setUserName(userName);

    certificateCreateService.save(certificateCreateEntity);
  }

  //提交证照中心线程
  @Async("doSomethingExecutor")
  public void licenseSendInfoRun(List<Long> registrationNums, UserEntity loginUser) {
      log.info("开始提交作品材料到证照中心...：\n");
      List<CopyrightManager> copyrightManagers = copyrightManagerService.getByIds(registrationNums);
      licenseSendInfo(copyrightManagers, loginUser);
  }

    public void licenseSendInfo(List<CopyrightManager> copyrightManagers, UserEntity loginUser) {
        for (CopyrightManager manager : copyrightManagers) {
          if (manager.getStatus_type() != CopyrightManager.CERT_CREATED
                  || manager.getEnable() == CopyrightManager.EnableStatusEnum.DISABLE
                  || manager.getInactiveType() == CopyrightManager.InactiveTypeValue.REVOKED
                  || manager.getLicenseState() == CopyrightManager.LicenseState.REPORTED) {
            copyrightManagers.remove(manager);
            continue;
          }
          manager.setLicenseStatus("submitting");
          manager.setLicenseState(CopyrightManager.LicenseState.REPORTING);
          copyrightManagerService.saveOrUpdateCopyright(manager);
        }
        LocalDateTime start = LocalDateTime.now();
        ResponseEntity<Map<String, Object>> result1 = licenseSendInfoSubmit(copyrightManagers, loginUser);
        CertificateCreateEntity certificateCreateEntity = new CertificateCreateEntity();
        certificateCreateEntity.setResult(result1.getBody().get("message").toString() + ";" + result1.getBody().get("erroeMsg"));
        LocalDateTime finish = LocalDateTime.now();
        certificateCreateEntity.setCreateTime(start);
        certificateCreateEntity.setFinishTime(finish);
        if (ObjectUtils.isEmpty(loginUser)) {
            certificateCreateEntity.setUserName("系统提交");
        } else {
            certificateCreateEntity.setUserName(loginUser.getUserName());
        }

        certificateCreateService.save(certificateCreateEntity);
    }

  // 自动提交证照中心线程
  @Async("doSomethingExecutor")
  public void licenseSendInfoAutoSubmit() {
    // 手动提交，推送1小时
    int now =  Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
    licenseSendInfoSubmit(now, now+1);
  }

  public void licenseSendInfoSubmit(int startHour, int endHour)
  {
      int num = 500;
      boolean flag = true;
      logger.info(new Date() + "【证照中心】调用开始：");
      while (flag) {
          try {
            int now =  Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
            // 自动提交, {8:00-20:00}点之间，暂停自动推送
            if (startHour > endHour)
              if (now >= endHour && now < startHour) {
                flag = false;
                continue;
              }

            // 手动提交，开始小时 小于 结束小时
            if (startHour <= endHour)
              if (now < startHour || now > endHour) {
                flag = false;
                continue;
              }

            List<CopyrightManager> copyrightManagers = copyrightManagerService.queryAllZJByCertificate(true, PageRequest.of(0, num));
            if (copyrightManagers.size() == 0) {
              flag = false;
              continue;
            } else if (copyrightManagers.size() < num) {
              flag = false;
            }
              licenseSendInfo(copyrightManagers, null);
          } catch (Exception e) {
              e.printStackTrace();
              logger.info(new Date() +"【证照中心】调用异常：", e);
              continue;
          }
      }
      logger.info(new Date() + "【证照中心】调用结束。");
  }

    //提交证照中心方法
    public ResponseEntity<Map<String, Object>> licenseSendInfoSubmit(List<CopyrightManager> copyrightManagers, UserEntity loginUser) {
        String errorMsg = "";

        if (ObjectUtils.isEmpty(loginUser)) {
            String cardId = "350104196910110013";
            List<UserEntity> userEntities = userServiceFacade.findByCardId(cardId);
            if (userEntities.size() > 1) {
                Optional<UserEntity> optionalUser = userEntities.stream().max(Comparator.comparing(UserEntity::getRegisterTime));
                if (optionalUser.isPresent()) {
                    loginUser = optionalUser.get();
                }
            } else {
                loginUser = userEntities.get(0);
            }
        }

        for (CopyrightManager cd : copyrightManagers) {
            try {
                for (int i = 0; i < 3; i++) {
                    /*上传证照信息*/
                    LicenseSendInfo licenseInfo = getLicenseSendInfo(cd);
                    JSONObject jsonInfo = licenseSendInfo(licenseInfo);
                    License license = new License(config);
                    String retValue = license.saveCertificate(jsonInfo);
                    JSONObject result = JSONObject.parseObject(retValue);
                    JSONObject headJson = result.getJSONObject("head");
                    if (headJson.getInteger("status").equals(0)) {
                        log.info("【证照中心】证照生成成功：{}", result.toJSONString());
                        JSONArray data = result.getJSONArray("data");
                        if (!ObjectUtils.isEmpty(data)) {
                          String certificateIdentifier = data.getJSONObject(0).getString("certificateIdentifier");
                          cd.setLicenseStatus(cd.getWorksNum());
                          cd.setLicenseState(CopyrightManager.LicenseState.REPORTED);
                          cd.setLicenseTime(LocalDateTime.now());
                          cd.setCertificateIdentifier(certificateIdentifier);

                          // 办结
                          finish(cd, loginUser);
                        }
                        break;
                    } else if (headJson.getInteger("status").equals(14)) {
                        logger.error("【证照中心】提交失败：" + result.toJSONString());
                        // 系统中已有该证照，不允许重复提交
                        JSONObject data = result.getJSONObject("data");
                        if (!ObjectUtils.isEmpty(data) && data.getString("certificateHolderCode").equals(cd.getOwnerList().get(0).getCopyIdCard())) {
                          String certificateIdentifier = data.getString("certificateIdentifier");
                          Long statisticsTime = data.getLong("createTime");
                          cd.setLicenseStatus(cd.getWorksNum());
                          cd.setLicenseState(CopyrightManager.LicenseState.REPORTED);
                          cd.setLicenseTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(statisticsTime), ZoneId.systemDefault()));
                          cd.setCertificateIdentifier(certificateIdentifier);
                          // 办结
                          finish(cd, loginUser);
                        }
                        errorMsg = errorMsg + cd.getWorksNum() + ":" + headJson.getString("message") + "\n";
                        break;
                    } else {
                      errorMsg = errorMsg + cd.getWorksNum() + ":" + headJson.getString("message") + "\n";
                      cd.setLicenseStatus(headJson.getString("message"));
                      cd.setLicenseState(CopyrightManager.LicenseState.FAILED);
                      cd.setLicenseTime(LocalDateTime.now());
                      logger.error("【证照中心】提交失败：" + result.toJSONString());
                      continue;
                    }
                }
                copyrightManagerService.saveOrUpdateCopyright(cd);
            } catch (Exception e) {
              // TODO Auto-generated catch block
              e.printStackTrace();
              logger.error("【证照中心】提交失败--系统异常：" + e.getMessage());
              Map<String, Object> result = new HashMap<String, Object>();
              result.put("result", 500);
              result.put("message", e.getMessage());
              cd.setLicenseStatus(e.getMessage());
              cd.setLicenseState(CopyrightManager.LicenseState.FAILED);
              copyrightManagerService.saveOrUpdateCopyright(cd);
              errorMsg = errorMsg + cd.getWorksNum() + ":" + e.getMessage() + "\n";
            }
        }
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("result", 200);
        if (StringUtils.isEmpty(errorMsg)) {
            result.put("message", "成功提交！");
        } else {
            result.put("message", "成功提交，未成功的作品有:\n" + errorMsg);
        }
        return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
    }

    @Transactional
    public void finish(CopyrightManager copyrightManager, UserEntity loginUser) {
        if (!config.getBackendIsOn()) return;
        try {
            List<Author> authorList = authorService.getAuthorByCopyrightId(copyrightManager.getRegistrationNum());
            copyrightManager.setAuthorList(authorList);
            List<ProcessProgressEntity> entities = processProgressService.findBySnCodeAndTypeAndState(copyrightManager.getSnCode(), ProcessProgressEntity.Type.PROCESS, ProcessProgressEntity.State.FINISHED);
            Optional<ProcessProgressEntity> entitiesOption = entities.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime));
            if (entitiesOption.isPresent()) {
                log.info("【做多采一次】开始--办结+证照,registrationNums:{}", copyrightManager.getRegistrationNum());
                backendServiceFacade.startWorkflow(copyrightManager, loginUser, ProcessProgressEntity.Type.FINISH, "办结", false);
                log.info("【做多采一次】结束--办结+证照,registrationNums:{}", copyrightManager.getRegistrationNum());
            }
        } catch (Exception e) {
            log.error("【做多采一次】失败--办结+证照,registrationNums:{}, 原因={}", copyrightManager.getRegistrationNum(), e.getLocalizedMessage());
        }
    }

    //提交证照中心方法
//  public ResponseEntity<Map<String, Object>> licenseSendInfoSubmitOld(List<CopyrightManager> copyrightManagers){
//    String errorMsg = "";
//    // 提交接口地址
//    String SUBMIT_URL = config.getLicenseUrl();
//    // 身份认证串--每天在LiceseGuidTask任务中跟新一次
//    String GUID = config.getLicenseGuid();
//    // 发送接口方法
//    String SENDSURFACE = config.getLicenseSurface();
//
//    for(CopyrightManager cd:copyrightManagers)
//    {
//      try {
//        /*上传证照信息*/
//        LicenseSendInfo licenseInfo=getLicenseSendInfo(cd);
//        /*证照信息JSON转XML*/
//        String xmlInfo = licenseSendInfoToXML(licenseInfo);
//        System.out.println(GUID+" "+APPNAME+" "+xmlInfo);
//        /*证照信息上传,远程调用接口*/
//        String retValue = WebServicesUtils.invoke(SUBMIT_URL,SENDSURFACE,new String[]{GUID,APPNAME,xmlInfo});
//        if(!licenseInfo.getWorksNum().equals(retValue))
//        {
//          errorMsg = errorMsg+cd.getWorksNum()+":"+retValue+"\n";
//        }
//        cd.setLicenseStatus(retValue);
//        cd.setLicenseTime(LocalDateTime.now());
//        copyrightManagerService.saveOrUpdateCopyright(cd);
//      } catch (Exception e) {
//        // TODO Auto-generated catch block
//        e.printStackTrace();
//        log.error("提交证照中心失败：", e.getMessage());
//        Map<String,Object> result=new HashMap<String,Object>();
//        result.put("result",500);
//        result.put("message",e.getMessage());
//        cd.setLicenseStatus(e.getMessage());
//        copyrightManagerService.saveOrUpdateCopyright(cd);
//        errorMsg = errorMsg+cd.getWorksNum()+":"+e.getMessage()+"\n";
//      }
//    }
//    Map<String,Object> result=new HashMap<String,Object>();
//    result.put("result",200);
//    if(StringUtils.isEmpty(errorMsg)) {
//      result.put("message", "成功提交！");
//    }else{
//      result.put("message", "成功提交，未成功的作品有:\n"+errorMsg);
//    }
//    return  new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
//  }

  //上报国家局方法
  @Transactional(rollbackFor = RuntimeException.class)
  public ResponseEntity<Map<String, Object>> certificatePush(List<BigInteger> registrationNums) {
    CloseableHttpClient client = HttpClientUtil.createHttpClient();
    String errorMsg = "";
    for(BigInteger registrationNum:registrationNums)
    {
      CopyrightBean crb = null;
      Map<String,Object> result=new HashMap<String,Object>();
      String key = UUID.randomUUID().toString();
      try {
        crb = getCopyrightBean(registrationNum);
        result = NationAddClient.Submit(client,crb,key);
      } catch(Exception e)
      {
        logger.error("error: ", e);
        result.put("result",500);
        result.put("message",e.getMessage());
        //return  new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
      }
      if (result.get("result").equals(200))
      {
        CopyrightManager cd = copyrightManagerServiceFacade.getById(registrationNum.longValue());
        cd.setRegNum(key);
        copyrightManagerService.saveOrUpdateCopyright(cd);
      }
      else
      {
        CopyrightManager cd = copyrightManagerServiceFacade.getById(registrationNum.longValue());
        if(result.get("message")!=null && result.get("message").toString().contains("登记号已存在")){
          if(cd.getRegNum()==null || cd.getRegNum().equals("") || cd.getRegNum().isEmpty() || cd.getRegNum().equals("REPORTING")){
            cd.setRegNum("linshiregnum");
            copyrightManagerService.saveOrUpdateCopyright(cd);
          }
        }else {
          System.out.println("Error:"+result.get("result")+result.get("message"));
          cd.setRegNum("");
          copyrightManagerService.saveOrUpdateCopyright(cd);
        }
        errorMsg = errorMsg+registrationNum+":"+((result.get("message")==null)?"服务器错误":result.get("message").toString())+"\n";
        //return new ResponseEntity<Map<String, Object>>(result, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
    if(client!=null){
      try {
        client.close();
      } catch (IOException e) {
        // TODO Auto-generated catch block
        logger.error("error: ", e);
      }
    }
    System.out.println("上报完成\n");
    Map<String,Object> results = new HashMap<String,Object>();
    results.put("result", 200);
    if (StringUtils.isEmpty(errorMsg)) {
        results.put("message", "上报完成");
    } else {
        results.put("message", "上报完成,未上报成功的作品有:\n" + errorMsg);
    }
    return new ResponseEntity<Map<String, Object>>(results, HttpStatus.OK);
  }

  // 将系统bean转换为国家接口对应bean
  public CopyrightBean getCopyrightBean(BigInteger registrationNum) {
    int i = 0;
    // 作品
    CopyrightManager cd = new CopyrightManager();
    cd = copyrightManagerServiceFacade.getById(registrationNum.longValue());
    // 首次发表时间类型转换
    Timestamp startime = new Timestamp(System.currentTimeMillis());
    try {
      startime = Timestamp.valueOf(cd.getFirstPublishTime() + " 00:00:00");
    } catch (Exception e) {
      // TODO Auto-generated catch block
    }
    // 权利拥有表类型转换
    CopyrightManager cp = new CopyrightManager();
    cp = copyrightManagerServiceFacade.getById(registrationNum.longValue());

    List<Integer> rightScope = cd.getHaverightIdList();
    String newRightScope = "";
    if(cd.getRightScope()!=2){
      List<Digital> digitals = digitalServiceFacade.getDictByPid(429);
      for(Digital digital:digitals){
        rightScope.add(digital.getSort());
      }
    }else{
      for(int j=0;j<rightScope.size();j++){
        rightScope.set(j,digitalServiceFacade.getById(rightScope.get(j)).getSort());
      }
    }
    for (i = 0; i < rightScope.size(); i++) {
      newRightScope = newRightScope + "," + rightScope.get(i);
    }
    newRightScope = newRightScope.replaceFirst(",", "");
    System.out.println(newRightScope);

    //权利归属方式处理
    int rightAttributionWay = 0;
    if(cd.getRightOwnMode()==1){
      rightAttributionWay=0;
    }else if(cd.getRightOwnMode()==2){
      rightAttributionWay=1;
    }else if(cd.getRightOwnMode()==3){
      rightAttributionWay=4;
    }else if(cd.getRightOwnMode()==4){
      rightAttributionWay=2;
    }else if(cd.getRightOwnMode()==5){
      rightAttributionWay=3;
    }
    //电子介质空值处理
    if(cd.getEmediumCD()==null||cd.getEmediumCD().isEmpty()){
      cd.setEmediumCD("");
    }
    if(cd.getEmediumOther()==null||cd.getEmediumOther().isEmpty()){
      cd.setEmediumOther("");
    }
    //作品完成国家/城市处理
    String finishCountry="";
    String finishCity="";
    String[] workAdress=cd.getCompleteAreaStr().split(" ");
    finishCountry=workAdress[0];
    if(workAdress.length>2){
      finishCity=workAdress[2];
    }else{
      finishCity=workAdress[1];
    }
    System.out.println(finishCountry+" and "+finishCity);
    String firstContry=null;
    String firstCity=null;
    try {
      firstContry= digitalServiceFacade.getById(Integer.parseInt(cd.getFirstCountry())).getDict_name();
      firstCity= digitalServiceFacade.getById(Integer.parseInt(cd.getFirstCity())).getDict_name();
    }
    catch (Exception e)
    {

    }
    // 与国家局Bean对接
    CopyrightManageBean copyrightManage = new CopyrightManageBean(
      cd.getProductionName(), cd.getWorksNum(),
      Date.from(cd.getCertificateCreateTime().atZone(ZoneId.systemDefault()).toInstant()),
      //Date.valueOf(cd.getCertificateCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-ddHH:mm:ss"))),
      digitalServiceFacade.getById(Integer.parseInt(cd.getProductionTypeId())).getSort(),
      cd.getOpuseDesc(), digitalServiceFacade.getById(Integer.parseInt(cd.getOpusInditekind())-1).getSort(),
      cd.getProductionTypeDesc(), Timestamp.valueOf(cd.getFinishTime()
      + " 00:00:00"), finishCountry,
      finishCity, (cd.getPublishState()-1), startime,
      firstContry ,firstCity,
      digitalServiceFacade.getById(cd.getObtainMode()).getSort(), "", rightAttributionWay, "",
      newRightScope, "", cd.getEmedium() + cd.getEmediumCD()
      + cd.getEmediumOther(), "1", "作品纸质介质", "1",
      cd.getApplyType()-1);
    // 作者
    List<Author> at = authorService.getAuthorByCopyrightId(cd.getRegistrationNum());//cd.getAuthorList();
    List<AuthorBean> authors = new ArrayList<AuthorBean>();
    for (i = 0; i < at.size(); i++) {
      AuthorBean author = new AuthorBean(at.get(i).getAuthorName(), at.get(i).getAuthorName());
      authors.add(author);
    }
    // 著作权人
    List<CopyrightOwner> coentity = copyrightOwnerService.getByCopyrightId(cd.getRegistrationNum());//cd.getOwnerList();
    List<CopyrightOwnerDto> co = CopyrightOwnerDtoMapper.INSTANCE.entityToDto(coentity);
    List<CopyrightOwnerBean> copyrightOwners = new ArrayList<CopyrightOwnerBean>();
    for (i = 0; i < co.size(); i++) {
      if(co.get(i).getCopySignature()==1){
        co.get(i).setCopySignature(0);
      }else if(co.get(i).getCopySignature()==3){
        co.get(i).setCopySignature(1);
      }
      CopyrightOwnerBean copyrightOwner = new CopyrightOwnerBean(co
        .get(i).getCopyName(), digitalServiceFacade.getById(co.get(i).getCopyCategory()).getSort(), digitalServiceFacade.getById(Integer.parseInt(co.get(i).getCopyCountries())).getSort(),
        digitalServiceFacade.getById(Integer.parseInt(co.get(i).getCopyProvince())).getSort(), co.get(i).getCopyCity(),digitalServiceFacade.getById(co.get(i).getCopyCertificate()).getSort(), co.get(i).getCopyIdCard(), co
        .get(i).getCopySignature(), co.get(i)
        .getCopySignatureName());
      copyrightOwners.add(copyrightOwner);
    }
    // 申请人
    List<SubmitterBean> submitters = new ArrayList<SubmitterBean>();
    Submitter temp = cd.getSubmitter();
    if(temp!=null)
    {
      SubmitterBean submitter = new SubmitterBean(temp.getCopyrightName(),
        temp.getCopyrightAddress(), temp.getCopyrightCode(),
        temp.getCopyrightOwner(), temp.getCopyrightTelephone(),
        temp.getCopyrightEmail(), temp.getCopyrightPhone(),
        temp.getCopyrightFax());
      submitters.add(submitter);
    }else{
      Agent otherTemp = cd.getAgentList();
      SubmitterBean submitter = new SubmitterBean(otherTemp.getAgentName(),
        otherTemp.getAgentAddress(), otherTemp.getAgentCode(),
        otherTemp.getAgentOwner(), otherTemp.getAgentTelephone(),
        otherTemp.getAgentEmail(), otherTemp.getAgentPhone(),
        otherTemp.getAgentFax());
      submitters.add(submitter);
    }
    // 代理人
    Agent agenttemp = (cd.getAgentList() == null)?new Agent():cd.getAgentList();
    AgentBean agent = new AgentBean(cd.getAgentBook()==null?"":cd.getAgentBook().getWorkName(), agenttemp.getAgentName(),
      agenttemp.getAgentAddress(), agenttemp.getAgentCode(), agenttemp.getAgentOwner(),
      agenttemp.getAgentTelephone(), agenttemp.getAgentEmail(), agenttemp.getAgentPhone(),
      agenttemp.getAgentFax());

    CopyrightBean crb = new CopyrightBean();
    crb.setAgent(agent);
    crb.setAuthor(authors);
    crb.setCopyrightManage(copyrightManage);
    crb.setCopyrightOwner(copyrightOwners);
    crb.setSubmitter(submitters);

        /*JSONObject json = JSONObject.fromObject(crb);
		System.out.println(json.toString());*/

    return crb;
  }

  private String getFileName(String path){
    String [] sz=path.split("/");
    return sz[sz.length-1];
  }

//  /**
//   * 证照信息转XML
//   * @param licenseInfo
//   */
//  public String licenseSendInfoToXML(LicenseSendInfo licenseInfo){
//    /*license根节点*/
//    JSONObject licenseNode = new JSONObject();//license节点
//    JSONObject licenseChildNode = new JSONObject();//定义license子节点
//
//    /*type节点，该节点信息由电子证照系统根据证照类型配置自动生成，业务系统不能修改*/
//    licenseChildNode.put("type", getNodeAttr("licensetype","作品登记证书","20161028204911"));
//
//    /*surfaceData节点，证照照面信息*/
//    licenseChildNode.put("surfacedata",getSurfaceChildNode(licenseInfo));
//
//    /*metaData节点，元数据，元数据中的个别信息来自于证照照面信息，必须保证二者的一致*/
//    licenseChildNode.put("metadata",getMetaChlidNode(licenseInfo));
//
//    licenseNode.put("license", licenseChildNode);
//
//    return WebServicesUtils.jsonToXML(licenseNode.toString());
//  }

//  /**
//   * 获取属性节点，参数对应属性KEY的值
//   * @param alias
//   * @param name
//   * @param value
//   * @return
//   */
//  public JSONObject getNodeAttr(String alias,String name, String value){
//    JSONObject nodeAttr = new JSONObject();
//    nodeAttr.put("@alias", alias);
//    nodeAttr.put("@name", name);
//    nodeAttr.put("@value", value);
//    return	nodeAttr;
//  }

//  /**
//   * surfaceData节点，证照照面信息
//   * @param licenseInfo
//   * @return SurfaceData节点的字节点内容
//   */
//  public JSONObject getSurfaceChildNode(LicenseSendInfo licenseInfo){
//    JSONObject surfaceChildNode = new JSONObject();
//
//    JSONObject baseNode = new JSONObject();//surfaceData节点->base节点
//    JSONArray baseItemNodeAttr = new JSONArray();//surfaceData节点->base节点->item节点->item节点属性
//    String nodeHead = licenseInfo.getNode().substring(0,2);
//    if(!nodeHead.equals("35")) {
//      baseItemNodeAttr.add(getNodeAttr("zzjgdm", "统一社会信用代码", licenseInfo.getNode()));
//    }else{
//      baseItemNodeAttr.add(getNodeAttr("sfzno", "身份证号码", licenseInfo.getNode()));
//    }
//    baseItemNodeAttr.add(getNodeAttr("bzdw","颁证单位","中共福建省委宣传部"));
//    baseItemNodeAttr.add(getNodeAttr("bzsj","颁证时间",licenseInfo.getRegistrationDate().split(" ")[0].replace("-","")));
//    baseItemNodeAttr.add(getNodeAttr("qymc","企业名称",licenseInfo.getAuthors()));
//    baseItemNodeAttr.add(getNodeAttr("zzbh","登记号",licenseInfo.getWorksNum()));
//    baseItemNodeAttr.add(getNodeAttr("yxqqs","有效期（起始）",licenseInfo.getRegistrationDate().split(" ")[0].replace("-","")));
//    baseItemNodeAttr.add(getNodeAttr("yxqjz","有效期（截止）","00000000"));
//    baseItemNodeAttr.add(getNodeAttr("zpzpmc","作品/制品名称",licenseInfo.getWorkName()));
//    baseItemNodeAttr.add(getNodeAttr("zz","作者",licenseInfo.getAuthors()));
//    baseItemNodeAttr.add(getNodeAttr("zplb","作品类别",licenseInfo.getWorkType()));
//    baseItemNodeAttr.add(getNodeAttr("zzqr","著作权人",licenseInfo.getOwners()));
//    baseItemNodeAttr.add(getNodeAttr("sccbzzrq","首次出版/制作日期",((licenseInfo.getFirstPublishTime()!=null && !licenseInfo.getFirstPublishTime().equals(""))?(licenseInfo.getFirstPublishTime().replace("-","")):"00000000")));
//    baseItemNodeAttr.add(getNodeAttr("sqr","申请人","著作权人"));
//    baseItemNodeAttr.add(getNodeAttr("shdw","审核单位","中共福建省委宣传部"));
//    baseItemNodeAttr.add(getNodeAttr("czwcrq","创作完成日期",licenseInfo.getFinishTime().replace("-","")));
//    baseNode.put("item",baseItemNodeAttr);
//    surfaceChildNode.put("base", baseNode);
//
//    return surfaceChildNode;
//  }

//  /**
//   * metaData节点，元数据，元数据中的个别信息来自于证照照面信息，必须保证二者的一致
//   * @param licenseInfo
//   * @return metaData节点的字节点内容
//   */
//  public JSONObject getMetaChlidNode(LicenseSendInfo licenseInfo){
//    JSONObject metaChlidNode = new JSONObject();
//
//    JSONObject baseNode = new JSONObject();//metaDataNode节点->base节点
//    JSONArray baseItemNodeAttr = new JSONArray();//metaDataNode节点->base节点->item节点->item节点属性
//    baseItemNodeAttr.add(getNodeAttr("zzlx","证照类型","作品登记证书"));
//    baseItemNodeAttr.add(getNodeAttr("zzbhgs","证照编号格式","闽作登字-XXXX-X-XXXXXXXX"));
//    baseItemNodeAttr.add(getNodeAttr("zzmc","证照名称",licenseInfo.getOwners() + "的作品登记证书"));
//    baseItemNodeAttr.add(getNodeAttr("zzbh","证照编号",licenseInfo.getWorksNum()));
//    baseItemNodeAttr.add(getNodeAttr("bzsj","颁证时间",licenseInfo.getRegistrationDate().split(" ")[0].replace("-","")));
//    baseItemNodeAttr.add(getNodeAttr("ksyxq","有效期（起始）",licenseInfo.getRegistrationDate().split(" ")[0].replace("-","")));
//    baseItemNodeAttr.add(getNodeAttr("jzyxq","有效期（截止）","00000000"));//八个0表示无期限
//    baseItemNodeAttr.add(getNodeAttr("bzdw","颁证单位","中共福建省委宣传部"));
//    baseItemNodeAttr.add(getNodeAttr("czz","持证者",licenseInfo.getOwners()));
//    baseItemNodeAttr.add(getNodeAttr("zzbgjl","证照变更记录",""));
//    baseItemNodeAttr.add(getNodeAttr("rjhj","软件环境",""));
//    baseItemNodeAttr.add(getNodeAttr("ywxw","业务行为",""));
//    baseItemNodeAttr.add(getNodeAttr("dzqzxx","电子签章信息",""));
//    baseItemNodeAttr.add(getNodeAttr("szzsxx","数字证书信息",""));
//    baseNode.put("item",baseItemNodeAttr);
//    metaChlidNode.put("base", baseNode);
//
//    JSONObject extendNode = new JSONObject();//metaDataNode节点->extend节点
//    JSONArray extendItemNodeAttr = new JSONArray();//metaDataNode节点->extend节点->item节点->item节点属性
//    extendItemNodeAttr.add(getNodeAttr("zzfzlbdz","证照废止列表地址",""));
//    extendItemNodeAttr.add(getNodeAttr("zzzxcxdz","证照在线查询地址",""));
//    extendItemNodeAttr.add(getNodeAttr("zzzxyzdz","证照在线验证地址",""));
//    extendItemNodeAttr.add(getNodeAttr("ztc","主题词",""));
//    extendItemNodeAttr.add(getNodeAttr("ly","来源",""));
//    extendItemNodeAttr.add(getNodeAttr("yy","语言",""));
//    extendItemNodeAttr.add(getNodeAttr("yjhj","硬件环境",""));
//    extendItemNodeAttr.add(getNodeAttr("tqm","提取码",""));
//    extendNode.put("item", extendItemNodeAttr);
//    metaChlidNode.put("extend", extendNode);
//
//    return metaChlidNode;
//  }

  LicenseSendInfo getLicenseSendInfo(CopyrightManager copyrightManager) throws Exception {
    String errorMsg;
    if (authorService.getAuthorByCopyrightId(copyrightManager.getRegistrationNum()).size() == 0) {
      errorMsg = copyrightManager.getWorksNum() + "作者为空";
      logger.error(errorMsg);
      throw new Exception(errorMsg);
    }
    if (copyrightOwnerService.getByCopyrightId(copyrightManager.getRegistrationNum()).size() == 0) {
      errorMsg = copyrightManager.getWorksNum() + "著作权人为空";
      logger.error(errorMsg);
      throw new Exception(errorMsg);
    }
    LicenseSendInfo licenseSendInfo=new LicenseSendInfo();
    licenseSendInfo.setAuthors(authorService.getAuthorByCopyrightId(copyrightManager.getRegistrationNum()).get(0).getAuthorName());
    licenseSendInfo.setFinishTime(StringUtils.isEmpty(copyrightManager.getFinishTime()) ? "" :
            LocalDate.parse(copyrightManager.getFinishTime()).format(DateTimeFormatter.ofPattern("yyyy年M月d日")));
    licenseSendInfo.setFirstPublishTime(StringUtils.isEmpty(copyrightManager.getFirstPublishTime()) ? "" :
            LocalDate.parse(copyrightManager.getFirstPublishTime()).format(DateTimeFormatter.ofPattern("yyyy年M月d日")));
    licenseSendInfo.setLicenseStatus(copyrightManager.getLicenseStatus());
    licenseSendInfo.setNode(copyrightOwnerService.getByCopyrightId(copyrightManager.getRegistrationNum()).get(0).getCopyIdCard());
    licenseSendInfo.setOwners(copyrightOwnerService.getByCopyrightId(copyrightManager.getRegistrationNum()).get(0).getCopyName());
    String ownerTypeName;
    String ownerTypeCode;
    switch (copyrightOwnerService.getByCopyrightId(copyrightManager.getRegistrationNum()).get(0).getCopyCategory()) {
      case CopyrightOwner.CopyCategoryValue.PEOPLE:
        switch (copyrightOwnerService.getByCopyrightId(copyrightManager.getRegistrationNum()).get(0).getCopyCertificate()) {
          case CopyrightOwner.CopyCertificateValue.IDCARD:
          case CopyrightOwner.CopyCertificateValue.RESIDENT:
            ownerTypeName = "公民身份号码";
            ownerTypeCode = "111";
            break;
          case CopyrightOwner.CopyCertificateValue.TWTXZ:
            ownerTypeName = "台湾居民来往大陆通行证号码";
            ownerTypeCode = "511";
            break;
          case CopyrightOwner.CopyCertificateValue.GATTXZ:
            ownerTypeName = "港澳居民来往内地通行证号码";
            ownerTypeCode = "516";
            break;
          default:
            ownerTypeName = "其他自然人有效证件代码";
            ownerTypeCode = "999";
            break;
        }
        break;
      case CopyrightOwner.CopyCategoryValue.OTHERORG:
        ownerTypeName = "其他法人或其他组织有效证件代码";
        ownerTypeCode = "099";
        break;
      default:
        ownerTypeName = "统一社会信用代码";
        ownerTypeCode = "001";
        break;
    }
    licenseSendInfo.setOwnerTypeName(ownerTypeName);
    licenseSendInfo.setOwnerTypeCode(ownerTypeCode);
    licenseSendInfo.setRegistrationDate(copyrightManager.getRegistrationDate().format(DateTimeFormatter.ofPattern("yyyy年M月d日")));
    licenseSendInfo.setRegistrationNum(copyrightManager.getRegistrationNum());
    licenseSendInfo.setWorkName(copyrightManager.getProductionName());
    licenseSendInfo.setWorksNum(copyrightManager.getWorksNum());
    licenseSendInfo.setWorkType(copyrightManager.getProductionTypeId());

    return  licenseSendInfo;
  }

  /**
   * 证照信息转XML
   * @param licenseInfo
   */
  public JSONObject licenseSendInfo(LicenseSendInfo licenseInfo){
    JSONObject licenseNode = new JSONObject();//证照信息，Json 字符串，按照证照系统提供的证照信息结构 封装证照信息
    JSONObject certificateNode = new JSONObject();//certificate节点
    certificateNode.put("certificateHolderName",licenseInfo.getOwners()); // 持证主体
    certificateNode.put("certificateHolderCode",licenseInfo.getNode()); // 持证主体代码
    certificateNode.put("certificateHolderTypeName",licenseInfo.getOwnerTypeName()); // 持证主体代码类型名称
    certificateNode.put("certificateHolderTypeCode",licenseInfo.getOwnerTypeCode()); // 持证主体代码类型代码
    certificateNode.put("certificateIssuedDate", licenseInfo.getRegistrationDate()); // 证照颁发日期
    certificateNode.put("certificateEffectiveDate", licenseInfo.getRegistrationDate()); // 证照有效期起始日期
    certificateNode.put("certificateExpiringDate","长期"); // 证照有效期截止日期
    certificateNode.put("certificateIssuingAuthorityName","中共福建省委宣传部"); // 证照颁发机构
    certificateNode.put("certificateIssuingAuthorityCode","11350000003591037R"); // 证照颁发机构代码
    certificateNode.put("certificateName","作品登记证书"); // 证照名称 licenseInfo.getOwners() + "的作品登记证书"
    certificateNode.put("certificateTypeName","作品登记证书"); // 证照类型名称
    certificateNode.put("certificateTypeCode","11100000MB0372496N045"); // 证照类型代码
    certificateNode.put("certificateNumber",licenseInfo.getWorksNum()); // 证照编号
    certificateNode.put("surface",getSurfaceNode(licenseInfo));
    certificateNode.put("catalog",getCatalogNode());
    certificateNode.put("detail",new JSONArray());
    licenseNode.put("certificate", certificateNode);
    return licenseNode;
  }

  /**
   * certificate节点
   * @param licenseInfo
   * @return certificate节点的字节点内容
   */
  public JSONArray getSurfaceNode(LicenseSendInfo licenseInfo){
    JSONArray surfaceNode = new JSONArray();
    surfaceNode.add(getNodeAttr2("string", "0", "zpzpmc","作品/制品名称",licenseInfo.getWorkName()));
    surfaceNode.add(getNodeAttr2("string", "0", "zz","作者",licenseInfo.getAuthors()));
    surfaceNode.add(getNodeAttr2("string", "0", "zplb","作品类别",licenseInfo.getWorkType()));
    surfaceNode.add(getNodeAttr2("string", "0", "zzqr","著作权人",licenseInfo.getOwners()));
    surfaceNode.add(getNodeAttr2("date", "0", "sccbzzrq","首次出版/制作日期",licenseInfo.getFirstPublishTime()));
    surfaceNode.add(getNodeAttr2("string", "0", "sqr","申请人","著作权人"));
    surfaceNode.add(getNodeAttr2("string", "0", "shdw","审核单位","中共福建省委宣传部"));
    surfaceNode.add(getNodeAttr2("date", "0", "czwcrq","创作完成日期",licenseInfo.getFinishTime()));

    return surfaceNode;
  }

  /**
   * 获取属性节点，参数对应属性KEY的值
   * @param
   * @param name
   * @param value
   * @return
   */
  public JSONObject getNodeAttr2(String valueType, String require, String columnName,String name, String value){
    JSONObject nodeAttr = new JSONObject();
    nodeAttr.put("valueType", valueType);
    nodeAttr.put("name", name);
    nodeAttr.put("require", require);
    nodeAttr.put("value", value);
    nodeAttr.put("columnName", columnName);
    return	nodeAttr;
  }

  /**
   * catalog节点
   * @param
   * @return catalog节点的内容
   */
  public JSONObject getCatalogNode(){
    JSONObject catalogNode = new JSONObject();
    catalogNode.put("templateName", "作品登记证书");
    catalogNode.put("certificateCatalogName", "作品登记证书");
    catalogNode.put("templateId", "172D420619504277BE9BAB49124FFE93");
    catalogNode.put("certificateCatalogCode", "BA750A9F0240442B834DC0B7B51D78BF");
    return catalogNode;
  }

  /**
   * 创建保存任务
   * @return
   */
  @Transactional(rollbackFor = RuntimeException.class)
  public void saveTask(TaskEntity taskEntity) {
    taskService.save(taskEntity);
  }

    /**
     * 获取任务实体
     *
     * @param id
     * @return
     */
    public TaskEntity getTask(Integer id) {
        return taskService.findById(id);
    }

    /**
     * 上报版保中心作品导出
     */
    @Async("doSomethingExecutor")
    public void exportDataExcelRun(@Valid @RequestBody QueryParam queryParam) {
        String fileName = "";
        String fileName2 = "";
        Map<String, Object> result = new HashedMap();

        Integer taskId = queryParam.getInteger("taskId");
        logger.info("----版保中心导出开始：" + taskId + "----");

        //查询作品
        JSONArray array = queryParam.getJSONArray("status_type");
        List<Integer> statusList = new ArrayList<>();
        if (array != null && !array.isEmpty()) {
            statusList = JSONObject.parseArray(array.toJSONString(), Integer.class);
        }

        UserDto userDto = userServiceFacade.findUserById(queryParam.getInteger("UID"));
        if (userDto == null) {
            userDto = new UserDto();
        }

        List<String> productionTypeIds = new ArrayList<>();
        List<String> productionTypeIdsREC = new ArrayList<>();
        String productionTypeId = queryParam.getString("productionTypeId");
        if (!org.springframework.util.StringUtils.isEmpty(productionTypeId)) { // 作品类别
            if (!productionTypeId.equals("98") && !productionTypeId.equals("99")) {
                productionTypeIds.add(productionTypeId);
            } else {
                productionTypeIdsREC.add(productionTypeId);
            }
        } else {
            //获取作品类型
            List<DigitalDto> digitalDtos = DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82));
            for (DigitalDto digitalDto:digitalDtos) {
                if (digitalDto.getId() != 98 && digitalDto.getId() != 99) {
                    productionTypeIds.add(String.valueOf(digitalDto.getId()));
                } else {
                    productionTypeIdsREC.add(String.valueOf(digitalDto.getId()));
                }
            }
        }

        List<CopyrightManageExportDto> list = copyrightManagerServiceFacade.queryList(queryParam.getString("worksNum"), queryParam.getString("productionName"), productionTypeIds,
                queryParam.getInteger("rightOwnMode"), queryParam.getString("startTime"), queryParam.getString("endTime"), statusList, null, queryParam.getString("agentName"), queryParam.getString("copyrightName"),
                queryParam.getBoolean("isCertificate"),queryParam.getBoolean("isSubmit"),queryParam.getBoolean("isreport"),userDto.getVisibleLevelCity(),userDto.getVisibleLevelCounty(),null,null,
                queryParam.getString("certificateStartDate"), queryParam.getString("certificateEndDate"));


        List<CopyrightManageExportDto> listREC = copyrightManagerServiceFacade.queryList(queryParam.getString("worksNum"), queryParam.getString("productionName"), productionTypeIdsREC,
                queryParam.getInteger("rightOwnMode"), queryParam.getString("startTime"), queryParam.getString("endTime"), statusList, null, queryParam.getString("agentName"), queryParam.getString("copyrightName"),
                queryParam.getBoolean("isCertificate"),queryParam.getBoolean("isSubmit"),queryParam.getBoolean("isreport"),userDto.getVisibleLevelCity(),userDto.getVisibleLevelCounty(),null,null,
                queryParam.getString("certificateStartDate"), queryParam.getString("certificateEndDate"));

        String filePath =null;
        String filePath2 =null;
        Label lab = null;
        WritableWorkbook workbook=null;
        WritableSheet sheet =null;
        List<String> urls = new ArrayList<>();
        // 输出的excel的路径
        //取得根目录路径，将当前的excel表格存入工程的路径下
        String contextPath = "File/"+config.getModelRoot();
        String rootPath =  contextPath + "exportDoc";
        String replace = rootPath.replace("\\", "/");
        new File(replace).mkdirs();

        LocalDateTime localDateTime=LocalDateTime.now();
        fileName="作品数据导出"+localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"))+".xls";
        String[] titleOne = {"登记号","登记日期","作品名称","作品类别","著作权人姓名/名称","创作完成日期","首次发表日期","作品数量","创作性质","国    籍","省    份","城    市","作者姓名/名称","权利取得方式","权利归属方式","登记机构"};
        filePath=rootPath+"/"+fileName;
        jxlUtil(list,lab,filePath,titleOne,workbook,sheet,"作品数据汇总");

        urls.add(filePath);

        fileName2="制品数据导出"+localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"))+".xls";
        String[] titleOne2 = {"登记号","登记日期","录音录像制品名称","制品类型","权利人姓名/名称","制作完成日期","首次发表日期","制作者姓名/名称","制品数量","权利人类型","国    籍","省    份","城    市","登记机构"};
        filePath2=rootPath+"/"+fileName2;
        jxlUtil(listREC,lab,filePath2,titleOne2,workbook,sheet,"制品数据汇总");

        urls.add(filePath2);
        result.put("urls",urls);

        if (taskId != null && taskId > 0) {
            TaskEntity taskEntity = this.getTask(taskId);
            taskEntity.setUrl(urls);
            this.saveTask(taskEntity);
        }
        logger.info("----版保中心导出结束："+ taskId + "----");
//    return  new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
    }

  public void jxlUtil(List<CopyrightManageExportDto> list, Label lab, String filePath, String[] title, WritableWorkbook workbook, WritableSheet sheet, String tableName){

    try {
      File file = new File(filePath);
      workbook = Workbook.createWorkbook(file);
      sheet = workbook.createSheet(tableName, 0);  //单元格

      WritableFont wf2= new WritableFont(WritableFont.ARIAL,14,WritableFont.BOLD,false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
      WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
      wcfTitle.setBackground(Colour.IVORY);  //象牙白
      wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,Colour.BLACK); //BorderLineStyle边框
      //wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
        wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐

      CellView navCellView = new CellView();
      navCellView.setAutosize(true); //设置自动大小
      navCellView.setSize(18);
      WritableFont wfcNav =new WritableFont(WritableFont.ARIAL,12, WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,Colour.BLACK);
      WritableCellFormat wcfN=new WritableCellFormat(wfcNav);

      Color color = Color.decode("#0099cc"); // 自定义的颜色
      workbook.setColourRGB(Colour.ORANGE, color.getRed(),color.getGreen(), color.getBlue());
      wcfN.setBackground(Colour.ORANGE);
        wcfN.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, Colour.BLACK); //BorderLineStyle边框
        wcfN.setAlignment(Alignment.CENTRE); //设置水平对齐
        wcfN.setWrap(false); //设置自动换行
      for(int i=0;i<title.length;i++){
        lab = new Label(i,0,title[i],wcfN); //Label(col,row,str);
        sheet.addCell(lab);
        sheet.setColumnView(i, new String(title[i]).length());
      }

      WritableFont wfcontent =new WritableFont(WritableFont.ARIAL,18, WritableFont.NO_BOLD,false,UnderlineStyle.NO_UNDERLINE,Colour.BLACK);
      WritableCellFormat wcfcontent = new WritableCellFormat(wfcontent);
      wcfcontent.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,Colour.BLACK); //BorderLineStyle边框
      wcfcontent.setAlignment(Alignment.CENTRE);
      if (tableName.equals("作品数据汇总")) {
        excelData(list, lab, sheet);
      } else {
        excelRECData(list, lab, sheet);
      }
      workbook.write();// 写入数据
      workbook.close();// 关闭文件
    } catch (Exception e) {
      logger.error("error: ", e);
      System.out.println("数据异常");
    }
  }

  /**
   * 组合数据，将数据拼接到excel的表格中
   * @param list
   * @param lab
   * @param sheet
   */
  public void excelData(List<CopyrightManageExportDto> list, Label lab, WritableSheet sheet){
    try {
      String productionTypeName;
      for (int j = 0; j < 17; j++) {sheet.setColumnView(j, 25);}//设置列宽
      for(int i=0;i<list.size();i++){
        sheet.setRowView(i+1,380);
        lab = new Label(0,i+1,list.get(i).getWorksNum()); // 登记号
        sheet.addCell(lab);
        lab = new Label(1,i+1,list.get(i).getRegistrationDate().toLocalDate().toString().replaceAll("-","/")); // 登记日期
        sheet.addCell(lab);
        lab = new Label(2,i+1,list.get(i).getProductionName());  // 名称
        sheet.addCell(lab);
//        productionTypeName = digitalServiceFacade.getById(Integer.parseInt(list.get(i).getProductionTypeId())).getDict_name();
        productionTypeName = list.get(i).getProductionType();
        if (productionTypeName.contains("美术")) {
          productionTypeName = "美术";
        }
        lab = new Label(3,i+1,productionTypeName ); // 类型
        sheet.addCell(lab);
        lab = new Label(4,i+1,list.get(i).getCopyName());   //权利人姓名/名称
        sheet.addCell(lab);
        lab = new Label(5,i+1,list.get(i).getFinishTime()==null?"":list.get(i).getFinishTime().replaceAll("-","/"));  // 完成日期
        sheet.addCell(lab);
        lab = new Label(6,i+1,list.get(i).getFirstPublishTime()==null?"":list.get(i).getFirstPublishTime().replaceAll("-","/"));  //首次发表日期
        sheet.addCell(lab);
//        lab = new Label(8,i+1,digitalServiceFacade.getById(Integer.parseInt(list.get(i).getOpusInditekind())).getDict_name());  //创作性质
        lab = new Label(8,i+1,list.get(i).getOpusInditekind());  //创作性质
        sheet.addCell(lab);
        lab = new Label(12,i+1,list.get(i).getAuthorName());  //作者姓名/名称
        sheet.addCell(lab);
//        lab = new Label(13,i+1,digitalServiceFacade.getById(list.get(i).getObtainMode()).getDict_name());  //权利取得方式
        lab = new Label(13,i+1,list.get(i).getObtainMode());  //权利取得方式
        sheet.addCell(lab);
//        lab = new Label(14,i+1,digitalServiceFacade.getById(list.get(i).getRightOwnMode()).getDict_name());  //权利归属方式
        lab = new Label(14,i+1,list.get(i).getRightOwnMode());  //权利归属方式
        sheet.addCell(lab);

      }
    } catch (Exception e) {
      logger.error("error: ", e);
    }
  }

  /**
   * 组合数据，将制品数据拼接到excel的表格中
   * @param list
   * @param lab
   * @param sheet
   */
  public void excelRECData(List<CopyrightManageExportDto> list, Label lab, WritableSheet sheet){
    try {
      for (int j = 0; j < 17; j++) {sheet.setColumnView(j, 25);}//设置列宽
      for(int i=0;i<list.size();i++){
        sheet.setRowView(i+1,380);
        lab = new Label(0,i+1,list.get(i).getWorksNum()); // 登记号
        sheet.addCell(lab);
        lab = new Label(1,i+1,list.get(i).getRegistrationDate().toLocalDate().toString().replaceAll("-","/")); // 登记日期
        sheet.addCell(lab);
        lab = new Label(2,i+1,list.get(i).getProductionName());  // 名称
        sheet.addCell(lab);
//        lab = new Label(3,i+1,digitalServiceFacade.getById(Integer.parseInt(list.get(i).getProductionTypeId())).getDict_name()); // 类型
        lab = new Label(3,i+1,list.get(i).getProductionType()); // 类型
        sheet.addCell(lab);
        lab = new Label(4,i+1,list.get(i).getCopyName());   //权利人姓名/名称
        sheet.addCell(lab);
        lab = new Label(5,i+1,list.get(i).getFinishTime()==null?"":list.get(i).getFinishTime().replaceAll("-","/"));  // 完成日期
        sheet.addCell(lab);
        lab = new Label(6,i+1,list.get(i).getFirstPublishTime()==null?"":list.get(i).getFirstPublishTime().replaceAll("-","/"));  //首次发表日期
        sheet.addCell(lab);
        lab = new Label(7,i+1,list.get(i).getAuthorName());  //作者姓名/名称
        sheet.addCell(lab);

      }
    } catch (Exception e) {
      logger.error("error: ", e);
    }
  }

}
