package blockChain.facade.service;

import blockChain.bean.Constant;
import blockChain.controller.exception.NotFountException;
import blockChain.dto.CopyrightManagerMarkerDto;
import blockChain.entities.CopyrightManager;
import blockChain.entities.CopyrightManagerMarker;
import blockChain.entities.TagEntity;
import blockChain.mapper.CopyrightManagerMarkerDtoMapper;
import blockChain.service.CopyrightManagerMarkerService;
import blockChain.service.CopyrightManagerService;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2020/4/14 14:30
 */
@Service
@AllArgsConstructor
public class CopyrightManagerMarkerServiceFacade {

  private CopyrightManagerMarkerService service;
  private CopyrightManagerService managerService;
  private CopyrightManagerService copyrightManagerService;
  private TagServiceFacade tagServiceFacade;

  @Transactional
  public void create(CopyrightManagerMarkerDto dto) {
    List<Long> ids = dto.getIdList();
    Assert.notEmpty(ids, "请选择作品");
    ids.forEach(id -> {
      CopyrightManager manager = managerService.getById(id);
      if (manager.getFocusWork() != Constant.INT_TRUE) {
          dto.setUuid(UUID.randomUUID().toString());
          if (!ObjectUtils.isEmpty(manager.getMarker()))
              delete(Collections.singletonList(manager.getMarker().getId()));
          CopyrightManagerMarker entity = CopyrightManagerMarkerDtoMapper.INSTANCE.toEntity(dto);
          service.save(entity);
          entity.setManager(manager);
          manager.setMarker(entity);
          //设置作品状态
          manager.setFocusWork(1);
          copyrightManagerService.saveOrUpdateCopyright(manager);
      }
    });
  }

  @Transactional
  public void isAccusationCreate(CopyrightManagerMarkerDto dto) {
    List<Long> ids = dto.getIdList();
    Assert.notEmpty(ids, "请选择作品");
    ids.forEach(id -> {
      /*dto.setUuid(UUID.randomUUID().toString());
      CopyrightManagerMarker entity = CopyrightManagerMarkerDtoMapper.INSTANCE.toEntity(dto);
      service.save(entity);
      CopyrightManager manager = managerService.getById(id);
      entity.setManager(manager);
      manager.setMarker(entity);*/
      //设置作品状态
      CopyrightManager copyrightManager = copyrightManagerService.getById(id);
      if(copyrightManager!=null){
        copyrightManager.setIsAccusation(true);
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
      }
    });

  }

  @Transactional
  public void update(CopyrightManagerMarkerDto dto) {
    CopyrightManagerMarker marker = service.findByUuid(dto.getUuid()).orElseThrow(NotFountException::new);
    CopyrightManagerMarkerDtoMapper.INSTANCE.update(dto, marker);
  }

  @Transactional
  public void delete(List<Long> ids) {
    Assert.notEmpty(ids, "请选择作品");
    ids.forEach(id -> {
      CopyrightManager manager = managerService.getById(id);
      if(manager!=null && manager.getMarker()!=null)
      {
        service.delete(manager.getMarker());
        //设置作品状态
        manager.setFocusWork(0);
        copyrightManagerService.saveOrUpdateCopyright(manager);
      }
    });
  }

  @Transactional
  public void isAccusationDelete(List<Long> ids) {
    Assert.notEmpty(ids, "请选择作品");
    ids.forEach(id -> {
      /*CopyrightManager manager = managerService.getById(id);
      CopyrightManagerMarker marker = manager.getMarker();
      service.delete(marker);*/
      //设置作品状态
      CopyrightManager copyrightManager = copyrightManagerService.getById(id);
      if(copyrightManager!=null && copyrightManager.getIsAccusation()!=null && copyrightManager.getIsAccusation()){
        // 非侵权作品取消侵权不修改
        copyrightManager.setIsAccusation(false);
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
      }
    });
  }

  @Transactional
  public void isAccusationDeleteList(List<Long> ids) {
    Assert.notEmpty(ids, "请选择作品");
    ids.forEach(id -> {
      /*CopyrightManager manager = managerService.getById(id);
      CopyrightManagerMarker marker = manager.getMarker();
      service.delete(marker);*/
      //设置作品状态
      CopyrightManager copyrightManager = copyrightManagerService.getById(id);
      if(copyrightManager!=null){
        if(copyrightManager.getIsAccusation()==null){
          copyrightManager.setIsAccusation(true);
        }else {
          copyrightManager.setIsAccusation(!copyrightManager.getIsAccusation());
        }
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
      }
    });
  }


  @Transactional
  public ResponseEntity<Map<String, Object>> saveTags(CopyrightManagerMarkerDto dto) {
    Map<String,Object> result=new HashMap<>();
    List<TagEntity> tags;
    Long registrationNum = dto.getRegistrationNum();
    List<Long> idList = dto.getIdList();
    if (idList.size()==0) {
      Assert.notNull(registrationNum, "作品ID为空");
      CopyrightManager copyrightManager = copyrightManagerService.getById(registrationNum);
      if(copyrightManager!=null) {
        List<Integer> ids = dto.getTagIds();
        if (StringUtils.isEmpty(ids)) {
          tags = new ArrayList<>();
        } else {
          List<Integer> realIds = new ArrayList<>();
          String idStr;
          for (Integer id : ids) {
            idStr = id.toString();
            if (idStr.length()==5) {
              Integer realId = Integer.valueOf(idStr.substring(1));
              if (idStr.startsWith("1")) {
                realIds.add(realId);
              }
            } else {
              realIds.add(id);
            }
          }
          tags = tagServiceFacade.getTagByIds(realIds);
        }
        List<String> tagStrs = dto.getTagStrs();
        if (!StringUtils.isEmpty(tagStrs)) {
          for (String tagStr : tagStrs) {
            TagEntity tag = tagServiceFacade.createTag(tagStr);
            tags.add(tag);
          }
        }

        copyrightManager.setTags(tags);
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
        result.put("allSuccess",true);
        result.put("message", "标签设置成功");
      }
    } else if (StringUtils.isEmpty(registrationNum)) {
      Assert.notEmpty(idList, "请选择作品");
      return plsaveTags(dto);
    }
    return  new ResponseEntity<>(result, HttpStatus.OK);
  }

  /**
   * 批量添加标签（增量）
   * @param dto
   */
  @Transactional
  public ResponseEntity<Map<String, Object>> plsaveTags(CopyrightManagerMarkerDto dto) {
    List<String> messageList = new ArrayList<>();
    messageList.add("");
    int maxTagNum = 5;
    AtomicInteger addNum = new AtomicInteger();
//    AtomicInteger failNum = new AtomicInteger();
    List<Long> idList = dto.getIdList();
    Assert.notEmpty(idList, "请选择作品");
    List<TagEntity> addTags = getTags(dto);
    if (addTags.size() == 0) throw new IllegalArgumentException("请选择标签");
    idList.forEach(id -> {
      //设置作品状态
      CopyrightManager copyrightManager = copyrightManagerService.getById(id);
      boolean errorflag = true;
      if (copyrightManager == null) {
        messageList.add("失败原因：其他");
        errorflag = true;
      } else if(!copyrightManager.getEnable().equals(CopyrightManager.EnableStatusEnum.ENABLE)
              || copyrightManager.getInactiveType().equals(CopyrightManager.InactiveTypeValue.REVOKED)
              || (copyrightManager.getStatus_type() != CopyrightManager.FIRST_REVIEW
              && copyrightManager.getStatus_type() != CopyrightManager.SECOND_REVIEW
              && copyrightManager.getStatus_type() != CopyrightManager.FINAL_REVIEW)) {
        messageList.add("【" + copyrightManager.getWorksNum() + "】失败原因：作品状态错误");
        errorflag = true;
      } else {
        List<TagEntity> tags = copyrightManager.getTags();
        for (TagEntity tag: addTags) {
          if (tags.contains(tag)) {
            errorflag = false;
            continue;
          }
          if (tags.size() >= maxTagNum) {
            messageList.add("【" + copyrightManager.getWorksNum() + "】失败原因：作品标签数量已达上限");
            errorflag = true;
            break;
          }
          tags.add(tag);
          errorflag = false;
        }

        copyrightManager.setTags(tags);
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
      }
      // 成功、失败数量+1
      if (!errorflag) addNum.getAndIncrement();
//      else failNum.getAndIncrement();
    });

    Map<String,Object> result=new HashMap<>();
    if(idList.size() == addNum.get()) {
      result.put("allSuccess",true);
      result.put("message", "批量添加标签成功 "+ addNum + " 件作品！");
    }else{
      result.put("allSuccess",false);
      messageList.set(0, "批量添加标签成功 " + addNum + " 件作品");
      result.put("messageList", messageList);
    }
    return  new ResponseEntity<>(result, HttpStatus.OK);
  }

  private List<TagEntity> getTags(CopyrightManagerMarkerDto dto) {
    List<TagEntity> tags;

    List<Integer> ids = dto.getTagIds();
    if (StringUtils.isEmpty(ids)) {
      tags = new ArrayList<>();
    } else {
      List<Integer> realIds = new ArrayList<>();
      String idStr;
      for (Integer tagId : ids) {
        idStr = tagId.toString();
        if (idStr.length()==5) {
          Integer realId = Integer.valueOf(idStr.substring(1));
          if (idStr.startsWith("1")) {
            realIds.add(realId);
          }
        } else {
          realIds.add(tagId);
        }
      }
      tags = tagServiceFacade.getTagByIds(realIds);
    }
    List<String> tagStrs = dto.getTagStrs();
    if (!StringUtils.isEmpty(tagStrs)) {
      for (String tagStr : tagStrs) {
        TagEntity tag = tagServiceFacade.createTag(tagStr);
        tags.add(tag);
      }
    }
    return tags;
  }
}
