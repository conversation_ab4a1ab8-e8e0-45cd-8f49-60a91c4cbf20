package blockChain.scheduling;

import blockChain.facade.service.AlertMessageServiceFacade;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9 16:39
 */
@Component
@AllArgsConstructor
@Slf4j
public class AlertMessageScheduling {

  private AlertMessageServiceFacade facade;

  /**
   * 每分钟检查AlertMessage状态
   */
  @Scheduled(cron="0 * * * * ? ")
  @Transactional
  public void closureNotice(){
    facade.closureNotice();
  }
}
