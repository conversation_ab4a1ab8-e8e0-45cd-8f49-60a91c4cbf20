
package org.tempuri;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "Cegn_FGWQueryDataSoap", targetNamespace = "http://tempuri.org/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface CegnFGWQueryDataSoap {


    /**
     * 发展改革委信用域_统一社会信用代码服务接口
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryTyshxydm", action = "http://tempuri.org/QueryTyshxydm")
    @WebResult(name = "QueryTyshxydmResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryTyshxydm", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryTyshxydm")
    @ResponseWrapper(localName = "QueryTyshxydmResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryTyshxydmResponse")
    public String queryTyshxydm(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委信用域_企业基本信息
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryEnterpriseInfo", action = "http://tempuri.org/QueryEnterpriseInfo")
    @WebResult(name = "QueryEnterpriseInfoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryEnterpriseInfo", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryEnterpriseInfo")
    @ResponseWrapper(localName = "QueryEnterpriseInfoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryEnterpriseInfoResponse")
    public String queryEnterpriseInfo(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_双公示即行政许可行政处罚信息
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryTwoPublishInfo", action = "http://tempuri.org/QueryTwoPublishInfo")
    @WebResult(name = "QueryTwoPublishInfoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryTwoPublishInfo", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryTwoPublishInfo")
    @ResponseWrapper(localName = "QueryTwoPublishInfoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryTwoPublishInfoResponse")
    public String queryTwoPublishInfo(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_社团法人信息
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryAssociationLegalperson", action = "http://tempuri.org/QueryAssociationLegalperson")
    @WebResult(name = "QueryAssociationLegalpersonResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryAssociationLegalperson", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryAssociationLegalperson")
    @ResponseWrapper(localName = "QueryAssociationLegalpersonResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryAssociationLegalpersonResponse")
    public String queryAssociationLegalperson(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_国有产权交易公告列表信息查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryRightsTransactionList", action = "http://tempuri.org/QueryRightsTransactionList")
    @WebResult(name = "QueryRightsTransactionListResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryRightsTransactionList", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryRightsTransactionList")
    @ResponseWrapper(localName = "QueryRightsTransactionListResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryRightsTransactionListResponse")
    public String queryRightsTransactionList(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_国有产权交易公告详细查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryRightsTransactionDetailInfo", action = "http://tempuri.org/QueryRightsTransactionDetailInfo")
    @WebResult(name = "QueryRightsTransactionDetailInfoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryRightsTransactionDetailInfo", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryRightsTransactionDetailInfo")
    @ResponseWrapper(localName = "QueryRightsTransactionDetailInfoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryRightsTransactionDetailInfoResponse")
    public String queryRightsTransactionDetailInfo(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_矿业权出让交易列表信息查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryMineRightList", action = "http://tempuri.org/QueryMineRightList")
    @WebResult(name = "QueryMineRightListResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryMineRightList", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryMineRightList")
    @ResponseWrapper(localName = "QueryMineRightListResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryMineRightListResponse")
    public String queryMineRightList(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_矿业权出让交易详细信息查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryMineRightDetailInfo", action = "http://tempuri.org/QueryMineRightDetailInfo")
    @WebResult(name = "QueryMineRightDetailInfoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryMineRightDetailInfo", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryMineRightDetailInfo")
    @ResponseWrapper(localName = "QueryMineRightDetailInfoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryMineRightDetailInfoResponse")
    public String queryMineRightDetailInfo(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_土地使用权交易列表信息查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryLandUseRightList", action = "http://tempuri.org/QueryLandUseRightList")
    @WebResult(name = "QueryLandUseRightListResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryLandUseRightList", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryLandUseRightList")
    @ResponseWrapper(localName = "QueryLandUseRightListResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryLandUseRightListResponse")
    public String queryLandUseRightList(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_土地使用权交易详细信息查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryLandUseRightDetailInfo", action = "http://tempuri.org/QueryLandUseRightDetailInfo")
    @WebResult(name = "QueryLandUseRightDetailInfoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryLandUseRightDetailInfo", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryLandUseRightDetailInfo")
    @ResponseWrapper(localName = "QueryLandUseRightDetailInfoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryLandUseRightDetailInfoResponse")
    public String queryLandUseRightDetailInfo(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_工程建设交易列表信息查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryBuildConstructionList", action = "http://tempuri.org/QueryBuildConstructionList")
    @WebResult(name = "QueryBuildConstructionListResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryBuildConstructionList", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryBuildConstructionList")
    @ResponseWrapper(localName = "QueryBuildConstructionListResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryBuildConstructionListResponse")
    public String queryBuildConstructionList(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_工程建设交易详细信息查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryBuildConstructionDetailInfo", action = "http://tempuri.org/QueryBuildConstructionDetailInfo")
    @WebResult(name = "QueryBuildConstructionDetailInfoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryBuildConstructionDetailInfo", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryBuildConstructionDetailInfo")
    @ResponseWrapper(localName = "QueryBuildConstructionDetailInfoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryBuildConstructionDetailInfoResponse")
    public String queryBuildConstructionDetailInfo(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_政府采购交易列表信息查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryGovProcureList", action = "http://tempuri.org/QueryGovProcureList")
    @WebResult(name = "QueryGovProcureListResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryGovProcureList", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryGovProcureList")
    @ResponseWrapper(localName = "QueryGovProcureListResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryGovProcureListResponse")
    public String queryGovProcureList(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委_政府采购交易详细信息查询服务
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryGovProcureDetailInfo", action = "http://tempuri.org/QueryGovProcureDetailInfo")
    @WebResult(name = "QueryGovProcureDetailInfoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryGovProcureDetailInfo", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryGovProcureDetailInfo")
    @ResponseWrapper(localName = "QueryGovProcureDetailInfoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryGovProcureDetailInfoResponse")
    public String queryGovProcureDetailInfo(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

    /**
     * 发展改革委信用域_事业单位登记信息查询
     * 
     * @param requestMsg
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryInstitution", action = "http://tempuri.org/QueryInstitution")
    @WebResult(name = "QueryInstitutionResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "QueryInstitution", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryInstitution")
    @ResponseWrapper(localName = "QueryInstitutionResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.QueryInstitutionResponse")
    public String queryInstitution(
        @WebParam(name = "requestMsg", targetNamespace = "http://tempuri.org/")
        String requestMsg);

}
