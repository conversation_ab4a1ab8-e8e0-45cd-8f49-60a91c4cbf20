package blockChain.facade.service;

import blockChain.bean.Constant;
import blockChain.bean.QueryParam;
import blockChain.config.ApplicationRuntimeProperties;
import blockChain.config.SpringConfig;
import blockChain.controller.OnlineFillController;
import blockChain.controller.exception.NotFountException;
import blockChain.controller.exception.VerificationFailedException;
import blockChain.dto.*;
import blockChain.dto.query.HomeQueryParam;
import blockChain.dto.query.PushQueryParam;
import blockChain.entities.*;
import blockChain.entities.threads.CertificateCreateEntity;
import blockChain.repository.CopyrightManagerPredicates;
import blockChain.service.*;
import blockChain.service.Thread.CertificateCreateService;
import blockChain.utils.*;
import cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.axis.client.Call;
import org.apache.axis.encoding.XMLType;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.CollectionUtils;
import org.json.XML;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.namespace.QName;
import java.io.File;
import java.rmi.RemoteException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static blockChain.entities.CopyrightManager.EnableStatusEnum.DISABLE;
import static blockChain.entities.CopyrightManager.MODIFIED;
import static blockChain.entities.CopyrightManager.SnStatus.*;
import static blockChain.entities.CopyrightManager.UNSUBMIT;
import static blockChain.entities.QCopyrightManager.copyrightManager;

/**
 * <AUTHOR> Jie
 * @date 2019/11/22 16:54
 */
@Service
@Slf4j
@AllArgsConstructor
public class CopyrightManagerServiceFacade {
    private CopyrightManagerService copyrightManagerService;
    private UserServiceFacade userServiceFacade;
  private UserService userService;
  private MessageServiceFacade messageServiceFacade;
  private UploadAttachmentService uploadAttachmentService;
  private DigitalService digitalService;
  private CertificateCreateService certificateCreateService;
  private CopyrightOwnerService copyrightOwnerService;
  private AuthorService authorService;
  private ProcessRecordService processRecordService;
  private SimilarAttachmentService similarAttachmentService;
  private DigitalServiceFacade digitalServiceFacade;
  private ApplicationRuntimeProperties runtimeProperties;
  private CopyrightManagerMarkerService copyrightManagerMarkerService;
  private TagServiceFacade tagServiceFacade;
    private EvaluationServiceFacade evaluationServiceFacade;

    private final BackendServiceFacade backendServiceFacade;

    private final ProcessProgressService processProgressService;

  private final SpringConfig config;
  private final static Logger logger = LoggerFactory.getLogger(CopyrightManagerServiceFacade.class);
  private final  DateTimeFormatter dfTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
  private final String emptyBlockChainId="0x0000000000000000000000000000000000000000000000000000000000000000";
  private final String errorBlockChainId="0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF";
  @Transactional(rollbackFor = RuntimeException.class)
  public void createCopyright(CopyrightManager copyrightManager) {
    //删除前端移除的文件
    try {
      copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
    } catch (Exception e) {
      logger.error("error createCopyright: ", e);
        saveCopyright(copyrightManager);
        throw new RuntimeException("出现了内部错误，请把如下信息反馈给开发人员：" + e.getMessage());
    }
  }

  @Transactional(rollbackFor = RuntimeException.class)
  public void saveCopyright(CopyrightManager copyrightManager) {
    try {
        List<Long> removeList = copyrightManager.getUploadWorkRemove();
        for (Long id : removeList) {
            if (!userServiceFacade.isMyFile2(id))
                throw new RuntimeException("权限错误！您无权删除附件！");
            uploadAttachmentService.removeById(id);
            // 追加附件删除日志
            logger.info(LocalDateTime.now() + "【删除作品附件：】 作品名称："
                    + copyrightManager.getProductionName()
                    + ", 作品ID：" + copyrightManager.getRegistrationNum()
                    + ", 附件ID：" + id);
        }

        copyrightManager.setStatus_type(CopyrightManager.UNSUBMIT);
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
    } catch (Exception e) {
      logger.error("error saveCopyright: ", e);
      throw new RuntimeException("出现了内部错误，请把如下信息反馈给开发人员：" + e.getMessage());
    }
  }

  @Transactional(rollbackFor = RuntimeException.class)
  public void save(CopyrightManager copyrightManager) {
    copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
  }

  public Page<CopyrightManager> reportQuery(String worksNum, String productionName,
                                            String productionTypeId, Integer rightOwnMode,
                                            String startDate, String endDate, String agentName, String copyrightName,
                                            Integer page, Integer size, String certificateStartDate, String certificateEndDate, List<Integer> regNums) {
    return copyrightManagerService.reportQuery(worksNum, productionName, regNums, productionTypeId, rightOwnMode, agentName, copyrightName,
            startDate, endDate, certificateStartDate, certificateEndDate, page, size);
  }

  public Page<CopyrightManager> query(Boolean isOnLineFill, String worksNum, String productionName,
                                      String productionTypeId, Integer rightOwnMode,
                                      String startDate, String endDate, List<Integer> status, String userName
          , String agentName, String copyrightName, Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level, Integer page, Integer size, Integer evaluateMin, Integer evaluateMax,
                                      String certificateStartDate, String certificateEndDate, List<Integer> regNums, List<Integer> tags, Integer isAccusation, Integer focusWork) {
    List<Sort.Order> list = new ArrayList<>();
    if (isOnLineFill != null && isOnLineFill) {
      Sort.Order order2 = new Sort.Order(Sort.Direction.DESC, "registrationDate");
      Sort.Order order3 = new Sort.Order(Sort.Direction.DESC, "registrationNum");
      list.add(order2);
      list.add(order3);
    }else {
      // 审核中追加侵权和关注排序
      boolean isAscSort = false;
      for (Integer state : status) {
        if (isAscSort) break;
        switch (state) {
          case CopyrightManager.FIRST_REVIEW:
          case CopyrightManager.SECOND_REVIEW:
          case CopyrightManager.FINAL_REVIEW:
            Sort.Order order1 = new Sort.Order(Sort.Direction.DESC, "focusWork");
            Sort.Order order4 = new Sort.Order(Sort.Direction.DESC, "isAccusation");
            list.add(order4);
            list.add(order1);
            isAscSort = true;
            break;
          default:
            continue;
        }
      }
      Sort.Order order2 = new Sort.Order(Sort.Direction.ASC, "registrationDate");
      Sort.Order order3 = new Sort.Order(Sort.Direction.ASC, "registrationNum");
      list.add(order2);
      list.add(order3);
    }
    Sort sort = Sort.by(list);
    Pageable of = PageRequest.of(page, size, sort);
    return copyrightManagerService.query(worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName,isCertificate,isSubmit,isreport,city_level,county_level,evaluateMin,evaluateMax,of,
      certificateStartDate,certificateEndDate,null, regNums, tags, isAccusation, focusWork);
  }

  //带有排序参数的query
  public Page<CopyrightManager> query(Boolean isOnLineFill,String worksNum, String productionName,
                                      String productionTypeId, Integer rightOwnMode,
                                      String startDate, String endDate, List<Integer> status, String userName
    , String agentName, String copyrightName, Boolean isCertificate,Boolean isSubmit,Boolean isreport,Integer city_level,Integer county_level,Integer page, Integer size,Integer evaluateMin,Integer evaluateMax,
                                      String certificateStartDate, String certificateEndDate, Boolean isAsc,Integer province, List<Integer> tags, Integer isAccusation, Integer focusWork) {
    List<Sort.Order> list = new ArrayList<>();
    if(isOnLineFill!=null && isOnLineFill){
      Sort.Order order2 = new Sort.Order(Sort.Direction.DESC, "registrationDate");
      Sort.Order order3 = new Sort.Order(Sort.Direction.DESC, "registrationNum");
      list.add(order2);
      list.add(order3);
    }else {
      // 审核中追加侵权和关注排序
      boolean isAscSort = false;
      for (Integer state : status) {
        if (isAscSort) break;
        switch (state) {
          case CopyrightManager.FIRST_REVIEW:
          case CopyrightManager.SECOND_REVIEW:
          case CopyrightManager.FINAL_REVIEW:
            Sort.Order order4 = new Sort.Order(Sort.Direction.DESC, "isAccusation");
            Sort.Order order1 = new Sort.Order(Sort.Direction.DESC, "focusWork");
            Sort.Order order5 = new Sort.Order(Sort.Direction.DESC, "workstationId");
            list.add(order4);
            list.add(order1);
            list.add(order5);
            isAscSort = true;
            break;
          default:
            continue;
        }
      }
      Sort.Order order2 = new Sort.Order(Sort.Direction.ASC, "registrationDate");
        Sort.Order order3 = new Sort.Order(Sort.Direction.ASC, "registrationNum");
        list.add(order2);
        list.add(order3);
    }
      Sort sort = Sort.by(list);
      Pageable of = PageRequest.of(page, size, sort);
      return copyrightManagerService.query(worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName, isCertificate, isSubmit, isreport, city_level, county_level, evaluateMin, evaluateMax, of,
              certificateStartDate, certificateEndDate, province, null, tags, isAccusation, focusWork);
  }

    /**
     * 上报版保中心作品导出
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public List<CopyrightManageExportDto> queryList(String worksNum, String productionName,
                                                    List<String> productionTypeIds, Integer rightOwnMode,
                                                    String startDate, String endDate, List<Integer> status, String userName
            , String agentName, String copyrightName, Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level, Integer evaluateMin, Integer evaluateMax,
                                                    String certificateStartDate, String certificateEndDate) {
        List<Sort.Order> list = new ArrayList<>();
        Sort.Order order4 = new Sort.Order(Sort.Direction.DESC, "isAccusation");
        Sort.Order order1 = new Sort.Order(Sort.Direction.DESC, "focusWork");
        Sort.Order order2 = new Sort.Order(Sort.Direction.ASC, "registrationDate");
        Sort.Order order3 = new Sort.Order(Sort.Direction.ASC, "registrationNum");
        list.add(order4);
        list.add(order1);
        list.add(order2);
        list.add(order3);
        Sort sort = Sort.by(list);
        return copyrightManagerService.queryList(worksNum, productionName, productionTypeIds, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName, isCertificate, isSubmit, isreport, city_level, county_level, evaluateMin, evaluateMax, certificateStartDate, certificateEndDate);
    }

    /**
     * 开放查询
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Page<CopyrightManager> queryByCertificate(String worksNum, String productionName,
                                                     String productionTypeId, Integer rightOwnMode,
                                                     String startDate, String endDate, List<Integer> status, String userName
            , String agentName, String copyrightName, Boolean isCertificate, Boolean isSubmit, Boolean isreport, Integer city_level, Integer county_level, String certificateStartDate, String certificateEndDate, Integer page, Integer size) {
        List<Sort.Order> list = new ArrayList<>();
        Sort.Order order1 = new Sort.Order(Sort.Direction.DESC, "focusWork");
        Sort.Order order2 = new Sort.Order(Sort.Direction.ASC, "registrationDate");
        Sort.Order order3 = new Sort.Order(Sort.Direction.ASC, "registrationNum");
        list.add(order1);
        list.add(order2);
        list.add(order3);
        Sort sort = Sort.by(list);
        Pageable of = PageRequest.of(page, size, sort);
        return copyrightManagerService.queryByCertificate(worksNum, productionName, productionTypeId, rightOwnMode, startDate, endDate, status, userName, agentName, copyrightName,isCertificate,isSubmit,isreport,city_level,county_level,certificateStartDate,certificateEndDate,of);
    }

  @Transactional(rollbackFor = RuntimeException.class)
  public List<CopyrightManager> queryAllByCertificate(Boolean isCertificate, Pageable pageable) {
    return copyrightManagerService.queryAllByCertificate(isCertificate, pageable);
  }

  public CopyrightManager getById(Long id) {
    return copyrightManagerService.getById(id);
  }

  /**
   * 重复作品数check
   * @param manager
   * @return
   */
  public long countAllSame(CopyrightManager manager) {
    QCopyrightManager qManager = QCopyrightManager.copyrightManager;
    QCopyrightOwner qOwner = QCopyrightOwner.copyrightOwner;
    BooleanBuilder builder = new BooleanBuilder();
    builder.and(qManager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE));
    builder.and(qManager.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
    builder.and(qManager.status_type.in(CopyrightManager.SUBMITTING, CopyrightManager.FIRST_REVIEW, CopyrightManager.SECOND_REVIEW, CopyrightManager.FINAL_REVIEW, MODIFIED));
    builder.and(qOwner.copyName.eq(manager.getOwnerList().get(0).getCopyName()));
    builder.and(qManager.productionTypeId.eq(manager.getProductionTypeId()));
    builder.and(qManager.productionName.eq(manager.getProductionName().trim()));
    if (manager.getRegistrationNum() != null)
        builder.and(qManager.registrationNum.ne(manager.getRegistrationNum()));

      return copyrightManagerService.countSameBy(builder);
  }

    @Transactional(rollbackFor = RuntimeException.class)
    public void remove(CopyrightManager copyrightManager) {
        copyrightManager.setEnable(DISABLE);
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
    }

    @Transactional
    public void onlineSubmit(CopyrightManager copyrightManager, UserEntity userEntity) {
        if (copyrightManager.getRegistrationNum() != null) {
            CopyrightManager entity = getById(copyrightManager.getRegistrationNum());
            if (entity != null) {
                copyrightManager.setSnCode(entity.getSnCode());
                entity.setStatus_type(CopyrightManager.SUBMITTING);
                this.save(entity);
            }
        }
        // 材料修改提交作品不改变原所属工作站
        if (copyrightManager.getStatus_type() != MODIFIED) {
            // 所属工作站
            if (userEntity.getRole().getIsWorkstation() != null && userEntity.getRole().getIsWorkstation() == Constant.BYTE_TRUE) {
                copyrightManager.setWorkstationId(userEntity.getRole().getId());
            }

        }
        userEntity.setLastCommitTime(LocalDateTime.now());
        userService.save(userEntity);
        onlineSubmitDataRun(copyrightManager,userEntity);
    }

    //作品提交线程
    public void onlineSubmitDataRun(CopyrightManager copyrightManager,UserEntity userEntity) {
//    synchronized (this) {
        Calendar konwYear = Calendar.getInstance();
        //临时分配登记号
        copyrightManager.setWorksNum("申-" + konwYear.get(Calendar.YEAR) + "-" + System.currentTimeMillis());
        //设置提交时间
        copyrightManager.setRegistrationDate(LocalDateTime.now());
        if(!StringUtils.isEmpty(copyrightManager.getSnCode()) &&
                (copyrightManager.getStatus_type() == UNSUBMIT && copyrightManager.getRestatus() > 0))
        {

            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
            String str = null;
            // 如果作品号跟sncode后6位相同
            if(copyrightManager.getRegistrationNum().toString().substring(copyrightManager.getRegistrationNum().toString().length()-6)
                    .equals(copyrightManager.getSnCode().substring(copyrightManager.getSnCode().length() - 6))){
                // 作品后5+1
                str = copyrightManager.getRegistrationNum().toString();
                if(str.length() >= 5){
                    str = str.substring(str.length() - 5);
                }else {
                    str = String.format("%06d", Integer.parseInt(str));
                }
            }else {
                // sncode后6位+1
                str = copyrightManager.getSnCode().toString();
                if(str.length() >= 6){
                    str = str.substring(str.length() - 6);
                }else {
                    str = String.format("%06d", Integer.parseInt(str));
                }
            }

            int i = Integer.parseInt(str);
            i += 1;
            copyrightManager.setSnCode(String.format("fjszK1003830%s%s", today, String.format("%06d",i)));

            String accessToken = backendServiceFacade.getAccessToken(userEntity);
            if (blockChain.utils.StringUtils.isEmpty(accessToken)) {
                log.error("撤销申请获取token失败");
            }

            String projectId = backendServiceFacade.getProjectId(userEntity, accessToken);
            copyrightManager.setProjectId(projectId);
        }


        //设置流程状态
      ProcessRecord processRecord = new ProcessRecord();
      if (copyrightManager.getRegistrationNum() != null) {
        List<ProcessRecord> flowRecords = processRecordService.getByCopyrightId(copyrightManager.getRegistrationNum());
        copyrightManager.setFlowRecord((flowRecords==null)?new ArrayList<>():flowRecords);
      }
      UserDto userDto = userServiceFacade.getByUserName(copyrightManager.getUserName());
      processRecord.setOpName(userDto.getRealName());
      if (copyrightManager.getStatus_type() == MODIFIED) {
        processRecord.setOpType(ProcessRecord.APPROVAL_NAME.get(copyrightManager.getStatus_type()).toString());
        processRecord.setOpResult(ProcessRecord.APPROVAL_NAME.get(copyrightManager.getStatus_type()).toString() + "成功");
        copyrightManager.setStatus_type(copyrightManager.getRestatus());
        copyrightManager.setRestatus(MODIFIED);
      } else {
        copyrightManager.setStatus_type(CopyrightManager.FIRST_REVIEW);
        processRecord.setOpType(ProcessRecord.APPROVAL_NAME.get(CopyrightManager.UNSUBMIT).toString());
          processRecord.setOpResult(ProcessRecord.APPROVAL_NAME.get(CopyrightManager.UNSUBMIT).toString() + "成功");

          /*String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
          int lastSnCode = processProgressService.getLastSncode(today);
          copyrightManager.setSnCode(String.format("fjszK1003830%s%06d", today, ++lastSnCode)); // TODO 移至作品首次提交*/
      }
        //著作权人信息填充
        Submitter submitter = copyrightManager.getSubmitter();
        List<CopyrightOwner> owners = copyrightManager.getOwnerList();
        if (owners != null && owners.size() > 0) {
            submitter.setCopyrightName(owners.get(0).getCopyName());
        }
        copyrightManager.getFlowRecord().add(processRecord);

        List<Long> removeList = copyrightManager.getUploadWorkRemove();
        for (Long id : removeList) {
            if (!userServiceFacade.isMyFile2(id))
                throw new RuntimeException("权限错误！您无权删除附件！");
            uploadAttachmentService.removeById(id);
            // 追加附件删除日志
            logger.info(LocalDateTime.now() + "【删除作品附件：】 作品名称："
                    + copyrightManager.getProductionName()
                    + ", 作品ID：" + copyrightManager.getRegistrationNum()
                    + ", 附件ID：" + id);
        }

        List<CopyrightManagerMarker> oldMarkers = copyrightManagerMarkerService.findByManagerId(copyrightManager.getRegistrationNum());
        if (!ObjectUtils.isEmpty(oldMarkers)) {
            copyrightManagerMarkerService.deleteAll(oldMarkers);
        }

        // 港澳台标关注星号
        int province = Integer.valueOf(copyrightManager.getOwnerList().get(0).getCopyProvince());
        if (runtimeProperties.getProvinceGatDigitalDefineCode().equals(digitalServiceFacade.getById(province).getCode())) {
            //设置作品的关注状态
            copyrightManager.setFocusWork(1);
            createCopyright(copyrightManager);

            CopyrightManagerMarker marker = new CopyrightManagerMarker();
            marker.setUuid(UUID.randomUUID().toString());
            marker.setContent("港澳台作品");
            marker.setManager(copyrightManager);
            copyrightManagerMarkerService.save(marker);
        } else {
            //取消作品的关注状态
            copyrightManager.setFocusWork(0);
            createCopyright(copyrightManager);
        }

        if(StringUtils.isEmpty(copyrightManager.getSnCode())){
            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
            String str = copyrightManager.getRegistrationNum().toString();
            if(str.length() >= 6){
                str = str.substring(str.length() - 6);
            }else {
                str = String.format("%06d", Integer.parseInt(str));
            }
            copyrightManager.setSnCode(String.format("fjszK1003830%s%s", today, str));
            createCopyright(copyrightManager);
        }
  }

  public int certificateCreateRun(QueryParam queryParam, HttpServletRequest request, HttpServletResponse response,String userName) {
      JSONArray idArray = queryParam.getJSONArray("idList");
      if (idArray != null && !idArray.isEmpty()) {
          List<Long> idList = JSONObject.parseArray(idArray.toJSONString(), Long.class);

          BooleanBuilder builder = new BooleanBuilder();
          builder.and(copyrightManager.registrationNum.in(idList));
          builder.and(copyrightManager.status_type.eq(CopyrightManager.CERT_CREATE));
          List<CopyrightManager> managers = Lists.newArrayList(copyrightManagerService.findAll(builder));
          if (CollectionUtils.isNotEmpty(managers)) {
              for (CopyrightManager temp : managers) {
                  temp.setStatus_type(CopyrightManager.REPORTING);
                  copyrightManagerService.saveOrUpdateCopyright(temp);
              }
          }

          System.out.println("添加了" + idList.size() + "个生成证书任务！");
          return managers.size();
      }
      return 0;
  }

  @Async("doSomethingExecutor")
  public void blockChainUpScheduling(){
    while(true) {
      try {
        CopyrightManager manager = copyrightManagerService.findFirstByStatus_typeAndBlockChainToken(CopyrightManager.CERT_CREATED,emptyBlockChainId);
        if (manager != null) {
          logger.info("开始上链:"+manager.getWorksNum());
          try {
            certBlockChain(manager, null, null, "");
            if (manager.getSampleUrl() != null) {
              certBlockChain(manager, null, null, "sampleUrl");
            }
            copyrightManagerService.saveOrUpdateCopyright(manager);
            logger.info("完成上链：" + manager.getWorksNum() + " 结果 Main:" + manager.getOnBlockToken() + " /Sample:" + (manager.getOnBlockSampleToken() != null ? manager.getOnBlockSampleToken() : ""));
          }
          catch (Exception e)
          {
            manager.setOnBlockToken(errorBlockChainId);
            manager.setOnBlockSampleToken(errorBlockChainId);
            copyrightManagerService.saveOrUpdateCopyright(manager);
            logger.error(manager.getWorksNum()+"上链失败: "+e);
          }
        } else {
          Thread.sleep(10000);
        }
      } catch (Exception e) {
        logger.error("上链失败: "+e);
      }
    }
  }

  @Async("doSomethingExecutor")
  public void certificateCreateScheduling(){
    while(true) {
      try {
        CopyrightManager manager = copyrightManagerService.findFirstByStatus_type(CopyrightManager.REPORTING);
        if (manager != null) {
          logger.info("开始生成证书:"+manager.getWorksNum());
          List<Long> idList = new ArrayList<>();
          idList.add(manager.getRegistrationNum());
          QueryParam queryParam = new QueryParam();
          queryParam.put("UID", null);
          queryParam.put("idList", idList);

          LocalDateTime start = LocalDateTime.now();
          ResponseEntity<Map<String, Object>> result1 = certificateCreate(queryParam, null, null);
          CertificateCreateEntity certificateCreateEntity = new CertificateCreateEntity();
          certificateCreateEntity.setCreateTime(start);
          certificateCreateEntity.setUserName("系统");
          certificateCreateEntity.setWorksNum(""+manager.getRegistrationNum());
          certificateCreateEntity.setProductName(manager.getProductionName());
          try {
            certificateCreateEntity.setResult(result1.getBody().get("message").toString() + ";" + result1.getBody().get("erroeMsg"));
            certificateCreateEntity.setFinishTime((LocalDateTime) result1.getBody().get("time"));
            certificateCreateEntity.setResultCode(result1.getBody().get("errorMsg").toString());
          }
          catch (Exception e)
          {

          }
          certificateCreateService.save(certificateCreateEntity);
          logger.info("完成证书生成："+certificateCreateEntity.getProductName()+" 结果："+result1.toString());
        } else {
            //System.out.println("无需要生成的证书");
            Thread.sleep(10000);
        }
      } catch (Exception e) {
          logger.error("error: ", e);
        /*CertificateCreateEntity certificateCreateEntity = new CertificateCreateEntity();
        certificateCreateEntity.setResult("systemError:"+e.getMessage());
        certificateCreateEntity.setUserName("系统");
        certificateCreateEntity.setResultCode("systemError:"+e.getMessage());
        certificateCreateService.save(certificateCreateEntity);*/
      }
    }
  }

    /**
     * 生成证书
     *
     * @param queryParam
     * @param request
     * @param response
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public ResponseEntity<Map<String, Object>> certificateCreate(QueryParam queryParam, HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<>();
        Integer result = 0;
        Integer failed = 0;
        String rootPath = "";
        String destUrl = "/OwnerCertificate.jpg";
        String CERTIFICATEURL = "/Certificate.jpg";
        String SEALURL = "/Copyright.png";
        String destSplUrl = "/OwnerSample.jpg";
        String userName = (queryParam.getInteger("UID")==null || queryParam.getInteger("UID")==0)?"管理员":userServiceFacade.findUserById(queryParam.getInteger("UID")).getRealName();
        JSONArray idArray = queryParam.getJSONArray("idList");

        if (idArray != null && !idArray.isEmpty()) {
            List<Long> idList = JSONObject.parseArray(idArray.toJSONString(), Long.class);
            String errorName = "";
            String copyrightUrl = SEALURL;
            String certificateUrl = CERTIFICATEURL;
            String documentCopyUrl = "/pdfCopyright.png";
            String contextPath = "File/"+config.getModelRoot();//request.备注getServletContext().getRealPath("/");
            Map<Long, String> wrongRecords = new HashMap<>();
            StringBuilder dirPath = new StringBuilder();
            for (Long id : idList) {
                rootPath = "E:\\static/images";//System.getProperty("user.dir") + File.separator + contextPath + "static/images";
                CopyrightManager casDict = copyrightManagerService.getById(id);
                if(casDict.getStatus_type()!=CopyrightManager.REPORTING) {
                    continue;
                }
                logger.info("【证书生成】开始生成证书：" + casDict.getWorksNum());
                String dirId = "";
                String existCertificateUrl = casDict.getCertificateUrl();
                if (!StringUtils.isEmpty(existCertificateUrl)) {
                    String[] str = existCertificateUrl.split("/");
                    if (str.length == 6 && str[4].length() > 20) {
                        dirId = str[4];
                        dirPath = new StringBuilder("fileModel");
                        dirPath.append(File.separatorChar).append(dirId);
                    } else if (str.length == 9) {
                        dirId = str[7];
                        dirPath = new StringBuilder("newFileModel");
                        dirPath.append(File.separatorChar).append(str[4]);
                        dirPath.append(File.separatorChar).append(str[5]);
                        dirPath.append(File.separatorChar).append(str[6]);
                        dirPath.append(File.separatorChar).append(dirId);
                    }
                }

                if (StringUtils.isEmpty(dirId)) {
                    dirId = Long.toHexString(new Date().getTime()) + UUID.randomUUID().toString().replaceAll("-", "");
                    LocalDate now = LocalDate.now();
                    dirPath = new StringBuilder("newFileModel");
                    dirPath.append(File.separatorChar).append(now.getYear());
                    dirPath.append(File.separatorChar).append(now.getMonthValue());
                    dirPath.append(File.separatorChar).append(now.getDayOfMonth());
                    dirPath.append(File.separatorChar).append(dirId);
                }
                worksNumber(casDict);
                List<Author> author = authorService.getAuthorByCopyrightId(casDict.getRegistrationNum());
                List<CopyrightOwner> owner = copyrightOwnerService.getByCopyrightId(casDict.getRegistrationNum());
                String newdestUrl = "E:\\" + dirPath + destUrl;//System.getProperty("user.dir") + File.separator + contextPath + "newFileModel/" + id + destUrl;
                // 为证书打印作品信息
                try {
                    //多作者处理
                    String authors = "";
                    for (int i = 0; i < author.size(); i++) {
                        authors = authors + author.get(i).getAuthorName() + " ";
                    }
                    //创作完成时间处理
                    if (casDict.getEndDate() != null) {
                        casDict.setEndDate(casDict.getFinishTime().replaceFirst("-", "年").replaceFirst("-", "月") + "日");
                    }
                    //首次发表/创作时间处理
                    String endingTime = "";
                    if (casDict.getFirstPublishTime()==null || casDict.getFirstPublishTime().isEmpty()) {
                        endingTime = "未发表";
                    } else {
                        casDict.setStarDate(casDict.getFirstPublishTime().replaceFirst("-", "年").replaceFirst("-", "月") + "日");
                        endingTime = casDict.getStarDate(); //首次发表日期
                    }
                    //多著作权人处理
                    String peoples = "";
                    for (int i = 0; i < owner.size(); i++) {
                        peoples = peoples + owner.get(i).getCopyName() + " ";
                    }

                    CertificateCreate cc = new CertificateCreate();
                    List<String> Strings = new ArrayList<String>();
                    List<Integer> SX = new ArrayList<Integer>();
                    List<Integer> FS = new ArrayList<>();
                    List<String> pics = new ArrayList<String>();
                    List<Integer> picsX = new ArrayList<Integer>();

                    String CRE_URL = config.getCertificateUrl();
                    String PIC_URL = config.getCertificatePicUrl();
                    String DOCUMENT_URL = config.getCertificateDocUrl();

                    CopyrightManager copyrightManager = new CopyrightManager();
                    copyrightManager.setCertificateUrl(contextPath + dirPath + destUrl);
                    logger.info("【证书生成】生成二维码...");

                    //证书上植入二维码
                    String content = config.getFrontUrl() + "api/qkl/" + copyrightManager.getCertificateUrl();
                    String qrcodepath = QRCodeUtils.drawQRCode(content, contextPath + dirPath, "QRCode.jpg");
//                    ImageUtils.scale(qrcodepath, qrcodepath, 0.24, false);

                    logger.info("【证书生成】构建文字信息...");
                    //设置证书文字信息参数
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                    Strings.add("著作权人");
                    SX.add(1235);
                    SX.add(1500);
                    SX.add(1590);
                    SX.add(1559);
                    FS.add(60);
                    Strings.add("福建省版权局");
                    SX.add(2075);
                    SX.add(1500);
                    SX.add(2660);
                    SX.add(1557);
                    FS.add(60);
                    if(casDict.getCertificateCreateTime()!=null){
                        Strings.add(casDict.getCertificateCreateTime().format(formatter));
                    }else {
                        Strings.add(LocalDate.now().format(formatter));
                    }
                    SX.add(1140);
                    SX.add(1920);
                    SX.add(1712);
                    SX.add(1980);
                    FS.add(60);
                    Strings.add(casDict.getWorksNum());//作品登记号
                    SX.add(1000);
                    SX.add(735);
                    SX.add(2400);
                    SX.add(795);
                    FS.add(60);
                    Strings.add(casDict.getProductionName());//作品名
                    SX.add(1068);
                    SX.add(947);
                    SX.add(1747);
                    SX.add(1067);
                    FS.add(60);
                    Strings.add(authors);//作者
                    SX.add(969);
                    SX.add(1076);
                    SX.add(1727);
                    SX.add(1196);
                    if (authors.length() < 33) {
                        FS.add(60);
                    } else if (authors.length() < 52) {
                        FS.add(45);
                    } else {
                        FS.add(38);
                    }
                    Strings.add(casDict.getFinishTime().replaceFirst("-", "年").replaceFirst("-", "月") + "日");//创作完成日期
                    SX.add(1040);
                    SX.add(1200);
                    SX.add(1727);
                    SX.add(1320);
                    FS.add(60);
                    Digital digital = digitalService.getById(Integer.parseInt(casDict.getProductionTypeId())).orElse(null);
                    String productionTypeStr = "";
                    if (digital!=null) {
                        productionTypeStr = digital.getDict_name();
                        if (productionTypeStr.contains("美术")) {
                            productionTypeStr = "美术";
                        }
                    }
                    Strings.add(productionTypeStr);//作品类别
                    SX.add(2110);
                    SX.add(947);
                    SX.add(3277);
                    SX.add(1067);
                    FS.add(60);
                    Strings.add(endingTime); //首次发表日期
                    SX.add(2480);
                    SX.add(1200);
                    SX.add(3277);
                    SX.add(1320);
                    FS.add(60);
                    Strings.add(peoples); //著作权人
                    SX.add(2110);
                    SX.add(1076);
                    SX.add(3365);
                    SX.add(1217);
                    if (peoples.length() <= 40) {
                        FS.add(60);
                    } else if (peoples.length() < 56) {
                        FS.add(45);
                    } else {
                        FS.add(38);
                    }

                    //设置证书图片信息参数
                    pics.add(rootPath + copyrightUrl);
                    picsX.add(2227);
                    picsX.add(1562);
                    picsX.add(2795);
                    picsX.add(2126);
                    pics.add(qrcodepath.replace("File/"+config.getModelRoot(),"E:\\"));
                    picsX.add(2701);
                    picsX.add(303);
                    picsX.add(3117);
                    picsX.add(719);
                    cc.setStrings(Strings);
                    cc.setCopyownerTPointXY(SX);
                    cc.setPicsUrl(pics);
                    cc.setPicsXY(picsX);
                    cc.setMainPicStr(rootPath + certificateUrl);
                    cc.setResultUrl(newdestUrl);
                    cc.setFont_family("宋体");
                    cc.setFont_size(FS);
                    String bc = JSONObject.toJSON(cc).toString();


                    //所需参数构建
                    List<String> picsUrl = new ArrayList<String>();
                    List<String> resultPicsUrl = new ArrayList<String>();
                    List<Integer> copyXY = new ArrayList<Integer>();
                    List<Integer> picXY = new ArrayList<Integer>();

                    //作品图片路径获取并存入
                    List<UploadAttachment> uploadWorksInfos = uploadAttachmentService.getByCopyrightId(casDict.getRegistrationNum());
                    Map<String,Object> documentParam = new HashMap<>();
                    if (casDict.getFileType() == 1) {
                        String targetRoot = "";
                        for (int i = 0; i < uploadWorksInfos.size(); i++) {
                            if (i == 0) {
                                targetRoot = "E:\\" + dirPath + "/works.jpg";
                            } else {
                                targetRoot = "E:\\" + dirPath + "/works" + i + ".jpg";
                            }
                            String imgurl = uploadWorksInfos.get(i).getWorkUrl().replace("File/"+config.getModelRoot(),"E:\\");
                            picsUrl.add(imgurl);
                            resultPicsUrl.add(targetRoot);
                        }
                    } else if(casDict.getFileType() == 2){
                        //文档相关文件盖章处理
                        List<String> originRoot = new ArrayList<>();
                        List<String> targetRoot = new ArrayList<>();
                        documentParam.put("sealpic",rootPath+documentCopyUrl);
                        for (int i = 0; i < uploadWorksInfos.size(); i++) {
                            originRoot.add(uploadWorksInfos.get(i).getWorkUrl().replace("File/"+config.getModelRoot(),"E:\\"));
                            if (i == 0) {
                                targetRoot.add("E:\\" + dirPath + "/works.pdf");
                            } else {
                                targetRoot.add("E:\\" + dirPath + "/works" + i + ".pdf");
                            }
                        }
                        documentParam.put("docList",originRoot);
                        documentParam.put("targetpdfList",targetRoot);
                    }

                    //设置图章与原图缩放大小
                    copyXY.add(136);
                    copyXY.add(135);
                    picXY.add(585);
                    picXY.add(830);

                    //图片处理参数组装
                    PicCreate pc = new PicCreate();
                    pc.setCopyXY(copyXY);
                    pc.setPicsXY(picXY);
                    pc.setPicsUrl(picsUrl);
                    pc.setResultPicUrl(resultPicsUrl);
                    pc.setCopyPicStr(rootPath + copyrightUrl);
                    String pcc = JSONObject.toJSON(pc).toString();

                    //请求证书生成与图片处理接口
                    String result1 = PictureHttpUtil.HttpConcent(bc, CRE_URL, response);
                    String result2;
                    if(casDict.getFileType() == 2) {
                        result2 = PictureHttpUtil.HttpConcent(JSONObject.toJSON(documentParam).toString(), DOCUMENT_URL, response);
                    }else{
                        result2 = PictureHttpUtil.HttpConcent(pcc, PIC_URL, response);
                    }
                    logger.info("【证书生成】调用外部接口盖章...");
                    // 文件类型是图片, 生成作品电子样本
                    String result3 = null;
                    if (casDict.getFileType() == 1) {
                        copyrightManager.setSampleUrl(contextPath + dirPath + destSplUrl);
                        String sc = getSampleData(casDict, rootPath, copyrightUrl, contextPath, destSplUrl, picsUrl, copyrightManager.getSampleUrl(), dirPath.toString());
                        result3 = PictureHttpUtil.HttpConcent(sc, CRE_URL, response);
                    }
                    logger.info("【证书生成】外部接口返回：result1："+result1+" result2："+result2+" result3："+result3);
                    if (result1.contains("0") && result2.contains("0")
                            && (result3 == null || (result3 != null && result3.contains("0")))
                    ) {
                        //加入证书路径
                        casDict.setCertificateUrl(copyrightManager.getCertificateUrl());
                        //插入流程记录
                        List<ProcessRecord> processRecords = processRecordService.getByCopyrightId(casDict.getRegistrationNum());
                        processRecords.add(OnlineFillController.processRecordCreate(ProcessRecord.APPROVAL_NAME.get(CopyrightManager.CERT_CREATE).toString(), ProcessRecord.APPROVAL_NAME.get(CopyrightManager.CERT_CREATE) + "成功", "", userName));
                        casDict.setFlowRecord(processRecords);
                        casDict.setStatus_type(CopyrightManager.CERT_CREATED);
                        if(casDict.getCertificateCreateTime()==null) {
                            casDict.setCertificateCreateTime(LocalDateTime.now());
                        }
                        //logger.info("【证书生成】证书上链...");
                        //证书上链，临时设置为空，加快生成速度，后面开服务单独跑生成
                       /*
                        certBlockChain(casDict, resultMap, request, "");
                        if (result3!=null) {
                            casDict.setSampleUrl(copyrightManager.getSampleUrl());
                            certBlockChain(casDict, resultMap, request, "sampleUrl");
                        }
                       */
                      casDict.setOnBlockToken(emptyBlockChainId);
                        if (result3!=null) {
                          casDict.setSampleUrl(copyrightManager.getSampleUrl());
                          casDict.setOnBlockSampleToken(emptyBlockChainId);
                        }
                        List<ProcessRecord> processRecords_ = processRecordService.getByCopyrightId(casDict.getRegistrationNum());
                        ProcessRecord processRecord=new ProcessRecord();
                        processRecord.setApproverOpinion("证书生成成功");
                        processRecord.setOpResult("证书生成成功");
                        processRecord.setOpType("证书生成");
                        processRecord.setTime(LocalDateTime.now());
                        processRecord.setOpName("系统");
                        processRecords_.add(processRecord);
                        casDict.setFlowRecord(processRecords_);
                        logger.info("【证书生成】写入数据库...");
                        copyrightManagerService.saveOrUpdateCopyright(casDict);
                        logger.info("【证书生成】完毕...");
                        result++;
                    } else {
                        failed++;
                        wrongRecords.put(id,"盖章失败！请检查您的作品文件格式！以下是技术信息：certificateCode:"+result1+" sampleDCode:"+result2 + "; picCode:"+result3);
                        logger.error("盖章失败: certificateCode:"+result1+" sampleDCode:"+result2 + "; picCode:"+result3);
                        resultMap.put("errorMsg","certificateCode:"+result1+" sampleDCode:"+result2 + "; picCode:"+result3);
                    }
                } catch (RuntimeException re) {
                    logger.error("盖章失败: ID=【", id,"】:", re);
                    failed++;
                    wrongRecords.put(id,"盖章失败！请检查您的作品文件格式！以下是技术信息： "+re.getMessage());
                    resultMap.put("erroeMsg", re.getMessage());
                } catch (Exception e) {
                    // TODO Auto-generated catch block
                    logger.error("盖章失败: ID=【", id,"】:", e);
                    failed++;
                    wrongRecords.put(id,"盖章失败！请检查您的作品文件格式！以下是技术信息： "+ e.getLocalizedMessage());
                    resultMap.put("erroeMsg", e.getLocalizedMessage());
                }
            }
            for (Map.Entry<Long,String> entry : wrongRecords.entrySet()) {
                CopyrightManager wrong = copyrightManagerService.getById(entry.getKey());
                List<ProcessRecord> processRecords = processRecordService.getByCopyrightId(wrong.getRegistrationNum());
                wrong.setStatus_type(MODIFIED);
                wrong.setRestatus(CopyrightManager.REPORTING);
                ProcessRecord processRecord=new ProcessRecord();
                processRecord.setApproverOpinion(entry.getValue());
                processRecord.setOpResult("盖章材料修改");
                processRecord.setOpType("作品盖章");
                processRecord.setTime(LocalDateTime.now());
                processRecord.setOpName("系统");
                processRecords.add(processRecord);
                wrong.setFlowRecord(processRecords);
                copyrightManagerService.saveOrUpdateCopyright(wrong);
            }
            //updateStateByIds(CopyrightManager.CERT_CREATE,wrongIds);
            resultMap.put("message", "证书生成成功" + result + "件，失败" + failed + "件。");
            resultMap.put("time",LocalDateTime.now());
            return new ResponseEntity<Map<String, Object>>(resultMap, HttpStatus.OK);
        } else {
            resultMap.put("message", "无作品id");
            return new ResponseEntity<Map<String, Object>>(resultMap, HttpStatus.OK);
        }
    }

    private String getSampleData(CopyrightManager casDict, String rootPath, String copyrightUrl, String contextPath, String destUrl, List<String> picsUrl, String sampleUrl
            , String dirPath) {
        CertificateCreate cc = new CertificateCreate();
        List<String> Strings = new ArrayList<>();
        List<Integer> SX = new ArrayList<>();
        List<Integer> FS = new ArrayList<>();
        List<String> pics = new ArrayList<>();
        List<Integer> picsX = new ArrayList<>();
        String SampleURL = "/Sample.jpg";
        String newdestUrl = "E:\\" + dirPath + destUrl;

        //证书上植入二维码
        String content = config.getFrontUrl() + "api/qkl/" + sampleUrl;
        String qrcodepath = QRCodeUtils.drawQRCode(content, contextPath + dirPath, "QRSpCode.jpg");
//    ImageUtils.scale(qrcodepath, qrcodepath, 0.24, false);

    Strings.add(casDict.getWorksNum());//作品登记号
    SX.add(1000);
    SX.add(685);
    SX.add(2400);
    SX.add(745);
    FS.add(60);

    if (picsUrl.size() == 1) {
      pics.add(picsUrl.get(0));
      picsX.add(1325);
      picsX.add(955);
      picsX.add(2175);
      picsX.add(1715);
    } else if (picsUrl.size() == 2) {
      pics.add(picsUrl.get(0));
      picsX.add(850);
      picsX.add(955);
      picsX.add(1700);
      picsX.add(1715);
      pics.add(picsUrl.get(1));
      picsX.add(1800);
      picsX.add(955);
      picsX.add(2650);
      picsX.add(1715);
    } else if (picsUrl.size() == 3) {
      pics.add(picsUrl.get(0));
      picsX.add(375);
      picsX.add(955);
      picsX.add(1225);
      picsX.add(1715);
      pics.add(picsUrl.get(1));
      picsX.add(1325);
      picsX.add(955);
      picsX.add(2175);
      picsX.add(1715);
      pics.add(picsUrl.get(2));
      picsX.add(2275);
      picsX.add(955);
      picsX.add(3125);
      picsX.add(1715);
    }
    //设置证书图片信息参数
    pics.add(rootPath + copyrightUrl); // 盖章 "E:\\static/images/Copyright.png"
    picsX.add(2227);
    picsX.add(1562);
    picsX.add(2795);
    picsX.add(2126);
    pics.add(qrcodepath.replace("File/"+config.getModelRoot(),"E:\\"));
    picsX.add(2701);
    picsX.add(303);
    picsX.add(3117);
    picsX.add(719);
    cc.setStrings(Strings);
    cc.setCopyownerTPointXY(SX);
    cc.setPicsUrl(pics);
    cc.setPicsXY(picsX);
        cc.setMainPicStr(rootPath + SampleURL); // 源证书路径 "E:\\static/images/Sample.jpg"
        cc.setResultUrl(newdestUrl); // 最终证书生成路径 "E:\\newFileModel/" + dirId +  "/OwnerSample.png";
        cc.setFont_family("宋体");
    cc.setFont_size(FS);
    return JSONObject.toJSON(cc).toString();
  }

  @Transactional(rollbackFor = RuntimeException.class)
  public void certBlockChain(CopyrightManager copyrightManager, Map<String, Object> result, HttpServletRequest request, String field) throws RuntimeException {
    String postUrl = config.getBlockUrl();
    //String contextPath = "File/";//"File/";
    try {
      String certUrl = "";
      if ("sampleUrl".equals(field)) {
        certUrl = copyrightManager.getSampleUrl();
      } else {
        certUrl = copyrightManager.getCertificateUrl();
      }
      /*String imgUrl = request.getSession().getServletContext().getRealPath("/fileModel/"+id) + "/QRCode.jpg";*/
      String imgUrl = System.getProperty("user.dir") + File.separator + certUrl;
      String HexStr = ImageUtil.ImageToHex(imgUrl);
      Map<String, Object> param = new HashMap<>();
      param.put("jsonrpc", "2.0");
      param.put("method", "personal_unlockAccount");
      param.put("params", new ArrayList<Object>() {{
        add("******************************************");
        add("123456");
        add(120);
      }});
      param.put("id", 1);
      String unlockParamStr = (new JSONObject(param)).toJSONString();
      String getUnlockResult = HttpUtil.requestPost(postUrl, unlockParamStr);
      JSONObject unlockResult = JSONObject.parseObject(getUnlockResult);
      if (unlockResult.containsKey("result") && unlockResult.getBooleanValue("result")) {
        //上链
        param.clear();
        param.put("jsonrpc", "2.0");
        param.put("method", "eth_sendTransaction");
        param.put("params", new ArrayList<Object>() {{
          add(new HashMap<String, Object>() {{
            put("from", "******************************************");
            put("to", "******************************************");
            put("value", "0x100000");
            put("data", HexStr);
          }});
        }});
        param.put("id", 1);
        String paramStr = (new JSONObject(param)).toJSONString();
        String getOnBlockResult = HttpUtil.requestPost(postUrl, paramStr);
        JSONObject onBlockResult = JSONObject.parseObject(getOnBlockResult);

        if (!onBlockResult.containsKey("result")) {
          logger.error("上链失败："+getOnBlockResult);
          throw new RuntimeException();
        }
        if ("sampleUrl".equals(field)) {
          copyrightManager.setOnBlockSampleToken(onBlockResult.getString("result"));
        } else {
          copyrightManager.setOnBlockToken(onBlockResult.getString("result"));
        }
      } else {
        logger.error("上链失败："+getUnlockResult);
        throw new RuntimeException();
      }
    } catch (Exception e) {
      logger.error("上链失败: ", e);
      throw new RuntimeException();
    }
  }


  /**
   * 禁用作品
   * 就用后需要给作品发布者通知消息
   * @param ids
   * @param content
   */
  @Transactional
  public void disableCopyright(List<Long> ids, String content, Integer opUID) {

    Assert.notEmpty(ids, "请选择作品");
    if(StringUtils.isEmpty(content)){
      throw new VerificationFailedException();
    }

    ids.forEach(id -> {
      CopyrightManager copyrightManager = copyrightManagerService.getById(id);
      if (copyrightManager!=null) {
          copyrightManager.setEnable(DISABLE);
          copyrightManager.setInactiveType(CopyrightManager.InactiveTypeValue.REVOKED);
        UserEntity user = userService.getByUserName(copyrightManager.getUserName()).orElseThrow(NotFountException::new);
        List<Integer> uid = new ArrayList<>(1);
        uid.add(user.getUserId());
        ProcessRecord processRecord = new ProcessRecord();
        processRecord.setOpType("撤销");
        processRecord.setOpResult("同意");
        processRecord.setApproverOpinion("");
        Optional<UserEntity> OpUser = userService.findById(opUID);
        if (OpUser.isPresent()) {
          processRecord.setOpName(OpUser.get().getRealName());
        } else {
          processRecord.setOpName("系统");
        }
        copyrightManager.getFlowRecord().add(processRecord);
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
        messageServiceFacade.sendMessageBase("作品撤销通知", content, false, uid);
      }
    });

  }
  @Transactional(rollbackFor = RuntimeException.class)
  public Map<String,Object> getUnreadForManager(HomeQueryParam param){
    Map<String,Object> result = new HashMap<>();
    Boolean isManager = param.getIsManager();
    Integer id = param.getId();
    if(id==null){
      result.put("error","UID为空");
      return result;
    }
    UserEntity user = userService.findById(id).orElse(null);
    if(user==null){
      result.put("error","找不到该用户信息");
      return result;
    }
    //获取当前时间
    LocalDateTime monthStartTime = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth());
    LocalDateTime monthEndTime = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth());
    LocalDateTime yearStartTime = LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear());
    LocalDateTime yearEndTime = LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear());

    long firstApprovingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.FIRST_REVIEW);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),null,null));
    long finalApprovingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.FINAL_REVIEW);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),null,null));
    long secondApprovingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.SECOND_REVIEW);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),null,null));
    long certificatingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.CERT_CREATE);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),null,null));

    result.put("firstApprovingCount",firstApprovingCount);
    result.put("secondApprovingCount",secondApprovingCount);
    result.put("finalApprovingCount",finalApprovingCount);
    result.put("certificatingCount",certificatingCount);

    return result;
  }

  @Transactional(rollbackFor = RuntimeException.class)
  public Map<String,Object> dataCount(HomeQueryParam param){
    Map<String,Object> result = new HashMap<>();
    Boolean isManager = param.getIsManager();
    Boolean isWorkstation = param.getIsWorkstation();
    if (isManager==null) isManager=false;
    if (isWorkstation==null) isWorkstation=false;
    Integer id = param.getId();
    if(id==null){
      result.put("error","UID为空");
      return result;
    }
    UserEntity user = userService.findById(id).orElse(null);
    if(user==null){
      result.put("error","找不到该用户信息");
      return result;
    }
    //获取当前时间
    LocalDateTime monthStartTime = LocalDateTime.of(LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).toLocalDate(), LocalTime.of(0, 0, 0));
    LocalDateTime monthEndTime = LocalDateTime.of(LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).toLocalDate(), LocalTime.of(23, 59, 59));
    LocalDateTime yearStartTime = LocalDateTime.of(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear()).toLocalDate(), LocalTime.of(0, 0, 0));
    LocalDateTime yearEndTime = LocalDateTime.of(LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear()).toLocalDate(), LocalTime.of(23, 59, 59));
    if(isManager){
      long firstApprovingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.FIRST_REVIEW);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),null,null));
      long finalApprovingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.FINAL_REVIEW);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),null,null));
      long secondApprovingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.SECOND_REVIEW);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),null,null));
      long certificatingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.CERT_CREATE);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),null,null));
      long totalCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.CERT_CREATED);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),null,null));
      long monthCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.CERT_CREATED);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),monthStartTime,monthEndTime));
      long yearCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.CERT_CREATED);}},null,param.getVisibleLevelCity(),param.getVisibleLevelCounty(),yearStartTime,yearEndTime));

      long workTotalCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(CopyrightManager.CERT_CREATED, null, null, null));
      long workMonthCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(CopyrightManager.CERT_CREATED, null, monthStartTime, monthEndTime));
      long workYearCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(CopyrightManager.CERT_CREATED, null, yearStartTime, yearEndTime));
      long workFinalApprovingCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(CopyrightManager.FINAL_REVIEW, null, null, null));
      long workSecondApprovingCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(CopyrightManager.SECOND_REVIEW, null, null, null));
      List<TagDto> workTagList = tagServiceFacade.getWorkstationList();
      List<UserEntity> userEntityList = userService.findByWorkstation();// 工作站数量以用户为单位

//      long submitSWBCount=copyrightManagerService.count(CopyrightManagerPredicates.countSWBreport());
      result.put("firstApprovingCount",firstApprovingCount);
      result.put("secondApprovingCount",secondApprovingCount);
      result.put("finalApprovingCount",finalApprovingCount);
      result.put("certificatingCount",certificatingCount);
//      result.put("reportSWBCount",submitSWBCount);
//      String cityName = null;
//      String countyName = null;
//      if(param.getVisibleLevelCity()!=null) {
//        Digital cityDigital = digitalService.getById(param.getVisibleLevelCity()).orElse(null);
//        cityName = (cityDigital==null)?null:cityDigital.getDict_name();
//      }
//      if(param.getVisibleLevelCounty()!=null) {
//        Digital countyDigital = digitalService.getById(param.getVisibleLevelCounty()).orElse(null);
//        countyName = (countyDigital==null)?null:countyDigital.getDict_name();
//      }
      /*JSONObject totalResponse = httpRequest(null,null,cityName,countyName);
      long OldTotal = totalResponse.getLong("total");*/
      result.put("totalCount",totalCount/*+OldTotal*/);
      /*JSONObject monthResponse = httpRequest(monthStartTime.toLocalDate(),monthEndTime.toLocalDate(),cityName,countyName);
      long OldmonthCount = monthResponse.getLong("total");*/
      result.put("monthCount",monthCount/*+OldmonthCount*/);
      /*JSONObject yearResponse = httpRequest(yearStartTime.toLocalDate(),yearEndTime.toLocalDate(),cityName,countyName);
      long OldyearCount = yearResponse.getLong("total");*/
      result.put("yearCount",yearCount/*+OldyearCount*/);
      result.put("workTotalCount",workTotalCount);
      result.put("workMonthCount",workMonthCount);
      result.put("workYearCount",workYearCount);
      result.put("workCount",userEntityList.size());
      // result.put("workCount",userEntityList.size());
      result.put("workFinalApprovingCount",workFinalApprovingCount);
      result.put("workSecondApprovingCount",workSecondApprovingCount);
      result.put("workTagList",workTagList);
    } else if (isWorkstation) {
      // 工作站首页
      long workTotalCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(null, user.getUserName(), null, null));
      long workMonthCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(null, user.getUserName(), monthStartTime, monthEndTime));
      long workYearCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(null, user.getUserName(), yearStartTime, yearEndTime));
      long workFinalApprovingCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(CopyrightManager.FINAL_REVIEW, user.getUserName(), null, null));
      long workSecondApprovingCount = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(CopyrightManager.SECOND_REVIEW, user.getUserName(), null, null));

      result.put("workTotalCount",workTotalCount);
      result.put("workMonthCount",workMonthCount);
      result.put("workYearCount",workYearCount);
      result.put("workFinalApprovingCount",workFinalApprovingCount);
      result.put("workSecondApprovingCount",workSecondApprovingCount);
    } else {
      long approvingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.FIRST_REVIEW);add(CopyrightManager.FINAL_REVIEW);add(CopyrightManager.SECOND_REVIEW);}},user.getUserName(),null,null,null,null));
      long certificatingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.CERT_CREATE);add(CopyrightManager.REPORTING);}},user.getUserName(),null,null,null,null));
      long unsubmitCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(CopyrightManager.UNSUBMIT);}},user.getUserName(),null,null,null,null));
      long modifingCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(new ArrayList<Integer>(){{add(MODIFIED);}},user.getUserName(),null,null,null,null));
      Integer countDown = copyrightManagerService.getCountDownLast(user.getUserName());
      long totalCount = copyrightManagerService.count(CopyrightManagerPredicates.homgPageQuery(null,user.getUserName(),null,null,null,null));
      result.put("approvingCount",approvingCount);
      result.put("certificatingCount",certificatingCount);
      result.put("unsubmitCount",unsubmitCount);
      result.put("modifingCount",modifingCount);
      result.put("countDown",countDown);
      result.put("totalCount",totalCount);
    }

    return result;
  }

  /**
   * 省局推送数据统计
   * @param param
   * @return
   */
  @Transactional(rollbackFor = RuntimeException.class)
  public Map<String,Object> pushDataCount(PushQueryParam param){
      Map<String, Object> result = new HashMap<>();
      List<StatisticStringDto> list = new ArrayList<>();

      Assert.isTrue(userServiceFacade.isManager(), "权限不足");

      long submitSWBCount = copyrightManagerService.countSWBreport(param.getStart(2).length(), param.getStart(2), param.getEnd(2));
      long submitHJCount = copyrightManagerService.countHJreport(param.getStart(4).length(), param.getStart(4), param.getEnd(4));
      long submitZZCount = copyrightManagerService.count(CopyrightManagerPredicates.countZZreport(param.getStartTime(), param.getEndTime()));

      list.add(new StatisticStringDto("swb", "省网办", submitSWBCount));
      list.add(new StatisticStringDto("hj", "汇聚平台", submitHJCount));
      list.add(new StatisticStringDto("zz", "证照提交", submitZZCount));

      List<StatisticDto> evaList = evaluationServiceFacade.countByAlternate(param.getStartTime(), param.getEndTime());
      long submitEvaCount = evaList.stream().collect(Collectors.summingLong(StatisticDto::getAmount));
      list.add(new StatisticStringDto("eva", "好差评汇总", submitEvaCount)); // 好差评统计数据追加

      if (evaList.size() > 0) {
          Map<String, Long> evaMap = evaList.stream().collect(Collectors.toMap(StatisticDto::getKey, StatisticDto::getAmount));

          long fiveStarCount = evaMap.get("5") == null ? 0 : evaMap.get("5");
          // 计算五星好评率（取两位小数）
          list.add(new StatisticStringDto("fiveStar", "五星好评数量", fiveStarCount));
          list.add(new StatisticStringDto("fiveStarRate", "五星好评占比", String.format("%.2f%%", ((double) fiveStarCount / submitEvaCount * 100))));

          long fourStarCount = evaMap.get("4") == null ? 0 : evaMap.get("4");
          list.add(new StatisticStringDto("fourStar", "四星好评数量", fourStarCount));
          list.add(new StatisticStringDto("fourStarRate", "四星好评占比", String.format("%.2f%%", ((double) fourStarCount / submitEvaCount * 100))));

          long threeStarCount = evaMap.get("3") == null ? 0 : evaMap.get("3");
          list.add(new StatisticStringDto("threeStar", "三星好评数量", threeStarCount));
          list.add(new StatisticStringDto("threeStarRate", "三星好评占比", String.format("%.2f%%", ((double) threeStarCount / submitEvaCount * 100))));

          long twoStarCount = evaMap.get("2") == null ? 0 : evaMap.get("2");
          list.add(new StatisticStringDto("twoStar", "二星好评数量", twoStarCount));
          list.add(new StatisticStringDto("twoStarRate", "二星好评占比", String.format("%.2f%%", ((double) twoStarCount / submitEvaCount * 100))));

          long oneStarCount = evaMap.get("1") == null ? 0 : evaMap.get("1");
          list.add(new StatisticStringDto("oneStar", "一星好评数量", oneStarCount));
          list.add(new StatisticStringDto("oneStarRate", "一星好评占比", String.format("%.2f%%", ((double) oneStarCount / submitEvaCount * 100))));
      }

      result.put("listData", list);
      result.put("total", submitSWBCount + submitHJCount + submitZZCount + submitEvaCount);
      return result;
  }

  /**
   * 工作站数据统计
   * @param param
   * @return
   */
  @Transactional(rollbackFor = RuntimeException.class)
  public Map<String,Object> workStationCount(PushQueryParam param){
    Map<String,Object> result = new HashMap<>();
    List<StatisticDto> list;

    Assert.isTrue(userServiceFacade.isManager(), "权限不足");

    list = copyrightManagerService.countWorkstationList(CopyrightManagerPredicates.countWorkstationPredicates(CopyrightManager.CERT_CREATED, null, param.getStartTime(), param.getEndTime()));
    long total = copyrightManagerService.countWorkstation(CopyrightManagerPredicates.countWorkstationPredicates(CopyrightManager.CERT_CREATED, null, param.getStartTime(), param.getEndTime()));

    result.put("listData",list);
    result.put("total",total);
    return result;
  }

  private JSONObject httpRequest(LocalDate start,LocalDate end,String city,String county){
    //处理旧参数，并请求旧系统数据
    Map<String,Object> oldParam = new HashedMap();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    oldParam.put("startTime",start==null?null:(start.format(formatter)+" 00:00:00"));
    oldParam.put("endTime", end==null?null:(end.format(formatter)+" 23:59:59"));
    oldParam.put("county",county);
    oldParam.put("city",city);
    oldParam.put("productTypes",new ArrayList<>());
    RestTemplate restTemplate = new RestTemplate();
    String url = config.getOldSystemUrl()+"dataStatistics/geographyOfTime.do";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
    HttpEntity<Map<String, Object>> request = new HttpEntity<>(oldParam, headers);
    ResponseEntity<String> response = restTemplate.postForEntity( url, request , String.class );
    return JSONObject.parseObject(response.getBody());
  }

  @Transactional
  public void modifiedCountdown(){
      log.info("驳回作品倒计时处理");
      BooleanBuilder builder = new BooleanBuilder();
      builder.and(copyrightManager.inactiveType.eq(CopyrightManager.InactiveTypeValue.NORMAL))
              .and(copyrightManager.countDown.gt(1))
              .and(copyrightManager.status_type.eq(MODIFIED));

      List<CopyrightManager> copyrightManagers = (List<CopyrightManager>) copyrightManagerService.findAll(builder);
      long res = copyrightManagerService.countDown();

      if (!config.getBackendIsOn()) return;
      if (copyrightManagers.size() == res) {
          UserEntity userEntity = null;
          // 调用办结
          String cardId = "350104196910110013";
          List<UserEntity> userEntities = userService.findByCardId(cardId);
          if (userEntities.size() > 1) {
              Optional<UserEntity> optionalUser = userEntities.stream().max(Comparator.comparing(UserEntity::getRegisterTime));
              if (optionalUser.isPresent()) {
                  userEntity = optionalUser.get();
              }
          } else {
              userEntity = userEntities.get(0);
          }


          log.info("定时任务驳回开开始----办结+证照，copyrightManagers：{},集合数量为:{}",copyrightManagers,copyrightManagers.size());
          for (CopyrightManager manager : copyrightManagers) {
              try {
                  backendServiceFacade.startWorkflow(manager, userEntity, ProcessProgressEntity.Type.FINISH, "退件", false);
              }catch (Exception e){
                  log.debug("审核通过后受理失败: {}", e.getMessage());
              }
          }
          log.info("定时任务驳回结束----办结+证照");
      }
  }

//  @Transactional
  @Async("doSomethingExecutor")
  public void onportemp() {
      try {
          org.apache.axis.client.Service service = new org.apache.axis.client.Service();
          // step 1 get Guid
          String guid = getGuid((Call) service.createCall());
          // step 2 pushXml
          pushXml((Call) service.createCall(), guid);
      } catch (Exception e) {
          // TODO Auto-generated catch block
          logger.error("数据汇聚失败: ", e);
      }
  }

    /**
     * get Guid
     * @param call
     * @return
     */
    public String getGuid(Call call) {
        String guid = null;
        try {
            //LoginByAccount 为用户身份验证接口方法名称，userid为用户名，password为密码
            call.setTargetEndpointAddress(config.getWsdl());
            QName qName = new QName("http://tempuri.org/", "LoginByAccount");
            call.setOperationName(qName);
            call.setUseSOAPAction(true);
            call.addParameter("userid", XMLType.XSD_STRING, javax.xml.rpc.ParameterMode.IN);
            call.addParameter("password", XMLType.XSD_STRING, javax.xml.rpc.ParameterMode.IN);
            call.setReturnType(XMLType.XSD_STRING);
            guid = (String) call.invoke(new Object[]{config.getConUserName(), config.getConPassWord()});
            System.out.println("获取guid：" + guid);
            logger.debug("【数据汇聚】获取guid：" + guid);
            if (guid.contains("ERR:")) {
                CertificateCreateEntity certificateCreateEntity = new CertificateCreateEntity();
                certificateCreateEntity.setResult("数据汇聚LoginByAccount--" + guid);
                LocalDateTime now = LocalDateTime.now();
                certificateCreateEntity.setCreateTime(now);
                certificateCreateEntity.setFinishTime(now);
                certificateCreateService.save(certificateCreateEntity);
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            logger.error("数据汇聚失败: ", e);
        }
        return guid;
    }

    /**
     * push data
     * @param callPushXml
     * @param guid
     * @return
     */
    public void pushXml(Call callPushXml, String guid) {
        List<CopyrightOwner> ownData = new ArrayList<>();
        try {
            callPushXml.setTargetEndpointAddress(config.getWsdl());
            QName qNamePush = new QName("http://tempuri.org/", "pushXml");
            callPushXml.setOperationName(qNamePush);
            callPushXml.setUseSOAPAction(true);
            callPushXml.addParameter("guid", XMLType.XSD_STRING, javax.xml.rpc.ParameterMode.IN);
            callPushXml.addParameter("catalogid", XMLType.XSD_STRING, javax.xml.rpc.ParameterMode.IN);
            callPushXml.addParameter("xmlstr", XMLType.XSD_STRING, javax.xml.rpc.ParameterMode.IN);
            callPushXml.setReturnType(XMLType.XSD_STRING);

//            pushManagerDel(callPushXml, guid);
            boolean flag = true;
            while (flag) {
                List<CopyrightManager> copyData = copyrightManagerService.queryAllByCertificate(true, PageRequest.of(0, 500));
                int length = copyData.size();
                if (copyData.size() == 0) {
                    flag = false;
                    continue;
                }
                if (copyData.size() < 500) flag = false;
                System.out.println("copyData length:" + length);

                // step 2  push manager
                ownData = pushManager(callPushXml, guid, copyData);

                // step 3 push owner
                pushOwner(callPushXml, guid, ownData);

                List<Author> authors = new ArrayList<>();
                for (CopyrightManager manager : copyData) {
                    List<Author> author = authorService.getAuthorByCopyrightId(manager.getRegistrationNum());
                    if (!ObjectUtils.isEmpty(author)) {
                        authors.addAll(author);
                    }
                }
                // step 3 push author
                pushAuthor(callPushXml, guid, authors);
            }
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            logger.error("error: ", e1);
        }

    }

    /**
     * push manager data
     * @param callPushXml
     * @param guid
     * @param copyData
     * @return
     */
    public List<CopyrightOwner> pushManager(Call callPushXml, String guid, List<CopyrightManager> copyData) {
        List<CopyrightOwner> ownData = new ArrayList<>();
        try {
            logger.info(new Date() + "【数据汇聚】manager调用");
            String pomXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><table>";
            for(int j=0;j<copyData.size();j++){
                pomXml = pomXml + "<row type=\"add\">" +
                        "<registrationnum name=\"作品id\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getRegistrationNum() + "]]></registrationnum>" +
                        "<worksnum name=\"作品登记号\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getWorksNum() + "]]></worksnum>" +
                        "<productionname name=\"作品名称\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getProductionName() + "]]></productionname>" +
                        "<registrationdate name=\"作品登记日期\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getRegistrationDate().format(dfTime) + "]]></registrationdate>" +
                        "<productiontypeid name=\"作品类别id\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getProductionTypeId() + "]]></productiontypeid>" +
                        "<finishtime name=\"创作完成时间\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></finishtime>" +
                        "<firstpublishtime name=\"首次发表日期\" isattachment=\"false\"><![CDATA[" + (copyData.get(j).getFirstPublishTime() == null ? "" : copyData.get(j).getFirstPublishTime()) + "]]></firstpublishtime>" +
                        "<opusInditekind name=\"作品创作性质ID\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getOpusInditekind() + "]]></opusInditekind>" +
                        "<finish_address name=\"作品完成地点\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishAddress() + "]]></finish_address>" +
                        "<first_country name=\"首次发表地点国家\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFirstCountry() + "]]></first_country>" +
                        "<work_publishArea name=\"首次发表的详细地址\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getWorkPublishArea() + "]]></work_publishArea>" +
                        "<obtain_mode name=\"权利取得方式\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getObtainMode() + "]]></obtain_mode>" +
                        "<right_own_mode name=\"权利归属方式\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getRightOwnMode() + "]]></right_own_mode>" +
                        "<right_scope name=\"权利拥有状况\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getRightScope() + "]]></right_scope>" +
                        "<submitter_name name=\"申请人姓名\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getSubmitter().getCopyrightName() + "]]></submitter_name>" +
                        "<submitter_telephone name=\"申请人电话\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getSubmitter().getCopyrightPhone() + "]]></submitter_telephone>" +
                        "<submitter_address name=\"申请人详细地址\" isattachment=\"false\"><![CDATA[]]></submitter_address>" +
                        "<submitter_code name=\"申请人邮编\" isattachment=\"false\"><![CDATA[]]></submitter_code>" +
                        "<submitter_owner name=\"申请人联系人\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></submitter_owner>" +
                        "<submitter_phone name=\"申请人手机\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></submitter_phone>" +
                        "<submitter_email name=\"申请人E-mail\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></submitter_email>" +
                        "<submitter_fax name=\"申请人传真\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></submitter_fax>" +
                        "<agent_name name=\"代理人姓名\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></agent_name>" +
                        "<agent_telephone name=\"代理人电话\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></agent_telephone>" +
                        "<agent_address name=\"代理人详细地址\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></agent_address>" +
                        "<agent_code name=\"代理人邮编\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></agent_code>" +
                        "<agent_owner name=\"代理人联系人\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></agent_owner>" +
                        "<agent_phone name=\"代理人手机\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></agent_phone>" +
                        "<agent_email name=\"代理人E-mail\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></agent_email>" +
                        "<agent_fax name=\"代理人传真\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></agent_fax>" +
                        "<certificate_create_time name=\"证书完成日期\" isattachment=\"false\"><![CDATA[" + copyData.get(j).getFinishTime() + "]]></certificate_create_time>" +
                        "</row>";
                ownData.addAll(copyData.get(j).getOwnerList());
            }
            pomXml+="</table>";
            String managerResult = (String) callPushXml.invoke(new Object[]{guid, config.getCatalogId_manager(), pomXml});
            //输出调用结果，Object数组第一条数据为返回结果
            org.json.JSONObject object = XML.toJSONObject(managerResult);
            if (object.has("Response")) {
                org.json.JSONObject res = object.getJSONObject("Response");
                System.out.println("get flag:"+res.getBoolean("flag"));
                if (res.getBoolean("flag")) {
                    String pch = res.getString("pch"); // 汇聚平台操作的批次号
                    updateManagerPch(copyData, pch);
                }
            }
            System.out.println("调用结果:" + managerResult);
//            logger.info(new Date() + "【数据汇聚】manager调用结果：" + managerResult);
//            logger.info(new Date() +"【数据汇聚】manager推送数据：" + pomXml);
        } catch (Exception e) {
            logger.error("error: ", e);
        }
        return ownData;
    }

    /**
     * push owner data
     * @param callPushXml
     * @param guid
     * @param ownData
     */
    public void pushOwner(Call callPushXml, String guid, List<CopyrightOwner> ownData) {
        try {
            logger.info(new Date() +"【数据汇聚】owner调用：");
            int size = ownData.size();
            int ownLen = size / 500;
            System.out.println("ownData size:"+size);

            List<CopyrightOwner> pushData;
            String pomXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><table>";
            for(int i=0;i<=ownLen;i++) {
                System.out.println(i);
                pushData = new ArrayList<>();
                int pushLen;

                if (size == i * 500) continue;

                if (i < ownLen) pushLen = (i+1) * 500;
                else pushLen = size;

                for (int j = i * 500; j < pushLen; j++) {
                    pomXml = pomXml + "<row type=\"add\">" +
                            "<copy_id name=\"著作权人id\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getId() + "]]></copy_id>" +
                            "<copy_name name=\"著作权人名\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getCopyName() + "]]></copy_name>" +
                            "<copyrightmanagerid name=\"对应作品id\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getManager().getRegistrationNum() + "]]></copyrightmanagerid>" +
                            "<copy_countries name=\"著作权人国籍\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getCopyCountries() + "]]></copy_countries>" +
                            "<copy_province name=\"著作权人省份\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getCopyProvince() + "]]></copy_province>" +
                            "<copy_city name=\"著作权人城市\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getCopyCity() + "]]></copy_city>" +
                            "<copy_county name=\"著作权人区县\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getCopyCity() + "]]></copy_county>" +
                            "<copy_idcard name=\"证件号\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getCopyIdCard() + "]]></copy_idcard>" +
                            "<copy_sign name=\"署名类型\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getCopySignature() + "]]></copy_sign>" +
                            "<copy_opussign name=\"别名\" isattachment=\"false\"><![CDATA[" + (ownData.get(j).getCopySignatureName() == null ? "" : ownData.get(j).getCopySignatureName()) + "]]></copy_opussign>" +
                            "<copy_category name=\"著作权人类别\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getCopyCategory() + "]]></copy_category>" +
                            "<copy_certificate name=\"著作权人证件类型\" isattachment=\"false\"><![CDATA[" + ownData.get(j).getCopyCertificate() + "]]></copy_certificate>" +
                            "</row>";
                    pushData.add(ownData.get(j));
                }
                pomXml += "</table>";
                String ownerResult = (String) callPushXml.invoke(new Object[]{guid, config.getCatalogId_owner(), pomXml});
                //输出调用结果，Object数组第一条数据为返回结果
                org.json.JSONObject ownerObject = XML.toJSONObject(ownerResult);
                if (ownerObject.has("Response")) {
                    org.json.JSONObject res = ownerObject.getJSONObject("Response");
                    System.out.println("get flag:" + res.getBoolean("flag"));
                    if (res.getBoolean("flag")) {
                        String pch = res.getString("pch"); // 汇聚平台操作的批次号
                        updateOwnerPch(pushData, pch);
                    }
                }
                System.out.println("调用结果:" + ownerResult);
//                logger.info(new Date() +"【数据汇聚】owner调用结果：" + ownerResult);
//                logger.info(new Date() +"【数据汇聚】owner推送数据：" + pomXml);
                pomXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><table>";
            }
        } catch (Exception e) {
            logger.error("error: ", e);
        }
    }

    /**
     * push author data
     *
     * @param callPushXml
     * @param guid
     * @param authorData
     */
    public void pushAuthor(Call callPushXml, String guid, List<Author> authorData) {
        try {
            logger.info(new Date() + "【数据汇聚】author调用：");
            int size = authorData.size();
            int ownLen = size / 500;
            System.out.println("authorData size:" + size);

            List<Author> pushData;
            String pomXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><table>";
            for (int i = 0; i <= ownLen; i++) {
                System.out.println(i);
                pushData = new ArrayList<>();
                int pushLen;

                if (size == i * 500) continue;

                if (i < ownLen) pushLen = (i + 1) * 500;
                else pushLen = size;

                for (int j = i * 500; j < pushLen; j++) {
                    pomXml = pomXml + "<row type=\"add\">" +
                            "<author_id name=\"作者id\" isattachment=\"false\"><![CDATA[" + authorData.get(j).getId() + "]]></author_id>" +
                            "<author_name name=\"作者姓名\" isattachment=\"false\"><![CDATA[" + authorData.get(j).getAuthorName() + "]]></author_name>" +
                            "<copyrightmanagerid name=\"对应作品序号\" isattachment=\"false\"><![CDATA[" + authorData.get(j).getCopyright_id() + "]]></copyrightmanagerid>" +
                            "<author_opussign name=\"作者别名\" isattachment=\"false\"><![CDATA[" + (authorData.get(j).getAuthorSignatureName() == null ? "" : authorData.get(j).getAuthorSignatureName()) + "]]></author_opussign>" +
                            "</row>";
                    pushData.add(authorData.get(j));
                }
                pomXml += "</table>";
                String ownerResult = (String) callPushXml.invoke(new Object[]{guid, config.getCatalogId_author(), pomXml});
                //输出调用结果，Object数组第一条数据为返回结果
                org.json.JSONObject ownerObject = XML.toJSONObject(ownerResult);
                if (ownerObject.has("Response")) {
                    org.json.JSONObject res = ownerObject.getJSONObject("Response");
                    System.out.println("get flag:" + res.getBoolean("flag"));
                    if (res.getBoolean("flag")) {
                        String pch = res.getString("pch"); // 汇聚平台操作的批次号
                        updateAuthorPch(pushData, pch);
                    }
                }
                logger.info(new Date() + "【数据汇聚】author调用结果：" + ownerResult);
                logger.info(new Date() + "【数据汇聚】author推送数据：" + pomXml);
                pomXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><table>";
            }
        } catch (Exception e) {
            logger.error("error: ", e);
        }
    }

    // 单独用
    public void pushManagerDel(Call callPushXml, String guid) {
        Long registrationNum = 9862l;
        try {
            logger.info(new Date() + "【数据汇聚】manager调用");
            String pomXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><table>";
            pomXml = pomXml + "<row type=\"delete\">" +
                    "<registrationnum name=\"作品id\" isattachment=\"false\"><![CDATA[" + registrationNum + "]]></registrationnum>" +
                    "</row>";
            pomXml += "</table>";
            String managerResult = (String) callPushXml.invoke(new Object[]{guid, config.getCatalogId_manager(), pomXml});
            //输出调用结果，Object数组第一条数据为返回结果
            org.json.JSONObject object = XML.toJSONObject(managerResult);
            if (object.has("Response")) {
                org.json.JSONObject res = object.getJSONObject("Response");
                System.out.println("get flag:" + res.getBoolean("flag"));
                if (res.getBoolean("flag")) {
                    String pch = res.getString("pch"); // 汇聚平台操作的批次号
                }
            }
            System.out.println("调用结果:" + managerResult);
            logger.info(new Date() + "【数据汇聚】manager调用结果：" + managerResult);
            logger.info(new Date() +"【数据汇聚】manager推送数据：" + pomXml);
        } catch (Exception e) {
            logger.error("error: ", e);
        }
    }


    @Transactional
    public void updateManagerPch(List<CopyrightManager> copyrightManagers, String pch) {
        if (StringUtils.isEmpty(pch))
            return;
        for (CopyrightManager copyrightManager: copyrightManagers) {
            copyrightManager.setPch(pch);
            copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
        }
    }

    @Transactional
    public void updateOwnerPch(List<CopyrightOwner> copyrightOwners, String pch) {
        if (StringUtils.isEmpty(pch))
            return;
        for (CopyrightOwner copyrightOwner : copyrightOwners) {
            copyrightOwner.setPch(pch);
            copyrightOwnerService.save(copyrightOwner);
        }
    }

    @Transactional
    public void updateAuthorPch(List<Author> authors, String pch) {
        if (StringUtils.isEmpty(pch))
            return;
        for (Author author : authors) {
            author.setPch(pch);
            authorService.save(author);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void updateStateByIds(Integer state, List<Long> ids) {
        copyrightManagerService.updateStateByIds(state, ids);
    }

    public List<Map<String, Object>> getBySimilarAttachmentId(Long copyrightId, Long attachmentId) {
        return similarAttachmentService.getBySimilarAttachmentId(copyrightId, attachmentId);
    }

    /**
     * 添加作品的序列号
     */
    public void worksNumber(CopyrightManager copyrightManager) {

        if (copyrightManager.getWorksNum().contains("闽作登字-")) return;

        Calendar konwYear = Calendar.getInstance();
//    String number = String.format("%08d", copyrightManager.getRegistrationNum());
    String number = String.format("%08d", getNumber(konwYear.get(Calendar.YEAR)));
    List<Digital> digit = digitalServiceFacade.getDictByPid(82);
    String areaData="N";
    for (int i = 0; i < digit.size(); i++)
    {
      if (copyrightManager.getProductionTypeId().equals(digit.get(i).getId() + ""))
      {
        areaData=digit.get(i).getCreate_data();
        break;
      }
    }
    copyrightManager.setWorksNum("闽作登字-" + konwYear.get(Calendar.YEAR) + "-" +areaData  + "-" + number);
  }

  /**
   * 获取登记号编号
   * @param year
   * @return
   */
  private int getNumber(int year) {
    String lastCopy = copyrightManagerService.getLastNum(year);
    if(!StringUtils.isEmpty(lastCopy)) {
      return Integer.valueOf(lastCopy)+1;
    }
    return 1;
  }

  /**
   * 省网办推送
   * @param waiLianService
   * @return
   */
  @Async("doSomethingExecutor")
  public void wailianPushSync(WaiLianServiceImpl waiLianService) {
      // 手动提交，推送1小时
      int now =  Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
      wailianPush(waiLianService, now,now+1);
  }

  public void wailianPush(WaiLianServiceImpl waiLianService, int startHour, int endHour) {
      boolean flag = true;
      int num = 100; // 每小时大约推送200-300件
      List<Sort.Order> list = new ArrayList<>();
      Sort.Order order = new Sort.Order(Sort.Direction.ASC, "certificateCreateTime");
      list.add(order);
      Sort sort = Sort.by(list);
      logger.info(new Date() + "【省网办】调用开始：");
      while (flag) {
          try {
              int now =  Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
              // 自动提交, {8:00-20:00}点之间，暂停自动推送
              if (startHour > endHour)
                  if (now >= endHour && now < startHour) {
                      flag = false;
                      continue;
                  }

              // 手动提交，开始小时 小于 结束小时
              if (startHour <= endHour)
                  if (now < startHour || now > endHour) {
                      flag = false;
                      continue;
                  }

              List<CopyrightManager> copyDatas = copyrightManagerService.queryAllXLByCertificate(true, PageRequest.of(0, num, sort));
              int length = copyDatas.size();
              if (length == 0) {
                  flag = false;
                  continue;
              }
              if (length < num) flag = false;

              for (int j = 0; j < copyDatas.size(); j++) {
                  if (copyDatas.get(j).getOwnerList().get(0).getCopyCertificateZM() == null || copyDatas.get(j).getOwnerList().get(0).getCopyCertificateZM().getId() == null) {
                      updateManagerSN(copyDatas.get(j), "not electronical certificate", NO_CERT);
                      continue;
                  }

                  switch (copyDatas.get(j).getSnStatus()) {
                      case WAIT_LZ:
                      case LX_XML_FAILD:
                      case LZ_ING:
                      case LZ_FAILD:
                          wailianLZSubmit(waiLianService, copyDatas.get(j));
                          break;
                      case WAIT_BJ:
                      case BJ_XML_FAILD:
                      case BJ_ING:
                      case BJ_FAILD:
                          wailianBJSubmit(waiLianService, copyDatas.get(j));
                          break;
                      default:
                          if (StringUtils.isEmpty(copyDatas.get(j).getSnCode())) {
                              // 新规则是24位   区域简码4位  +事项简码8位  +6位年月日+6位序号。地市跟省直文档都有说明新申报号规则
                              // fjsz K1003830
                              /*String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
                              int lastSnCode = processProgressService.getLastSncode(today);
                              copyDatas.get(j).setSnCode(String.format("fjszK1003830%s%06d", today, ++lastSnCode));*/
                              if(StringUtils.isEmpty(copyDatas.get(j).getSnCode())){
                                  String str = copyDatas.get(j).getRegistrationNum().toString();
                                  String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
                                  if(str.length() >= 6){
                                      str = str.substring(str.length() - 6);
                                  }else {
                                      str = String.format("%06d", Integer.parseInt(str));
                                  }
                                  copyDatas.get(j).setSnCode(String.format("fjszK1003830%s%s", today, str));
                                  createCopyright(copyDatas.get(j));
                              }
                          }
                          wailianSQSubmit(waiLianService, copyDatas.get(j));
                          break;
                  }
              }
          } catch (Exception e) {
              logger.info(new Date() +"【省网办】数据查询异常：", e);
              continue;
          }
      }
      logger.info(new Date() + "【省网办】调用结束：");
  }

  /**
   * 省网办项目申请
   * @param waiLianService
   * @param manager
   */
    public void wailianSQSubmit(WaiLianServiceImpl waiLianService,CopyrightManager manager) {
        String sqxml = "";
        try {
            // step1 项目信息申请
//            updateManagerSN(manager,"submitting", 1); // sn: submitting; sn_status:1 申请xml取得中

            sqxml = getXmlWLSQ(manager);
            String ret = wailianSubmit(waiLianService, manager, sqxml);
            //输出调用结果，SN为返回结果
            if (!ret.contains("成功")) {
                logger.info(new Date() + "【省网办】项目信息调用结果：" + manager.getWorksNum() + " " + ret);
                updateManagerSN(manager, manager.getSnCode() + "：" + ret, APPLY_ERROR); // sn: "error"; sn_status:4 申请异常
                return;
            }

            updateManagerSNStatus(manager, WAIT_LZ); // sn: sncode; sn_status:4 申请成功(流转xml取得中)

            // step2 环节流转
            wailianLZSubmit(waiLianService, manager);
        } catch (Exception e) {
            logger.info(new Date() + "【省网办】项目信息调用结果：" + manager.getWorksNum() + " " + e);
            e.printStackTrace();
            updateManagerSN(manager, manager.getSnCode() + "：" + e.getMessage(), APPLY_ERROR); // sn: "error"; sn_status:4 申请异常
        }
    }

    public void wailianLZSubmit(WaiLianServiceImpl waiLianService,CopyrightManager manager) {
        String lzxml = "";
        try {
            lzxml = getXmlWLLZ(manager);
            String retlz = wailianSubmit(waiLianService, manager, lzxml);
            if (!retlz.contains("成功")) {
                logger.info(new Date() + "【省网办】环节流转调用结果：" + manager.getWorksNum() + " " + retlz);
                updateManagerSNStatus(manager, LZ_FAILD);  // sn: "sncode"; sn_status:8 流转失败
                return;
            }

            updateManagerSNStatus(manager, WAIT_BJ);  // sn: "sncode"; sn_status:9 流转成功（待办结）
            // Step3 办结调用
            wailianBJSubmit(waiLianService, manager);
        } catch (Exception e) {
            logger.info(new Date() + "【省网办】环节流转调用结果：" + manager.getWorksNum() + " " + e);
            e.printStackTrace();
            updateManagerSNStatus(manager, LZ_FAILD);  // sn: "sncode"; sn_status:8 流转失败
        }
    }

    public void wailianBJSubmit(WaiLianServiceImpl waiLianService,CopyrightManager manager) {
        String bjxml = "";
        try {
            bjxml = getXmlWLBJ(manager);
            String retBJ = wailianSubmit(waiLianService, manager, bjxml);

            logger.info(new Date() + "【省网办】办结调用结果：" + manager.getWorksNum() + " " + retBJ);
            if (!retBJ.contains("成功")) {
                updateManagerSNStatus(manager, BJ_FAILD);  // sn: "sncode"; sn_status:12 办结失败
                return;
            }
            updateManagerSNStatus(manager, COMPLETE);  // sn: "sncode"; sn_status:13 办结成功
        } catch (Exception e) {
            logger.info(new Date() + "【省网办】办结调用结果：" + manager.getWorksNum() + " " + e);
            e.printStackTrace();
            updateManagerSNStatus(manager, BJ_FAILD);  // sn: "sncode"; sn_status:12 办结失败
        }
    }

    public String wailianSubmit(WaiLianServiceImpl waiLianService, CopyrightManager manager, String xml) throws RemoteException {
        return waiLianService.newSubmit("11350000003591037R", "123456", xml);
    }

    /**
     * 项目信息申请
     * @param copyData
     * @return
     */
    public String getXmlWLSQ(CopyrightManager copyData) {
        String pomXml = "";
        String attrCode = "";
        String attrName = "";
        String fileName = "";
        try {
            String ITEMCODE = "11350000003591037RXK000041"; // 审批事项编码:法人
            if (copyData.getOwnerList().get(0).getCopyCategory()==135) // 自然人
                ITEMCODE = "003591504GF05462";

            pomXml = "<?xml version=\"1.0\" encoding=\"GB2312\"?><Case xmlns=\"http://FUJIAN/WSSP/NEWWSSP\">";
            pomXml= pomXml + "<RowGuid>" + UUID.randomUUID().toString() + "</RowGuid>" +
                    "<SN>" + copyData.getSnCode() + "</SN>" +
                    "<DeclareTime>" + copyData.getCertificateCreateTime().format(dfTime) + "</DeclareTime>" +
                    "<ITEMCODE>" + ITEMCODE + "</ITEMCODE>" +
                    "<ProjectName>" + XMLUtil.escapeSpecialCharacters(copyData.getProductionName()) + "</ProjectName>" +
                    "<ApplyFrom>41</ApplyFrom>" +
                    "<Proposer>" +
                    "<ApplyType>" + copyData.getOwnerList().get(0).getApplyType() + "</ApplyType>" +
                    "<Name>" + copyData.getOwnerList().get(0).getCopyName() + "</Name>" +
                    "<IDType>" + copyData.getOwnerList().get(0).getIDType() + "</IDType>" +
                    "<ID>" + copyData.getOwnerList().get(0).getCopyIdCard() + "</ID>";

            if (copyData.getOwnerList().get(0).getCopyCategory()!=135)
                pomXml = pomXml + "<UnitType>" + copyData.getOwnerList().get(0).getUnitType() + "</UnitType>" +
                        // <!-- 法人代表姓名，仅申报种类为法人填写且是【必填】必填项 -->
                        "<UnitLealPerson>" + copyData.getSubmitter().getCopyrightName() + "</UnitLealPerson>"
                        + "<FRIDType>" + copyData.getOwnerList().get(0).getIDType() + "</FRIDType>" +
                        "<FRID>" + copyData.getOwnerList().get(0).getCopyIdCard() + "</FRID>";
            pomXml=pomXml+
                    // <!--【必填】性别：男、女 -->
                    "<Sex>男</Sex>"+
                    "<MobilePhone>"+copyData.getSubmitter().getCopyrightPhone()+"</MobilePhone>"+
                    "<Addr>"+copyData.getSubmitter().getCopyrightAddress()+"</Addr>"+
                    "<OperatorName>郑开辟</OperatorName>"+
                    "<OperatorSex>男</OperatorSex>"+
                    "<OperatorCertificateType>SF</OperatorCertificateType>"+
                    "<OperatorCertificateNumber>350322196907140012</OperatorCertificateNumber>"+
                    "<OperatorMobilePhone>13305913533</OperatorMobilePhone>"+
                    "</Proposer>";

            // "<!--用户提交的附件列表（即申报材料）暂定只提供著作权人电子证照-->"+
            if (copyData.getOwnerList().get(0).getCopyCertificateZM()!=null && copyData.getOwnerList().get(0).getCopyCertificateZM().getId()!=null) {
                attrCode = "003591504GF05462_08";
                attrName = "著作权人和作者身份证明文件";
                fileName = copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateTypeName();
                pomXml = pomXml + "<Attrs>" +
                        "<Attr>" +
                        // <!--mode取值为数字，0(无)、1(网上)、2(网下)、3(材料表单)、4（电子证照）
                        "<Mode>4</Mode>" +
                        // <!-- 【必填】附件编码，这个编码是各审批事项所需提交材料即附件的编码，每一种附件都有唯一的附件编码。事项编码+附件编码为全局唯一的，能够唯一定位到一个附件实例上。-->
                        "<Code>" + attrCode + "</Code>" +
                        // <!-- 【必填】审批事项定义中定义的附件名称 -->
                        "<Name>" + attrName + "</Name>" +
                        // 附件的文件名称（包括扩展名，如“关于**的批复.doc”）
                         "<FileName>" + fileName + "</FileName>" +
                        // <!-- 附件内容，使用base64编码-->
                        // "<Content></Content>" +
                        // <!--附件序号，如该审批事项有20个附件，则序号依次为1～20，顺序以发布的顺序为准。当mode=”1”时有效。预留，可为空-->
                        "<SeqNo>1</SeqNo>" +
                        // <!--是否来源于数据共享为Y时来源于数据共享，N或者空为否 -->
                        "<Datashare>Y</Datashare>" +
                      // <!-- 证照编号，当mode=”4”时使用 -->
                        "<certificateNumber>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateNumber() + "</certificateNumber>" +
                        // <!-- 证照名称，当mode=”4”时使用 -->
                        "<certificateName>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateName() + "</certificateName>" +
                        // <!-- 证照类型名称，当mode=”4”时使用 -->
                        "<certificateTypeName>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateTypeName() + "</certificateTypeName>" +
                        // <!-- 电子证照标识，当mode=”4”时使用 -->
                        "<certificateIdentifier>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateIdentifier() + "</certificateIdentifier>" +
                        // <!-- 证照类型代码，当mode=”4”时使用 -->
                        "<certificateTypeCode>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateTypeCode() + "</certificateTypeCode>" +
                        // <!-- 持证者代码，当mode=”4”时使用 -->
                        "<certificateHolderCode>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateHolderCode() + "</certificateHolderCode>" +
                        "<AttrFormInfo>" +
                        "<BaseInfo>" +
                        "<Item name=\"sTitle\">电子证照</Item>" +
                        "<Item name=\"KeyWord\">电子证照</Item>" +
                        "<Item name=\"文件名\">电子证照</Item>" +
                        "</BaseInfo>" +
                        "</AttrFormInfo>" +
                        "</Attr>" +
                        "</Attrs>";
            } else if (copyData.getOwnerList().get(0).getCopyCertificate() != CopyrightOwner.CopyCertificateValue.CODE) {// 非统一信用代码、身份证、居住证（港澳台通行证、护照）
              pomXml = pomXml + "<Attrs>" +
                        "<Attr>"+
                        "<Mode>1</Mode>"+
                        "<Code>"+copyData.getAuthorIdCar().getId()+"</Code>"+
                        "<Name>"+copyData.getAuthorIdCar().getWorkName()+"</Name>"+
                        "<FileName>"+copyData.getAuthorIdCar().getWorkName()+"</FileName>"+
                        "<Content>"+DocumentHandlerUtil.getImageStr(copyData.getAuthorIdCar().getWorkUrl())+"</Content>"+
                        "<SeqNo>1</SeqNo>"+
                        "<AttrFormInfo>"+
                        "<BaseInfo>"+
                        "<Item name=\"sTitle\">"+copyData.getAuthorIdCar().getWorkName()+"</Item>"+
                        "<Item name=\"KeyWord\">"+copyData.getAuthorIdCar().getWorkName()+"</Item>"+
                        "<Item name=\"文件名\">"+copyData.getAuthorIdCar().getWorkName()+"</Item>"+
                        "</BaseInfo>"+
                        "</AttrFormInfo>"+
                        "</Attr>"+
                        "</Attrs>";
            }
            pomXml = pomXml + "</Case>";
            updateManagerSNStatus(copyData, APPLYING); //sn: submitting; sn_status:3 申请中
        } catch (Exception e) {
            logger.error("error: ", e);
            updateManagerSN(copyData, copyData.getSnCode() + "：" + e.getMessage(), APPLY_XML_FAILD); //sn: {error}; sn_status:2 申请xml取得失败
        }
        return pomXml;
    }

    /**
     * 流转
     * @param copyData
     * @return
     */
    public String getXmlWLLZ(CopyrightManager copyData) {
        String pomXml = "";
        try {
            pomXml = "<?xml version=\"1.0\" encoding=\"GB2312\"?><Case xmlns=\"http://FUJIAN/WSSP/NEWNEXT\">";
            pomXml=pomXml+"<RowGuid>"+UUID.randomUUID().toString()+"</RowGuid>"+
                    "<SN>"+copyData.getSnCode()+"</SN>"+
                    "<DeclareTime>" + copyData.getCertificateCreateTime().format(dfTime) + "</DeclareTime>" +
                    "<FlowInfo>" +
                    "<finishnode caption=\"受理\">" +
                    "<transactor>郑开辟</transactor>" +
                    "<StartTime>" + copyData.getCertificateCreateTime().format(dfTime) + "</StartTime>" +
                    "<OpinionType>1</OpinionType>" +
                    "<opinion>通过</opinion>" +
                    "</finishnode>" +
                    "</FlowInfo>" +
                    "</Case>";
            updateManagerSNStatus(copyData, LZ_ING); //sn: {sncode}; sn_status:7 流转中
        } catch (Exception e) {
            logger.error("error: ", e);
            updateManagerSNStatus(copyData, LX_XML_FAILD); //sn: {sncode}; sn_status:6 流转失败
        }
        return pomXml;
    }

    /**
     * 办结
     * @param copyData
     * @return
     */
    public String getXmlWLBJ(CopyrightManager copyData) {
        String pomXml = "";
        String attrCode = "";
        String attrName = "";
        try {
            pomXml = "<?xml version=\"1.0\" encoding=\"GB2312\"?><Case xmlns=\"http://FUJIAN/WSSP/NEWFINISH\">";
            pomXml=pomXml+"<RowGuid>"+UUID.randomUUID().toString()+"</RowGuid>"+
                    "<SN>"+copyData.getSnCode()+"</SN>"+
                    "<DeclareTime>"+copyData.getCertificateCreateTime().format(dfTime)+"</DeclareTime>"+
                    "<type>Y</type>"+
                    "<reason>证书已生成</reason>"+
                    "<transactor>"+copyData.getSubmitter().getCopyrightName()+"</transactor>"+
                    "<DeliveryResult>N</DeliveryResult>"+
                    "<Attrs><Attr>"+
                    "<Name>证书</Name>"+
                    "<FileName>OwnerCertificate.jpg</FileName>"+
                    "<Content>"+DocumentHandlerUtil.getImageStr(copyData.getCertificateUrl())+"</Content>"+
                    "</Attr></Attrs>"+
                    "<ProjInfo>"+
                        "<ProjectName>"+copyData.getProductionName()+"</ProjectName>"+
                        "<Proposer>"+
                            "<ApplyType>0</ApplyType>"+
                            "<Sex>男</Sex>"+
                            "<Name>"+copyData.getSubmitter().getCopyrightName()+"</Name>"+
                            "<IDType>"+copyData.getOwnerList().get(0).getIDType()+"</IDType>"+
                            "<ID>"+copyData.getOwnerList().get(0).getCopyIdCard()+"</ID>";

            if (copyData.getOwnerList().get(0).getCopyCategory()!=135)
                pomXml=pomXml+"<UnitType>"+copyData.getOwnerList().get(0).getUnitType()+"</UnitType>"+
                        // <!-- 法人代表姓名，仅申报种类为法人填写且是【必填】必填项 -->
                        "<UnitLealPerson>"+copyData.getSubmitter().getCopyrightName()+"</UnitLealPerson>";

            String addrStr = copyData.getSubmitter().getCopyrightAddress();
            if (StringUtils.isEmpty(addrStr))
              addrStr = copyData.getOwnerList().get(0).getCopyAreaNamesStr();
            pomXml=pomXml+"<MobilePhone>"+copyData.getSubmitter().getCopyrightPhone()+"</MobilePhone>"+
                            "<Addr>"+addrStr+"</Addr>"+
                            "<OperatorName>郑开辟</OperatorName>"+
                            "<OperatorSex>男</OperatorSex>"+
                            "<OperatorCertificateType>SF</OperatorCertificateType>"+
                            "<OperatorCertificateNumber>350322196907140012</OperatorCertificateNumber>"+
                            "<OperatorMobilePhone>13305913533</OperatorMobilePhone>"+
                        "</Proposer>";

            // "<!--用户提交的附件列表（即申报材料）-->"+
            if (copyData.getOwnerList().get(0).getCopyCertificateZM()!=null && copyData.getOwnerList().get(0).getCopyCertificateZM().getId()!=null) {
                attrCode = "003591504GF05462_08";
                attrName = "著作权人和作者身份证明文件";
                pomXml=pomXml+"<Attrs>" +
                        "<Attr>" +
                        "<Mode>4</Mode>" +
                        // <!--附件编码 预留 -->
                        "<Code>" + attrCode +"</Code>" +
                        // <!-- 附件序号，如该审批事项有20个附件，则序号依次为1～20，顺序以发布的顺序为准。当mode=”1”时有效。预留，可为空 -->
                        "<SeqNo>1</SeqNo>" +
                        // "<!--若有附件则必填，审批事项定义中定义的附件名称 -->"+
                        "<Name>" + attrName +"</Name>" +
                        // "<FileName></FileName>" +
                        //"<!--若有附件则必填，附件内容使用base64编码-->"+
                        // "<Content></Content>" +
                        // <!--是否来源于数据共享为Y时来源于数据共享，N或者空为否 -->
                        "<Datashare>Y</Datashare>" +
                        // <!-- 证照编号，当mode=”4”时使用 -->
                        "<certificateNumber>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateNumber() + "</certificateNumber>" +
                        // <!-- 证照名称，当mode=”4”时使用 -->
                        "<certificateName>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateName() + "</certificateName>" +
                        // <!-- 证照类型名称，当mode=”4”时使用 -->
                        "<certificateTypeName>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateTypeName() + "</certificateTypeName>" +
                        // <!-- 电子证照标识，当mode=”4”时使用 -->
                        "<certificateIdentifier>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateIdentifier() + "</certificateIdentifier>" +
                        // <!-- 证照类型代码，当mode=”4”时使用 -->
                        "<certificateTypeCode>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateTypeCode() + "</certificateTypeCode>" +
                        // <!-- 持证者代码，当mode=”4”时使用 -->
                        "<certificateHolderCode>" + copyData.getOwnerList().get(0).getCopyCertificateZM().getCertificateHolderCode() + "</certificateHolderCode>" +
                        "<AttrFormInfo>" +
                            "<BaseInfo>" +
                                "<Item name=\"sTitle\">电子证照</Item>" +
                                "<Item name=\"KeyWord\">电子证照</Item>" +
                                "<Item name=\"文件名\">电子证照</Item>" +
                            "</BaseInfo>" +
                        "</AttrFormInfo>" +
                        "</Attr>" +
                        "</Attrs>";
            } else if (copyData.getOwnerList().get(0).getCopyCertificate() != CopyrightOwner.CopyCertificateValue.CODE) {// 非统一信用代码、身份证、居住证（港澳台通行证、护照）
                pomXml=pomXml+"<Attrs>" +
                        "<Attr>" +
                        "<Mode>1</Mode>" +
                        "<Code>" + copyData.getAuthorIdCar().getId() + "</Code>" +
                        "<SeqNo>1</SeqNo>" +
                        // "<!--若有附件则必填，审批事项定义中定义的附件名称 -->"+
                        "<Name>" + copyData.getAuthorIdCar().getWorkName() + "</Name>" +
                        "<FileName>" + copyData.getAuthorIdCar().getWorkName() + "()</FileName>" +
                        //"<!--若有附件则必填，附件内容使用base64编码-->"+
                        "<Content>" + DocumentHandlerUtil.getImageStr(copyData.getAuthorIdCar().getWorkUrl()) + "</Content>" +
                        "</Attr>" +
                        "</Attrs>";
            }

            pomXml = pomXml + "</ProjInfo>" +
                    "<IsExistLicence>0</IsExistLicence>" +
                    "</Case>";
            updateManagerSNStatus(copyData, BJ_ING);  // sn: "sncode"; sn_status:11 办结xml取得中
        } catch (Exception e) {
            logger.error("error: ", e);
            updateManagerSNStatus(copyData, BJ_XML_FAILD); // sn: "sncode"; sn_status:11 办结xml取得失败
        }
        return pomXml;
    }

    public void updateManagerSN(CopyrightManager copyrightManager, String snCode, Integer sn_status) {
        if (StringUtils.isEmpty(snCode))
            return;
        copyrightManager.setSnCode(snCode);
        copyrightManager.setSnStatus(sn_status);
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
    }

    public void updateManagerSNStatus(CopyrightManager copyrightManager, Integer sn_status) {
        copyrightManager.setSnStatus(sn_status);
        copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
    }

    public long countByTYSJM(String projectId) {
        return copyrightManagerService.count(copyrightManager.projectId.eq(projectId));
    }
}
