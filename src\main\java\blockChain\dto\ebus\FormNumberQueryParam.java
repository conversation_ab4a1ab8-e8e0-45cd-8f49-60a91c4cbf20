package blockChain.dto.ebus;

import blockChain.entities.CopyrightOwner;
import blockChain.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 表单请求参数
 */
@Data
public class FormNumberQueryParam {

    @NotEmpty
    @ApiModelProperty("统一收件码")
    private String projectId;

    @NotEmpty
    @ApiModelProperty("用户类别：自然人135 ")
    private String userType;

    public String getUserType() {
        if (!StringUtils.isEmpty(userType) && Integer.valueOf(userType) == CopyrightOwner.CopyCategoryValue.PEOPLE)
            return "1";
        return "2";
    }

    @NotEmpty
    @ApiModelProperty("姓名/名称")
    private String name;

    @NotEmpty
    @ApiModelProperty("证件号码")
    private String certificateNumber;

    @ApiModelProperty("证件类别")
    private String idCardType;

    /**
     * 111：身份证 414：普通护照 516：港澳居民往来内地通行证 511：台湾居民往来内地通行证
     */
    public String getIdCardType() {
        return toIdCardType(Integer.valueOf(idCardType));
    }

    @ApiModelProperty("办理申请人员的证件号码--自定义")
    @JsonIgnore
    private String comm_userCheckIdCardType;

    public String getComm_userCheckIdCardType() {
        if (!StringUtils.isEmpty(comm_userCheckIdCardType))
            return toIdCardType(Integer.valueOf(comm_userCheckIdCardType));
        return "";
    }

    @ApiModelProperty("办理申请人员的证件号码")
    @JsonIgnore
    private String comm_userCheckIdCard;

    @ApiModelProperty("办理申请人员的姓名")
    @JsonIgnore
    private String comm_userCheckName;

    @ApiModelProperty("办理申请人员凭证-后台获取")
    @JsonIgnore
    private String comm_userVerifyCode;

    @ApiModelProperty("授权类型-后台获取 1-核验本人授权他人服务 2-核验本人授权企业服务 3-核验企业授权经办人服务")
    @JsonIgnore
    private String authType;

    @ApiModelProperty("授权凭证码-后台获取")
    @JsonIgnore
    private String authCode;

    @JsonIgnore
    boolean needAuth = true;

    public String toIdCardType(Integer idCardType) {
        if (idCardType == null)
            return "";
        switch (idCardType) {
            case CopyrightOwner.CopyCertificateValue.IDCARD:
            case CopyrightOwner.CopyCertificateValue.RESIDENT:
                return "111";
            case CopyrightOwner.CopyCertificateValue.PASSPORT:
                return "414";
            case CopyrightOwner.CopyCertificateValue.GATTXZ:
                return "516";
            case CopyrightOwner.CopyCertificateValue.TWTXZ:
                return "511";
            default:
                return "";
        }
    }
}

