package blockChain.utils;

import blockChain.config.SpringConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.conn.params.ConnRoutePNames;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.FileEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Slf4j
public class HttpUtil {

	private static final boolean useProxy = false;//是否使用代理
	private static final String proxyHost = "*************";//代理地址
	private static final int proxyPort = 7777;//代理端口
	private static final PoolingHttpClientConnectionManager cm;//httpClient连接池，用于复用连接
	private static final int timeout = 60000;//超时时间


	static {
		cm = new PoolingHttpClientConnectionManager();
		cm.setMaxTotal(400);//总连接数
		cm.setDefaultMaxPerRoute(200);//单host最大连接数
		cm.setValidateAfterInactivity(15000);//httpClient休眠检查，休眠时间超过15000ms就检查连接是否可用
	}

	/**
	 * 无参数GET请求
	 *
	 * @param url
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String requestGet(String url) throws IOException {
		return requestString(url, useProxy, true, null);
	}

	/**
	 * 有参数GET请求
	 *
	 * @param url
	 * @param map
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String requestGet(String url, Map<String, Object> map) throws IOException {
		HttpEntity entity = null;
		try {
			entity = formData(map);
		} catch (UnsupportedEncodingException e) {
			log.error("error", e);
		}
		return requestString(url, useProxy, true, entity);
	}

  /**
   * POST参数
   * @param url
   * @param params
   * @return
   */
  public static String sendPostParams(String url,Map<String,Object> params){
    DefaultHttpClient httpclient = new DefaultHttpClient(
      new ThreadSafeClientConnManager());
    HttpPost httpost = new HttpPost(url);
    BasicResponseHandler responseHandler = new BasicResponseHandler();
    // 添加参数
    List<NameValuePair> nvps = new ArrayList<NameValuePair>();
    if (params != null && params.keySet().size() > 0) {
      Iterator iterator = params.entrySet().iterator();
      while (iterator.hasNext()) {
        Map.Entry<String,Object> entry = (Entry<String,Object>) iterator.next();
        nvps.add(new BasicNameValuePair((String) entry.getKey(),
          (String) entry.getValue()));
      }
    }
    httpost.setEntity(new UrlEncodedFormEntity(nvps, Consts.UTF_8));
    try {
      String result =  httpclient.execute(httpost,responseHandler).toString();
      httpclient.getConnectionManager().shutdown();
      return result;
    } catch (ClientProtocolException e) {
      e.printStackTrace();
    } catch (IOException e) {
      e.printStackTrace();
    }
    return null;
  }

  /**
   * POST参数
   * @param url
   * @param params
   * @return
   */
  public static String sendPostParams(String url,Map<String,Object> params,String proxyIP,Integer proxyPort){
    DefaultHttpClient httpclient = new DefaultHttpClient(
      new ThreadSafeClientConnManager());
    HttpPost httpost = new HttpPost(url);
    BasicResponseHandler responseHandler = new BasicResponseHandler();
    // 添加参数
    List<NameValuePair> nvps = new ArrayList<NameValuePair>();
    if (params != null && params.keySet().size() > 0) {
      Iterator iterator = params.entrySet().iterator();
      while (iterator.hasNext()) {
        Map.Entry<String,Object> entry = (Entry<String,Object>) iterator.next();
        nvps.add(new BasicNameValuePair((String) entry.getKey(),
          (String) entry.getValue()));
      }
    }
    httpost.setEntity(new UrlEncodedFormEntity(nvps, Consts.UTF_8));
    if(proxyIP!=null && proxyPort!=null && !proxyIP.equals("none") && proxyPort!=0) {
      HttpHost proxy = new HttpHost(proxyIP, proxyPort);
      httpclient.getParams().setParameter(ConnRoutePNames.DEFAULT_PROXY, proxy);
    }
    try {
      String result =  httpclient.execute(httpost,responseHandler).toString();
      httpclient.getConnectionManager().shutdown();
      return result;
    } catch (ClientProtocolException e) {
      e.printStackTrace();
    } catch (IOException e) {
      e.printStackTrace();
    }
    return null;
  }

	/**
	 * 有参数GET请求
	 *
	 * @param url
	 * @param map
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String requestPost(String url, Map<String, Object> map) throws IOException {
		HttpEntity entity = null;
		try {
			entity = formData(map);
		} catch (UnsupportedEncodingException e) {
			log.error("error", e);
		}
		return requestString(url, useProxy, false, entity);
	}

	/**
	 * JSON格式参数POST请求
	 *
	 * @param url
	 * @param json
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String requestPost(String url, String json) throws IOException {
		HttpEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
		return requestString(url, useProxy, false, entity);
	}

	/**
	 * 文件上传，无参数POST请求
	 *
	 * @param url
	 * @param file
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String requestPost(String url, File file) throws IOException {
		HttpEntity entity = new FileEntity(file);
		return requestString(url, useProxy, false, entity);
	}

	/**
	 * http请求，返回值为字符串
	 *
	 * @param url
	 * @param useProxy
	 * @param isGet
	 * @param entity
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String requestString(String url, boolean useProxy, boolean isGet, HttpEntity entity) throws IOException {
		return request(url, useProxy, isGet, entity, new StringResponseHandler());
	}

	/**
	 * 文件下载
	 *
	 * @param url
	 * @param filepath 文件父目录路径
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public static File requestFile(String url, String filepath) throws IOException {
		return requestFile(url, filepath, useProxy, true, null);
	}

	public static File requestFile(String url, String filepath, Map<String, Object> map) throws IOException {
		HttpEntity entity = null;
		try {
			entity = formData(map);
		} catch (UnsupportedEncodingException e) {
			log.error("error", e);
		}
		return requestFile(url, filepath, useProxy, false, entity);
	}


	public static File requestFile(String url, String filepath, boolean useProxy, boolean isGet, HttpEntity entity) throws IOException {
		return request(url, useProxy, isGet, entity, FileResponseHandler.newInstance(filepath));
	}

	public static <T> T request(String url, boolean useProxy, boolean isGet, HttpEntity entity, ResponseHandler<T> handler) throws IOException {
		CloseableHttpClient client = getCloseableHttpClient();
		Builder builder = RequestConfig.custom();
		if (useProxy) {
			HttpHost proxy = new HttpHost(proxyHost, proxyPort);
			builder.setProxy(proxy);
		}
		RequestConfig config = builder.setConnectionRequestTimeout(timeout).setSocketTimeout(timeout).setConnectTimeout(timeout).build();
		if (isGet) {
			if (entity != null) {
				try {
					String params = EntityUtils.toString(entity, "UTF-8");
					url = url + '?' + params;
				} catch (Exception e) {
					log.error("error", e);
				}
			}
			HttpGet method = new HttpGet(url);

			return request(client, method, config, handler);
		} else {
			HttpPost method = new HttpPost(url);
			if (entity != null) {
				method.setEntity(entity);
			}

			return request(client, method, config, handler);
		}
	}

	private static <T> T request(CloseableHttpClient client, HttpRequestBase method, RequestConfig config, ResponseHandler<T> handler) throws IOException {
		method.setConfig(config);
		T result;
		try {
			result = client.execute(method, handler);
		} finally {
			if (client != null) {
				client.close();
			}
		}
		return result;
	}

	private static CloseableHttpClient getCloseableHttpClient() {
		return HttpClients.custom().setConnectionManager(cm).setConnectionManagerShared(true).build();
	}

	private static SSLContext getSSLContext(String keyStorePath, String password) throws KeyManagementException, NoSuchAlgorithmException, KeyStoreException, CertificateException, IOException {
		File keyStoreFile = new File(keyStorePath);
		return SSLContexts.custom().loadTrustMaterial(keyStoreFile, password.toCharArray(), new TrustSelfSignedStrategy()).build();
	}

	/**
	 * map键值对转化为httpEntity
	 *
	 * @param map
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static HttpEntity formData(Map<String, Object> map) throws UnsupportedEncodingException {
		List<NameValuePair> list = new ArrayList<NameValuePair>(map.size());
		if (map != null && !map.isEmpty()) {
			Iterator<Entry<String, Object>> it = map.entrySet().iterator();
			while (it.hasNext()) {
				Entry<String, Object> next = it.next();
				String name = next.getKey();
				String value = next.getValue().toString();
				list.add(new BasicNameValuePair(name, value));
			}
		}
		return new UrlEncodedFormEntity(list, "UTF-8");
	}


  /**
	 * 请求字符串处理类
	 *
	 * <AUTHOR>
	 */
	private static class StringResponseHandler implements ResponseHandler<String> {

		@Override
		public String handleResponse(HttpResponse response)
				throws IOException {
			HttpEntity entity = response.getEntity();
			return EntityUtils.toString(entity, "UTF-8");
		}
	}

	/**
	 * 请求文件处理类
	 *
	 * <AUTHOR>
	 */
	private static class FileResponseHandler implements ResponseHandler<File> {

		private File file;

		private FileResponseHandler() {
		}

		public static FileResponseHandler newInstance(String path) {
			FileResponseHandler handler = new FileResponseHandler();
			handler.file = new File(path);
			if (!handler.file.exists()) {
				handler.file.mkdirs();
			}
			return handler;
		}

		@Override
		public File handleResponse(HttpResponse response)
				throws IOException {
			int status = response.getStatusLine().getStatusCode();
			System.out.println("status : " + status);

			Header[] allHeaders = response.getAllHeaders();
			for (Header header : allHeaders) {
				System.out.println(header.getName() + " : " + header.getValue());
			}

			Header[] headers = response.getHeaders("Content-Disposition");

			String fileName;
			if (headers.length > 0) {
				String headerValue = headers[0].getValue();
				String[] split = headerValue.split("[;|=]");
				if (split.length >= 3 && split[2] != null && split[2].length() > 0) {
					fileName = split[2];
				} else {
					fileName = Long.toHexString(System.currentTimeMillis());
				}
			} else {
				fileName = Long.toHexString(System.currentTimeMillis());
			}
			file = new File(file, fileName);
			if (!file.exists()) {
				file.createNewFile();
			}
			try {
				InputStream is = response.getEntity().getContent();
				FileOutputStream fout = new FileOutputStream(file);
				IOUtils.copyLarge(is, fout);
				fout.flush();
			} catch (Exception e) {
				e.getStackTrace();
			}
			return this.file;
		}
	}
}
