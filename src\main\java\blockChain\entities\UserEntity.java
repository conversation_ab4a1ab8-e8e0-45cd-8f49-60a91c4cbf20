package blockChain.entities;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tb_user")
@Accessors(chain = true)
@Where(clause = "deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class UserEntity {

  /**
   * 主键
   */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer userId;

  /**
   * 用户名称
   */
  private String userName;
  /**
   * 用户密码
   */
  private String password;

  /**
   * 用户状态
   */
  private Integer userStatus;

  /**
   * 用户真实姓名
   */
  private String realName;

  /**
   * 用户性别
   */
  private Integer sex;

  /**
   * 身份证号码
   */
  private String cardId;

  /**
   * 身份证图片---废弃
   */
  private String cardImgf;

    /**
     * 身份证类型(用户类型)：1个人，2法人
     */
    private Integer identityKind;

    /**
     * 证件类型
     */
    private Integer cardType;

    /**
     * 法人身份证：新版加密
     */
    private String legalPersionIdcard;

    /**
     * 法定代表人：新版加密
     */
    private String legalPersionName;

    /**
     * 法人类型(企业法人: C01 社会组织法人: C02 事业单位法人 C03 个体工商户:C04 )
     */
    private String legalPersionType;

    /**
     * 法人类型值
     */
    public interface LegalPersionTypeValue {
        String COMPANYPEOPLE = "C01";  //企业法人
        String SOCIALORG = "C02";  //社会组织法人
        String INSTITUTION = "C03";  //事业单位法人
        String INDIVIDUAL = "C04";  //个体工商户
    }

    /**
     * 经办人身份证
     */
    private String jbrCardId;

    /**
     * 经办人姓名
     */
    private String jbrName;
    /**
     * 经办人证件类型
     */
    private Integer jbrCardType;

    /**
     * 代理公司名称
     */
    private String agencyCompanyName;

    /**
     * 手机号码
     */
    private String phoneNum;

  /**
   * 邮箱
   */
  private String email;

  /**
   * 地址
   */
  private String address;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 随机激活码
     */
    private String randKey;

    /**
     * 随机码过期时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime randKeyExpireTime;

    /**
     * 固定电话
     */
    private String fixedPhoneNum;

    /**
     * 所在国家
     */
    private String countryName;

  /**
   * 所在省
   */
  private String provinceName;

  /**
   * 所在市
   */
  private String cityName;

    /**
     * 所在区
     */
    private String countyName;

    private String uuid;

    private String homePageKey; //首页设置参数

    /**
     * 上次提交作品时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastCommitTime;//上次提交到初审时间

    /**
     * 福建省政务服务总线token
     */
    private String accessToken;

    /**
     * 总线token过期时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime accessTokenExpireTime;//上次提交到初审时间

    /**
     * 登录 accessToken
     */
    private String oauthAccessToken;

    /**
     * 登录 refreshToken
     */
    private String oauthRefreshToken;

    /**
     * 登录 授权令牌的有效期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime oauthAccessTokenExpireTime;

    /**
     * 省可见级别
     */
    @ManyToOne
    @JoinColumn(name = "visible_level_province_dict_id")
    private Digital visibleLevelProvince;
    /**
     * 市可见级别
     */
    @ManyToOne
    @JoinColumn(name = "visible_level_city_dict_id")
    private Digital visibleLevelCity;
    /**
     * 区可见级别
     */
    @ManyToOne
    @JoinColumn(name = "visible_level_county_dict_id")
    private Digital visibleLevelCounty;

  /**
   * 注册日期
   */
  @CreatedDate
  private LocalDateTime registerTime;

  /**
   * 是否删除
   */
  private Boolean deleted;

  /**
   * 是否代理人
   */
  private Integer isProxyer;

  /**
   * 登陆人ip
   */
  private String ip;

  /**
   * 用户角色
   */
  @JoinColumn(name = "role_id")
  @ManyToOne(fetch = FetchType.LAZY)
  private RoleEntity role;

  @Transient
  private List<String> personalArea = null;
  private String personalAreaStr;

  public List<String> getPersonalArea() {
    if (this.personalArea == null && this.personalAreaStr != null && !this.personalAreaStr.equals("")) {
      this.personalArea = new ArrayList<>(Arrays.asList(this.personalAreaStr.split(",")));
    }
    return this.personalArea;
  }

  public void setPersonalArea(List<String> personalArea) {
    this.personalArea = personalArea;
    if (personalArea != null) {
      String str = "";
      for (int i = 0; i < personalArea.size(); i++) {
        if (i == 0) {
          str = str + personalArea.get(i);
        } else {
          str = str + "," + personalArea.get(i);
        }
      }
      this.personalAreaStr = str;
    }
  }
}
