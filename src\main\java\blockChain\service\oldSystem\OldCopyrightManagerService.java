package blockChain.service.oldSystem;

import blockChain.entities.oldSystem.OldAuthor;
import blockChain.entities.oldSystem.OldCopyrightManager;
import blockChain.repository.oldSystem.OldAuthorRepository;
import blockChain.repository.oldSystem.OldCopyrightManagerRepository;
import blockChain.service.BaseService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9 12:08
 */
@Service
@AllArgsConstructor
public class OldCopyrightManagerService implements BaseService<OldCopyrightManagerRepository, OldCopyrightManager, Long> {
  private OldCopyrightManagerRepository repository;

  @Override
  public OldCopyrightManagerRepository getRepository() {
    return repository;
  }

  public Long selectAll(){
    return repository.selectAll();
  }

}
