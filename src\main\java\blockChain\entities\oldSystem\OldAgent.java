package blockChain.entities.oldSystem;

import blockChain.entities.CopyrightManager;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="old_agent")
public class OldAgent {
	// 旧系统数据迁移用实体类，迁移后删除
	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer agent_id;
  private String agentBook;// 委托事项
  private String agent_descript;// 存委托事项路径
  private String agent_descript1;// 存委托事项真实路径
  private String agent_name;// 代理人姓名
  private String agent_contactPerson;// 联系人
  private String agent_telephone;// 电话号码
  private String agent_mobile;// 手机
  private String agent_address;// 地址及
  private String agent_zipCode;// 邮编
  private String agent_email;// E-mail
  private String agent_fax;// 传真

  private Long copyrightmanagerId;//在线填报表的ID
}
