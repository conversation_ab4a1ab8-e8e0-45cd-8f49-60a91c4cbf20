package blockChain.repository;

import blockChain.entities.TaskEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2023/01/03
 */
@Repository
public interface TaskRepository extends JpaRepository<TaskEntity, Integer>, QuerydslPredicateExecutor<TaskEntity> {
}
