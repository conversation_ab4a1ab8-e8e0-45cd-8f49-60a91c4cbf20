package blockChain.dto;

import lombok.Data;

@Data
public class AgentDto {
    private Long id;
    private String agentBook;// 委托事项
    private String agent_descript;// 存委托事项路径
    private String agent_descript1;// 存委托事项真实路径
    private String agentName;// 代理人姓名
    private String agentOwner;// 联系人
    private String agentTelephone;// 电话号码
    private String agentPhone;// 手机
    private String agentAddress;// 地址及
    private String agentCode;// 邮编
    private String agentEmail;// E-mail
    private String agentFax;// 传真

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAgentBook() {
        return agentBook;
    }

    public void setAgentBook(String agentBook) {
        this.agentBook = agentBook;
    }

    public String getAgent_descript() {
        return agent_descript;
    }

    public void setAgent_descript(String agent_descript) {
        this.agent_descript = agent_descript;
    }

    public String getAgent_descript1() {
        return agent_descript1;
    }

    public void setAgent_descript1(String agent_descript1) {
        this.agent_descript1 = agent_descript1;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentOwner() {
        return agentOwner;
    }

    public void setAgentOwner(String agentOwner) {
        this.agentOwner = agentOwner;
    }

    public String getAgentTelephone() {
        return agentTelephone;
    }

    public void setAgentTelephone(String agentTelephone) {
        this.agentTelephone = agentTelephone;
    }

    public String getAgentPhone() {
        return agentPhone;
    }

    public void setAgentPhone(String agentPhone) {
        this.agentPhone = agentPhone;
    }

    public String getAgentAddress() {
        return agentAddress;
    }

    public void setAgentAddress(String agentAddress) {
        this.agentAddress = agentAddress;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentEmail() {
        return agentEmail;
    }

    public void setAgentEmail(String agentEmail) {
        this.agentEmail = agentEmail;
    }

    public String getAgentFax() {
        return agentFax;
    }

    public void setAgentFax(String agentFax) {
        this.agentFax = agentFax;
    }
}
