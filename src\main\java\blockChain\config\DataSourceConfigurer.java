/*
package blockChain.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;

@Configuration
public class DataSourceConfigurer {

  */
/**
   * 第一种方法
   * @return
   *//*

  @Primary
  @Bean(name = "primaryDataSourceProperties")
  @ConfigurationProperties(prefix = "spring.datasource")
  public DataSourceProperties primaryDataSourceProperties() {
    return new DataSourceProperties();
  }
  @Primary
  @Bean(name = "primaryDataSource")
  @ConfigurationProperties("spring.datasource")
  public HikariDataSource primaryDataSource(@Qualifier("primaryDataSourceProperties") DataSourceProperties properties) {
    HikariDataSource hikariDataSource = properties.initializeDataSourceBuilder().type(HikariDataSource.class)
      .build();
    return hikariDataSource;
  }

  */
/*@Primary
  @Bean(name = "primarySqlSessionFactory")
  public SqlSessionFactory primarySqlSessionFactory(@Qualifier("primaryDataSource") DataSource dataSource) throws Exception {
    SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
    bean.setDataSource(dataSource);
    bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/slave/*,/*.xml"));
    //添加PageHelper插件
    Interceptor interceptor = new PageInterceptor();
    Properties properties = new Properties();
    //数据库
    properties.setProperty("helperDialect", "mysql");
    //是否将参数offset作为PageNum使用
    properties.setProperty("offsetAsPageNum", "true");
    //是否进行count查询
    properties.setProperty("rowBoundsWithCount", "true");
    //是否分页合理化
    properties.setProperty("reasonable", "true");

    interceptor.setProperties(properties);
    bean.setPlugins(new Interceptor[] {interceptor});
    return bean.getObject();
  }*//*


  */
/*@Primary
  @Bean(name="primaryTransactionManager")
  public DataSourceTransactionManager primaryTransactionManager(@Qualifier("primaryDataSource") DataSource dataSource){
    DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager(dataSource);
    return dataSourceTransactionManager;
  }*//*


  */
/**
   * 第二种方法
   * @return
   *//*

  @Bean(name = "secondaryDataSourceProperties")
  @ConfigurationProperties(prefix = "spring.datasource.secondary")
  public DataSourceProperties secondaryDataSourceProperties() {
    return new DataSourceProperties();
  }

  @Bean(name = "secondaryDataSource")
  @ConfigurationProperties("spring.datasource.secondary")
  public HikariDataSource secondaryDataSource(@Qualifier("secondaryDataSourceProperties") DataSourceProperties properties) {
    HikariDataSource hikariDataSource = properties.initializeDataSourceBuilder().type(HikariDataSource.class)
      .build();
    return hikariDataSource;
  }

  */
/*@Bean(name = "secondarySqlSessionFactory")
  public SqlSessionFactory secondarySqlSessionFactory(@Qualifier("secondaryDataSource") DataSource dataSource) throws Exception {
    *//*
*/
/**
   * @Qualifier：
   *//*
*/
/*
    SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
    bean.setDataSource(dataSource);
    bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/slave/*,/*.xml"));
    //添加PageHelper插件
    Interceptor interceptor = new PageInterceptor();
    Properties properties = new Properties();
    //数据库
    properties.setProperty("helperDialect", "mysql");
    //是否将参数offset作为PageNum使用
    properties.setProperty("offsetAsPageNum", "true");
    //是否进行count查询
    properties.setProperty("rowBoundsWithCount", "true");
    //是否分页合理化
    properties.setProperty("reasonable", "true");

    interceptor.setProperties(properties);
    bean.setPlugins(new Interceptor[] {interceptor});
    return bean.getObject();
  }*//*


  */
/*@Bean(name="secondaryTransactionManager")
  public DataSourceTransactionManager secondaryTransactionManager(@Qualifier("secondaryDataSource") DataSource dataSource){
    DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager(dataSource);
    return dataSourceTransactionManager;
  }*//*

}
*/
