package blockChain.mapper;

import blockChain.bean.QueryParam;
import blockChain.dto.query.CopyrightQueryParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2022/2/03 16:14
 */
@Mapper(config = CommonConfig.class)
public interface QueryParamMapper {
    QueryParamMapper INSTANCE = Mappers.getMapper(QueryParamMapper.class);

    CopyrightQueryParam toCopyrightQueryParam(QueryParam queryParam);
}
