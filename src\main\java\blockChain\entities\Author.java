package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name = "tb_author")
public class Author implements Serializable {
    // 作者
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String authorName;// 作者姓名
    /*private BigInteger copyrightmanagerId;//在线填报表的ID*/
    private String authorIdCard;// 身份证号码
	private int authorCategory;// 用户类别
	private int authorCertificate;// 证件类别
	private String authorAddress;// 地址
	private String authorPhong;// 电话
	private int authorSignature;// 署名情况
	private String authorSignatureName;// 别名
	private int authorClassification;//分别是草稿箱中保存的数据，还是作品提交的数据

	private String authorSignature_String; //署名情况(文字)
	private String authorCategory_String; //类别(文字)
	private String authorCertificate_String; //证件类型(文字)
    /**
     * 是否少数民族
     */
    private byte isMinority;// 是否少数民族 (1是)

    /**
     * 作者电子证照(证件类型为身份证（142）居住证（156）)
     */
    @JoinColumn(name = "certificate_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private CertificateEntity authorCertificateZM; //作者电子证照
    @JoinColumn(name = "authorIdCar_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UploadAttachment authorIdCar; //作者身份证件
    /**
     * 中枢表单用数ID
     */
    private Long formDataId;
    /**
     * 统一收件码
     */
    private String projectId;

    /**
     * 作品id
     */
    private Long copyright_id;

    /**
     * 数据汇聚操作的批次号：汇聚追加
     */
    private String pch;
}
