package blockChain.repository;

import blockChain.dto.StatisticDto;
import blockChain.entities.Author;
import blockChain.entities.CopyrightOwner;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:42
 */
public interface CopyrightOwnerRepositoryCustom {
  List<StatisticDto> getCompanies(PageRequest of, Predicate... predicate);
  List<StatisticDto> getAgent(long limit,Predicate... predicate);
  List<StatisticDto> getOwner(long limit, Predicate... predicate);
  Long countCompanies(Predicate... predicate);

  List<StatisticDto> getProductionTypes(Predicate... predicate);

  List<StatisticDto> getProductionTypesByFujian(Predicate... predicate);

  /**
   * 福建省各个地市数量分布情况
   * @param predicate
   * @return
   */
  List<StatisticDto> getAreaDistributedForFujian(Predicate ... predicate);

  /**
   * 国家发布统计
   * @param predicate
   * @return
   */
  StatisticDto getAreaDistributedCountry(Predicate ... predicate);

  /**
   * 省份发布统计
   * @param predicate
   * @return
   */
  StatisticDto getAreaDistributedProvince(Predicate ... predicate);

  /**
   * 城市分布统计
   * @param predicate
   * @return
   */
  StatisticDto getAreaDistributedCity(Predicate ... predicate);

  /**
   * 获取用户已填过的代理人列表
   * @param predicate
   * @return
   */
  List<CopyrightOwner> getOwnerFilled(Predicate ... predicate);

  long countBy(Predicate ... predicate);

  /**
   * 按月/年统计
   * @param predicate
   * @return
   */
  List<StatisticDto> countListBy(Predicate predicate, Boolean isYear);

  /*List<CopyrightOwner> getByCopyrightId(Long copyrightId);*/

}
