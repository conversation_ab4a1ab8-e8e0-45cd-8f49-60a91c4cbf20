package blockChain.facade.service;

import blockChain.bean.PageResponse;
import blockChain.config.SpringConfig;
import blockChain.controller.exception.cscp.UserNotFondException;
import blockChain.dto.CopyrightManagerDto;
import blockChain.dto.CopyrightRevokeDto;
import blockChain.entities.*;
import blockChain.exception.EntityNotFoundException;
import blockChain.mapper.CopyrightManagerDtoMapper;
import blockChain.repository.CopyrightManagerPredicates;
import blockChain.repository.RevokeRecordRepository;
import blockChain.service.CopyrightManagerService;
import blockChain.service.ProcessProgressService;
import blockChain.service.UploadAttachmentService;
import blockChain.service.UserService;
import blockChain.utils.StringUtils;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Chan
 * @date 2020/4/17 16:31
 */
@Service
@AllArgsConstructor
@Slf4j
public class CopyrightRevokeServiceFacade {
    private CopyrightManagerService copyrightManagerService;
    private CopyrightManagerServiceFacade copyrightManagerServiceFacade;
    private UploadAttachmentService fileService;
    private UserService userService;
    private MessageServiceFacade messageServiceFacade;

    @Autowired
    private UserServiceFacade userServiceFacade;

    @Autowired
    private BackendServiceFacade backendServiceFacade;


    private final RevokeRecordRepository repository;

    private final ProcessProgressService processProgressService;

    private final SpringConfig config;

    @Transactional
    public void revoke(CopyrightRevokeDto dto) {
        Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
        Assert.notNull(currentUserId, "匿名用户不允许访问");
        UserEntity userEntity = userService.findById(currentUserId).orElseThrow(() -> new EntityNotFoundException("未找到该用户"));

        Assert.notNull(dto.getRegistrationNum(), "RegistrationNum is null");
        Assert.notNull(dto.getCancelTheProofFileId(), "CancelTheProofFileId is null");
        CopyrightManager manager = copyrightManagerService.getById(dto.getRegistrationNum());

        if (!userEntity.getUserName().equals(manager.getUserName())) {
            throw new IllegalArgumentException("没有权限操作当前作品");
        }

        UploadAttachment file = fileService.getById(dto.getCancelTheProofFileId());

        CopyrightRevokeDto.InactiveTypeEnum typeEnum = CopyrightRevokeDto.InactiveTypeEnum.convertToInactiveTypeEnum(manager.getInactiveType()).orElse(CopyrightRevokeDto.InactiveTypeEnum.NORMAL);

        if (!CopyrightRevokeDto.InactiveTypeEnum.NORMAL.equals(typeEnum) || // 已经在撤销流程的不能撤销
      CopyrightManager.CERT_CREATED != manager.getStatus_type() || // 未下发的不能撤销
      !CopyrightManager.EnableStatusEnum.ENABLE.equals(manager.getEnable()) // 删除的不能撤销
    ) {
      throw new IllegalArgumentException("当前作品不能申请撤销");
    }

    manager
      .setInactiveType(CopyrightRevokeDto.InactiveTypeEnum.REVOKING.convertToInactiveTypeValue())
      .setCancelTheProofUrl(file.getWorkUrl());

        if (!config.getBackendIsOn()) return;

    String accessToken = backendServiceFacade.getAccessToken(userEntity);
    if (StringUtils.isEmpty(accessToken)) {
      log.error("撤销申请获取token失败");
    }

    String projectId = backendServiceFacade.getProjectId(userEntity, accessToken);
    if(projectId != null){
      // 将数据保存到撤销记录表
      RevokeRecordEntity record = new RevokeRecordEntity();
      record.setAttachmentId(file.getId());
      record.setCreateTime(Instant.now());
      record.setProjectId(projectId);
      record.setRegistrationNum(dto.getRegistrationNum());
      repository.save(record);
      // 激活
      log.info("=============申请撤销(激活开始)=============");
      backendServiceFacade.projectIdActive(userEntity,projectId);
      log.info("=============申请撤销(激活结束)=============");
      // 办件
      try {
        log.info("===========申请撤销开始(办件推送)==========");
        if(!blockChain.utils.StringUtils.isEmpty(projectId)){
          /*String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
          int lastSnCode = processProgressService.getLastSncode(today);
          manager.setSnCode(String.format("fjszK1003830%s%06d", today, ++lastSnCode));*/

          String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
          String str = manager.getRegistrationNum().toString();
          if(str.length() >= 6){
            str = str.substring(str.length() - 6);
          }else {
            str = String.format("%06d", Integer.parseInt(str));
          }
          manager.setSnCode(String.format("fjszK1003830%s%s", today, str));
          copyrightManagerServiceFacade.createCopyright(manager);

          manager.setProjectId(projectId);
          manager.setSyncStatus("I");
            backendServiceFacade.startWorkflow(manager, userEntity, ProcessProgressEntity.Type.PUSH, null, false);
        }
        log.info("===========申请撤销结束(办件推送)==========");
      }catch (Exception e){
        log.debug("办件信息推送失败{}",e.getMessage());
      }
    }
  }

  @Transactional(readOnly = true)
  public PageResponse<CopyrightManagerDto> queryCopyrightRevoke(Pageable pageable, CopyrightRevokeDto dto) {
    Page<CopyrightManager> page = copyrightManagerService.findAll(
      CopyrightManagerPredicates.revokePredicates(
        dto.getInactiveType() != null ? dto.getInactiveType().convertToInactiveTypeValue() : null,
      dto.getWorksNum(), dto.getProductionName(),
      dto.getProductionTypeId(), dto.getRightOwnMode(),
      null, null, null,
      null, dto.getAgentName(), dto.getCopyrightName(),true,null,null,dto.getVisibleLevelCity(),dto.getVisibleLevelCounty(),null
    ), pageable);

    return CopyrightManagerDtoMapper.INSTANCE.toDto(page);
  }

  @Transactional(readOnly = true)
  public Page<CopyrightManager> query(String inactiveType, String worksNum, String productionName, String productionTypeId, Integer rightOwnMode,
                                                 String agentName, String copyrightName, Integer city_level,Integer county_level,Integer page, Integer size) {
    Integer inactiveTypeVal = !StringUtils.isEmpty(inactiveType) ? CopyrightRevokeDto.InactiveTypeEnum.fromValue(inactiveType).convertToInactiveTypeValue() : null;
    Pageable pageable = PageRequest.of(page, size);
    return copyrightManagerService.findAll(
      CopyrightManagerPredicates.revokePredicates(
        inactiveTypeVal,
        worksNum, productionName, productionTypeId, rightOwnMode,
        null, null, null,
        null, agentName, copyrightName,true,null,null,city_level,county_level,null
      ), pageable);
  }

  /**
   * 查询我的撤销申请
   * @param pageable
   * @param dto
   * @return
   */
  @Transactional(readOnly = true)
  public PageResponse<CopyrightManagerDto> queryMineCopyrightRevoke(Pageable pageable, CopyrightRevokeDto dto) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
    Assert.notNull(currentUserId, "匿名用户不允许访问");
    UserEntity userEntity = userService.findById(currentUserId).orElseThrow(() -> new EntityNotFoundException("未找到该用户"));


    Page<CopyrightManager> page = copyrightManagerService.findAll(CopyrightManagerPredicates.revokePredicates(
      dto.getInactiveType() != null ? dto.getInactiveType().convertToInactiveTypeValue() : null,
      dto.getWorksNum(), dto.getProductionName(),
      dto.getProductionTypeId(), dto.getRightOwnMode(),
      null, null, null,
      userEntity.getUserName(), dto.getAgentName(), dto.getCopyrightName(),null,null,null,null,null,true
    ), pageable);

    return CopyrightManagerDtoMapper.INSTANCE.toDto(page);
  }

  /**
   * 发起人取消申请撤销
   * @param dto
   */
  @Transactional()
  public void deleteCopyrightRevoke(CopyrightRevokeDto dto) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(null);
    Assert.notNull(currentUserId, "匿名用户不允许访问");
    UserEntity userEntity = userService.findById(currentUserId).orElseThrow(() -> new EntityNotFoundException("未找到该用户"));

    CopyrightManager manager = copyrightManagerService.getById(dto.getRegistrationNum());

    if(!userEntity.getUserName().equals(manager.getUserName())){
      throw new IllegalArgumentException("没有权限操作当前作品");
    }

    if (CopyrightRevokeDto.InactiveTypeEnum.REVOKED.convertToInactiveTypeValue().equals(manager.getInactiveType()) || // 已经撤销的不能取消撤销
      !CopyrightManager.EnableStatusEnum.ENABLE.equals(manager.getEnable()) // 删除的不能撤销
    ) {
      throw new IllegalArgumentException("当前作品不能取消撤销");
    }

    manager
      .setInactiveType(CopyrightRevokeDto.InactiveTypeEnum.NORMAL.convertToInactiveTypeValue())
      .setCancelTheProofUrl(null);
  }

  /**
   * 处理撤销申请
   * @param handleType
   * @param registrationNums
   * @param rejectReason
   */
  @Transactional()
  public void handleCopyrightRevoke(CopyrightRevokeDto.HandleTypeEnum handleType, List<Long> registrationNums, String rejectReason, Integer uid) {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    UserEntity loginUser = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());
    registrationNums.forEach(id -> {
      CopyrightManager manager = copyrightManagerService.getById(id);
      // 受理
/*      log.info("处理撤销申请---受理开始,manager:{}",manager);
      try {
        backendServiceFacade.startWorkflow(manager,loginUser,ProcessProgressEntity.Type.ACCEPT,null);
      }catch (Exception e){
        log.debug("处理撤销申请受理失败{}",e.getMessage());
      }
      log.info("处理撤销申请---受理结束");*/
      switch (handleType) {
        case REVOKED:
          UserEntity user = userService.getByUserName(manager.getUserName()).orElseThrow(UserNotFondException::new);
          List<Integer> userIds = new ArrayList<>();
          userIds.add(user.getUserId());
          messageServiceFacade.sendMessageBase("作品拒绝撤销通知", rejectReason, false, userIds);
          manager.setInactiveType(CopyrightManager.InactiveTypeValue.NORMAL);
          ProcessRecord processRecord = new ProcessRecord();
          processRecord.setOpType("撤销");
            processRecord.setOpResult("拒绝");
            processRecord.setApproverOpinion(rejectReason);
            Optional<UserEntity> OpUser = userService.findById(uid);
            if (OpUser.isPresent()) {
                processRecord.setOpName(OpUser.get().getRealName());
            } else {
                processRecord.setOpName("系统");
            }
            manager.getFlowRecord().add(processRecord);
            manager.setInactiveType(CopyrightManager.InactiveTypeValue.NORMAL);
            copyrightManagerService.saveOrUpdateCopyright(manager);
            // TODO 接入国家级撤销接口

            if (!config.getBackendIsOn()) break;

            log.info("处理撤销申请拒绝开始(办结+证照开始),manager:{}", manager);
            try {
                List<ProcessProgressEntity> progressEntitiesOne = processProgressService.findBySnCodeAndTypeAndState(manager.getSnCode(), ProcessProgressEntity.Type.PUSH, ProcessProgressEntity.State.FINISHED);
                Optional<ProcessProgressEntity> entityOptional = progressEntitiesOne.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime));
                entityOptional.ifPresent((processProgressEntity) -> {
                    manager.setSnCode(processProgressEntity.getSnCode());
                    manager.setProjectId(processProgressEntity.getProjectId());
                    backendServiceFacade.startWorkflow(manager, loginUser, ProcessProgressEntity.Type.FINISH, "退件", false);
                });
            } catch (Exception e) {
                log.debug("审核通过后退件失败{}", e.getMessage());
            }
            log.info("处理撤销申请拒绝结束(办结+证照开始)");
            break;
          case ACCEPTED:
              List<Long> ids = new ArrayList<>();
              ids.add(id);
              copyrightManagerServiceFacade.disableCopyright(ids, "您的作品已被撤销", uid);

              if (!config.getBackendIsOn()) break;

              log.info("处理撤销申请接受开始(办结+证照开始),manager:{}", manager);
              try {
                  List<ProcessProgressEntity> progressEntitiesOne = processProgressService.findBySnCodeAndTypeAndState(manager.getSnCode(), ProcessProgressEntity.Type.PUSH, ProcessProgressEntity.State.FINISHED);
                  Optional<ProcessProgressEntity> entityOptional = progressEntitiesOne.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime));
                  entityOptional.ifPresent((processProgressEntity) -> {
                      manager.setSnCode(processProgressEntity.getSnCode());
                      manager.setProjectId(processProgressEntity.getProjectId());
                      backendServiceFacade.startWorkflow(manager, loginUser, ProcessProgressEntity.Type.FINISH, "办结", false);
                  });
              } catch (Exception e) {
                  log.debug("审核通过后办结失败{}", e.getMessage());
              }
              log.info("处理撤销申请接受结束(办结+证照结束)");
              break;
      }
    });
  }
}
