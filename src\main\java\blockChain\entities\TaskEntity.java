package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_task")
public class TaskEntity {
	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	private String url;
    @Transient
    private List<String> urls = new ArrayList<>();
	//创建时间
	@CreatedDate
	private LocalDateTime createTime;
	//更新时间
	@LastModifiedDate
	private LocalDateTime updateTime;


  public List<String> getUrl() {
    if (this.url != null) {
      return Arrays.asList(this.url.split(","));
    } else {
      return new ArrayList<>();
    }
  }

  public void setUrl(List<String> urls) {
    this.urls = urls;
    if (urls != null) {
      String str = "";
      for (int i = 0; i < urls.size(); i++) {
        if (i == 0) {
          str = str + urls.get(i);
        } else {
          str = str + "," + urls.get(i);
        }
      }
      this.url = str;
    }
  }
}
