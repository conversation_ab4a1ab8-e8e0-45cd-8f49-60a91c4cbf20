package blockChain.mapper;

import blockChain.dto.WorksReportDto;
import blockChain.entities.WorksReport;
import blockChain.mapper.transform.CopyrightManagerTransform;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR> XXX
 * @date 2019/11/29.
 */
@Mapper(uses={CopyrightManagerTransform.class})
public interface WorksReportDtoMapper {
    WorksReportDtoMapper INSTANCE = Mappers.getMapper(WorksReportDtoMapper.class);

    WorksReportDto entityToDto(WorksReport digital);

    List<WorksReportDto> entityToDto(List<WorksReport> digitals);

    List<WorksReportDto> digitalPageToDto(Page<WorksReport> pageable);
}
