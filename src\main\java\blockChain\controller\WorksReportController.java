
//todaychange
package blockChain.controller;

import blockChain.bean.QueryParam;
import blockChain.config.SpringConfig;
import blockChain.dto.UserDto;
import blockChain.dto.WorksReportDto;
import blockChain.entities.ProcessRecord;
import blockChain.entities.UploadAttachment;
import blockChain.entities.WorksReport;
import blockChain.facade.service.DigitalServiceFacade;
import blockChain.facade.service.UserServiceFacade;
import blockChain.facade.service.WorksReportServiceFacade;
import blockChain.mapper.DigitalDtoMapper;
import blockChain.mapper.WorksReportDtoMapper;
import blockChain.utils.HttpUtil;
import blockChain.utils.ImageUtil;
import blockChain.utils.PictureHttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("举报")
@Slf4j
@RestController
@RequestMapping("worksReport")
@RequiredArgsConstructor
public class WorksReportController {
	@Autowired
    private WorksReportServiceFacade worksReportServiceFacade;
	@Autowired
	private DigitalServiceFacade digitalServiceFacade;
    @Autowired
    private UserServiceFacade userServiceFacade;

    private final SpringConfig config;

	/*@ApiOperation("作品提交")
	@PostMapping("test")
	public void test(){
		UploadAttachment uploadAttachment = uploadAttachmentServiceFacade.getById(Long.parseLong("159"));
		return;
	}*/

	@ApiOperation("举报提交")
	@PostMapping("WorkReport")
	public ResponseEntity<Map<String,Object>> submitData(@Valid @RequestBody WorksReport worksReport) {
		Map<String,Object> result = new HashMap<>();
		if(worksReport.isUpdateFlag()){
			WorksReport getItem = worksReportServiceFacade.getById(worksReport.getId());
			worksReport.setCopyrightManager(getItem.getCopyrightManager());
			worksReport.setCertificateCreateTime(getItem.getCertificateCreateTime());
			worksReport.setCreateTime(getItem.getCreateTime());
		}
		//设置流程状态
		worksReport.setReportStatus(WorksReport.REVIEWING);
		ProcessRecord processRecord = new ProcessRecord();
		processRecord.setOpName(worksReport.getCopyrightManager().getUserName());
		processRecord.setOpType("用户提交");
		processRecord.setOpResult("用户提交成功");
		worksReport.getFlowRecord().add(processRecord);
		worksReportServiceFacade.createWorksReport(worksReport);
		result.put("message","提交成功！");
		return new ResponseEntity<Map<String,Object>>(result,HttpStatus.OK);
	}

	@ApiOperation("举报删除")
	@PostMapping("remove")
	public ResponseEntity<Map<String,Object>> removeCopyright(@Valid @RequestBody QueryParam queryParam){
		Map<String,Object> result = new HashMap<>();
		try {
			WorksReport data = worksReportServiceFacade.getById(queryParam.getLong("id"));
			if (data == null) {
				result.put("message","未找到该举报！");
				return new ResponseEntity<Map<String,Object>>(result,HttpStatus.OK);
			}else{
				worksReportServiceFacade.remove(data);
				result.put("message","删除成功！");
				return new ResponseEntity<Map<String,Object>>(result,HttpStatus.OK);
			}
		}catch (Exception e){
			result.put("message","删除失败！");
			result.put("errorMsg",e.getLocalizedMessage());
			return new ResponseEntity<Map<String,Object>>(result,HttpStatus.BAD_REQUEST);
		}
	}

	@ApiOperation("举报审批")
	@PostMapping("ReportApprove")
	@Transactional(rollbackFor = RuntimeException.class)
	public ResponseEntity<Map<String,Object>> approval(@Valid @RequestBody QueryParam queryParam,HttpServletRequest request) throws RuntimeException {
		Map<String,Object> result = new HashMap<>();
		try {
			worksReportServiceFacade.approval(queryParam,request);
			result.put("message", "操作成功！");
			return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
		}catch (Exception e){
			result.put("message", "操作失败！");
			result.put("errosMsg", e.getLocalizedMessage());
			return new ResponseEntity<Map<String, Object>>(result, HttpStatus.BAD_REQUEST);
		}
	}

	@ApiOperation("获取详情")
	@PostMapping("get")
	public ResponseEntity<WorksReportDto> getCopyrightManager(@Valid @RequestBody QueryParam queryParam){
		//Map<String,Object> result = new HashMap<>();
		WorksReport data = worksReportServiceFacade.getById(queryParam.getLong("id"));
		/*result.put("Data", CopyrightManagerDtoMapper.INSTANCE.entityToDto(data));
		result.put("message","获取作品内容成功！");*/
		return new ResponseEntity<WorksReportDto>(WorksReportDtoMapper.INSTANCE.entityToDto(data),HttpStatus.OK);
	}

	@ApiOperation("作品列表查询")
	@PostMapping("query")
	public ResponseEntity<Map<String,Object>> queryCopyrightManager(@Valid @RequestBody QueryParam queryParam) {
		Map<String, Object> result = new HashMap<>();
		try {
			JSONArray array = queryParam.getJSONArray("reportStatus");
			List<Integer> statusList = new ArrayList<>();
			if(array != null && !array.isEmpty()){
				statusList = JSONObject.parseArray(array.toJSONString(),Integer.class);
			}
			//判断是否为用户查询，若是则只查询当前用户的作品
			String userName = null;
			boolean isWorksReport = queryParam.getBooleanValue("isWorksReport");
			if(isWorksReport){
                UserDto userDto = userServiceFacade.findUserById(queryParam.getInteger("UID"));
                if(userDto!=null){
                    userName = userDto.getUserName();
                }
            }
			Page<WorksReport> worksReportPage = worksReportServiceFacade.query(queryParam.getString("worksNum"),queryParam.getString("productionName"),queryParam.getString("productionTypeId"),
					queryParam.getString("registrationStartDate"),queryParam.getString("registrationEndDate"), queryParam.getString("certificateStartDate"),queryParam.getString("certificateEndDate"),
					statusList,userName,new Long(queryParam.getPage()).intValue()-1,new Long(queryParam.getPageSize()).intValue());
			List<WorksReport> list = worksReportPage.getContent();
			result.put("listData",WorksReportDtoMapper.INSTANCE.entityToDto(list));
			//设置分页项
			result.put("total",worksReportPage.getTotalElements());
			result.put("page",queryParam.getPage());
			result.put("pageSize",queryParam.getPageSize());
			//获取数据字典
			//获取作品类型
			result.put("productionTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82)));
			//获取权利归属方式字典
			result.put("rightOwnModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(115)));
			//获取权利取得方式字典
			result.put("obtainModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(121)));
            //获取作品创作性质字典
			result.put("opusInditekindList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(126)));
            //获取著作权人类别字典
			result.put("manTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(134)));
            //获取著作权人证件类别字典
			result.put("manIdentityTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(141)));
            //获取国家字典
			result.put("CountryList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(157)));
            //获取省份字典
			//result.put("ProvinceList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(393)));
            //获取权力拥有状况字典
			result.put("rightScopeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(429)));
			//获取审批状态
			result.put("statusList",getApprovalType());
			result.put("message", "查询成功！");
		}catch (Exception e){
			result.put("message", "查询失败！");
			result.put("errorCode",e.getLocalizedMessage());
			return new ResponseEntity<Map<String,Object>>(result,HttpStatus.BAD_REQUEST);
		}finally {
			return new ResponseEntity<Map<String,Object>>(result,HttpStatus.OK);
		}
	}

	@ApiOperation("图片校对")
	@PostMapping("pictureCompare")
	public ResponseEntity<Map<String,Object>> test(@Valid @RequestBody QueryParam queryParam,HttpServletRequest request, HttpServletResponse response){
		Map<String,Object> result = new HashMap<>();
		try {
			String contextPath = "File/"+config.getModelRoot();
			Long id = queryParam.getLong("id");
			if(id==null){
				result.put("message","校对失败");
				return new ResponseEntity<Map<String,Object>>(result,HttpStatus.BAD_REQUEST);
			}
			WorksReport worksReport = worksReportServiceFacade.getById(id);
			UploadAttachment reportImg = worksReport.getReportImg();
			if(reportImg==null){
				result.put("message","校对失败");
				return new ResponseEntity<Map<String,Object>>(result,HttpStatus.BAD_REQUEST);
			}
			String picUrl = reportImg.getWorkUrl();
			String CRE_URL = "http://127.0.0.1:18080/SimilarPictureQuery";
			Map<String, Object> param = new HashMap<String, Object>() {{
				put("sourceImage", System.getProperty("user.dir")+ File.separator  + picUrl);
				put("compareDir", System.getProperty("user.dir")+ File.separator + contextPath + "copyright_newupload/");
			}};
			String bc = JSONObject.toJSON(param).toString();
			String result1 = PictureHttpUtil.HttpConcent(bc, CRE_URL, response);
			JSONObject jsonObject = JSON.parseObject(result1);
			result.put("message","校对成功");
			result.put("similarWork",jsonObject);
			return new ResponseEntity<Map<String,Object>>(result,HttpStatus.OK);
		}catch (Exception e){
			result.put("message","校对失败");
			result.put("errorMsg",e.getLocalizedMessage());
			return new ResponseEntity<Map<String,Object>>(result,HttpStatus.BAD_REQUEST);
		}
	}

	private List<Map<String,Object>> getApprovalType(){
		List<Map<String,Object>> result = new ArrayList<Map<String,Object>>(){{
			add(new HashMap<String,Object>(){{
				put("id",WorksReport.REVIEWING);
				put("name","审批中");
			}});
			add(new HashMap<String,Object>(){{
				put("id",WorksReport.MODIFIED);
				put("name","驳回");
			}});
			add(new HashMap<String,Object>(){{
				put("id",WorksReport.PASSED);
				put("name","通过");
			}});
		}};
		return result;
	}

	@ApiOperation("报告获链")
	@PostMapping("getBlockChain")
	public ResponseEntity<Map<String,Object>> getBlockChain(@Valid @RequestBody QueryParam queryParam,HttpServletRequest request){
		Map<String,Object> result = new HashMap<>();
		String postUrl = config.getBlockUrl();
		//String contextPath = "File/";
		Long id = queryParam.getLong("id");
		try{
			WorksReport worksReport = worksReportServiceFacade.getById(id);
			String token = worksReport.getOnBlockChain();
			UploadAttachment file = worksReport.getTortReport();
			//获取区块链上数据
			Map<String,Object> param = new HashMap<>();
			param.put("jsonrpc","2.0");
			param.put("method","eth_getTransactionByHash");
			param.put("params",new ArrayList<Object>(){{
				add(token);
			}});
			param.put("id",1);
			String getParamStr = (new JSONObject(param)).toJSONString();
			String getResultStr = HttpUtil.requestPost(postUrl,getParamStr);
			JSONObject getResult = JSONObject.parseObject(getResultStr);
			if(getResult.containsKey("result")) {
				String input = getResult.getJSONObject("result").getString("input");
				String blockNum = getResult.getJSONObject("result").getString("blockNumber");
				//判断区块链数据是否一致
				String url = worksReport.getTortReport().getWorkUrl();
				/*String imgUrl = request.getSession().getServletContext().getRealPath("/fileModel/"+id) + "/QRCode.jpg";*/
				String fileUrl = System.getProperty("user.dir")+File.separator+url;
                String HexStr = ImageUtil.ImageToHex(fileUrl);
                if(input.equalsIgnoreCase(HexStr)) {
                    //获取证书路径
                    String certUrl = worksReport.getCopyrightManager().getCertificateUrl();
                    result.put("message", "获取报告成功！");
                    result.put("file", file);
                    result.put("blockNumberHex", blockNum);
                    result.put("blockNumber",Integer.parseInt(blockNum.replace("0x",""),16));
                    result.put("certUrl", certUrl);
                    result.put("md5",input);
                    return new ResponseEntity<Map<String, Object>>(result, HttpStatus.OK);
                }else{
                    result.put("message","报告与区块链数据不符！");
                    return new ResponseEntity<Map<String,Object>>(result,HttpStatus.BAD_REQUEST);
                }
			}else{
				result.put("message","获取报告失败！");
				return new ResponseEntity<Map<String,Object>>(result,HttpStatus.BAD_REQUEST);
			}
		}catch (NullPointerException e){
            result.put("message","区块编号正在分配，请稍后......");
            log.info(e.getLocalizedMessage());
            return new ResponseEntity<Map<String,Object>>(result,HttpStatus.BAD_REQUEST);
        }catch (Exception e){
			result.put("message","获取报告失败！");
			log.info(e.getLocalizedMessage());
			return new ResponseEntity<Map<String,Object>>(result,HttpStatus.BAD_REQUEST);
		}
	}

}

