package blockChain.facade.service;

import blockChain.config.ApplicationRuntimeProperties;
import blockChain.config.SpringConfig;
import blockChain.dto.StatementMonthlyDto;
import blockChain.dto.StatisticDto;
import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.dto.query.StatementQueryParam;
import blockChain.entities.Digital;
import blockChain.repository.CopyrightManagerPredicates;
import blockChain.repository.CopyrightOwnerPredicates;
import blockChain.repository.DigitalPredicates;
import blockChain.service.CopyrightManagerService;
import blockChain.service.CopyrightOwnerService;
import blockChain.service.DigitalService;
import blockChain.utils.DecimalFormatUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> Chan
 * @date 2020/4/16 12:02
 */
@Service
@AllArgsConstructor
public class StatementServiceFacade {
  private CopyrightOwnerService ownerService;

  private CopyrightManagerService managerService;

  private ApplicationRuntimeProperties runtimeProperties;

  private DigitalService digitalService;

  private final SpringConfig config;


  @Transactional(readOnly = true)
  public StatementMonthlyDto getStatementForMonthly(StatementQueryParam param) {

    System.out.println("月报统计开始时间:"+ LocalDateTime.now());
    StatementMonthlyDto result = new StatementMonthlyDto()
      .setMonthly(param.getMonthlyOrYear());

    // 不包括无数据的分类
    System.out.println("月报类型统计开始时间:"+ LocalDateTime.now());
    List<StatisticDto> productionTypesTemp = ownerService.getProductionTypeByFujian(CopyrightManagerPredicates.certificateDateBeforeByFujian(param.getFirstMonthly(),param.getLastMonthly(),null));
    List<StatisticDto> productionTypes = new ArrayList<>();
    //设置未查询到的作品为0
    List<Digital> digitals = Lists.newArrayList(digitalService.getDictByCode(runtimeProperties.getProductTypeDigitalDefineCode(),0));
    boolean isIn = false;
    for(Digital digital:digitals){
      isIn = false;
      for(StatisticDto statisticDtos:productionTypesTemp){
        if(digital.getId().equals(Integer.parseInt(statisticDtos.getKey()))){
          isIn = true;
          StatisticDto statisticDto = new StatisticDto();
          statisticDto.setKey(digital.getId().toString());
          statisticDto.setName(digital.getDict_name());
          statisticDto.setAmount(statisticDtos.getAmount());
          productionTypes.add(statisticDto);
          break;
        }
      }
      if(!isIn){
        StatisticDto statisticDto = new StatisticDto();
        statisticDto.setKey(digital.getId().toString());
        statisticDto.setName(digital.getDict_name());
        statisticDto.setAmount(Long.valueOf(0));
        productionTypes.add(statisticDto);
      }
    }

    //按登记量排序
    Collections.sort(productionTypes, (o1, o2) -> (int)(o2.getAmount()-o1.getAmount()));

    System.out.println("月报类地域统计开始时间:"+ LocalDateTime.now());
    System.out.println("月报类地域统计--福建各地区:"+ LocalDateTime.now());
    List<StatisticDto> areaDistributedForOnlyFujianCity = managerService.getAreaDistributedCity(DigitalPredicates.digitalQueryByPid(runtimeProperties.getProvinceFujianDigitalDefinePid(), 0),CopyrightManagerPredicates.certificateDateBefore(param.getFirstMonthly(),param.getLastMonthly()));

    Long fujianAmountDto = 0L;
    for(StatisticDto statisticDto:areaDistributedForOnlyFujianCity){
      fujianAmountDto+=statisticDto.getAmount();
    }
//    StatisticDto fujianAmountDto = ownerService.getAreaDistributedCity(DigitalPredicates.digitalQueryByPid(runtimeProperties.getProvinceFujianDigitalDefinePid(), 0,param.getFirstMonthly(), param.getLastMonthly()));

    System.out.println("月报类统计 全部:"+ LocalDateTime.now());
    long total = managerService.countBy(CopyrightManagerPredicates.certificateDateBeforeByFujian(param.getFirstMonthly(),param.getLastMonthly(),null));
//    StatisticDto areaDistributedForForeign = managerService.getAreaDistributed(DigitalPredicates.digitalQueryByPidAndCodeNotEq(runtimeProperties.getCountryDigitalDefinePid(), 0, runtimeProperties.getCnDigitalDefineCode(),param.getFirstMonthly(), param.getLastMonthly()));
//    areaDistributedForOnlyFujianCity.add(areaDistributedForForeign.setName("国外").setIsleaf(true));

    System.out.println("月报类地域统计--港澳台地区:"+ LocalDateTime.now());
    StatisticDto areaDistributedForGat = managerService.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceGatDigitalDefineCode(), 0,param.getFirstMonthly(), param.getLastMonthly()));
    areaDistributedForOnlyFujianCity.add(areaDistributedForGat.setName("港澳台地区").setIsleaf(true));

    System.out.println("月报类地域统计--其它:"+ LocalDateTime.now());
    StatisticDto areaDistributedForNoFujian = managerService.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceDigitalDefineCode(), 0,param.getFirstMonthly(), param.getLastMonthly()));
    // 其它=所有省份（不含港澳台）-福建
    areaDistributedForOnlyFujianCity.add(areaDistributedForNoFujian.setName("其它").setAmount(areaDistributedForNoFujian.getAmount()-fujianAmountDto).setIsleaf(true));

    System.out.println("月报类地域统计--国外:"+ LocalDateTime.now());
    StatisticDto areaDistributedForForeign = new StatisticDto(999,"国外",0l,true);
    // 国外=全部-所有省份-港澳台
    areaDistributedForOnlyFujianCity.add(areaDistributedForForeign.setAmount(total-fujianAmountDto-areaDistributedForNoFujian.getAmount()-areaDistributedForGat.getAmount()));

    //按登记量排序
    Collections.sort(areaDistributedForOnlyFujianCity,(o1,o2) -> (int)(o2.getAmount()-o1.getAmount()));
    System.out.println("月报类地域统计结束时间:"+ LocalDateTime.now());

    System.out.println("月报类统计 去年同期:"+ LocalDateTime.now());
    long periodLastMonthOrYearTotalSize = managerService.countBy(CopyrightManagerPredicates.certificateDateBeforeByFujian(param.getLastFirstMonthly(),param.getLastLastMonthly(),null));

    Double rate = 0.00;
    if(periodLastMonthOrYearTotalSize!=0) {
      rate = DecimalFormatUtil.format2FixDouble((Double.valueOf(total - periodLastMonthOrYearTotalSize) / periodLastMonthOrYearTotalSize)*100);
    }else{
      rate = 100.00;
    }
    System.out.println("月报类统计结束时间:"+ LocalDateTime.now());
    result
      .setTypes(productionTypes)
      .setTotalSize(total)
      .setRate(rate)
      .setLocationAdditions(areaDistributedForOnlyFujianCity)
      .setPeriodLastMonthOrYearTotalSize(periodLastMonthOrYearTotalSize)
    ;

    return result;
  }

  @Transactional(readOnly = true)
  public StatementMonthlyDto getStatementForYears(StatementQueryParam param) {
    System.out.println("年报类统计开始时间:"+ LocalDateTime.now());
    StatementMonthlyDto result = new StatementMonthlyDto()
      .setMonthly(param.getMonthlyOrYear());

    // 不包括无数据的分类
    List<StatisticDto> productionTypes = ownerService.getProductionTypeByFujian(CopyrightManagerPredicates.certificateDateBeforeByFujian(param.getFirstYear(),param.getLastYear(),null));
    //设置未查询到的作品为0
    List<Digital> digitals = Lists.newArrayList(digitalService.getDictByCode(runtimeProperties.getProductTypeDigitalDefineCode(),0));
    boolean isIn = false;
    for(Digital digital:digitals){
      isIn = false;
      for(StatisticDto statisticDto:productionTypes){
        if(digital.getId().equals(Integer.parseInt(statisticDto.getKey())))
        {
          statisticDto.setKey(digital.getId().toString());
          statisticDto.setName(digital.getDict_name());
          isIn = true;
          break;
        }
      }
      if(!isIn){
        StatisticDto statisticDto = new StatisticDto();
        statisticDto.setKey(digital.getId().toString());
        statisticDto.setName(digital.getDict_name());
        statisticDto.setAmount(Long.valueOf(0));
        productionTypes.add(statisticDto);
      }
    }
    List<StatisticDto> areaDistributedForOnlyFujianCity = managerService.getAreaDistributedCity(DigitalPredicates.digitalQueryByPid(runtimeProperties.getProvinceFujianDigitalDefinePid(), 0),CopyrightManagerPredicates.certificateDateBefore(param.getFirstYear(),param.getLastYear()));

    Long FujianAmount = 0L;
    for(StatisticDto statisticDto:areaDistributedForOnlyFujianCity){
      FujianAmount+=statisticDto.getAmount();
    }
//    StatisticDto fujianAmountDto = ownerService.getAreaDistributedCity(DigitalPredicates.digitalQueryByPid(runtimeProperties.getProvinceFujianDigitalDefinePid(), 0,param.getFirstYear(),param.getLastYear()));
//    StatisticDto areaDistributedForForeign = managerService.getAreaDistributed(DigitalPredicates.digitalQueryByPidAndCodeNotEq(runtimeProperties.getCountryDigitalDefinePid(), 0, runtimeProperties.getCnDigitalDefineCode(),param.getFirstYear(),param.getLastYear()));
//    areaDistributedForOnlyFujianCity.add(areaDistributedForForeign.setName("国外").setIsleaf(true));

    StatisticDto areaDistributedForGat = managerService.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceGatDigitalDefineCode(), 0,param.getFirstYear(),param.getLastYear()));
    areaDistributedForOnlyFujianCity.add(areaDistributedForGat.setName("港澳台地区").setIsleaf(true));

    StatisticDto areaDistributedForNoFujian = managerService.getAreaDistributedProvince(DigitalPredicates.digitalQueryByCode(runtimeProperties.getProvinceDigitalDefineCode(), 0,param.getFirstYear(),param.getLastYear()));
    areaDistributedForOnlyFujianCity.add(areaDistributedForNoFujian.setName("其它").setAmount(areaDistributedForNoFujian.getAmount()-FujianAmount).setIsleaf(true));

    long total = managerService.countBy(CopyrightManagerPredicates.certificateDateBeforeByFujian(param.getFirstYear(),param.getLastYear(),null));
    StatisticDto areaDistributedForForeign = new StatisticDto(999,"国外",0l,true);
    // 国外=全部-所有省份-港澳台
    areaDistributedForOnlyFujianCity.add(areaDistributedForForeign.setAmount(total-FujianAmount-areaDistributedForNoFujian.getAmount()-areaDistributedForGat.getAmount()));

    //按登记量排序
    Collections.sort(productionTypes, (o1, o2) -> (int)(o2.getAmount()-o1.getAmount()));
    Collections.sort(areaDistributedForOnlyFujianCity, (o1, o2) -> (int)(o2.getAmount()-o1.getAmount()));

    long periodLastMonthOrYearTotalSize = managerService.countBy(CopyrightManagerPredicates.certificateDateBeforeByFujian(param.getLastFirstYear(),param.getLastLastYear(),null));

    Double rate = 0.00;
    if(periodLastMonthOrYearTotalSize!=0) {
      rate = DecimalFormatUtil.format2FixDouble((Double.valueOf(total - periodLastMonthOrYearTotalSize) / periodLastMonthOrYearTotalSize)*100);
    }else{
      rate = 100.00;
    }

    System.out.println("年报类统计结束时间:"+ LocalDateTime.now());
    result
      .setTypes(productionTypes)
      .setTotalSize(total)
      .setRate(rate)
      .setLocationAdditions(areaDistributedForOnlyFujianCity)
      .setPeriodLastMonthOrYearTotalSize(periodLastMonthOrYearTotalSize)
    ;

    return result;
  }

  private JSONObject httpRequest(LocalDate start,LocalDate end,String city,List<String> productTypes){
    //处理旧参数，并请求旧系统数据
    Map<String,Object> oldParam = new HashedMap();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    oldParam.put("startTime",start==null?null:(start.format(formatter)+" 00:00:00"));
    oldParam.put("endTime", end==null?null:(end.format(formatter)+" 23:59:59"));
    oldParam.put("productTypes",productTypes);
    oldParam.put("city",city);
    RestTemplate restTemplate = new RestTemplate();
    String url = config.getOldSystemUrl()+"dataStatistics/geographyOfTime.do";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
    HttpEntity<Map<String, Object>> request = new HttpEntity<>(oldParam, headers);
    ResponseEntity<String> response = restTemplate.postForEntity( url, request , String.class );
    return JSONObject.parseObject(response.getBody());
  }

  private JSONObject httpRequest(LocalDate start,LocalDate end,String city,List<String> productTypes,String province){
    //处理旧参数，并请求旧系统数据
    Map<String,Object> oldParam = new HashedMap();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    oldParam.put("startTime",start==null?null:(start.format(formatter)+" 00:00:00"));
    oldParam.put("endTime", end==null?null:(end.format(formatter)+" 23:59:59"));
    oldParam.put("productTypes",productTypes);
    oldParam.put("city",city);
    oldParam.put("province",province);
    RestTemplate restTemplate = new RestTemplate();
    String url = config.getOldSystemUrl()+"dataStatistics/geographyOfTime.do";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
    HttpEntity<Map<String, Object>> request = new HttpEntity<>(oldParam, headers);
    ResponseEntity<String> response = restTemplate.postForEntity( url, request , String.class );
    return JSONObject.parseObject(response.getBody());
  }
}
