package blockChain.facade.service;

import blockChain.bean.PageResponse;
import blockChain.controller.exception.NotFountException;
import blockChain.dto.message.AlertMessageDto;
import blockChain.dto.message.QAMessageDtoQueryParam;
import blockChain.dto.message.QuestionAnswerMessageDto;
import blockChain.entities.message.AlertMessage;
import blockChain.entities.message.QuestionAnswerMessageEntity;
import blockChain.mapper.AlertMessageDtoMapper;
import blockChain.mapper.QuestionAnswerMessageDtoMapper;
import blockChain.repository.AlertMessagePredicates;
import blockChain.repository.QuestionAnswerMessagePredicates;
import blockChain.service.QuestionAnswerMessageService;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> Chan
 * @date 2020/4/13 9:37
 */
@Service
@AllArgsConstructor
public class QuestionAnswerMessageServiceFacade {

  private QuestionAnswerMessageService service;

  @Transactional(rollbackFor = RuntimeException.class)
  public void create(QuestionAnswerMessageDto dto) {
    Assert.notNull(dto, "参数不正确");
    QuestionAnswerMessageEntity entity = QuestionAnswerMessageDtoMapper.INSTANCE.toEntity(dto);
    service.save(entity.setUuid(UUID.randomUUID().toString()));
  }

  @Transactional(rollbackFor = RuntimeException.class)
  public void delete(String uuid) {
    Assert.notNull(uuid, "uuid  not null");

    QuestionAnswerMessageEntity entity = service.findByUuid(uuid).orElseThrow(NotFountException::new);

    service.delete(entity);

  }

  @Transactional(rollbackFor = RuntimeException.class)
  public void update(QuestionAnswerMessageDto dto) {
    Assert.notNull(dto, "参数不正确");
    Assert.notNull(dto.getUuid(), "参数不正确");

    QuestionAnswerMessageEntity entity = service.findByUuid(dto.getUuid()).orElseThrow(NotFountException::new);

    QuestionAnswerMessageDtoMapper.INSTANCE.update(dto, entity);

  }

  @Transactional(readOnly = true)
  public QuestionAnswerMessageDto getOne(String uuid) {
    Assert.notNull(uuid, "参数不正确");
    QuestionAnswerMessageEntity entity = service.findByUuid(uuid).orElseThrow(NotFountException::new);
    return QuestionAnswerMessageDtoMapper.INSTANCE.toDto(entity);
  }

  @Transactional(readOnly = true)
  public PageResponse<QuestionAnswerMessageDto> queryQAMessage(QAMessageDtoQueryParam dto) {
    Pageable pageable = dto.getPageable();
    List<Sort.Order> list = new ArrayList<>();
    Sort.Order order1 = new Sort.Order(Sort.Direction.DESC, "createTime");
    list.add(order1);
    Sort sort = Sort.by(list);
    Pageable pageable1 = PageRequest.of(pageable.getPageNumber(),pageable.getPageSize(),sort);
    Page<QuestionAnswerMessageEntity> all = service.findAll(QuestionAnswerMessagePredicates.titleKeyAndAnswerLikeAndCreateTimeBetween(dto.getKey(), dto.getAnswer(),dto.getCreateTimeStart(), dto.getCreateTimeEnd() ), pageable1);
    return QuestionAnswerMessageDtoMapper.INSTANCE.toDto(all);
  }

  /**
   * 提问响应
   * 处理方式：使用like+ 每个字符间插入%
   * @param dto
   * @return
   */
  public PageResponse<QuestionAnswerMessageDto> questionAndAnswer(QAMessageDtoQueryParam dto) {
    Page<QuestionAnswerMessageEntity> page = service.findAll(QuestionAnswerMessagePredicates.qaQuery(dto.getKey()), dto.getPageable());
    return QuestionAnswerMessageDtoMapper.INSTANCE.toDto(page);
  }
}
