/**
* @Title: CopyrightResult.java
* @Package jetsennet.ncss.webservice.copyrightmanage.bean
* @Description:
* <AUTHOR>
* @date Mar 13, 2015 4:13:53 PM
* @version V1.0
 */
package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @ClassName: CopyrightResult
 * @Description:
 * <AUTHOR>
 * @change
 * @date Mar 13, 2015 4:13:53 PM
 *
 */
@XmlRootElement
public class CopyrightResult {
	//500服务器出错  200返回成功  400参数错误
	private int code;
	private String registerNum;
	private String type;
	private List<String> msg;

	public int getCode() {
		return code;
	}
	public void setCode(int code) {
		this.code = code;
	}
	public String getRegisterNum() {
		return registerNum;
	}
	public void setRegisterNum(String registerNum) {
		this.registerNum = registerNum;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public List<String> getMsg() {
		return msg;
	}
	public void setMsg(List<String> msg) {
		this.msg = msg;
	}
}
