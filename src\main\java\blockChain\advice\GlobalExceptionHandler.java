package blockChain.advice;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.web.servlet.error.AbstractErrorController;
import org.springframework.boot.web.servlet.error.ErrorAttributes;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.zalando.problem.spring.web.advice.security.SecurityAdviceTrait;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 全局异常处理
 * Http Status Code 定义参见：https://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html
 * 1xx : Informational
 * 2xx : Successful
 * 3xx : Redirection
 * 4xx : Client Error
 * 5xx : Server Error
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler extends AbstractErrorController implements SecurityAdviceTrait {

  public GlobalExceptionHandler(ErrorAttributes errorAttributes) {
    super(errorAttributes);
  }

  private static final String PATH = "/errorpage";

  @RequestMapping(value = PATH, produces = {MediaType.APPLICATION_JSON_VALUE})
	@ExceptionHandler
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public ResponseEntity<Map<String, Object>> commonError(HttpServletRequest request, Throwable cause){
		log.error("error", cause);
    Map<String, Object> body = getErrorAttributes(request, false);
    body.put("resultCode", 1);
    return new ResponseEntity<>(body, HttpStatus.INTERNAL_SERVER_ERROR);
	}

  @Override
  public String getErrorPath() {
    return PATH;
  }
}
