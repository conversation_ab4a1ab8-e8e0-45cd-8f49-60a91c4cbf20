package blockChain.utils;

import blockChain.dto.KaptchaDto;

import javax.servlet.ServletException;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Random;

public class KaptchaUtil {
  public static KaptchaDto getContext()
    throws ServletException, IOException {

    KaptchaDto kaptchaDto = new KaptchaDto();
    int width = 155;      //指定验证码的宽度
    int height = 45;      //指定验证码的高度
    BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
    Graphics graphics = image.getGraphics();      //获取Graphics类的对象
    Random random = new Random();          //实例化一个Random对象
    Font mFont = new Font("宋体", Font.ITALIC, 26);   //通过Font构造字体
    graphics.setColor(getRandomColor(200, 250)); //设置颜色
    graphics.fillRect(0, 0, width, height);
    graphics.setFont(mFont);    //设置字体
    graphics.setColor(getRandomColor(180, 200)); //设置文字的颜色

    String chckcode = "";
    String ctmp = "";
    for (int i = 0; i < 4; i++) {
      String[] rBase = {"0", "1", "2", "3", "4", "5", "6", "7", "8",
        "9", "a", "b", "c", "d", "e", "f"};
      //生成第一位的区码
      int rand1 = random.nextInt(3) + 11;    //生成11到14之间的随机数
      String strR1 = rBase[rand1];
      //生成第二位的区码
      int rand2;
      if (rand1 == 13) {
        rand2 = random.nextInt(7);        //生成0-7之间的随机数
      } else {
        rand2 = random.nextInt(16);        //生成0-16之间的随机数
      }
      String strR2 = rBase[rand2];

      //生成第一位的位码
      int rand3 = random.nextInt(6) + 10;    //生成10到16之间的随即数
      String strR3 = rBase[rand3];
      //生成第二位的位吗
      int rand4;
      if (rand3 == 10) {
        rand4 = random.nextInt(15) + 1;    //生成1到16之间的随机数
      } else if (rand3 == 15) {
        rand4 = random.nextInt(15);        //生成0到15之间的随机数
      } else {
        rand4 = random.nextInt(16);        //生成0到16之间的随机数
      }
      String strR4 = rBase[rand4];

      //将生成的机内码转换为汉字
      byte[] bytes = new byte[2];
      //将生成的区码保存到字节数组的第一个元素中
      String strR12 = strR1 + strR2;
      int tempLow = Integer.parseInt(strR12, 16);
      bytes[0] = (byte) tempLow;

      String strR34 = strR3 + strR4;
      int tempHigh = Integer.parseInt(strR34, 16);
      bytes[1] = (byte) tempHigh;

      ctmp = new String(bytes,"gb2312");            //根据字节数组生成汉字
      chckcode = chckcode + ctmp;
      Color color = new Color(20 + random.nextInt(110), 20 + random.nextInt(110), 20 + random.nextInt(110));
      graphics.setColor(color);

      /** **随机缩放文字并将文字旋转指定角度* */
      Graphics2D graphics2d = (Graphics2D) graphics;
      //实例化AffineTransform类的对象
      AffineTransform trans = new AffineTransform();
      //进行旋转
      trans.rotate(random.nextInt(25) * 3.14 / 180, 26 * i + 8, 7);

      // 缩放文字
      float scaleSize = random.nextFloat() + 0.7f;
      if (scaleSize > 1f) scaleSize = 1f;
      trans.scale(scaleSize, scaleSize);   //进行缩放
      graphics2d.setTransform(trans);
      graphics.drawString(ctmp, width / 6 * i + 30, height / 3 + 5);
    }
    kaptchaDto.setContext(chckcode);
    kaptchaDto.setImage(image);
    return kaptchaDto;
  }

  /**
   * 生成随即颜色
   * @param fc
   * @param bc
   * @return
   */
  public static Color getRandomColor(int fc, int bc){
    Random random = new Random();
    Color randomColor = null;
    if(fc>255) fc=255;
    if(bc>255) bc=255;
    //设置个0-255之间的随机颜色值
    int r=fc+random.nextInt(bc-fc);
    int g=fc+random.nextInt(bc-fc);
    int b=fc+random.nextInt(bc-fc);
    randomColor = new Color(r,g,b);
    return randomColor;//返回具有指定红色、绿色和蓝色值的不透明的 sRGB 颜色
  }
}
