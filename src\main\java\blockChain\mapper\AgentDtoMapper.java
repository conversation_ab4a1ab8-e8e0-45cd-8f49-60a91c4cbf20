package blockChain.mapper;

import blockChain.dto.AgentDto;
import blockChain.dto.UserDto;
import blockChain.entities.Agent;
import blockChain.entities.UserEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AgentDtoMapper {
    AgentDtoMapper INSTANCE = Mappers.getMapper(AgentDtoMapper.class);

    AgentDto entityToDto(Agent agent);

    List<AgentDto> entityToDto(List<Agent> agents);
}
