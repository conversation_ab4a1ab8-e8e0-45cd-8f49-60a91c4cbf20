package blockChain.service;

import blockChain.repository.BaseRepository;
import blockChain.repository.SecSimilarAttachmentRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Handler;

/**
 * <AUTHOR>
 * @date 2020/3/31 16:25
 */
@Service
public class SimilarAttachmentService implements BaseService {

  private final SecSimilarAttachmentRepository repository;

  public SimilarAttachmentService(SecSimilarAttachmentRepository repository){
    this.repository = repository;
  }

  @Override
  public BaseRepository getRepository() {
    return repository;
  }



  public List<Map<String,Object>> getBySimilarAttachmentId(Long copyrightId, Long attachmentId) {
    List<Map<String,Object>> result = new ArrayList<>();
    List<Map<String,Object>> list = repository.getBySimilarAttachmentId(copyrightId,attachmentId);
    for(Map<String,Object> map:list){
      Map<String,Object> data = new HashMap<>(map);
      result.add(data);
    }
    return result;
  }
}

