package blockChain.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Deprecated
public class ResultCodeResponse {

	private int resultCode;

	private String message;


	public ResultCodeResponse(Throwable e){
        this.resultCode = ResultCode.RESULT_FAIL;
        this.message = e.getMessage();
	}

	public ResultCodeResponse(int resultCode, String message) {
		this.resultCode = resultCode;
		this.message = message;
	}

	public static ResultCodeResponse successResponse(){
		return new ResultCodeResponse(ResultCode.RESULT_SUCCESS, "操作成功");
	}

	public static ResultCodeResponse failResponse(){
		return failResponse("系统异常");
	}

    public static ResultCodeResponse failResponse(String message){
        return new ResultCodeResponse(ResultCode.RESULT_FAIL, message);
    }
}
