package blockChain.repository;

import blockChain.entities.CertificateEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/29 14:44
 */
@Repository
public interface CertificateRepository extends JpaRepository<CertificateEntity, Long>, QuerydslPredicateExecutor<CertificateEntity> {
    List<CertificateEntity> findAllByCertificateHolderCode(String certificateHolderCode);
}
