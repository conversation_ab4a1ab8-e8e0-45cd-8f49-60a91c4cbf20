package blockChain.repository;

import blockChain.entities.PaperCertificateEntity;
import blockChain.entities.QPaperCertificateEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/16 16:53
 */
public final class PaperCertificatePredicates {
  public static Predicate statusEqAndCreateTimeBetween(Integer currentUserId, PaperCertificateEntity.StatusEnum status, LocalDateTime startDate, LocalDateTime endDate) {
    QPaperCertificateEntity cert = QPaperCertificateEntity.paperCertificateEntity;

    BooleanBuilder builder = new BooleanBuilder();

    if(status != null){
      builder.and(cert.status.eq(status));
    }

    if(startDate != null && endDate != null){
      builder.and(cert.createTime.between(startDate, endDate));
    }

    if(currentUserId != null){
      builder.and(cert.creator.userId.eq(currentUserId));
    }


    return builder;
  }

  public static Predicate statusEqAndCreateTimeBetweenAndStatusNotIn(Integer currentUserId, PaperCertificateEntity.StatusEnum status, LocalDateTime startDate, LocalDateTime endDate, PaperCertificateEntity.StatusEnum ... statuses) {
    Predicate predicate = statusEqAndCreateTimeBetween(currentUserId, status, startDate, endDate);
    BooleanBuilder builder = new BooleanBuilder(predicate);

    QPaperCertificateEntity cert = QPaperCertificateEntity.paperCertificateEntity;

    if(statuses != null && statuses.length > 0){
      builder.and(cert.status.notIn(statuses));
    }

    return builder;
  }
}
