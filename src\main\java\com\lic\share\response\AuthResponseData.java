package com.lic.share.response;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <pre>
 * ClassName AuthorityResponseData
 * Description 认证授权返回的data
 * Author glq
 * Date 2020-5-11
 * </pre>
 */
@Data
@Accessors(chain = true)
public class AuthResponseData {

    /**
     * 访问令牌
     */
    @NotNull(message = "accessToken不能为空")
    String accessToken;

    /**
     * 有效期，以秒为单位
     */
    @NotNull(message = "expiresIn不能为空")
    long expiresIn;
}
