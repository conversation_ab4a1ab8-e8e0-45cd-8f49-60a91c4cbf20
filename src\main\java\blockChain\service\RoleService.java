package blockChain.service;

import blockChain.bean.Constant;
import blockChain.entities.QRoleEntity;
import blockChain.entities.RoleEntity;
import blockChain.repository.RoleRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/16 16:21
 */
@Service
@AllArgsConstructor
public class RoleService implements BaseService<RoleRepository, RoleEntity, Long> {

    private final RoleRepository repository;

    @Override
    public RoleRepository getRepository() {
        return repository;
    }

    public List<RoleEntity> findByIdIn(List<Long> idList) {
        QRoleEntity qRoleEntity = QRoleEntity.roleEntity;
        return repository.findAll(qRoleEntity.id.in(idList));
    }
    public List<RoleEntity> getWorkstationList() {
      return repository.findAllByIsWorkstation(Constant.BYTE_TRUE);
    }

    public boolean existRoleName(String roleName) {
        QRoleEntity qRoleEntity = QRoleEntity.roleEntity;
        return repository.exists(qRoleEntity.roleName.eq(roleName));
    }
}
