package blockChain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DataRefluxDTO {
//    @ApiModelProperty("联系中枢对接人")
//    private String modelKey;

    @ApiModelProperty("姓名/企业名称")
    private String name;

    @ApiModelProperty("证件号码")
    private String certificateNumber;

//    @ApiModelProperty("事项编码")
//    private String subItemCode;

    @ApiModelProperty("主体类型 1-个人 2-企业")
    private Integer subjectType;
}
