package blockChain.utils;

/**
 * Created by epcsoft on 2020/8/3.
 */


import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.*;

/**
 * 封装了XML转换成object，object转换成XML的代码
 *
 * <AUTHOR>
 *
 */
public class XMLUtil {
  /**
   * 将对象直接转换成String类型的 XML输出
   *
   * @param obj
   * @return
   */
  public static String convertToXml(Object obj) {
    // 创建输出流
    StringWriter sw = new StringWriter();
    try {
      // 利用jdk中自带的转换类实现
      JAXBContext context = JAXBContext.newInstance(obj.getClass());

      Marshaller marshaller = context.createMarshaller();
      // 格式化xml输出的格式
      marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT,
        Boolean.TRUE);
      // 将对象转换成输出流形式的xml
      marshaller.marshal(obj, sw);
    } catch (JAXBException e) {
      e.printStackTrace();
    }
    return sw.toString();
  }

  /**
   * 将对象根据路径转换成xml文件
   *
   * @param obj
   * @param path
   * @return
   */
  public static void convertToXml(Object obj, String path) {
    try {
      // 利用jdk中自带的转换类实现
      JAXBContext context = JAXBContext.newInstance(obj.getClass());

      Marshaller marshaller = context.createMarshaller();
      // 格式化xml输出的格式
      marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT,
        Boolean.TRUE);
      // 将对象转换成输出流形式的xml
      // 创建输出流
      FileWriter fw = null;
      try {
        fw = new FileWriter(path);
      } catch (IOException e) {
        e.printStackTrace();
      }
      marshaller.marshal(obj, fw);
    } catch (JAXBException e) {
      e.printStackTrace();
    }
  }

  @SuppressWarnings("unchecked")
  /**
   * 将String类型的xml转换成对象
   */
  public static Object convertXmlStrToObject(Class clazz, String xmlStr) {
    Object xmlObject = null;
    try {
      JAXBContext context = JAXBContext.newInstance(clazz);
      // 进行将Xml转成对象的核心接口
      Unmarshaller unmarshaller = context.createUnmarshaller();
      StringReader sr = new StringReader(xmlStr);
      xmlObject = unmarshaller.unmarshal(sr);
    } catch (JAXBException e) {
      e.printStackTrace();
    }
    return xmlObject;
  }

  @SuppressWarnings("unchecked")
  /**
   * 将file类型的xml转换成对象
   */
  public static Object convertXmlFileToObject(Class clazz, String xmlPath) {
    Object xmlObject = null;
    try {
      JAXBContext context = JAXBContext.newInstance(clazz);
      Unmarshaller unmarshaller = context.createUnmarshaller();
      FileReader fr = null;
      try {
        fr = new FileReader(xmlPath);
      } catch (FileNotFoundException e) {
          e.printStackTrace();
      }
        xmlObject = unmarshaller.unmarshal(fr);
    } catch (JAXBException e) {
        e.printStackTrace();
    }
      return xmlObject;
  }

    public static String escapeSpecialCharacters(String input) {
        // XML5个转义符
        // 将所有可能存在的特殊字符替换为对应的实体引用
        String output = input
                .replace("&", "&amp;")   // &
                .replace("<", "&lt;")   // <
                .replace(">", "&gt;")   // >
                .replace("\"", "&quot;") // "
                .replace("'", "&apos;");  // '
        return output;
    }

}
