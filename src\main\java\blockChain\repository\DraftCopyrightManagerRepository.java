package blockChain.repository;

import blockChain.entities.CopyrightManager;
import blockChain.entities.DraftCopyrightManager;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * <AUTHOR>
 * @date 2019/12/2 16:06
 */
public interface DraftCopyrightManagerRepository extends JpaRepository<DraftCopyrightManager, Integer>,QuerydslPredicateExecutor<DraftCopyrightManager> {

    public DraftCopyrightManager getByRegistrationNum(Long registrationNum);

}
