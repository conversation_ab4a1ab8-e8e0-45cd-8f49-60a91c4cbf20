package blockChain.bean;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * Created by epcsoft on 2020/8/17.
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "root")
@Data
public class AuthenticationBean implements Serializable
{
  private String username;
  private String password;
  private String logintime;
}
