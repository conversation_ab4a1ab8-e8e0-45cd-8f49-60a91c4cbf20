package blockChain.repository;
import blockChain.entities.CopyrightManager;
import blockChain.entities.EvaluateEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * Created by epcsoft on 2020/8/6.
 */
@Repository
public interface EvaluationRepository extends JpaRepository<EvaluateEntity, Long>, QuerydslPredicateExecutor<EvaluateEntity>, EvaluationRepositoryCustom
{
    int countAllByManager(CopyrightManager copyrightManager);
}
