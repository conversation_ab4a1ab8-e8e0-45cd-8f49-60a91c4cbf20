package blockChain.bean;

import lombok.Data;


/**
 * Created by epcsoft on 2020/8/12.
 */
@Data
public class LicenseSendInfo
{
  // 作品号
  private Long registrationNum;
  // 登记日期
  private String registrationDate;
  // 登记号
  private String worksNum;
  // 作品名称
  private String workName;
  // 作品类别
  private String workType;
  // 作者
  private String authors;
  // 著作权人
  private String owners;
  // 著作权人 持证主体代码类型名称
  private String ownerTypeName;
  // 著作权人 持证主体代码类型代码
  private String ownerTypeCode;
  // 创作完成日期
  private String finishTime;
  // 首次发表、出版、制作日期
  private String firstPublishTime;
  //上传状态
  private String licenseStatus;
  //证件号
  private String node;
}
