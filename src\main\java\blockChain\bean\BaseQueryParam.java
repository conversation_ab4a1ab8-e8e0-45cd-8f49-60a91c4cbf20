package blockChain.bean;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.List;

/**
 * 基础查询接口
 * <AUTHOR>
 * @date 2020/3/31 15:03
 */
@Getter
@Setter
public abstract class BaseQueryParam {

  private List<Long> idList = new ArrayList<>(1);

  /**
   * 从1开始
   */
  private int page = 1;
  private int pageSize = 10;
  public long getOffset() {
    return (getPage() - 1) * getPageSize();
  }

  public Pageable getPageable(){
    return PageRequest.of(page - 1, pageSize);
  }
}
