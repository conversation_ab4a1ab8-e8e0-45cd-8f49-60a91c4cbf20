package com.lic.share.api;


import cn.hutool.core.io.file.FileReader;
import com.lic.share.config.RestHelper;
import com.lic.share.untils.SmCall;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.SignatureException;

/**
 * <pre>
 * ClassName Authorization
 * Description
 * Author glq
 * Date 2019/4/9
 * </pre>
 */
@RestController
@RequestMapping(value = {"/submit/"})
public class SubmitService extends BaseConfig {


    @Autowired
    RestHelper restHelper;

    @PostMapping(value = {"saveLicenseData"})
    public String saveLicenseData(String accessToken, String path) throws SignatureException {
        FileReader reader = new FileReader(path, "UTF-8");
        String jsonData = reader.readString();
        String cerTypeUrl = submitPreUrl + "saveLicenseData";

        String sign = SmCall.createSM2Signature(jsonData, priKey);

        String s = restHelper.postRestTemplateByJson(cerTypeUrl, accountId, sign, jsonData);
        return s;
    }

}
