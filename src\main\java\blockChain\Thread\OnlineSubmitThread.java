package blockChain.Thread;

import blockChain.config.BeanContext;
import blockChain.entities.CopyrightManager;
import blockChain.facade.service.CopyrightManagerServiceFacade;
import org.springframework.scheduling.annotation.Async;

public class OnlineSubmitThread extends Thread {
  private CopyrightManager copyrightManager;
  private CopyrightManagerServiceFacade copyrightManagerServiceFacade;


  public void onlineSubmit(CopyrightManager copyrightManager){
    this.copyrightManager = copyrightManager;
    this.start();
  }

  @Async("doSomethingExecutor")
  public void run(){
    System.out.println("线程启动--作品提交:"+copyrightManager.getWorksNum());
    this.copyrightManagerServiceFacade = BeanContext.getApplicationContext().getBean(CopyrightManagerServiceFacade.class);
    copyrightManagerServiceFacade.onlineSubmitDataRun(copyrightManager,null);
    System.out.println("线程启动--作品提交完成:"+copyrightManager.getWorksNum());
  }
}
