package com.lic.share.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <pre>
 * Description 电子证照返回的data
 * Author wenghq
 * Date 2022-2-24
 * </pre>
 */
@Data
@Accessors(chain = true)
public class CertificateData {

    /**
     * 电子证照标识
     * eg: 004155341-3502032018122850114-410727199802207325
     * 1.2.156.3005.2.11100000000013127D001.5dd2830477b21486d85d644e.9UXYF1YE1XFV6PBT4N1D.001.L
     */
    String certificateIdentifier;

    /**
     * 证照类型代码
     * eg: 11100000000013127D050(居住证)
     * 11100000000013127D001（中华人民共和国居民身份证）
     */
    String certificateTypeCode;

    /**
     * 证照类型名称
     * eg: 居住证/中华人民共和国居民身份证
     */
    String certificateTypeName;

    /**
     * 证照所属行政区划
     * eg: 350104
     */
    String certificateAreaCode;

    /**
     * 数据来源，由三位数字组成，
     * eg: 135
     */
    String dataSource;

    /**
     * 证照编号
     * eg: 3502032018122850114(居住证编号)
     * （身份证号码）
     */
    String certificateNumber;

    /**
     * 证照名称
     * eg: 福建省居住证/中华人民共和国居民身份证
     */
    String certificateName;

    /**
     * 持证主体
     * eg: 用户名
     */
    String certificateHolderName;

    /**
     * 持证主体代码
     * eg: 身份证号
     */
    String certificateHolderCode;

    /**
     * 持证主体代码类型代码
     * eg: 公民身份号码:111; 其他自然人有效证件代码:999
     */
    String certificateHolderTypeCode;

    /**
     * 持证主体代码类型名称
     * eg: 公民身份号码
     */
    String certificateHolderTypeName;

    /**
     * 证照颁发机构
     * eg: 福州市公安局仓山分局
     */
    String certificateIssuingAuthorityName;

    /**
     * 证照颁发机构代码
     * eg: 11350100003606753D
     */
    String certificateIssuingAuthorityCode;

    /**
     * 证照颁发日期
     * eg: 2019-09-24
     */
    String certificateIssuedDate;

    /**
     * 证照有效期起始日期
     * eg: 2019-09-24
     */
    String certificateEffectiveDate;

    /**
     * 证照有效期截止日期
     * eg: 2021-09-24
     */
    String certificateExpiringDate;

    /**
     * 电子证照文件格式：edc(居住证)、ofd（身份证）（如果是省外证照数据，则为空）
     * eg: 公民身份号码
     */
    String fileFormat;

    /**
     * 分类:证照、批文（如果是省外证照数据，则为空）
     */
    String KZ_classification;

    /**
     * 安全共享等级:低、中、高
     */
    String KZ_securityClass;
}
