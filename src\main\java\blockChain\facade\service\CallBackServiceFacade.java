package blockChain.facade.service;

import blockChain.entities.CallBackEntity;
import blockChain.entities.CopyrightManager;
import blockChain.entities.ProcessProgressEntity;
import blockChain.repository.CallBackRepository;
import blockChain.service.CopyrightManagerService;
import blockChain.service.ProcessProgressService;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class CallBackServiceFacade {

    @Autowired
    private CallBackRepository repository;

    @Autowired
    private ProcessProgressService processProgressService;
    private final CopyrightManagerService managerService;

    @Async("doSomethingExecutor")
    public void callBackScheduling() {
        while(true) {
            try {
                List<CallBackEntity> callBackEntities = Lists.newArrayList(repository.findAll(PageRequest.of(0, 500)));
                if (callBackEntities.size() > 0) {
                    for (CallBackEntity callBackEntity : callBackEntities) {
                        log.info("更新回调开始,processId:{}", callBackEntity.getProcessId());
                        ProcessProgressEntity progressEntity = processProgressService.findByProcessId(callBackEntity.getProcessId());
                        if (ObjectUtils.isEmpty(progressEntity)) continue; // fix when record not exits

                        if (callBackEntity.getState() == 1) {
                            CopyrightManager manager = managerService.getById(progressEntity.getRegistrationNum());
                            switch (manager.getSnStatus()) {
                                case 16:
                                    manager.setSnStatus(15);
                                    break;
                                case 18:
                                    manager.setSnStatus(17);
                                    break;
                                case 20:
                                    manager.setSnStatus(19);
                                    break;
                                case 22:
                                    manager.setSnStatus(21);
                                    break;
                            }
                            managerService.saveOrUpdateCopyright(manager);
                        }

                        long res = processProgressService.updateData(callBackEntity.getProcessId(), callBackEntity.getMessage(), callBackEntity.getState());
                        log.info("更新回调结束,processId:{},是否成功,{}", callBackEntity.getProcessId(), res);
                        repository.delete(callBackEntity);
                    }
                } else {
                    Thread.sleep(1000);
                }
            } catch (Exception e) {
                log.error("更新回调失败: " + e);
            }
        }
    }

    @Transactional
    public void saveData(CallBackEntity entity) {
        repository.save(entity);
    }
}
