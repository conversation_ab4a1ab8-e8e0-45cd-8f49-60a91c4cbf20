package blockChain.repository;

import blockChain.dto.message.MessageDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2019/12/18 17:56
 */
public interface CustomizedMessageRepository {

  Page<MessageDto> findPageByUserId(int userId, String title, String content, Byte state, LocalDateTime createTimeStart, LocalDateTime createTimeEnd, Pageable pageable);

  public Long countUnreadSize(int userId, Byte state);
}
