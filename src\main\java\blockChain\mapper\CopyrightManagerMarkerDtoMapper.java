package blockChain.mapper;

import blockChain.dto.CopyrightManagerMarkerDto;
import blockChain.entities.CopyrightManagerMarker;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2020/4/14 14:42
 */
@Mapper(config = CommonConfig.class)
public interface CopyrightManagerMarkerDtoMapper {
  CopyrightManagerMarkerDtoMapper INSTANCE = Mappers.getMapper(CopyrightManagerMarkerDtoMapper.class);

  CopyrightManagerMarker toEntity(CopyrightManagerMarkerDto dto);

  @Mappings({
    @Mapping(target = "registrationNum", source = "manager.registrationNum"),
  })
  CopyrightManagerMarkerDto toDto(CopyrightManagerMarker entity);

  @Mappings({
    @Mapping(target = "uuid", ignore = true),
    @Mapping(target = "id", ignore = true),
    @Mapping(target = "manager", ignore = true),
    @Mapping(target = "createTime", ignore = true),
    @Mapping(target = "updateTime", ignore = true),
  })
  void update(CopyrightManagerMarkerDto dto,@MappingTarget CopyrightManagerMarker marker);
}
