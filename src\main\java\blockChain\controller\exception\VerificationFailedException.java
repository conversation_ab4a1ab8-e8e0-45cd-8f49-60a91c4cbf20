package blockChain.controller.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 请求出现语法错误。
 * 参数验证失败
 * <AUTHOR>
 * @date 2019/6/4 9:43
 */
@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class VerificationFailedException extends RuntimeException{
  public VerificationFailedException() {
    super("参数验证失败");
  }

  /**
   * Constructs a new runtime exception with the specified detail message.
   * The cause is not initialized, and may subsequently be initialized by a
   * call to {@link #initCause}.
   *
   * @param message the detail message. The detail message is saved for
   *                later retrieval by the {@link #getMessage()} method.
   */
  public VerificationFailedException(String message) {
    super(message);
  }
}
