
//todaychange
package blockChain.controller;

import blockChain.Thread.BiyiServiceThread;
import blockChain.bean.BaseResponseDto;
import blockChain.bean.BatchDownloadRequest;
import blockChain.bean.Constant;
import blockChain.bean.QueryParam;
import blockChain.config.SpringConfig;
import blockChain.constant.AuthoritiesConstant;
import blockChain.dto.*;
import blockChain.dto.ebus.FormDataDTO;
import blockChain.dto.message.MessageDtoQueryParam;
import blockChain.entities.*;
import blockChain.facade.service.*;
import blockChain.facade.service.oldSystem.DataRemoveServiceFacade;
import blockChain.mapper.CopyrightManagerDtoMapper;
import blockChain.mapper.CopyrightOwnerDtoMapper;
import blockChain.mapper.DigitalDtoMapper;
import blockChain.mapper.FormDataMapper;
import blockChain.service.BiyiService;
import blockChain.service.CopyrightManagerService;
import blockChain.service.ProcessProgressService;
import blockChain.utils.DateUtils;
import blockChain.utils.HttpUtil;
import blockChain.utils.ImageUtil;
import blockChain.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.mascloud.sdkclient.Client;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Api("作品填报")
@Slf4j
@RestController
@RequestMapping("onlineFill")
@RequiredArgsConstructor
public class OnlineFillController {
    @Autowired
    private CopyrightManagerServiceFacade copyrightManagerServiceFacade;
    @Autowired
    private DigitalServiceFacade digitalServiceFacade;
    @Autowired
    private UserServiceFacade userServiceFacade;
    @Autowired
    private CopyrightOwnerStatisticServiceFacade ownerStatisticServiceFacade;
    @Autowired
    private MessageServiceFacade messageServiceFacade;
    @Autowired
    private CopyrightManagerService copyrightManagerService;
    @Autowired
    private BiyiService biyiService;
    @Autowired
    private DataRemoveServiceFacade dataRemoveServiceFacade;
    @Autowired
    private UploadAttachmentServiceFacade uploadAttachmentServiceFacade;

    @Autowired
    private CertificateServiceFacade certificateServiceFacade;
    @Autowired
    private SensitiveWordServiceFacade sensitiveWordServiceFacade;

    private final SpringConfig config;

    @Autowired
    private BackendServiceFacade backendServiceFacade;
    private final OutSideServiceFacade outSideServiceFacade;

    @Autowired
    private ProcessProgressService processProgressService;

    final String contextPath = "File/";

//
//    @ApiOperation("数据导入")
//    @PostMapping("test")
//    public ResponseEntity<Map<String, Object>> test() {
//        dataRemoveServiceFacade.run();
//        Map<String, Object> result = new HashMap<>();
//        return new ResponseEntity<>(result, HttpStatus.OK);
//    }

    @ApiOperation("批量下载证书")
    @PostMapping("batchdownloadCert")
    public ResponseEntity<Map<String, Object>> batchdownloadCert(@Valid @RequestBody BatchDownloadRequest batchDownloadRequest) {
        Map<String, Object> result = new HashMap<>();
        try {
            Optional<Integer> userId = SecurityUtils.getOptionalCurrentUserId();
            UserDto userDto = userServiceFacade.findUserById(userId.get());

            //调用比翼微服务
//      String url = config.getBiyiServiceUrl()+"getMD5Str?data="+userDto.getRealName();
//      Thread connect = new BiyiServiceThread(null, "GET", url);
//      connect.start();
            biyiService.GetMD5Str(userDto.getRealName());

            //是用户，判断是否是他自己的作品，管理员则不判断
            if (userDto.getRoleId() == RoleEntity.ROLE_USER) {
                for (Long id : batchDownloadRequest.getIdList()) {
                    CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);

                    if (copyrightManager == null || !copyrightManager.getUserName().equals(userDto.getUserName())) {
                        result.put("message", "权限错误！您无权下载！");
                        result.put("errorMsg", "");
                        return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
                    }
                }
            }
            String tmpdir = System.getProperty("user.dir") + File.separator + contextPath + config.getModelRoot() + "fileModel" + File.separator + "tmp" + File.separator;
            try {
                new File(tmpdir).mkdir();
            } catch (Exception e) {

            }
            try {
                byte[] buffer = new byte[1024];
                // String strZipPath = COM_REPORT_PATH+"/"+user.getOid()+"/"+report.getOid()+"/"+tmpFileName;
                String downloadFile = UUID.randomUUID() + ".zip";
                result.put("downloadurl", contextPath + config.getModelRoot() + "fileModel" + File.separator + "tmp" + File.separator + downloadFile);
                String strZipPath = tmpdir + downloadFile;
                File tmpZipFile = new File(strZipPath);
                if (!tmpZipFile.exists())
                    tmpZipFile.createNewFile();

                ZipOutputStream out = new ZipOutputStream(new FileOutputStream(strZipPath));
                // 需要同时下载的两个文件result.txt ，source.txt
                //用户未选择任何作品时返回异常
                if (batchDownloadRequest.getIdList() == null || batchDownloadRequest.getIdList().size() == 0) {
                    result.put("message", "请勾选您需要下载证书的作品！");
                    result.put("errorMsg", "");
                    return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
                }
                for (Long id : batchDownloadRequest.getIdList()) {
                    CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);
                    if (copyrightManager != null && copyrightManager.getCertificateUrl() != null) {
                        //取证书文件夹路径
                        String pngFilePath = System.getProperty("user.dir") + File.separator + copyrightManager.getCertificateUrl();
                        int seperatorPos = pngFilePath.lastIndexOf(File.separator);
                        if (seperatorPos <= 0) {
                            log.error("ZIP:invalid path:" + pngFilePath);
                            continue;
                        }
                        String pngDirPath = pngFilePath.substring(0, seperatorPos);
                        File fileList = new File(pngDirPath);
                        if (!fileList.isDirectory()) {
                            log.error("ZIP:invalid dir:" + pngDirPath);
                            continue;
                        }
                        for (File singlefile : fileList.listFiles()) {
                            String zipEntryName = copyrightManager.getWorksNum() + File.separator + singlefile.getName();
                            System.out.print("add zip entry:" + singlefile.getAbsolutePath() + "===>" + zipEntryName);
                            FileInputStream fis = new FileInputStream(singlefile);
                            out.putNextEntry(new ZipEntry(zipEntryName));
                            int len;
                            // 读入需要下载的文件的内容，打包到zip文件
                            while ((len = fis.read(buffer)) > 0) {
                                out.write(buffer, 0, len);
                            }
                            out.closeEntry();
                            fis.close();
                        }
                    }
                }
                out.close();
                result.put("message", "打包下载地址！");
            } catch (Exception e) {
                result.put("message", "压缩包创建失败！");
                result.put("errorMsg", e.getMessage());
                return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
            }

        } catch (Exception e) {
            result.put("message", "用户未登录！");
            result.put("errorMsg", e.getMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("作品提交")
    @PostMapping("onlineSubmitData")
    public ResponseEntity<Map<String, Object>> onlineSubmitData(@Valid @RequestBody CopyrightManager copyrightManager) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (config.getSubmitInterval() > config.getSubmitIntervalMax())
                throw new RuntimeException("作品提交功能目前暂时关闭");

            //防止频繁提交，判断该用户提交作品时间差
            int currentUserId = SecurityUtils.getCurrentUserId();
            UserEntity userEntity = userServiceFacade.findById(currentUserId);
            if (userEntity.getLastCommitTime() != null) {
                Duration duration = Duration.between(userEntity.getLastCommitTime(), LocalDateTime.now());

                if (duration.getSeconds() < config.getSubmitInterval()) {
                    throw new RuntimeException("提交作品间隔必须大于" + config.getSubmitInterval() + "秒！");
                }
            }

            Long registrationNum = copyrightManager.getRegistrationNum();
            CopyrightManager manager = null;
            if (registrationNum != null && registrationNum > 0) {
                manager = copyrightManagerServiceFacade.getById(copyrightManager.getRegistrationNum());
                if (manager == null)
                    throw new RuntimeException("作品不存在。");

                if (manager.getInactiveType() != null && manager.getInactiveType() == CopyrightManager.InactiveTypeValue.REVOKED) {
                    throw new RuntimeException("已撤销作品不可提交。");
                }
            }

            checkInvalidation(copyrightManager);
            //判断附件是否上传完毕，未上传完毕则报错
            if (ObjectUtils.isEmpty(copyrightManager.getUploadWorks())) {
                throw new RuntimeException("样本附件未上传，请返回上传完毕后再进行提交。");
            } else {
                // 重复附件校验
                List<Long> uploadIdTemp = new ArrayList<>();
                for (UploadAttachment uploadAttachment : copyrightManager.getUploadWorks()) {
                    if (uploadAttachment.getId() == null) {
                        throw new RuntimeException("样本附件未上传完成，请返回等待附件上传完毕后再进行提交。");
                    }
                    if (uploadIdTemp.contains((uploadAttachment.getId()))) {
                        throw new RuntimeException("样本附件重复，请删除重复附件后再进行提交。");
                    } else {
                        uploadIdTemp.add(uploadAttachment.getId());
                    }

                    UploadAttachment attachment = uploadAttachmentServiceFacade.getById(uploadAttachment.getId());
                    if (ObjectUtils.isEmpty(attachment)) {
                        throw new RuntimeException("样本不存在，请重新上传样本文件。");
                    }
                    if (manager != null) {
                        List<Long> copyrightIds = uploadAttachmentServiceFacade.getCopyrightId(uploadAttachment.getId());
                        if (CollectionUtils.isNotEmpty(copyrightIds) &&
                                (copyrightIds.size() > 1 || !copyrightIds.get(0).equals(manager.getRegistrationNum()))
                        ) {
                            throw new RuntimeException("样本不可重复使用，请重新上传样本文件。");
                        }
                    }
                    // 1是图片，2是文档,3视频
                    switch (copyrightManager.getFileType()) {
                        case 1:
                            if (!isPic2(attachment))
                                throw new RuntimeException("图片类型样本只允许上传png/jpg/gif/jpeg格式，请重新上传样本文件。");
                            break;
                        case 2:
                            if (!attachment.getWorkName().endsWith(".pdf") && !attachment.getWorkName().endsWith(".PDF"))
                                throw new RuntimeException("文档类型样本只允许上传.pdf格式，请重新上传样本文件。");
                            break;
                        case 3:
                            if (!isVide(attachment))
                                throw new RuntimeException("视频/音频类型样本只允许上传mp4/MP3/wav格式，请重新上传样本文件。");
                            break;
                        default:
                            throw new RuntimeException("样本上传文件类型不存在");
                    }
                }
            }

            for (Long id : copyrightManager.getUploadWorkRemove()) {
                if (!userServiceFacade.isMyFile2(id))
                    throw new RuntimeException("权限错误！您无权删除附件！");
            }

            if (copyrightManager.getOwnerList() != null && copyrightManager.getOwnerList().size() > 0) {
                // add 202212 著作权人重复判断 ，地址str 字典获取
                List<String> cardIds = new ArrayList();
                for (CopyrightOwner owner : copyrightManager.getOwnerList()) {
                    owner.setCopyName(owner.getCopyName().trim());
                    if (owner.getCopyName() == null || owner.getCopyName().equals("")) {
                        throw new RuntimeException("著作权人姓名不能为空！");
                    }

                    // 法人(非自然人)
//          if(owner.getCopyCategory()!=CopyrightOwner.CopyCategoryValue.PEOPLE) continue;

                    // 著作权人证件
                    if (!ObjectUtils.isEmpty(owner.getCopyIdCardZM())) {
                        // 著作权人(自然人)无电子证照，需上传著作权人证件
                        if (owner.getCopyIdCardZM() == null || owner.getCopyIdCardZM().getId() == null) {
                            throw new RuntimeException("著作权人证件未上传完成，请返回等待附件上传完毕后再进行提交。");
                        }

                        if (owner.getCopyIdCardZM() != null && !isPic(owner.getCopyIdCardZM())) {
                            throw new RuntimeException("著作权人证件只允许上传jpg/png/jpeg格式，请重新上传。");
                        }
                    }

                    boolean needZM = false;
                    // 著作权人电子证照/中枢表单数据
                    if (!ObjectUtils.isEmpty(owner.getCopyCertificateZM()) && owner.getCopyCertificateZM().getId() != null) {
                        if (StringUtils.isEmpty(owner.getProjectId())) {
                            // 校验电子证照
                            CertificateEntity certificateEntity = certificateServiceFacade.findById(owner.getCopyCertificateZM().getId());
                            if (!certificateEntity.getCertificateHolderName().equals(owner.getCopyName()) || !certificateEntity.getCertificateHolderCode().equals(owner.getCopyIdCard())) {
                                throw new RuntimeException("著作权人 " + owner.getCopyName() + " 信息有误，请重新填写后再进行提交。");
                            }
                            owner.setFormDataId(null);
                        } else {
                            // 校验表单数据
                            FormDataEntity formDataEntity = outSideServiceFacade.findFormDataById(owner.getCopyCertificateZM().getId());
                            owner.setFormDataId(formDataEntity.getId());
                            owner.setCopyCertificateZM(null);
                            if (!formDataEntity.getApplyName().equals(owner.getCopyName())
                                    || !formDataEntity.getCertificateNumber().equals(owner.getCopyIdCard())) {
                                throw new RuntimeException("著作权人 " + owner.getCopyName() + " 信息有误，请重新填写作者信息后再进行提交。");
                            }
                            if (formDataEntity.getCertCountries() != owner.getCopyCountries()
                                    || formDataEntity.getCertProvince() != owner.getCopyProvince()
                                    || formDataEntity.getCertCity() != owner.getCopyCity()
                                    || formDataEntity.getCertCounty() != owner.getCopyCounty()) {
//                                throw new RuntimeException("著作权人 " + owner.getCopyName() + " 信息有误，请重新填写作者信息后再进行提交。");
                                needZM = true;
                            }
                        }
                    }

                    if (needZM && ObjectUtils.isEmpty(owner.getCopyIdCardZM())) {
                        throw new RuntimeException("著作权人证件信息不足，请返回补充证件后再进行提交。");
                    }

                    if (cardIds.contains(owner.getCopyIdCard()))
                        throw new RuntimeException("著作权人不可重复：" + owner.getCopyIdCard());
                    cardIds.add(owner.getCopyIdCard());

                    if (owner.getCopyCountries() == 0) {
                        throw new RuntimeException("著作权人地址有误！");
                    }
                    Digital country = digitalServiceFacade.getById(owner.getCopyCountries());
                    if (StringUtils.isEmpty(country.getDict_name()) || country.getLevel() != 1)
                        throw new RuntimeException("著作权人地址有误！");
                    String copyAreaNamesStr = country.getDict_name();
                    if (owner.getCopyProvince() != 0) {
                        Digital province = digitalServiceFacade.getById(owner.getCopyProvince());
                        if (StringUtils.isEmpty(province.getDict_name()) || province.getLevel() != 2)
                            throw new RuntimeException("著作权人地址有误！");
                        copyAreaNamesStr += "," + province.getDict_name();
                    }
                    if (owner.getCopyCity() != 0) {
                        Digital city = digitalServiceFacade.getById(owner.getCopyCity());
                        if (StringUtils.isEmpty(city.getDict_name()) || city.getLevel() != 3)
                            throw new RuntimeException("著作权人地址有误！");
                        copyAreaNamesStr += "," + city.getDict_name();
                    }
                    if (owner.getCopyCounty() != 0) {
                        Digital county = digitalServiceFacade.getById(owner.getCopyCounty());
                        if (StringUtils.isEmpty(county.getDict_name()) || county.getLevel() != 4)
                            throw new RuntimeException("著作权人地址有误！");
                        copyAreaNamesStr += "," + county.getDict_name();
                    }
                    owner.setCopyAreaNamesStr(copyAreaNamesStr);
                }
            } else {
                throw new RuntimeException("著作权人不能为空！");
            }

            if (copyrightManager.getAuthorList() != null && copyrightManager.getAuthorList().size() > 0) {
                for (Author author : copyrightManager.getAuthorList()) {
                    author.setAuthorName(author.getAuthorName().trim());

                    // 自然人无电子证照（证件要上传）
                    if (!ObjectUtils.isEmpty(author.getAuthorCertificateZM()) && author.getAuthorCertificateZM().getId() != null) {
                        if (StringUtils.isEmpty(author.getProjectId())) {
                            // 校验电子证照
                            CertificateEntity certificateEntity = certificateServiceFacade.findById(author.getAuthorCertificateZM().getId());
                            if (!certificateEntity.getCertificateHolderName().equals(author.getAuthorName()) || !certificateEntity.getCertificateHolderCode().equals(author.getAuthorIdCard())) {
                                throw new RuntimeException("作者 " + author.getAuthorName() + " 信息有误，请重新填写作者信息后再进行提交。");
                            }
                            author.setFormDataId(null);
                        } else {
                            // 校验表单数据
                            FormDataEntity formDataEntity = outSideServiceFacade.findFormDataById(author.getAuthorCertificateZM().getId());
                            author.setFormDataId(formDataEntity.getId());
                            if (!formDataEntity.getApplyName().equals(author.getAuthorName()) || !formDataEntity.getCertificateNumber().equals(author.getAuthorIdCard())) {
                                throw new RuntimeException("作者 " + author.getAuthorName() + " 信息有误，请重新填写作者信息后再进行提交。");
                            }
                            author.setAuthorCertificateZM(null);
                        }
                        author.setAuthorIdCar(null);
                    } else {
                        // 作者(自然人)无电子证照，需上传作者身份证件
                        if (author.getAuthorIdCar() == null || author.getAuthorIdCar().getId() == null) {
                            throw new RuntimeException("作者身份证未上传完成，请返回等待附件上传完毕后再进行提交。");
                        }

                        if (author.getAuthorIdCar() != null && !isPic(author.getAuthorIdCar())) {
                            throw new RuntimeException("作者身份证只允许上传jpg/png/jpeg格式，请重新上传。");
                        }
                        author.setAuthorCertificateZM(null);
                        author.setFormDataId(null);
                        author.setProjectId(null);
                    }

                    if (manager != null && author.getId() != null) {
                        author.setCopyright_id(manager.getRegistrationNum());
                    }
                }
            } else {
                throw new RuntimeException("作者不能为空！");
            }

            if (copyrightManager.getRightGuarantee() != null && copyrightManager.getRightGuarantee().getId() == null) {
                throw new RuntimeException("权利保证书未上传完成，请返回等待附件上传完毕后再进行提交。");
            }
            if (copyrightManager.getRightGuarantee() != null && !isPic2(copyrightManager.getRightGuarantee())) {
                throw new RuntimeException("权利保证书只允许上传gif/jpeg/jpg/bmp/png格式，请重新上传。");
            }
            if (copyrightManager.getAgentBook() != null && copyrightManager.getAgentBook().getId() == null) {
                throw new RuntimeException("代理委托书未上传完成，请返回等待附件上传完毕后再进行提交。");
            }
            if (copyrightManager.getAgentBook() != null && !isPic2(copyrightManager.getAgentBook())) {
                throw new RuntimeException("代理委托书只允许上传gif/jpeg/jpg/bmp/png格式，请重新上传。");
            }
            if (copyrightManager.getCooperationContract() != null && copyrightManager.getCooperationContract().getId() == null) {
                throw new RuntimeException("合作作品合同未上传完成，请返回等待附件上传完毕后再进行提交。");
            }
            if (copyrightManager.getCooperationContract() != null && !isPic2(copyrightManager.getCooperationContract())) {
                throw new RuntimeException("合作作品合同只允许上传gif/jpeg/jpg/bmp/png格式，请重新上传。");
            }
            if (copyrightManager.getPositionContract() != null && copyrightManager.getPositionContract().getId() == null) {
                throw new RuntimeException("职务作品合同未上传完成，请返回等待附件上传完毕后再进行提交。");
            }
            if (copyrightManager.getPositionContract() != null && !isPic2(copyrightManager.getPositionContract())) {
                throw new RuntimeException("职务作品合同只允许上传gif/jpeg/jpg/bmp/png格式，请重新上传。");
            }
            if (copyrightManager.getEntrustContract() != null && copyrightManager.getEntrustContract().getId() == null) {
                throw new RuntimeException("委托作品合同未上传完成，请返回等待附件上传完毕后再进行提交。");
            }
            if (copyrightManager.getEntrustContract() != null && !isPic2(copyrightManager.getEntrustContract())) {
                throw new RuntimeException("委托作品合同只允许上传gif/jpeg/jpg/bmp/png格式，请重新上传。");
            }
            if (copyrightManager.getTrusteeContract() != null && copyrightManager.getTrusteeContract().getId() == null) {
                throw new RuntimeException("受托合同书未上传完成，请返回等待附件上传完毕后再进行提交。");
            }
            if (copyrightManager.getTrusteeContract() != null && !isPic2(copyrightManager.getTrusteeContract())) {
                throw new RuntimeException("委托作品合同只允许上传gif/jpeg/jpg/bmp/png格式，请重新上传。");
            }
            if (copyrightManager.getSubmitter() != null && (copyrightManager.getSubmitter().getCopyrightName() == null || copyrightManager.getSubmitter().getCopyrightName().equals(""))) {
                throw new RuntimeException("由著作权人申请时，著作权人姓名不能为空！");
            }

            copyrightManager.setProductionName(copyrightManager.getProductionName().trim());
            if (StringUtils.isEmpty(copyrightManager.getProductionName())) {
                throw new RuntimeException("作品名称不可为空！");
            }
            if (copyrightManager.getProductionName().length() > 16) {
                throw new RuntimeException("作品名称长度有误！");
            }

            if (copyrightManager.getApplyType() == 2) {
                if (ObjectUtils.isEmpty(copyrightManager.getAgentList()) || !copyrightManager.getAgentList().getAgentName().equals(userEntity.getRealName())) {
                    throw new RuntimeException("代理人信息有误，请修改后再提交！");
                }
            } else if (copyrightManager.getApplyType() != 1) {
                throw new RuntimeException("办理方式信息有误，请修改后再提交！");
            }

            // 敏感词判断
            List<String> sensitiveList = sensitiveWordServiceFacade.getSensitiveWords();
            for (String sensitive : sensitiveList) {
                if (copyrightManager.getProductionName().contains(sensitive)) {
                    throw new RuntimeException("作品名称中不可出现敏感词！");
                }
            }

            if (copyrightManagerServiceFacade.countAllSame(copyrightManager) > 0) {
                throw new RuntimeException("著作权人【" + copyrightManager.getOwnerList().get(0).getCopyName() + "】申请中作品已有相同作品名称作品，请修改作品名称后再进行提交！");
            }

            copyrightManager.setUserName(userEntity.getUserName());
            //创建请求线程
//    OnlineSubmitThread onlineSubmitThread = new OnlineSubmitThread();
//    onlineSubmitThread.onlineSubmit(copyrightManager);
//    result.put("message", "作品提交完成！提交成功的作品可在我的作品列表查看。");
            int status_type = copyrightManager.getStatus_type();

            // 去掉线程
            copyrightManagerServiceFacade.onlineSubmit(copyrightManager, userEntity);
            result.put("message", "您提交的作品已受理成功！");
            log.info("作品受理成功");

            try {
                if (!StringUtils.isEmpty(copyrightManager.getProjectId()) && config.getBackendIsOn()) {
                    backendServiceFacade.projectIdActive(userEntity, copyrightManager.getProjectId());
                    List<ProcessProgressEntity> entities = processProgressService.findBySnCodeAndTypeAndState(copyrightManager.getSnCode(), ProcessProgressEntity.Type.PUSH, ProcessProgressEntity.State.FINISHED);
                    if (ObjectUtils.isEmpty(entities)) {
                        copyrightManager.setSyncStatus("I");
                        // 办件推送
                        backendServiceFacade.startWorkflow(copyrightManager, userEntity, ProcessProgressEntity.Type.PUSH, null, false);
                        // 表单数据回流
                        outSideServiceFacade.dataReflux(copyrightManager);
                    } // TODO 材料修改 U
                }
            } catch (Exception e) {
                log.debug("办件信息推送失败：{}", e.getMessage());
            }
        } catch (RuntimeException e) {
//            loglog(e);
            log.error("【作品提交失败】：{}, userId={}", e.getLocalizedMessage(), SecurityUtils.getCurrentUserId());
            result.put("message", e.getLocalizedMessage());
            result.put("errorMsg", e.getMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
//            loglog(e);
            log.error("作品提交失败：{}, userId={}", e.getLocalizedMessage(), SecurityUtils.getCurrentUserId());
            result.put("message", "作品提交失败，请稍后重试！");
            result.put("errorMsg", e.getMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    private void checkInvalidation(CopyrightManager copyrightManager) {
        if (StringUtils.isEmpty(copyrightManager.getProductionTypeId())) {
            throw new RuntimeException("作品类别不可为空，请重新选择！");
        } else {
            try {
                Digital productionType = digitalServiceFacade.getById(Integer.parseInt(copyrightManager.getProductionTypeId()));
                if (ObjectUtils.isEmpty(productionType) || productionType.getPid() != 82) {
                    throw new RuntimeException("作品类别有误，请重新选择！");
                }
            } catch (Exception e) {
                throw new RuntimeException("作品类别有误，请重新选择！");
            }
        }

        if (StringUtils.isEmpty(copyrightManager.getOpusInditekind())) {
            throw new RuntimeException("作品创作性质不可为空，请重新选择！");
        } else {
            try {
                Digital opus = digitalServiceFacade.getById(Integer.parseInt(copyrightManager.getOpusInditekind()));
                if (ObjectUtils.isEmpty(opus) || opus.getPid() != 126) {
                    throw new RuntimeException("作品创作性质有误，请重新选择！");
                }
            } catch (Exception e) {
                throw new RuntimeException("作品创作性质有误，请重新选择！");
            }
        }

        // fileType 上传文件类型

        if (StringUtils.isEmpty(copyrightManager.getFinishTime())) {
            throw new RuntimeException("作品完成日期不可为空，请重新选择！");
        } else if (!DateUtils.dateIsFuture(copyrightManager.getFinishTime())) {
            // 作品完成日期不能在当前日期之后
            throw new RuntimeException("作品完成日期有误，请重新选择！");
        }

        if (StringUtils.isEmpty(copyrightManager.getCompleteCountries())) {
            throw new RuntimeException("作品完成地点不可为空，请重新选择！");
        } else {
            try {
                Digital completeCountries = digitalServiceFacade.getById(Integer.parseInt(copyrightManager.getCompleteCountries()));
                if (ObjectUtils.isEmpty(completeCountries) || completeCountries.getPid() != 157) {
                    throw new RuntimeException("作品完成地点有误，请重新选择！");
                }
                if (!StringUtils.isEmpty(copyrightManager.getCompleteProvince())) {
                    Digital completeProvince = digitalServiceFacade.getById(Integer.parseInt(copyrightManager.getCompleteProvince()));
                    if (!completeProvince.getPid().equals(completeCountries.getId())) {
                        throw new RuntimeException("作品完成地点有误，请重新选择！");
                    }
                    if (!StringUtils.isEmpty(copyrightManager.getCompleteCity())) {
                        Digital completeCity = digitalServiceFacade.getById(Integer.parseInt(copyrightManager.getCompleteCity()));
                        if (!completeCity.getPid().equals(completeProvince.getId())) {
                            throw new RuntimeException("作品完成地点有误，请重新选择！");
                        }
                        if (!StringUtils.isEmpty(copyrightManager.getCompleteCounty())) {
                            Digital completeCounty = digitalServiceFacade.getById(Integer.parseInt(copyrightManager.getCompleteCounty()));
                            if (!completeCounty.getPid().equals(completeCity.getId())) {
                                throw new RuntimeException("作品完成地点有误，请重新选择！");
                            }
                        }
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException("作品完成地点有误，请重新选择！");
            }
        }

        if (StringUtils.isEmpty(copyrightManager.getCompleteArea())) {
            throw new RuntimeException("作品完成详细地点不可为空，请填写！");
        }

        if (copyrightManager.getPublishState() == 2) {
            // 首次发表日期不能在作品完成日期之前
            if (StringUtils.isEmpty(copyrightManager.getFirstPublishTime())) {
                throw new RuntimeException("首次发表日期不可为空，请重新选择！");
            } else if (!DateUtils.dateIsFuture(copyrightManager.getFirstPublishTime())
                    || DateUtils.dateCompare(copyrightManager.getFirstPublishTime(), copyrightManager.getFinishTime()) < 0) {
                // 首次发表日期不能在当前日期之后
                // 首次发表日期不能在作品完成日期之前
                throw new RuntimeException("首次发表日期有误，请重新选择！");
            }
        } else {
            copyrightManager.setFirstPublishTime(null);
            copyrightManager.setFirstCountry(null);
            copyrightManager.setFirstProvince(null);
            copyrightManager.setFirstCity(null);
            copyrightManager.setFirstCounty(null);
        }
        /**
         *
         "rightOwnMode":116,
         "obtainMode":122,
         rightGuarantee ismyfile
         "rightScope":1,
         */
    }

    private void loglog(Exception e) {
        StackTraceElement[] stackTraceElements = e.getStackTrace();
        for (StackTraceElement stackTrace : stackTraceElements) {
            log.error(stackTrace.toString());
            // 检查类名是否属于本工程（包含子包）
            if (stackTrace.getClassName().contains("blockChain.")) {
                log.error(stackTrace.toString());
            }
        }
    }

    private boolean isPic(UploadAttachment attachment) {
        boolean isPic = false;
        // gif/jpeg/jpg/bmp/png
        // png/jpg/gif/jpeg
        UploadAttachment attachment1 = uploadAttachmentServiceFacade.getById(attachment.getId());
        if (attachment1 == null)
            return false;
        String workname = attachment1.getWorkName();
        if (workname.endsWith(".jpg")
                || workname.endsWith(".JPG")
                || workname.endsWith(".png")
                || workname.endsWith(".PNG")
                || workname.endsWith(".jpeg")
                || workname.endsWith(".JPEG")) {
            isPic = true;
        }
        return isPic;
    }

    private boolean isPic2(UploadAttachment attachment) {
        boolean isPic = false;
        // gif/jpeg/jpg/bmp/png
        UploadAttachment attachment1 = uploadAttachmentServiceFacade.getById(attachment.getId());
        if (attachment1 == null)
            return false;
        String workname = attachment1.getWorkName();
        if (workname.endsWith(".jpg")
                || workname.endsWith(".JPG")
                || workname.endsWith(".png")
                || workname.endsWith(".PNG")
                || workname.endsWith(".jpeg")
                || workname.endsWith(".JPEG")
                || workname.endsWith(".gif")
                || workname.endsWith(".GIF")
                || workname.endsWith(".bmp")
                || workname.endsWith(".BMP")) {
            isPic = true;
        }
        return isPic;
    }

    private boolean isVide(UploadAttachment attachment) {
        boolean isPic = false;
        UploadAttachment attachment1 = uploadAttachmentServiceFacade.getById(attachment.getId());
        if (attachment1 == null)
            return false;
        String workname= attachment1.getWorkName();
        // mp4/MP3/wav
        if(workname.endsWith(".mp4")
                || workname.endsWith(".MP4")
                || workname.endsWith(".mp3")
                || workname.endsWith(".MP3")
                || workname.endsWith(".wav")
                || workname.endsWith(".WAV")) {
            isPic = true;
        }
        return isPic;
    }

    @ApiOperation("作品保存")
    @PostMapping("save")
    public ResponseEntity<Map<String, Object>> saveCopyright(@Valid @RequestBody CopyrightManager copyrightManager) {
        int currentUserId = SecurityUtils.getCurrentUserId();
        UserEntity userEntity = userServiceFacade.findById(currentUserId);

        Long registrationNum = copyrightManager.getRegistrationNum();
        CopyrightManager manager = null;
        String snCode = null;
        if (registrationNum != null && registrationNum > 0) {
            manager = copyrightManagerServiceFacade.getById(copyrightManager.getRegistrationNum());
            if (!manager.getUserName().equals(userEntity.getUserName()))
                throw new RuntimeException("权限错误");

            snCode = manager.getSnCode();
            if (StringUtils.isEmpty(copyrightManager.getSnCode())) {
                copyrightManager.setSnCode(snCode);
            }
            copyrightManager.setFlowRecord(copyrightManagerServiceFacade.getById(registrationNum).getFlowRecord());
        } else {
            copyrightManager.setUserName(userEntity.getUserName());
        }
        Map<String, Object> result = new HashMap<>();
        BiyiServiceThread biyiThread = new BiyiServiceThread();
        biyiThread.TextSimilarity(copyrightManager.getProductionName(), copyrightManager.getWorksNum());
        //著作权人信息填充
        Submitter submitter = copyrightManager.getSubmitter();
        List<CopyrightOwner> owners = copyrightManager.getOwnerList();
        if (owners != null && owners.size() > 0) {
            submitter.setCopyrightName(owners.get(0).getCopyName());
            // add 202212 著作权人重复判断 ，地址str 字典获取
            List<String> cardIds = new ArrayList();
            for (CopyrightOwner owner : copyrightManager.getOwnerList()) {
                owner.setCopyName(owner.getCopyName().trim());
                // 自然人无电子证照/表单id
                if (!ObjectUtils.isEmpty(owner.getCopyCertificateZM()) && owner.getCopyCertificateZM().getId() != null) {
                    if (StringUtils.isEmpty(owner.getProjectId())) {
                        owner.setFormDataId(null);
                    } else {
                        owner.setFormDataId(owner.getCopyCertificateZM().getId());
                        owner.setCopyCertificateZM(null);
                    }
                }

                if (cardIds.contains(owner.getCopyIdCard()))
                    throw new RuntimeException("著作权人不可重复：" + owner.getCopyIdCard());
                cardIds.add(owner.getCopyIdCard());

                Digital country = digitalServiceFacade.getById(owner.getCopyCountries());
                if (StringUtils.isEmpty(country.getDict_name()) || country.getLevel() != 1)
                    throw new RuntimeException("著作权人地址有误！");
                String copyAreaNamesStr = country.getDict_name();
                if (owner.getCopyProvince() != 0) {
                    Digital province = digitalServiceFacade.getById(owner.getCopyProvince());
                    if (StringUtils.isEmpty(province.getDict_name()) || province.getLevel() != 2)
                        throw new RuntimeException("著作权人地址有误！");
                    copyAreaNamesStr += "," + province.getDict_name();
                }
                if (owner.getCopyCity() != 0) {
                    Digital city = digitalServiceFacade.getById(owner.getCopyCity());
                    if (StringUtils.isEmpty(city.getDict_name()) || city.getLevel() != 3)
                        throw new RuntimeException("著作权人地址有误！");
                    copyAreaNamesStr += "," + city.getDict_name();
                }
                if (owner.getCopyCounty() != 0) {
                    Digital county = digitalServiceFacade.getById(owner.getCopyCounty());
                    if (StringUtils.isEmpty(county.getDict_name()) || county.getLevel() != 4)
                        throw new RuntimeException("著作权人地址有误！");
                    copyAreaNamesStr += "," + county.getDict_name();
                }
                owner.setCopyAreaNamesStr(copyAreaNamesStr);
            }
        }
        List<Author> authors = copyrightManager.getAuthorList();
        if (authors != null && authors.size() > 0) {
            for (Author author : authors) {
                author.setAuthorName(author.getAuthorName().trim());

                if (!ObjectUtils.isEmpty(author.getAuthorCertificateZM()) && author.getAuthorCertificateZM().getId() != null) {
                    if (StringUtils.isEmpty(author.getProjectId())) {
                        author.setProjectId(null);
                    } else {
                        author.setFormDataId(author.getAuthorCertificateZM().getId());
                        author.setAuthorCertificateZM(null);
                    }
                    author.setAuthorIdCar(null);
                } else {
                    author.setAuthorCertificateZM(null);
                    author.setFormDataId(null);
                    author.setProjectId(null);
                }
                if (manager != null && author.getId() != null) {
                    author.setCopyright_id(manager.getRegistrationNum());
                }
            }
        }

        // 敏感词判断
        if (!StringUtils.isEmpty(copyrightManager.getProductionName())) {
            List<String> sensitiveList = sensitiveWordServiceFacade.getSensitiveWords();
            for (String sensitive : sensitiveList) {
                if (copyrightManager.getProductionName().contains(sensitive)) {
                    throw new RuntimeException("作品名称中不可出现敏感词！");
                }
            }
        }

        copyrightManagerServiceFacade.saveCopyright(copyrightManager);
        result.put("message", "作品保存成功！");

        if (snCode != null) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserEntity loginUser = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());
            List<ProcessProgressEntity> entities = processProgressService.findBySnCodeAndTypeAndState(snCode, ProcessProgressEntity.Type.PUSH, ProcessProgressEntity.State.FINISHED);
            Optional<ProcessProgressEntity> entitiesOption = entities.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime));
            if (entitiesOption.isPresent()) {
                //办结
                try {
                    log.info("回收办结开始");
                    backendServiceFacade.startWorkflow(copyrightManager, loginUser, ProcessProgressEntity.Type.FINISH, "退件", false);
                    log.info("回收办结结束");
                } catch (Exception e) {
                    log.info("回收办结失败");
                }
            }
        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("作品删除")
    @PostMapping("remove")
    public ResponseEntity<Map<String, Object>> removeCopyright(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
            Integer userId = SecurityUtils.getCurrentUserId();
            UserDto userDto = userServiceFacade.findUserById(userId);
            CopyrightManager data = copyrightManagerServiceFacade.getById(queryParam.getLong("id"));
            if (data == null) {
                result.put("message", "未找到该作品！");
                return new ResponseEntity<>(result, HttpStatus.OK);
            }
            if (!data.getUserName().equals(userDto.getUserName())) {
                result.put("message", "权限错误！您无权删除！");
                return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
            }

            // 追加作品删除日志
            log.info(LocalDateTime.now() + "【删除作品：】 作品名称：" + data.getProductionName()
                    + ", 用户：" + data.getUserName());
            copyrightManagerServiceFacade.remove(data);
            result.put("message", "删除成功！");
            return new ResponseEntity<>(result, HttpStatus.OK);
        } catch (Exception e) {
            result.put("message", "删除失败！");
            result.put("errorMsg", e.getLocalizedMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
    }

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.FirstReview + "','" + AuthoritiesConstant.SecondReview + "','" + AuthoritiesConstant.FinalReview + "'" +
            ",'" + AuthoritiesConstant.Certifing + "')")
    @ApiOperation(value = "作品删除(管理员)", nickname = "createAlertMessage", notes = "disable a new instance of a `AlertMessage`.", tags = {"OnlineFillController", "作品删除(管理员)",})
    @ApiResponses(value = {
            @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class)})
    @PostMapping("su/remove")
    public ResponseEntity<Map<String, Object>> disableCopyright(@Valid @RequestBody MessageDtoQueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        Integer userId = SecurityUtils.getCurrentUserId();
        copyrightManagerServiceFacade.disableCopyright(queryParam.getIdList(), queryParam.getContent(), userId);
        result.put("message", "删除成功！");
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.FirstReview + "','" + AuthoritiesConstant.SecondReview + "','" + AuthoritiesConstant.FinalReview + "'" +
            ",'" + AuthoritiesConstant.Certifing + "','" + AuthoritiesConstant.MotifyList + "')")
    @ApiOperation("作品审批")
    @PostMapping("approval")
    public ResponseEntity<Map<String, Object>> approval(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (!userServiceFacade.isManager()) {
                result.put("message", "权限异常");
                return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
            }
            JSONArray idArray = queryParam.getJSONArray("idList");
            Byte isWorkstation;
            if (idArray != null && !idArray.isEmpty()) {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                UserEntity loginUser = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());
                isWorkstation = loginUser.getRole().getIsWorkstation();

                List<String> authorities = authentication.getAuthorities().stream().map(grantedAuthority -> {
                    if (grantedAuthority.getAuthority().equals(AuthoritiesConstant.FirstReview)
                            || grantedAuthority.getAuthority().equals(AuthoritiesConstant.SecondReview)
                            || grantedAuthority.getAuthority().equals(AuthoritiesConstant.FinalReview)
                            || grantedAuthority.getAuthority().equals(AuthoritiesConstant.Certifing)
                            || grantedAuthority.getAuthority().equals(AuthoritiesConstant.MotifyList)) {
                        return grantedAuthority.getAuthority();
                    }
                    return null;
                }).collect(Collectors.toList());

                List<Long> idList = JSONObject.parseArray(idArray.toJSONString(), Long.class);

                log.info(LocalDateTime.now() + " 【作品审批】开始, 当前状态:{}, 审批结果{}, 作品id{}", queryParam.getInteger("status_type"), queryParam.getString("approverResult"), idArray.toJSONString());
                for (Long id : idList) {
                    Integer state = queryParam.getInteger("status_type");
                    Integer restatus = queryParam.getInteger("restatus");
                    String approverResult = queryParam.getString("approverResult");
                    Integer uid = loginUser.getUserId();
                    //Long id = queryParam.getLong("id");
                    //设置流程记录
                    ProcessRecord processRecord = new ProcessRecord();
                    if (!state.equals(CopyrightManager.MODIFIED)) {
                        processRecord.setOpType(ProcessRecord.APPROVAL_NAME.get(state).toString());
                        processRecord.setOpResult(ProcessRecord.APPROVAL_NAME.get(state).toString() + ProcessRecord.APPROVAL_RESULT.get(approverResult));
                    } else {
                        processRecord.setOpType("撤回");
                        processRecord.setOpResult("材料修改撤回");
                    }
                    processRecord.setApproverOpinion(queryParam.getString("approverOpinion"));
                    CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);
                    if (state == CopyrightManager.UNSUBMIT) {
                        if (restatus != null) {
                            state = restatus;
                        } else {
                            state = CopyrightManager.FIRST_REVIEW;
                        }
                    } else {
                        if (config.getBackendIsOn())
                            backendServiceFacade.saveApprovalWorkFlow(state, approverResult, loginUser, copyrightManager);
                        if (approverResult.equals("pass")) {
                            switch (state) {
                                case CopyrightManager.FIRST_REVIEW:
                                case CopyrightManager.FIRST_REVIEW_REJECT:
                                    if (!authorities.contains(AuthoritiesConstant.FirstReview)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    //获取图片较对信息
                                    QueryParam queryParam1 = new QueryParam();
                                    queryParam1.put("id", id);
                                    Map<String, Object> cresult = getCompareResult(queryParam1);
                                    List<List<Map<String, Object>>> lists = cresult.containsKey("results") ? (List<List<Map<String, Object>>>) cresult.get("results") : null;
                                    if (lists != null && lists.size() > 0) {
                                        copyrightManager.setIsAccusation(true);
                                        boolean hasReject = cresult.containsKey("hasReject") ? (Boolean) cresult.get("hasReject") : false;
                                        copyrightManager.setIsAccusationReject(hasReject);
                                    }
                                    state = CopyrightManager.SECOND_REVIEW;
                                    break;
                                case CopyrightManager.SECOND_REVIEW:
                                case CopyrightManager.SECOND_REVIEW_REJECT:
                                    if (!authorities.contains(AuthoritiesConstant.SecondReview)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    state = CopyrightManager.FINAL_REVIEW;
                                    // 侵权设置单独接口，不在审核中设置/取消侵权
                                    // copyrightManager.setIsAccusation(queryParam.getBoolean("isAccusation"));
                                    break;
                                case CopyrightManager.FINAL_REVIEW:
                                case CopyrightManager.FINAL_REVIEW_REJECT:
                                    if (!authorities.contains(AuthoritiesConstant.FinalReview)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    //copyrightManagerServiceFacade.worksNumber(copyrightManager);
                                    state = CopyrightManager.CERT_CREATE;
                                    break;
//                                case CopyrightManager.CERT_CREATE:
//                                    state = CopyrightManager.CERT_CREATED;
//                                    break;
                                default:
                                    result.put("message", "作品" + copyrightManager.getWorksNum() + "状态无法被拒绝");
                                    return new ResponseEntity<>(result, HttpStatus.NOT_FOUND);
                            }
                        } else if (approverResult.equals("reject")) {
                            switch (state) {
                                case CopyrightManager.FIRST_REVIEW:
                                    if (!authorities.contains(AuthoritiesConstant.FirstReview)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    state = CopyrightManager.FIRST_REVIEW_REJECT;
                                    break;
                                case CopyrightManager.SECOND_REVIEW:
                                    if (!authorities.contains(AuthoritiesConstant.SecondReview)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    state = CopyrightManager.SECOND_REVIEW_REJECT;
                                    break;
                                case CopyrightManager.FINAL_REVIEW:
                                    if (!authorities.contains(AuthoritiesConstant.FinalReview)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    state = CopyrightManager.FINAL_REVIEW_REJECT;
                                    break;
                                case CopyrightManager.CERT_CREATE:
                                    if (!authorities.contains(AuthoritiesConstant.Certifing)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    state = CopyrightManager.CERT_CREATE_REJECT;
                                    break;
                                default:
                                    result.put("message", "作品" + copyrightManager.getWorksNum() + "状态无法被拒绝");
                                    return new ResponseEntity<>(result, HttpStatus.NOT_FOUND);
                            }
                            //发送拒绝消息通知
                            List<Integer> userId = new ArrayList<>(1);
                            UserDto userDto = userServiceFacade.getByUserName(copyrightManager.getUserName());
                            userId.add(userDto.getUserId());
                            String content = "尊敬的用户" + userDto.getRealName() + ",您的作品" + copyrightManager.getProductionName() +
                                    "已被拒绝，拒绝原因：" + queryParam.getString("approverOpinion") + ";被拒绝的作品无法再次进行更改或提交，请重新填写申请表。";
                            messageServiceFacade.sendMessageBase("作品拒绝通知", content, false, userId);
                        } else if (approverResult.equals("modify")) {
                            switch (state) {
                                case CopyrightManager.FIRST_REVIEW:
                                    if (!authorities.contains(AuthoritiesConstant.FirstReview)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    restatus = CopyrightManager.FIRST_REVIEW;
                                    break;
                                case CopyrightManager.SECOND_REVIEW:
                                    if (!authorities.contains(AuthoritiesConstant.SecondReview)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    restatus = CopyrightManager.SECOND_REVIEW;
                                    break;
                                case CopyrightManager.FINAL_REVIEW:
                                    if (!authorities.contains(AuthoritiesConstant.FinalReview)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    restatus = CopyrightManager.FINAL_REVIEW;
                                    break;
                                case CopyrightManager.CERT_CREATE:
                                    if (!authorities.contains(AuthoritiesConstant.Certifing)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    restatus = CopyrightManager.CERT_CREATE;
                                    break;
                                default:
                                    result.put("message", "作品" + copyrightManager.getWorksNum() + "状态无法被驳回");
                                    return new ResponseEntity<>(result, HttpStatus.NOT_FOUND);
                            }
                            //restatus = state;
                            state = CopyrightManager.MODIFIED;
                            copyrightManager.setCountDown(30);
                            //发送驳回消息通知
                            List<Integer> userId = new ArrayList<>(1);
                            UserDto userDto = userServiceFacade.getByUserName(copyrightManager.getUserName());
                            userId.add(userDto.getUserId());
                            String content = "尊敬的用户" + userDto.getRealName() + ",您的作品" + copyrightManager.getProductionName() +
                                    "已被驳回，驳回原因：" + queryParam.getString("approverOpinion") + ";被驳回的作品逾期30天未提交，系统将自动删除,请您尽快修改提交。";
                            messageServiceFacade.sendMessageBase("作品驳回通知", content, false, userId);
                        } else if (approverResult.equals("rollback")) {
                            switch (copyrightManager.getRestatus()) {
                                case CopyrightManager.FIRST_REVIEW:
                                case CopyrightManager.SECOND_REVIEW:
                                case CopyrightManager.FINAL_REVIEW:
                                case CopyrightManager.CERT_CREATE:
                                case CopyrightManager.REPORTING:
                                    if (!authorities.contains(AuthoritiesConstant.MotifyList)) {
                                        result.put("message", "权限异常");
                                        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
                                    }
                                    state = copyrightManager.getRestatus();
                                    break;
                                default:
                                    result.put("message", "作品" + copyrightManager.getWorksNum() + "无法被撤回");
                                    return new ResponseEntity<>(result, HttpStatus.NOT_FOUND);
                            }
                        }
                    }
                    if (copyrightManager == null) {
                        result.put("message", "未找到该作品");
                        return new ResponseEntity<>(result, HttpStatus.NOT_FOUND);
                    }
                    if (isWorkstation != null && isWorkstation == Constant.BYTE_TRUE && !copyrightManager.getUserName().equals(loginUser.getUserName())) {
                        result.put("message", "权限异常,请重新登录或联系管理员");
                        return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
                    }
                    //获取当前操作人
                    processRecord.setOpName(userServiceFacade.findUserById(uid).getRealName());

                    copyrightManager.setStatus_type(state);
                    copyrightManager.getFlowRecord().add(processRecord);
                    if (restatus != null) {
                        copyrightManager.setRestatus(restatus);
                    }
                    copyrightManagerServiceFacade.createCopyright(copyrightManager);
                }

                log.info(LocalDateTime.now() + " 【作品审批】结束, 当前状态:{}, 审批结果{}, 作品id{}", queryParam.getInteger("status_type"), queryParam.getString("approverResult"), idArray.toJSONString());
                result.put("message", "操作成功！");
                return new ResponseEntity<>(result, HttpStatus.OK);
            } else {
                result.put("message", "请选择作品");
                return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            result.put("message", "操作失败！");
            result.put("errosMsg", e.getLocalizedMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
    }

    @ApiOperation("用户作品回收")
    @PostMapping("recover")
    public ResponseEntity<Map<String, Object>> recover(@Valid @RequestBody QueryParam queryParam) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity loginUser = userServiceFacade.findById(((CscpUserDetail) authentication.getPrincipal()).getId());

        Map<String, Object> result = new HashMap<>();
        Long id = queryParam.getLong("id");
        try {
            CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);
            copyrightManager.setRestatus(copyrightManager.getStatus_type());
            copyrightManager.setStatus_type(CopyrightManager.UNSUBMIT);
            ProcessRecord processRecord = new ProcessRecord();
            UserDto userDto = userServiceFacade.getByUserName(copyrightManager.getUserName());
            processRecord.setOpName(userDto.getRealName());
            processRecord.setOpType("回收");
            copyrightManager.getFlowRecord().add(processRecord);
            copyrightManagerServiceFacade.createCopyright(copyrightManager);
            result.put("message", "流程回收成功！");


            List<ProcessProgressEntity> entities = processProgressService.findBySnCodeAndTypeAndState(copyrightManager.getSnCode(), ProcessProgressEntity.Type.PUSH, ProcessProgressEntity.State.FINISHED);
            Optional<ProcessProgressEntity> entitiesOption = entities.stream().max(Comparator.comparing(ProcessProgressEntity::getCreateTime));
            if(entitiesOption.isPresent()) {
                //办结
                try {
                    log.info("回收办结开始");
                    backendServiceFacade.startWorkflow(copyrightManager, loginUser, ProcessProgressEntity.Type.FINISH, "办结", false);
                    log.info("回收办结结束");
                }catch (Exception e){
                    log.info("回收办结失败");
                }
            }
            return new ResponseEntity<>(result, HttpStatus.OK);
        } catch (Exception e) {
            result.put("message", "操作失败！");
            result.put("errosMsg", e.getLocalizedMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
    }

    @ApiOperation("获取当前代理人填写过的著作权人")
    @PostMapping("getHistOwners")
    public ResponseEntity<Map<String, Object>> getHistOwners(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        Integer id = queryParam.getInteger("UID");
        UserDto userDto = userServiceFacade.findUserById(id);
        //获取代理人著作权人填写历史列表
        List<CopyrightOwner> ownerSelectList = ownerStatisticServiceFacade.getOwnersByUserId(userDto.getUserId());
        result.put("ownerSelectList", CopyrightOwnerDtoMapper.INSTANCE.entityToDto(ownerSelectList));
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("获取作品详情")
    @PostMapping("get")
    public ResponseEntity<CopyrightManagerDto> getCopyrightManager(@Valid @RequestBody QueryParam queryParam) {
        //Map<String,Object> result = new HashMap<>();
        CopyrightManager data = copyrightManagerServiceFacade.getById(queryParam.getLong("id"));
        CopyrightManagerDto dataDto = CopyrightManagerDtoMapper.INSTANCE.entityToDto(data);
        List<CopyrightOwnerDto> ownerList = dataDto.getOwnerList();
        for (CopyrightOwnerDto dto : ownerList) {
            if (StringUtils.isEmpty(dto.getProjectId()) || dto.getFormDataId() == null)
                continue;
            FormDataEntity formDataEntity = outSideServiceFacade.findFormDataById(dto.getFormDataId());
            FormDataDTO formDataDTO = FormDataMapper.INSTANCE.toDTO(formDataEntity);

            CertificateEntity certificateZM = new CertificateEntity();
            certificateZM.setId(formDataDTO.getId());
            certificateZM.setAreaNamesStr(formDataDTO.getAreaNamesStr());
            dto.setCopyCertificateZM(certificateZM);
//            dto.setCopyIdCardZM(null);
        }
        List<AuthorDto> authorList = dataDto.getAuthorList();
        for (AuthorDto dto : authorList) {
            if (StringUtils.isEmpty(dto.getProjectId()) || dto.getFormDataId() == null)
                continue;
            FormDataEntity formDataEntity = outSideServiceFacade.findFormDataById(dto.getFormDataId());
            FormDataDTO formDataDTO = FormDataMapper.INSTANCE.toDTO(formDataEntity);

            CertificateEntity certificateZM = new CertificateEntity();
            certificateZM.setId(formDataDTO.getId());
            certificateZM.setAreaNamesStr(formDataDTO.getAreaNamesStr());
            dto.setAuthorCertificateZM(certificateZM);
            dto.setAuthorIdCar(null);
        }
        //著作权人重排序
        Collections.sort(dataDto.getOwnerList(), (o1, o2) -> ((o1 != null && o1.getNoDeleteUpdate() != null) ? -1 : 1));

        boolean isOnLineFill = queryParam.getBooleanValue("isOnLineFill");
        if (isOnLineFill) {
            //处理流程记录的内容，隐藏操作人真实姓名
            for (ProcessRecordDto processRecord : dataDto.getFlowRecord()) {
                switch (processRecord.getOpType()) {
                    case "初审":
                        processRecord.setOpName("初审者");
                        break;
                    case "复审":
                        processRecord.setOpName("复审者");
                        break;
                    case "终审":
                        processRecord.setOpName("终审者");
                        break;
                    case "证书生成":
                        processRecord.setOpName("证书管理员");
                        break;
                    default:
                        break;
                }
            }
        }

        JSONArray idArray = queryParam.getJSONArray("idList");
        List<Long> ids = new ArrayList<>();
        if (idArray != null && !idArray.isEmpty()) {
            ids = JSONObject.parseArray(idArray.toJSONString(), Long.class);
        }
        Long currentId = data.getRegistrationNum();
        for (int i = 0; i < ids.size(); i++) {
            if (currentId.equals(ids.get(i))) {
                if (i > 0) {
                    CopyrightManager preData = copyrightManagerServiceFacade.getById(ids.get(i - 1));
                    dataDto.setPreId(preData.getRegistrationNum());
                    dataDto.setPreName(preData.getProductionName());
                    dataDto.setPreStatus(preData.getStatus_type());
                }
                if (i < ids.size() - 1) {
                    CopyrightManager nextData = copyrightManagerServiceFacade.getById(ids.get(i + 1));
                    dataDto.setNextId(nextData.getRegistrationNum());
                    dataDto.setNextName(nextData.getProductionName());
                    dataDto.setNextStatus(nextData.getStatus_type());
                }
                break;
            }
        }

        Map<String, Object> result = new HashMap<>();

        //获取数据字典
        //获取作品类型
        result.put("productionTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82)));
        //获取权利归属方式字典
        result.put("rightOwnModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(115)));
        //获取权利取得方式字典
        result.put("obtainModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(121)));
        //获取作品创作性质字典
        result.put("opusInditekindList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(126)));
        //获取著作权人类别字典
        result.put("manTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(134)));
        //获取著作权人证件类别字典
        result.put("manIdentityTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(141)));
        //获取国家字典
        result.put("CountryList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(157)));
        //获取省份字典
        //result.put("ProvinceList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(393)));
        //获取权力拥有状况字典
        result.put("rightScopeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(429)));
        dataDto.setDigitals(result);

        return new ResponseEntity<>(dataDto, HttpStatus.OK);
    }

    @ApiOperation("作品列表查询")
    @PostMapping("query")
    public ResponseEntity<Map<String, Object>> queryCopyrightManager(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();

        //调用比翼微服务
//    String url = config.getBiyiServiceUrl()+"getWeekOfYear";
//    Thread connect = new BiyiServiceThread(null, "GET", url);
//    connect.start();


        try {
            JSONArray array = queryParam.getJSONArray("status_type");
            List<Integer> statusList = new ArrayList<>();
            if (array != null && !array.isEmpty()) {
                statusList = JSONObject.parseArray(array.toJSONString(), Integer.class);
            }
            JSONArray tagArray = queryParam.getJSONArray("tags");
            List<Integer> tags = new ArrayList<>();
            if (tagArray != null && !tagArray.isEmpty()) {
                tags = JSONObject.parseArray(tagArray.toJSONString(), Integer.class);
            }
            //判断是否为用户查询，若是则只查询当前用户的作品
            String userName = null;
            UserDto userDto = new UserDto();

            boolean isOnLineFill = queryParam.getBooleanValue("isOnLineFill");
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (isOnLineFill) {
//          userDto = userServiceFacade.findUserById(queryParam.getInteger("UID"));
                userDto = userServiceFacade.findUserById(((CscpUserDetail) authentication.getPrincipal()).getId());
                if (userDto != null) {
                    userName = userDto.getUserName();
                } else {
                    userDto = new UserDto();
                }
            } else if (userServiceFacade.isWorkStation() || !userServiceFacade.isManager()) {
                userName = ((CscpUserDetail) authentication.getPrincipal()).getUsername();
            }

            Page<CopyrightManager> copyrightManagerPage;
            if (queryParam.containsKey("isAsc")) {
                copyrightManagerPage = copyrightManagerServiceFacade.query(isOnLineFill, queryParam.getString("worksNum"), queryParam.getString("productionName"), queryParam.getString("productionTypeId"),
                        queryParam.getInteger("rightOwnMode"), queryParam.getString("startTime"), queryParam.getString("endTime"), statusList, userName, queryParam.getString("agentName"), queryParam.getString("copyrightName"),
                        null, null, null, userDto.getVisibleLevelCity(), userDto.getVisibleLevelCounty(), new Long(queryParam.getPage()).intValue() - 1, new Long(queryParam.getPageSize()).intValue(), queryParam.getInteger("evaluateMin"), queryParam.getInteger("evaluateMax")
                        , null, null, true, null, tags, queryParam.getInteger("isAccusation"), queryParam.getInteger("focusWork"));
            } else {
                copyrightManagerPage = copyrightManagerServiceFacade.query(isOnLineFill, queryParam.getString("worksNum"), queryParam.getString("productionName"), queryParam.getString("productionTypeId"),
                        queryParam.getInteger("rightOwnMode"), queryParam.getString("startTime"), queryParam.getString("endTime"), statusList, userName, queryParam.getString("agentName"), queryParam.getString("copyrightName"),
                        null, null, null, userDto.getVisibleLevelCity(), userDto.getVisibleLevelCounty(), new Long(queryParam.getPage()).intValue() - 1, new Long(queryParam.getPageSize()).intValue(), queryParam.getInteger("evaluateMin"), queryParam.getInteger("evaluateMax")
                        , null, null, null, tags, queryParam.getInteger("isAccusation"), queryParam.getInteger("focusWork"));
            }
            List<CopyrightManager> list = copyrightManagerPage.getContent();
/*      for(CopyrightManager copyrightManager:list)
      {
        BiyiServiceThread biyiThread=new BiyiServiceThread();
        biyiThread.TextCorrection(copyrightManager.getProductionName());
      }
*/
            List<Long> idList = new ArrayList<>(list.size());
            for (CopyrightManager copyrightManager : list) {
                idList.add(copyrightManager.getRegistrationNum());
            }

            result.put("listData", CopyrightManagerDtoMapper.INSTANCE.entityToDto(list));
            result.put("idList", idList);
            //设置分页项
            result.put("total", copyrightManagerPage.getTotalElements());
            result.put("page", queryParam.getPage());
            result.put("pageSize", queryParam.getPageSize());
            //获取数据字典
            //获取作品类型
            result.put("productionTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82)));
            //获取权利归属方式字典
            result.put("rightOwnModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(115)));
            //获取权利取得方式字典
            result.put("obtainModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(121)));
            //获取作品创作性质字典
            result.put("opusInditekindList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(126)));
            //获取著作权人类别字典
            result.put("manTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(134)));
            //获取著作权人证件类别字典
            result.put("manIdentityTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(141)));
            //获取国家字典
            result.put("CountryList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(157)));
            //获取省份字典
            //result.put("ProvinceList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(393)));
            //获取权力拥有状况字典
            result.put("rightScopeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(429)));
            //获取审批状态
            result.put("statusList", getApprovalType());
            result.put("GetWeekOfYear", biyiService.GetWeekOfYear());
            result.put("message", "查询成功！");
        } catch (Exception e) {
            log.error("error：{}", e);
            result.put("message", "查询失败！");
            result.put("errorCode", e.getLocalizedMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        } finally {
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
    }

    @ApiOperation("短信身份验证")
    @PostMapping("messageLogin")
    public Boolean messageLogin() {
        Client client = Client.getInstance();

        //String testUrl = "http://112.33.1.13/app/sdk/login";

        String officalUrl = "http://mas.ecloud.10086.cn/app/sdk/login";

        String userName = "sxsms1";

        String password = "sxsms@XU8K";
        Boolean loginresult = client.login(officalUrl, userName, password, "福建省委宣传部");
        return loginresult;
    }

  /*@ApiOperation("短信发送")
  @PostMapping("messageSend")
  public void messageSend() {
    try {
      SmsTesta2.main();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }*/

    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.AdminView + "')")
    @ApiOperation("管理员纸质证书查询")
    @PostMapping("managerQuery")
    public ResponseEntity<Map<String, Object>> managerQuery(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
            JSONArray array = queryParam.getJSONArray("status_type");
            List<Integer> statusList = new ArrayList<>();
            if (array != null && !array.isEmpty()) {
                statusList = JSONObject.parseArray(array.toJSONString(), Integer.class);
            }
            JSONArray tagArray = queryParam.getJSONArray("tags");
            List<Integer> tags = new ArrayList<>();
            if (tagArray != null && !tagArray.isEmpty()) {
                tags = JSONObject.parseArray(tagArray.toJSONString(), Integer.class);
            }
            //判断是否为用户查询，若是则只查询当前用户的作品
            String userName = null;
            UserDto userDto = new UserDto();
            boolean isOnLineFill = queryParam.getBooleanValue("isOnLineFill");
            if (isOnLineFill) {
                userDto = userServiceFacade.findUserById(queryParam.getInteger("UID"));
                if (userDto != null) {
                    userName = userDto.getUserName();
                } else {
                    userDto = new UserDto();
                }
            }
            Page<CopyrightManager> copyrightManagerPage = copyrightManagerServiceFacade.query(null, queryParam.getString("worksNum"), queryParam.getString("productionName"), queryParam.getString("productionTypeId"),
                    queryParam.getInteger("rightOwnMode"), queryParam.getString("startTime"), queryParam.getString("endTime"), statusList, userName, queryParam.getString("agentName"), queryParam.getString("copyrightName"),
                    null, null, null, queryParam.getInteger("copyCity"), queryParam.getInteger("copyCounty"), new Long(queryParam.getPage()).intValue() - 1, new Long(queryParam.getPageSize()).intValue(), queryParam.getInteger("evaluateMin"), queryParam.getInteger("evaluateMax"),
                    null, null, true, queryParam.getInteger("copyProvince"), tags, queryParam.getInteger("isAccusation"), queryParam.getInteger("focusWork"));
            List<CopyrightManager> list = copyrightManagerPage.getContent();
            result.put("listData", CopyrightManagerDtoMapper.INSTANCE.entityToDto(list));
            //设置分页项
            result.put("total", copyrightManagerPage.getTotalElements());
            result.put("page", queryParam.getPage());
            result.put("pageSize", queryParam.getPageSize());
            //获取数据字典
            //获取作品类型
            result.put("productionTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82)));
            //获取权利归属方式字典
            result.put("rightOwnModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(115)));
            //获取权利取得方式字典
            result.put("obtainModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(121)));
            //获取作品创作性质字典
            result.put("opusInditekindList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(126)));
            //获取著作权人类别字典
            result.put("manTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(134)));
            //获取著作权人证件类别字典
            result.put("manIdentityTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(141)));
            //获取国家字典
            result.put("CountryList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(157)));
            //获取省份字典
            //result.put("ProvinceList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(393)));
            //获取权力拥有状况字典
            result.put("rightScopeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(429)));
            //获取审批状态
            result.put("statusList", getApprovalType());
            result.put("message", "查询成功！");
        } catch (Exception e) {
            result.put("message", "查询失败！");
            result.put("errorCode", e.getLocalizedMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        } finally {
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
    }


    @PreAuthorize("hasAnyAuthority('" + AuthoritiesConstant.RejectList + "')")
    @ApiOperation("撤回拒绝作品")
    @PostMapping("revokeRefuse")
    public ResponseEntity<Map<String, Object>> revokeRefuse(@Valid @RequestBody CopyrightManager copyrightManager) {
        Map<String, Object> result = new HashMap<>();
        CopyrightManager manager = copyrightManagerServiceFacade.getById(copyrightManager.getRegistrationNum());
        if (manager == null) {
            result.put("message", "无此作品！");
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
        if (manager.getStatus_type() == CopyrightManager.FIRST_REVIEW_REJECT || manager.getStatus_type() == CopyrightManager.SECOND_REVIEW_REJECT || manager.getStatus_type() == CopyrightManager.FINAL_REVIEW_REJECT
                || manager.getStatus_type() == CopyrightManager.CERT_CREATE_REJECT) {
            switch (manager.getStatus_type()) {
                case CopyrightManager.FIRST_REVIEW_REJECT:
                    manager.setStatus_type(CopyrightManager.FIRST_REVIEW);
                    break;
                case CopyrightManager.SECOND_REVIEW_REJECT:
                    manager.setStatus_type(CopyrightManager.SECOND_REVIEW);
                    break;
                case CopyrightManager.FINAL_REVIEW_REJECT:
                    manager.setStatus_type(CopyrightManager.FINAL_REVIEW);
                    break;
                case CopyrightManager.CERT_CREATE_REJECT:
                    manager.setStatus_type(CopyrightManager.CERT_CREATE);
                    break;
            }
            copyrightManagerServiceFacade.save(manager);
            result.put("message", "成功！");
        } else {
            result.put("message", "作品状态不正确！");
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("作品全局查询")
    @PostMapping("globleQuery")
    public ResponseEntity<Map<String, Object>> globleQuery(@Valid @RequestBody QueryParam queryParam, HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        int paramCount = 0;
        try {
            JSONArray array = queryParam.getJSONArray("status_type");
            List<Integer> statusList = new ArrayList<>();
            if (array != null && !array.isEmpty()) {
                statusList = JSONObject.parseArray(array.toJSONString(), Integer.class);
            }

            //查询验证
            String kaptchaCode = queryParam.getString("kaptchaCode");
            String rightCode = (String) request.getSession().getAttribute("rightCode");
            if (!rightCode.equals(kaptchaCode)) {
                result.put("message", "验证码错误！");
                return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
            }

            String worksNum = queryParam.getString("worksNum");
            if (!StringUtils.isEmpty(worksNum)) paramCount++;

            String productionName = queryParam.getString("productionName");
            if (!StringUtils.isEmpty(productionName)) paramCount++;

            String productionTypeId = queryParam.getString("productionTypeId");
            if (!StringUtils.isEmpty(productionTypeId)) paramCount++;

            Integer rightOwnMode = queryParam.getInteger("rightOwnMode");
            if (rightOwnMode != null && rightOwnMode > 0) paramCount++;

            String startTime = queryParam.getString("startTime");
            if (!StringUtils.isEmpty(startTime)) paramCount++;

            String endTime = queryParam.getString("endTime");
            if (!StringUtils.isEmpty(endTime)) paramCount++;

            String agentName = queryParam.getString("agentName");
            if (!StringUtils.isEmpty(agentName)) paramCount++;

            String copyrightName = queryParam.getString("copyrightName");
            if (!StringUtils.isEmpty(copyrightName)) paramCount++;

            String certificateStartDate = queryParam.getString("certificateStartDate");
            if (!StringUtils.isEmpty(certificateStartDate)) paramCount++;

            String certificateEndDate = queryParam.getString("certificateEndDate");
            if (!StringUtils.isEmpty(certificateEndDate)) paramCount++;

            if (paramCount < 3) {
                result.put("message", "搜索前至少填写3项搜索条件！");
                return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
            }
            Page<CopyrightManager> copyrightManagerPage = copyrightManagerServiceFacade.queryByCertificate(worksNum, productionName, productionTypeId,
                    rightOwnMode, startTime, endTime, statusList, null, agentName, copyrightName,
                    null, null, null, null, null, certificateStartDate, certificateEndDate, new Long(queryParam.getPage()).intValue() - 1, new Long(queryParam.getPageSize()).intValue());
            List<CopyrightManager> list = copyrightManagerPage.getContent();
            result.put("listData", CopyrightManagerDtoMapper.INSTANCE.entityToDto(list));
            //设置分页项
            result.put("total", copyrightManagerPage.getTotalElements());
            result.put("page", queryParam.getPage());
            result.put("pageSize", queryParam.getPageSize());
            //获取数据字典
            //获取作品类型
            result.put("productionTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(82)));
            //获取权利归属方式字典
            result.put("rightOwnModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(115)));
            //获取权利取得方式字典
            result.put("obtainModeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(121)));
            //获取作品创作性质字典
            result.put("opusInditekindList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(126)));
            //获取著作权人类别字典
            result.put("manTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(134)));
            //获取著作权人证件类别字典
            result.put("manIdentityTypeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(141)));
            //获取国家字典
            result.put("CountryList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(157)));
            //获取省份字典
            //result.put("ProvinceList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(393)));
            //获取权力拥有状况字典
            result.put("rightScopeList", DigitalDtoMapper.INSTANCE.entityToDto(digitalServiceFacade.getDictByPid(429)));
            //获取审批状态
            result.put("statusList", getApprovalType());
            result.put("message", "查询成功！");
            return new ResponseEntity<>(result, HttpStatus.OK);
        } catch (Exception e) {
            result.put("message", "查询失败！");
            result.put("errorCode", e.getLocalizedMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
    }

    private List<Map<String, Object>> getApprovalType() {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>() {{
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.FINAL_REVIEW);
                put("name", "终审中");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.SECOND_REVIEW);
                put("name", "复审中");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.FIRST_REVIEW);
                put("name", "初审中");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.CERT_CREATE);
                put("name", "证书生成中");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.CERT_CREATED);
                put("name", "证书已生成");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.MODIFIED);
                put("name", "材料修改");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.CERT_CREATE_REJECT);
                put("name", "证书生成拒绝");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.FINAL_REVIEW_REJECT);
                put("name", "终审拒绝");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.SECOND_REVIEW_REJECT);
                put("name", "复审拒绝");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.FIRST_REVIEW_REJECT);
                put("name", "初审拒绝");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.UNSUBMIT);
                put("name", "未提交");
            }});
            add(new HashMap<String, Object>() {{
                put("id", CopyrightManager.REPORTING);
                put("name", "证书正在生成");
            }});
        }};
        return result;
    }


    @ApiOperation("证书生成")
    @PostMapping(value = "certificateCreate", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResponseEntity<Map<String, Object>> certificateCreate(@Valid @RequestBody QueryParam queryParam, HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        Integer uid = queryParam.getInteger("UID");

        if (!userServiceFacade.isManager() || userServiceFacade.isWorkStation()) {
            result.put("message", "权限异常！");
            return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        UserDto userDto = userServiceFacade.findUserById(uid);
        try {
            JSONArray idArray = queryParam.getJSONArray("idList");
            if (idArray == null || idArray.isEmpty()) {
                result.put("message", "请选择证书！");
                return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
            }
            result.put("message", "已发起后台证书生成，添加了" + copyrightManagerServiceFacade.certificateCreateRun(queryParam, request, response, userDto.getRealName()) + "个任务，请耐心等待结果");
            //Thread.sleep(3000);
            return new ResponseEntity<>(result, HttpStatus.OK);
        } catch (Exception e) {
            log.error("error：{}", e);
            result.put("message", "证书生成启动失败");
            return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @ApiOperation("作品上链")
    @PostMapping("onBlockChain")
    public ResponseEntity<Map<String, Object>> onBlockChain(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        String postUrl = config.getBlockUrl();
        String contextPath = "File/" + config.getModelRoot();
        try {
            JSONArray idArray = queryParam.getJSONArray("idList");
            if (idArray != null && !idArray.isEmpty()) {
                List<Long> idList = JSONObject.parseArray(idArray.toJSONString(), Long.class);
                for (Long id : idList) {
                    CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);
                    String certUrl = copyrightManager.getCertificateUrl();
                    /*String imgUrl = request.getSession().getServletContext().getRealPath("/fileModel/"+id) + "/QRCode.jpg";*/
                    String imgUrl = contextPath + certUrl;
                    JSONObject onBlockResult = blockChain(copyrightManager, imgUrl, postUrl);
                    if (onBlockResult != null && onBlockResult.containsKey("result")) {
                        copyrightManager.setOnBlockToken(onBlockResult.getString("result"));

                        if (copyrightManager.getFileType() == 1) {
                            String sampleUrl = copyrightManager.getSampleUrl();
                            String sampleImgUrl = contextPath + sampleUrl;
                            JSONObject onBlockResult2 = blockChain(copyrightManager, sampleImgUrl, postUrl);
                            if (onBlockResult2 != null && onBlockResult2.containsKey("result")) {
                                copyrightManager.setOnBlockSampleToken(onBlockResult2.getString("result"));
                            }
                        }
                        copyrightManagerServiceFacade.createCopyright(copyrightManager);
                    } else {
                        result.put("message", "上链失败！");
                        result.put("errorMsg", "上链解锁账号失败");
                        return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
                    }

                }
                result.put("message", "上链成功！");
                return new ResponseEntity<>(result, HttpStatus.OK);
            } else {
                result.put("message", "无作品id");
                return new ResponseEntity<>(result, HttpStatus.OK);
            }
        } catch (Exception e) {
            result.put("message", "上链失败！");
            result.put("errorMsg", e.getLocalizedMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
    }

    private JSONObject blockChain(CopyrightManager copyrightManager, String imgUrl, String postUrl) throws Exception {
        String HexStr = ImageUtil.ImageToHex(imgUrl);
        Map<String, Object> param = new HashMap<>();
        param.put("jsonrpc", "2.0");
        param.put("method", "personal_unlockAccount");
        param.put("params", new ArrayList<Object>() {{
            add("******************************************");
            add("123456");
            add(120);
        }});
        param.put("id", 1);
        String unlockParamStr = (new JSONObject(param)).toJSONString();
        String getUnlockResult = HttpUtil.requestPost(postUrl, unlockParamStr);
        JSONObject unlockResult = JSONObject.parseObject(getUnlockResult);
        if (unlockResult.containsKey("result") && unlockResult.getBooleanValue("result")) {
            //上链
            param.clear();
            param.put("jsonrpc", "2.0");
            param.put("method", "eth_sendTransaction");
            param.put("params", new ArrayList<Object>() {{
                add(new HashMap<String, Object>() {{
                    put("from", "******************************************");
                    put("to", "******************************************");
                    put("value", "0x1");
                    put("data", HexStr);
                }});
            }});
            param.put("id", 1);
            String paramStr = (new JSONObject(param)).toJSONString();
            String getOnBlockResult = HttpUtil.requestPost(postUrl, paramStr);
            JSONObject onBlockResult = JSONObject.parseObject(getOnBlockResult);
            return onBlockResult;
        } else {
            return null;
        }
    }

    @ApiOperation("作品获链")
    @PostMapping("getBlockChain_old")
    public ResponseEntity<Map<String, Object>> getBlockChain_old(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();

        String postUrl = config.getBlockUrl();
        //String contextPath = "File/";
        Long id = queryParam.getLong("id");
        CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);
        String token = copyrightManager.getOnBlockToken();
        try {
            //获取区块链上数据
            Map<String, Object> param = new HashMap<>();
            param.put("jsonrpc", "2.0");
            param.put("method", "eth_getTransactionByHash");
            param.put("params", new ArrayList<Object>() {{
                add(token);
            }});
            param.put("id", 1);
            String getParamStr = (new JSONObject(param)).toJSONString();
            String getResultStr = HttpUtil.requestPost(postUrl, getParamStr);
            JSONObject getResult = getResultStr != null ? JSONObject.parseObject(getResultStr) : null;

            if (getResult != null && getResult.containsKey("result")) {
                String input = getResult.getJSONObject("result").getString("input");
                String blockNum = getResult.getJSONObject("result").getString("blockNumber");
                //将原有证书再次转码
                String certUrl = copyrightManager.getCertificateUrl();
                /*String imgUrl = request.getSession().getServletContext().getRealPath("/fileModel/"+id) + "/QRCode.jpg";*/
                String imgUrl = System.getProperty("user.dir") + File.separator + certUrl;
                String HexStr = ImageUtil.ImageToHex(imgUrl);
                if (input.equalsIgnoreCase(HexStr)) {
                    result.put("message", "获取证书成功！");
                    result.put("url", certUrl);
                    result.put("blockNumberHex", blockNum);
                    result.put("md5", input);
                    result.put("blockNumber", Integer.parseInt(blockNum.replace("0x", ""), 16));
                    return new ResponseEntity<>(result, HttpStatus.OK);
                } else {
                    if (!copyrightManager.getRegistrationDate().isBefore(LocalDateTime.of(2020, 9, 8, 9, 0, 0))) {
                        result.put("message", "证书与区块链数据不匹配！");
                        return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
                    } else {
                        result.put("message", "获取证书成功！");
                        result.put("url", certUrl);
                        result.put("blockNumberHex", blockNum);
                        result.put("md5", input);
                        result.put("blockNumber", Integer.parseInt(blockNum.replace("0x", ""), 16));
                        return new ResponseEntity<>(result, HttpStatus.OK);
                    }
                }
            } else {
                //旧系统未上链的证书过滤
                if (!copyrightManager.getRegistrationDate().isBefore(LocalDateTime.of(2020, 9, 8, 9, 0, 0))) {
                    result.put("message", "获取证书失败！");
                    return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
                } else {
                    result.put("message", "获取证书成功！");
                    result.put("url", copyrightManager.getCertificateUrl());
                    result.put("blockNumberHex", "null");
                    result.put("md5", "null");
                    result.put("blockNumber", 0);
                    return new ResponseEntity<>(result, HttpStatus.OK);
                }
            }
        } catch (NullPointerException e) {
            try {
                //将原有证书再次转码
                String certUrl = copyrightManager.getCertificateUrl();
                /*String imgUrl = request.getSession().getServletContext().getRealPath("/fileModel/"+id) + "/QRCode.jpg";*/
                String imgUrl = System.getProperty("user.dir") + File.separator + certUrl;
                String HexStr = ImageUtil.ImageToHex(imgUrl);
                result.put("message", "获取证书成功！");
                result.put("url", copyrightManager.getCertificateUrl());
                result.put("blockNumberHex", "等待生成");
                result.put("md5", "等待生成");
                result.put("blockNumber", "等待生成");
                return new ResponseEntity<>(result, HttpStatus.OK);
            } catch (Exception e1) {
                result.put("message", "获取证书失败！");
                log.info(e1.getLocalizedMessage());
                return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e2) {
            result.put("message", "获取证书失败！");
            log.info(e2.getLocalizedMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("getBlockChain")
    public ResponseEntity<Map<String, Object>> getBlockChain(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        Long id = queryParam.getLong("id");
        CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);
        if (copyrightManager.getStatus_type() != CopyrightManager.REPORTING && copyrightManager.getStatus_type() != CopyrightManager.CERT_CREATED) {
            result.put("message", "当前信息已过期，请刷新列表！");
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
        if (copyrightManager.getStatus_type() == CopyrightManager.REPORTING) {
            result.put("message", "证书生成中，请稍后再查询");
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
        if (StringUtils.isEmpty(copyrightManager.getCertificateUrl())) {
            copyrightManager.setStatus_type(CopyrightManager.REPORTING);
            copyrightManagerService.saveOrUpdateCopyright(copyrightManager);
            result.put("message", "证书生成中，请稍后再查询");
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
        result = getBlockChainDetail(copyrightManager, "certificateUrl");
        if (result != null && result.containsKey("url")) {

            if (StringUtils.isEmpty(copyrightManager.getSampleUrl()))
                return new ResponseEntity<>(result, HttpStatus.OK);

            Map<String, Object> result2 = getBlockChainDetail(copyrightManager, "sampleUrl");
            if (result2 != null && result2.containsKey("url")) {
                result.put("sampleUrl", result2.get("url"));
                result.put("samp_blockNumberHex", result2.get("blockNumberHex"));
                result.put("samp_md5", result2.get("md5"));
                result.put("samp_blockNumber", result2.get("blockNumber"));
            }
            result.put("samp_message", result2.get("message"));
            return new ResponseEntity<>(result, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
    }

    private Map<String, Object> getBlockChainDetail(CopyrightManager copyrightManager, String field) {
        Map<String, Object> result = new HashMap<>();
        String postUrl = config.getBlockUrl();
        String token;
        String certUrl;
        if ("certificateUrl".equals(field)) {
            token = copyrightManager.getOnBlockToken();
            certUrl = copyrightManager.getCertificateUrl();
        } else {
            token = copyrightManager.getOnBlockSampleToken();
            certUrl = copyrightManager.getSampleUrl();
        }
        try {
            //获取区块链上数据
            Map<String, Object> param = new HashMap<>();
            param.put("jsonrpc", "2.0");
            param.put("method", "eth_getTransactionByHash");
            param.put("params", new ArrayList<Object>() {{
                add(token);
            }});
            param.put("id", 1);
            String getParamStr = (new JSONObject(param)).toJSONString();
            String getResultStr = HttpUtil.requestPost(postUrl, getParamStr);
            JSONObject getResult = getResultStr != null ? JSONObject.parseObject(getResultStr) : null;

            if (getResult != null && getResult.containsKey("result")) {
                String input = getResult.getJSONObject("result").getString("input");
                String blockNum = getResult.getJSONObject("result").getString("blockNumber");
                //将原有证书再次转码
                String imgUrl = System.getProperty("user.dir") + File.separator + certUrl;
                String HexStr = ImageUtil.ImageToHex(imgUrl);
                if (input.equalsIgnoreCase(HexStr)) {
                    result.put("message", "获取证书成功！");
                    result.put("url", certUrl);
                    result.put("blockNumberHex", blockNum);
                    result.put("md5", input);
                    result.put("blockNumber", Integer.parseInt(blockNum.replace("0x", ""), 16));
                    return result;
                } else {
                    if (!copyrightManager.getRegistrationDate().isBefore(LocalDateTime.of(2020, 9, 8, 9, 0, 0))) {
                        result.put("message", "证书与区块链数据不匹配！");
                        return result;
                    } else {
                        result.put("message", "获取证书成功！");
                        result.put("url", certUrl);
                        result.put("blockNumberHex", blockNum);
                        result.put("md5", input);
                        result.put("blockNumber", Integer.parseInt(blockNum.replace("0x", ""), 16));
                        return result;
                    }
                }
            } else {
                //旧系统未上链的证书过滤
                if (!copyrightManager.getRegistrationDate().isBefore(LocalDateTime.of(2020, 9, 8, 9, 0, 0))) {
                    result.put("message", "获取证书失败！");
                    return result;
                } else {
                    result.put("message", "获取证书成功！");
                    result.put("url", certUrl);
                    result.put("blockNumberHex", "null");
                    result.put("md5", "null");
                    result.put("blockNumber", 0);
                    return result;
                }
            }
        } catch (NullPointerException e) {
            try {
                //将原有证书再次转码
                result.put("message", "获取证书成功！");
                result.put("url", certUrl);
                result.put("blockNumberHex", "等待生成");
                result.put("md5", "等待生成");
                result.put("blockNumber", "等待生成");
                return result;
            } catch (Exception e1) {
                result.put("message", "获取证书失败！");
                log.info(e1.getLocalizedMessage());
                return result;
            }
        } catch (Exception e2) {
            result.put("message", "获取证书失败！");
            log.info(e2.getLocalizedMessage());
            return result;
        }
    }

    public static ProcessRecord processRecordCreate(String opType, String opResult, String approverOpinion, String userName) {
        //设置流程记录
        ProcessRecord processRecord = new ProcessRecord();
        processRecord.setOpType(opType);
        processRecord.setOpResult(opResult);
        processRecord.setApproverOpinion(approverOpinion);
        processRecord.setOpName(userName);
        return processRecord;
    }

    @ApiOperation("获取打印数据")
    @PostMapping(value = "getPrintInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResponseEntity<Map<String, Object>> getPrintInfo(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        Long id = queryParam.getLong("registrationNum");
        CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);
        String ownerNames = "";
        String authorNames = "";
        for (CopyrightOwner owner : copyrightManager.getOwnerList()) {
            ownerNames = ownerNames + owner.getCopyName() + " ";
        }
        for (Author author : copyrightManager.getAuthorList()) {
            authorNames = authorNames + author.getAuthorName() + " ";
        }
        String md5 = "";
        String blockNumber = "";
        result = getBlockChainDetail(copyrightManager, "certificateUrl");
        if (result != null && result.containsKey("url")) {
            md5 = String.valueOf(result.get("md5"));
            blockNumber = String.valueOf(result.get("blockNumber"));
        } else {
        }
        result.put("md5", md5);
        result.put("blockNumber", blockNumber);
        result.put("worksNum", copyrightManager.getWorksNum());
        result.put("productionName", copyrightManager.getProductionName());
        String productionTypeStr = digitalServiceFacade.getById(Integer.parseInt(copyrightManager.getProductionTypeId())).getDict_name();
        if (productionTypeStr.contains("美术"))
            productionTypeStr = "美术";
        result.put("productionType", productionTypeStr);
        result.put("ownerNames", ownerNames);
        result.put("authorNames", authorNames);
        result.put("finishTime", copyrightManager.getFinishTime());
        result.put("publishTime", copyrightManager.getFirstPublishTime());
        result.put("certificateTime", copyrightManager.getCertificateCreateTime());

        String path = "";
        String certificateUrl = copyrightManager.getCertificateUrl();
        if (!StringUtils.isEmpty(certificateUrl)) {
            String[] str = certificateUrl.split("/");
            // if (str.length==6 && str[4].length()>20)
            if (str.length >= 6) {
                path = certificateUrl.replace("OwnerCertificate.jpg", "");
                path = path.replace("OwnerCertificate.png", "");
            }
        }

        result.put("QRCode", path + "QRCode.jpg");
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("图片校对")
    @PostMapping("pictureCompare")
    public ResponseEntity<Map<String, Object>> test(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
    /*result.put("message","功能维护中...");
    return new ResponseEntity<Map<String,Object>>(result,HttpStatus.OK);*/
        try {
            Long id = queryParam.getLong("id");
            if (id == null) {
                result.put("message", "校对失败");
                return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
            }
            CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);
            List<UploadAttachment> reportImgs = copyrightManager.getUploadWorks();
            if (reportImgs == null) {
                result.put("message", "校对失败");
                return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
            }
            List<List<Map<String, Object>>> resultList = new ArrayList<>();
            List<List<Map<String, Object>>> extraList = new ArrayList<>();
            for (UploadAttachment reportImg : reportImgs) {
                List<Map<String, Object>> list = copyrightManagerServiceFacade.getBySimilarAttachmentId(id, reportImg.getId());
//        List<Map<String,Object>> list = copyrightManagerServiceFacade.getBySimilarAttachmentId(id,reportImg.getId(), false);
                extraList.add(list);
                if (list.size() > 0) {
                    Iterator<Map<String, Object>> iterator = list.iterator();
                    while (iterator.hasNext()) {
                        Map<String, Object> node = iterator.next();
                        String scoreText;
                        Integer score = (Integer) node.get("score");
                        if (score > config.getSimilarScore()) {
                            iterator.remove();
                        } else {
                            switch (score) {
                                case 0:
                                    scoreText = "非常相似";
                                    break;
                                case 1:
                                case 2:
                                case 3:
                                    scoreText = "较为相似";
                                    break;
                                default:
                                    scoreText = "近似";
                                    break;
                            }
                            node.put("scoreText", scoreText);
            /*Map<String,Object> newMap = new HashMap<>(node);
            newMap.put("scoreText",scoreText);
            list.set(i,newMap);*/
                        }
                    }
                    resultList.add(list);
                }
            }
            result.put("message", "校对完成");
            result.put("results", resultList);
            result.put("extra", extraList);
      /*result.put("message","开始校对，校对时间较长，请耐心等待结果");
      result.put("tokens",tokens);*/
            return new ResponseEntity<>(result, HttpStatus.OK);
        } catch (Exception e) {
            result.put("message", "校对失败");
            result.put("errorMsg", e.getLocalizedMessage());
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
    }

    @ApiOperation("获取图片校对结果")
    @PostMapping("getCompareResult")
    public Map<String, Object> getCompareResult(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long id = queryParam.getLong("id");
            if (id == null) {
                return result;
            }
            CopyrightManager copyrightManager = copyrightManagerServiceFacade.getById(id);
            List<UploadAttachment> reportImgs = copyrightManager.getUploadWorks();
            if (reportImgs == null) {
                return result;
            }
            List<List<Map<String, Object>>> resultList = new ArrayList<>();
            boolean hasReject = false;
            for (UploadAttachment reportImg : reportImgs) {
                List<Map<String, Object>> list = copyrightManagerServiceFacade.getBySimilarAttachmentId(id, reportImg.getId());
//        List<Map<String,Object>> list = copyrightManagerServiceFacade.getBySimilarAttachmentId(id,reportImg.getId(), true);
                if (list.size() > 0) {
                    Iterator<Map<String, Object>> iterator = list.iterator();
                    while (iterator.hasNext()) {
                        Map<String, Object> node = iterator.next();
                        Integer score = (Integer) node.get("score");
                        if (score > config.getSimilarScore()) {
                            iterator.remove();
                        } else if (score == 0) {
                            switch ((int) node.get("similarStauts")) {
                                case CopyrightManager.FIRST_REVIEW_REJECT:
                                case CopyrightManager.SECOND_REVIEW_REJECT:
                                case CopyrightManager.FINAL_REVIEW_REJECT:
                                    hasReject = true;
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                    if (list.size() > 0) {
                        resultList.add(list);
                    }
                }
            }
            result.put("results", resultList);
            result.put("hasReject", hasReject);
            return result;
        } catch (Exception e) {
            return result;
        }
    }

    private String MD5(String s) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(s.getBytes(StandardCharsets.UTF_8));
            return toHex(bytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String toHex(byte[] bytes) {
        final char[] HEX_DIGITS = "0123456789ABCDEF".toCharArray();
        StringBuilder ret = new StringBuilder(bytes.length * 2);
        for (int i = 0; i < bytes.length; i++) {
            ret.append(HEX_DIGITS[(bytes[i] >> 4) & 0x0f]);
            ret.append(HEX_DIGITS[bytes[i] & 0x0f]);
        }
        return ret.toString();
    }

    @ApiOperation("查看证书")
    @GetMapping("certificateImgs")
    public void certificateImgs(HttpServletRequest request, HttpServletResponse response) {
        String certificateUrls = request.getParameter("certificateUrls");
        //  /api/qkl/File/media/nas/copyright_upload/fileModel/112158/OwnerCertificate.png
        String returnUrl = config.getFrontUrl() + "api/qkl/" + contextPath
                + config.getModelRoot() + "copyright_upload/fileModel" + certificateUrls;
        try {
            response.sendRedirect(returnUrl);
        } catch (IOException e) {
            log.error("error：{}", e);
        }
    }

    @ApiOperation("获取电子证照")
    @PostMapping("getCertificateZM")
    public ResponseEntity<Map<String, Object>> getCertificateZM(@Valid @RequestBody QueryParam queryParam) {
        Map<String, Object> result = new HashMap<>();
        try {
            JSONArray cardTypeArray = queryParam.getJSONArray("cardTypes");
            List<Integer> cardTypeList = new ArrayList<>();
            if (cardTypeArray != null && !cardTypeArray.isEmpty()) {
                cardTypeList = JSONObject.parseArray(cardTypeArray.toJSONString(), Integer.class);
            }
            // 批量获取电子证照
            JSONArray idArray = queryParam.getJSONArray("idCards");
            if (idArray != null && !idArray.isEmpty()) {
                List<String> idList = JSONObject.parseArray(idArray.toJSONString(), String.class);
                int num = 0;
                for (String idCard : idList) {
//                    CertificateEntity certificateEntity = certificateServiceFacade.getIndexByHolderCode(idCard, "", cardTypeList);
//                    if (certificateEntity != null) num++;
                    result.put("message", "已成功获取" + num + "个电子证照");
                }
            } else {
                // 获取单个电子证照
                String idCard = queryParam.getString("idCard");
                String name = queryParam.getString("name");
                if (StringUtils.isEmpty(idCard)) {
                    result.put("message", "证件号码不能为空！");
                    result.put("errorCode", 201);
                    return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
                }
                // 证照调用去掉
//                CertificateEntity certificateEntity = certificateServiceFacade.getIndexByHolderCode(idCard, name, cardTypeList);
//                if (certificateEntity == null) {
//                    certificateEntity = new CertificateEntity();
//                }
//                result.put("certificateZM", certificateEntity);
                result.put("certificateZM", new CertificateEntity());
            }
            result.put("message", "查询成功！");
            return new ResponseEntity<>(result, HttpStatus.OK);
        } catch (Exception e) {
            result.put("message", "电子证照获取失败！");
            result.put("errorCode", 500);
            log.error("error: {}", e);
            return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
        }
    }
}

