package com.lic.share.params;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class RequestHead {

    /**
     * 用户身份验证通过后获取的访问令牌
     */
    String accessToken;

    /**
     * 服务调用方的账号标识
     */
    String accountId;

    /**
     * 唯一标识，由服务调用方生成并确保其唯一性，每次调用都需重新生成，格式：14位时间戳+调用方账号标识+流水号
     */
    String accessId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime reqTime;

    ClientInfo clientInfo;

    ProjectInfo projectInfo;

}
