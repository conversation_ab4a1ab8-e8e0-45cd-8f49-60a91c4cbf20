package blockChain.entities;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_processrecord")
public class ProcessRecord {
	public static final Map<Integer,Object> APPROVAL_NAME = new HashMap<Integer,Object>(){{
		put(CopyrightManager.FIRST_REVIEW,"初审");
    put(CopyrightManager.SECOND_REVIEW,"复审");
		put(CopyrightManager.FINAL_REVIEW,"终审");
		put(CopyrightManager.UNSUBMIT,"用户提交");
		put(CopyrightManager.MODIFIED,"材料修改提交");
		put(CopyrightManager.CERT_CREATE,"证书生成");
	}};

	public static final Map<Integer,Object> REPROT_APPROVAL_NAME = new HashMap<Integer,Object>(){{
		put(WorksReport.REVIEWING,"审核");
		put(WorksReport.MODIFIED,"材料修改提交");
	}};

	public static final Map<String,Object> APPROVAL_RESULT = new HashMap<String,Object>(){{
		put("pass","通过");
		put("reject","驳回");
		put("modify","材料修改");
	}};

	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	private String opType; //审核状态
	private String opResult; //审核结果
	private String approverOpinion;  //审核意见
	private String opName; //审批人姓名
	//创建时间
	@CreatedDate
	private LocalDateTime time;
	//更新时间
	@LastModifiedDate
	private LocalDateTime updateTime;
}
