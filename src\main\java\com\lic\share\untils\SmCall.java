package com.lic.share.untils;


import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.SM2;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.util.Base64;

public final class SmCall {
    /**
     * 要把前后完整的公钥串复制到省汇聚平台，如：
     * -----BEGIN PUBLIC KEY-----
     * MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEHUjH3h26KGUwPi/iRigC1HsXXNnB
     * FftWELo7PS+7/AGWFyD6c5V4ue39vpTX6Zzs4gHzAuK9D52iBaOs2CGy0g==
     * -----END PUBLIC KEY-----
     */
//    static String pubKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEHUjH3h26KGUwPi/iRigC1HsXXNnB\n" +
//            "FftWELo7PS+7/AGWFyD6c5V4ue39vpTX6Zzs4gHzAuK9D52iBaOs2CGy0g==\n";
//
//    static String priKey = "MHcCAQEEIB70ofrJ7L6v/hghulCjvZfGTs99NbzyG9C8hYwMcZIAoAoGCCqBHM9V\n" +
//            "AYItoUQDQgAEHUjH3h26KGUwPi/iRigC1HsXXNnBFftWELo7PS+7/AGWFyD6c5V4\n" +
//            "ue39vpTX6Zzs4gHzAuK9D52iBaOs2CGy0g==\n";

    static String pubKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEwgbSlqmq11Xuy/eEY259yacPlvED\n" +
            "UT8sQnYucBIw3hvpDt4dyBpX2/ZwQp/1jutJ7FMY7d8mGqtfL2EvikYzOw==\n";

    static String priKey="MHcCAQEEILCi4UYINuDTaehanoi7jT7EVP65ZKrBE+17GIMPvl4CoAoGCCqBHM9V\n" +
            "AYItoUQDQgAEwgbSlqmq11Xuy/eEY259yacPlvEDUT8sQnYucBIw3hvpDt4dyBpX\n" +
            "2/ZwQp/1jutJ7FMY7d8mGqtfL2EvikYzOw==\n";

    public static String createSM2Signature(String text, String priKey) {
        final SM2 sm2 = new SM2(priKey, pubKey);
        byte[] msgData = text.getBytes(StandardCharsets.UTF_8);
        byte[] sigData = sm2.sign(msgData);
        return Base64.getEncoder().encodeToString(sigData);
    }

    public static boolean createSM2SigVerifier(String text, String sig) {
        final SM2 sm2 = new SM2(priKey, pubKey);
        byte[] msgData = text.getBytes(StandardCharsets.UTF_8);
        byte[] sigData = Base64.getDecoder().decode(sig);
        return sm2.verify(msgData, sigData);
    }

}