package blockChain.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 版保中心导出用dto
 *
 * <AUTHOR>
 * @date 2020/3/31 16:45
 */
@Data
@ApiModel(description = "统计通用DTO")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CopyrightManageExportDto {
  private String worksNum;
  private LocalDateTime registrationDate;
  private String productionName;
  private String productionType;
//  private String productionTypeId;
  private String copyName;
  private String finishTime;
  private String firstPublishTime;
  private String opusInditekind;
  private String obtainMode;
  private String rightOwnMode;
  private String authorName;
}
