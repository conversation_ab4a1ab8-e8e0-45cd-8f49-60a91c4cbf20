package blockChain.service;

import blockChain.entities.ArticleEntity;
import blockChain.repository.ArticleRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/9 12:08
 */
@Service
@AllArgsConstructor
public class ArticleService implements BaseService<ArticleRepository, ArticleEntity, Long>{
  private ArticleRepository repository;

  @Override
  public ArticleRepository getRepository() {
    return repository;
  }

  public Optional<ArticleEntity> findByUuid(String uuid) {
    return repository.findByUuid(uuid);
  }
}
