package blockChain.utils;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.codec.binary.Base64;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Map;

public class DocumentHandlerUtil {
	private Configuration configuration = null;
	public DocumentHandlerUtil() {
		configuration = new Configuration();
		configuration.setDefaultEncoding("utf-8");
	}

	public void createDoc(HttpServletRequest request, Map<String, Object> dataMap
			, String template
			, String saveUrl) throws IOException {
		//获取类路径
		ServletContext servletContext = request.getServletContext();
		//将word 模板放入WEB-INF
    File dir = new File("File/media/nas/exportDoc");
		configuration.setDirectoryForTemplateLoading(dir);
		Template t = null;
		try {
			t = configuration.getTemplate(template);
			t.setEncoding("utf-8");
		} catch (IOException e) {
			e.printStackTrace();
		}

		File outFile = new File(saveUrl);

		Writer out = null;
		try {
			out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "utf-8"));
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		try {
			//加载数据到模板文件
			t.process(dataMap, out);
			out.close();
		} catch (TemplateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}finally{
			if(out != null){
			   try {
				out.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			}
		}

	}


	/**
	 * 下载服务器上的文件
	 * @param response
	 * @param request
	 */

	public void download(HttpServletResponse response, HttpServletRequest request, String fileName,String urlRoot) throws UnsupportedEncodingException{
		 downloadFile(response,request,fileName,urlRoot);
	}


	/***
	 * 	下载服务器的文档数据
	 * @param response
	 * @param request
	 * @param fileName
	 * @param urlRoot
	 */
	public static void downloadFile(HttpServletResponse response, HttpServletRequest request, String fileName, String urlRoot){

		 //获取文件名
		 BufferedInputStream bis = null;
	     BufferedOutputStream bos = null;
	     try{
	    	 //解决文件中文乱码
	         if (request.getHeader("User-Agent").toLowerCase().indexOf("firefox") > 0){
	        	 fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");}//firefox浏览器
	         else if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0){
	        	 fileName = URLEncoder.encode(fileName, "UTF-8");}//IE浏览器
	         response.reset();//清空缓存区，防止存在某些字符使得下载的文件格式错误
	         response.setContentType("application/vnd.ms-word");
           //如果是谷歌浏览器有时候会出现下载文件出现中文名导致下载文件的时候就只有后缀，原因可能是中文乱码（但是在打印出来不是乱码），谷歌自动将乱码的屏蔽了
	         response.addHeader("Content-Disposition", "attachment;filename=" + new String((fileName).getBytes("GBK"),"ISO8859_1"));
	         bis = new BufferedInputStream(new FileInputStream(urlRoot));
	         bos = new BufferedOutputStream(response.getOutputStream());
	         byte[] buff = new byte[2048];
	         int bytesRead;
	         while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
	             bos.write(buff, 0, bytesRead);
	         }


	     }catch(Exception e){
	    	 e.printStackTrace();
	     }finally{
	    	 if(bis != null){
	    		 try {
	 				bis.close();

	 			} catch (IOException e) {
	 				e.printStackTrace();
	 			}finally{
	 				if(bos != null){
	 					try {
							bos.close();
						} catch (IOException e) {
							e.printStackTrace();
						}
	 				}
	 			}
	    	 }
	     }
	}

	/**
	   * BASE64Encoder 对本地图片进行编码后返回字符串
	   * @param file
	   * @return
	   */
	  public static String getImageStr(String file) {
			 String imgFile = file;
			 InputStream in = null;
			 byte[] data = null;
			 try {
				 in = new FileInputStream(imgFile);
				 data = new byte[in.available()];
				 in.read(data);
				 in.close();
			 } catch (IOException e) {
				 e.printStackTrace();
			 }
			 byte[] encodeBase64 = Base64.encodeBase64(data);
			 return  new String(encodeBase64) ;

		 }
}
