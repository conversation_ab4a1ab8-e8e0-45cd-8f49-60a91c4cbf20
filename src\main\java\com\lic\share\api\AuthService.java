package com.lic.share.api;


import com.alibaba.fastjson.JSONObject;
import com.lic.share.config.RestHelper;
import com.lic.share.untils.SmCall;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.SignatureException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <pre>
 * ClassName AuthService
 * Description 身份验证接口
 * Author glq
 * Date 2019/4/9
 * </pre>
 */
@RestController
@RequestMapping(value = {"/auth/"})
public class AuthService extends BaseConfig {


//    @Autowired
//    RestHelper restHelper;
    RestHelper restHelper = new RestHelper();


    @GetMapping(value = {"token"})
    public String token() throws SignatureException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeStr = sdf.format(new Date());
        SnowflakeIdWorker3rd idWorker = new SnowflakeIdWorker3rd();

        int id = idWorker.createId();
        String accessId = timeStr + accountId + id;
        JSONObject object = new JSONObject();
        object.put("accountId", accountId);
        object.put("accessId", accessId);
        object.put("sign", SmCall.createSM2Signature(accessId, priKey));
        return restHelper.postRestTemplateByJson(authUrl, object.toJSONString());
    }

}
