package blockChain.dto.ebus;

import lombok.Data;

/**
 * 用户扩展信息
 */
@Data
public class CustomizeValues {
    private String populationProcode; // 籍贯省份代码
    private String populationCitycode; // 籍贯城市代码
    private String populationAreacode; // 籍贯县区代码
    private String populationcprocode; // 常住省份代码
    private String populationccitycode; // 常住城市代码
    private String populationcareacode; // 常住县区代码
    private String populationcaddress; // 常住地址
    private String nationCode; // 国籍编码
    private String czdCitycode; // 常驻城市编码
    private String czdAreacode; // 常驻区县编码
    private String czdAddress; // 常驻地址
    private String issuingAuthority; // 签发机关
    private String populationHjdz; // 户籍地址
    private String gpsSfbm; // GPS地里位置省
    private String gpsCsbm; // GPS地里位置市
    private String gpsQxbm; // GPS地里位置区
}
