package blockChain.utils;

import org.apache.http.Consts;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

import static blockChain.utils.HttpClientUtil.createHttpClient;


public class PictureHttpUtil {
    private final static Logger logger = LoggerFactory.getLogger(PictureHttpUtil.class);

	/*private static Properties prop=ControllerUtils.readProperties("/email_config.properties");
	private static String HTTP_URL=prop.getProperty("EMAIL_HTTP_URL");*/

	/**
	 * 连接数据的封装
	 */
	public static String HttpConcent(String data,String HTTP_URL,HttpServletResponse response)
  {
    int timeout = 300;
    RequestConfig config = RequestConfig.custom()
      .setConnectTimeout(timeout*1000)
      .setSocketTimeout(timeout * 1000)
      .build();

		CloseableHttpClient client = HttpClientBuilder.create().setDefaultRequestConfig(config).build();

    	HttpPost httpPost = new HttpPost(HTTP_URL);

    	String success="";
		try {
	    	StringEntity entity= new StringEntity(data,"utf-8");
			httpPost.setEntity(entity);
			CloseableHttpResponse httpResponse = client.execute(httpPost);

			InputStream is = httpResponse.getEntity().getContent();

      StringBuffer sb = new StringBuffer();

      byte[] b = new byte[1024];
      int readlen=0;
      while ((readlen=is.read(b, 0, 1024)) != -1)
      {
        sb.append(new String(b,0,readlen, Consts.UTF_8));
      }
			int result = httpResponse.getStatusLine().getStatusCode();
			System.out.println(result);
			String s1 = sb.toString();
			System.out.println(s1);
			success=s1;
//			JSONObject jsonObject = JSONObject.fromObject(s1);
//			String results = jsonObject.getString("result");
//
//			if(!results.equals("")){
//
//				Map<String, Object> list = new HashMap<String, Object>();
//				list.put("results", results);
//				ControllerUtils.writeJsonByJSONObject(list, response);
//			}
			is.close();
			httpResponse.close();
		} catch (ClientProtocolException e) {
            logger.error("error", e);
		} catch (IOException e) {
            logger.error("error", e);
		}
		return success;
	}
}
