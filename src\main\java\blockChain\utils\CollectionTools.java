package blockChain.utils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/8/6 17:58
 */
public interface CollectionTools {

  /**
   * Iterable 转 Set
   * @param iterable
   * @param <T>
   * @return
   */
  static <T> Set<T> toSet(Iterable<T> iterable){
    Set<T> set = new HashSet<>();
    if(iterable == null){
      return set;
    }
    iterable.forEach(i -> set.add(i));
    return set;
  }
  static <T> List<T> toList(Iterable<T> iterable){
    List<T> list = new ArrayList<>();
    if(iterable == null){
      return list;
    }
    iterable.forEach(i -> list.add(i));
    return list;
  }

  /**
   * 获取当前网络ip
   * @param request
   * @return
   */
  static String getIpAddr(HttpServletRequest request){
    String ipAddress = request.getHeader("x-forwarded-for");
    if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
      ipAddress = request.getHeader("Proxy-Client-IP");
    }
    if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
      ipAddress = request.getHeader("WL-Proxy-Client-IP");
    }
    if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
      ipAddress = request.getRemoteAddr();
      if(ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")){
        //根据网卡取本机配置的IP
        InetAddress inet=null;
        try {
          inet = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
          e.printStackTrace();
        }
        ipAddress= inet.getHostAddress();
      }
    }
    //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
    if(ipAddress!=null && ipAddress.length()>15){ //"***.***.***.***".length() = 15
      if(ipAddress.indexOf(",")>0){
        ipAddress = ipAddress.substring(0,ipAddress.indexOf(","));
      }
    }
    return ipAddress;
  }

}
