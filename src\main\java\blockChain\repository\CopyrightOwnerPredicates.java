package blockChain.repository;

import blockChain.dto.query.CopyrightQueryStatisticGetParam;
import blockChain.entities.CopyrightManager;
import blockChain.entities.QAgent;
import blockChain.entities.QCopyrightManager;
import blockChain.entities.QCopyrightOwner;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.List;

import static blockChain.entities.CopyrightManager.EnableStatusEnum.ENABLE;

/**
 * <AUTHOR>
 * @date 2020/3/31 17:26
 */
public class CopyrightOwnerPredicates {
  private static QCopyrightOwner owner = QCopyrightOwner.copyrightOwner;
  private static QAgent agent = QAgent.agent;
  private static QCopyrightManager copyrightManager = QCopyrightManager.copyrightManager;

  public static Predicate copyrightOwnerStatisticPredicates(@NotNull CopyrightQueryStatisticGetParam queryParam, List<Integer> copyCategorys) {
    BooleanBuilder builder = new BooleanBuilder();
    if (queryParam.getStartTime() != null && queryParam.getEndTime() != null) {
      builder.and(owner.manager.certificateCreateTime.between(queryParam.getStartTime().plusSeconds(-1L), queryParam.getEndTime().plusSeconds(1L)));
    }

    if (queryParam.getProductionTypeIds() != null && queryParam.getProductionTypeIds().size() > 0) {
      builder.and(owner.manager.productionTypeId.in(queryParam.getProductionTypeIds()));
    }
    List<String> area = queryParam.getArea();
    if (area != null && area.size() > 0) {
      for (int i = 0; i < area.size(); i++) {
        // 从省开始
        switch (i) {
         /* case 0:
            builder.and(owner.copyCountries.eq(area.get(i)));
            break;*/
          case 0:
            builder.and(owner.copyProvince.eq(Integer.valueOf(area.get(i))));
            break;
          case 1:
            builder.and(owner.copyCity.eq(Integer.valueOf(area.get(i))));
            break;
          case 2:
            builder.and(owner.copyCounty.eq(Integer.valueOf(area.get(i))));
            break;
        }
      }
    }
    if(copyCategorys != null && copyCategorys.size() > 0){
      builder.and(owner.copyCategory.in(copyCategorys));
    }
    builder.and(owner.manager.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
    builder.and(owner.manager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE));
    builder.and(owner.manager.status_type.eq(CopyrightManager.CERT_CREATED));
    return builder;
  }

  public static Predicate copyrightAgentStatisticPredicates(@NotNull CopyrightQueryStatisticGetParam queryParam) {
    BooleanBuilder builder = new BooleanBuilder();
    if (queryParam.getStartTime() != null && queryParam.getEndTime() != null) {
      builder.and(copyrightManager.certificateCreateTime.between(queryParam.getStartTime().plusSeconds(-1L), queryParam.getEndTime().plusSeconds(1L)));
    }

    if (queryParam.getProductionTypeIds() != null && queryParam.getProductionTypeIds().size() > 0) {
      builder.and(copyrightManager.productionTypeId.in(queryParam.getProductionTypeIds()));
    }

    List<String> area = queryParam.getArea();
    if (area != null && area.size() > 0) {
      for (int i = 0; i < area.size(); i++) {
        // 从省开始
        switch (i) {
         /* case 0:
            builder.and(owner.copyCountries.eq(area.get(i)));
            break;*/
          case 0:
            if(area.get(i)!=null && area.get(i)!="") {
              builder.and(owner.copyProvince.eq(Integer.valueOf(area.get(i))));
            }
            break;
          case 1:
            if(area.get(i)!=null && area.get(i)!="") {
              builder.and(owner.copyCity.eq(Integer.valueOf(area.get(i))));
            }
            break;
          case 2:
            if(area.get(i)!=null && area.get(i)!="") {
              builder.and(owner.copyCounty.eq(Integer.valueOf(area.get(i))));
            }
            break;
        }
      }
    }

    return builder;
  }

  public static Predicate copyrightManagerStatisticPredicates(@NotNull CopyrightQueryStatisticGetParam queryParam, List<Integer> copyCategorys) {
    BooleanBuilder builder = new BooleanBuilder();

    if (queryParam.getProductionTypeIds() != null && queryParam.getProductionTypeIds().size() > 0) {
      builder.and(copyrightManager.productionTypeId.in(queryParam.getProductionTypeIds()));
    }
    if(copyCategorys != null && copyCategorys.size() > 0){
      builder.and(owner.copyCategory.in(copyCategorys));
    }
    List<String> area = queryParam.getArea();
    if (area != null && area.size() > 0) {
      for (int i = 0; i < area.size(); i++) {
        // 从省开始
        switch (i) {
         /* case 0:
            builder.and(owner.copyCountries.eq(area.get(i)));
            break;*/
          case 0:
            if(area.get(i)!=null && area.get(i)!="") {
              builder.and(owner.copyProvince.eq(Integer.valueOf(area.get(i))));
            }
            break;
          case 1:
            if(area.get(i)!=null && area.get(i)!="") {
              builder.and(owner.copyCity.eq(Integer.valueOf(area.get(i))));
            }
            break;
          case 2:
            if(area.get(i)!=null && area.get(i)!="") {
              builder.and(owner.copyCounty.eq(Integer.valueOf(area.get(i))));
            }
            break;
        }
      }
    }
    builder.and(copyrightManager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE));
    builder.and(copyrightManager.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
    if (queryParam.getIsWorkstation()==null || !queryParam.getIsWorkstation()) {
      builder.and(copyrightManager.status_type.eq(CopyrightManager.CERT_CREATED));
      if (queryParam.getStartTime() != null) {
        builder.and(copyrightManager.certificateCreateTime.after(queryParam.getStartTime()).or(copyrightManager.certificateCreateTime.eq(queryParam.getStartTime())));
      }

      if(queryParam.getEndTime() != null){
        builder.and(copyrightManager.certificateCreateTime.before(queryParam.getEndTime()).or(copyrightManager.certificateCreateTime.eq(queryParam.getEndTime())));
      }
    } else if (!StringUtils.isEmpty(queryParam.getUserName())){
      builder.and(copyrightManager.userName.eq(queryParam.getUserName()));
      if (queryParam.getStartTime() != null) {
        builder.and(copyrightManager.registrationDate.after(queryParam.getStartTime()).or(copyrightManager.registrationDate.eq(queryParam.getStartTime())));
      }

      if(queryParam.getEndTime() != null){
        builder.and(copyrightManager.registrationDate.before(queryParam.getEndTime()).or(copyrightManager.registrationDate.eq(queryParam.getEndTime())));
      }
      builder.and(copyrightManager.workstationId.gt(0));
    }
    return builder;
  }

  public static Predicate copyrightManagerTotalStatisticPredicates(@NotNull CopyrightQueryStatisticGetParam queryParam, List<Integer> copyCategorys) {
    BooleanBuilder builder = new BooleanBuilder();
    if (queryParam.getEndTime() != null) {
      builder.and(owner.manager.certificateCreateTime.before(queryParam.getEndTime().plusSeconds(1L)));
    }

    if (queryParam.getProductionTypeIds() != null && queryParam.getProductionTypeIds().size() > 0) {
      builder.and(owner.manager.productionTypeId.in(queryParam.getProductionTypeIds()));
    }
    if (queryParam.getProductionTypeIds() != null && queryParam.getProductionTypeIds().size() > 0) {
      builder.and(owner.manager.productionTypeId.in(queryParam.getProductionTypeIds()));
    }
    if(copyCategorys != null && copyCategorys.size() > 0){
      builder.and(owner.copyCategory.in(copyCategorys));
    }
    List<String> area = queryParam.getArea();
    if (area != null && area.size() > 0) {
      for (int i = 0; i < area.size(); i++) {
        // 从省开始
        switch (i) {
         /* case 0:
            builder.and(owner.copyCountries.eq(area.get(i)));
            break;*/
          case 0:
            if(area.get(i)!=null && area.get(i)!="") {
              builder.and(owner.copyProvince.eq(Integer.valueOf(area.get(i))));
            }
            break;
          case 1:
            if(area.get(i)!=null && area.get(i)!="") {
              builder.and(owner.copyCity.eq(Integer.valueOf(area.get(i))));
            }
            break;
          case 2:
            if(area.get(i)!=null && area.get(i)!="") {
              builder.and(owner.copyCounty.eq(Integer.valueOf(area.get(i))));
            }
            break;
        }
      }
    }
    builder.and(owner.manager.enable.eq(CopyrightManager.EnableStatusEnum.ENABLE));
    builder.and(owner.manager.inactiveType.ne(CopyrightManager.InactiveTypeValue.REVOKED));
    builder.and(owner.manager.status_type.eq(CopyrightManager.CERT_CREATED));
    return builder;
  }
}
