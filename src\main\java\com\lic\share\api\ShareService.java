package com.lic.share.api;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lic.share.config.RestHelper;
import com.lic.share.params.ClientInfo;
import com.lic.share.params.ProjectInfo;
import com.lic.share.params.RequestHead;
import com.lic.share.params.RequestParam;
import com.lic.share.response.ResponseResult;
import com.lic.share.untils.SmCall;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.SignatureException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <pre>
 * ClassName ShareService
 * Description 证照共享接口
 * Author glq
 * Date 2019/4/9
 * </pre>
 */
//@RestController
//@RequestMapping(value = {"/share/"})
@Api("电子共享")
@Slf4j
@RequiredArgsConstructor
public class ShareService extends BaseConfig {
//    @Autowired
    RestHelper restHelper = new RestHelper();

//    @Autowired
    AuthService authService = new AuthService();


//    @PostMapping(value = {"getCertificateType"})
    public String getCertificateType() throws SignatureException {
        String accessToken = getAccessToken();

        String cerTypeUrl = sharePreUrl + "getCertificateType";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeStr = sdf.format(new Date());
        SnowflakeIdWorker3rd idWorker = new SnowflakeIdWorker3rd();

        int id = idWorker.createId();
        String accessId = timeStr + accountId + id;

        RequestParam requestParam = new RequestParam();
        ClientInfo clientInfo = new ClientInfo().setAreaCode("35000")
                .setAreaName("福建省")
                .setDeptCode("DeptCode")
                .setDeptName("DeptName")
                .setOperId("OperId")
                .setOperName("OperName")
                .setSystemName("SystemName");

        ProjectInfo projectInfo = new ProjectInfo()
                .setProjectNo("ProjectNo")
                .setTaskCode("TaskCode").setTaskName("TaskName");
        RequestHead head = new RequestHead().setAccessToken(accessToken)
                .setAccessId(accessId)
                .setAccountId(accountId)
                .setReqTime(LocalDateTime.now())
                .setClientInfo(clientInfo)
                .setProjectInfo(projectInfo);
        requestParam.setHead(head);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", "中国质量奖证书");
        jsonObject.put("page", "1");
        jsonObject.put("size", "10");
        requestParam.setData(jsonObject);
        String jsonData = JSON.toJSONString(requestParam);

        String sign = SmCall.createSM2Signature(jsonData, priKey);

        String s = restHelper.postRestTemplateByJson(cerTypeUrl, accountId, sign, jsonData);
        return s;
    }

//    @PostMapping(value = {"getFullyElectronicCertificateType"})
    public String getFullyElectronicCertificateType() throws SignatureException {
        String accessToken = getAccessToken();

        String cerTypeUrl = sharePreUrl + "getFullyElectronicCertificateType";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeStr = sdf.format(new Date());
        SnowflakeIdWorker3rd idWorker = new SnowflakeIdWorker3rd();

        int id = idWorker.createId();
        String accessId = timeStr + accountId + id;

        RequestParam requestParam = new RequestParam();
        ClientInfo clientInfo = new ClientInfo().setAreaCode("35000")
                .setAreaName("福建省")
                .setDeptCode("DeptCode")
                .setDeptName("DeptName")
                .setOperId("OperId")
                .setOperName("OperName")
                .setSystemName("SystemName");

        ProjectInfo projectInfo = new ProjectInfo()
                .setProjectNo("ProjectNo")
                .setTaskCode("TaskCode").setTaskName("TaskName");
        RequestHead head = new RequestHead().setAccessToken(accessToken)
                .setAccessId(accessId)
                .setAccountId(accountId)
                .setReqTime(LocalDateTime.now())
                .setClientInfo(clientInfo)
                .setProjectInfo(projectInfo);
        requestParam.setHead(head);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", "中国质量奖证书");
        jsonObject.put("page", "1");
        jsonObject.put("size", "10");
        requestParam.setData(jsonObject);
        String jsonData = JSON.toJSONString(requestParam);

        String sign = SmCall.createSM2Signature(jsonData, priKey);

        String s = restHelper.postRestTemplateByJson(cerTypeUrl, accountId, sign, jsonData);
        return s;
    }

//    @PostMapping(value = {"getIndexByHolderCode"})
    public String getIndexByHolderCode(String certificateHolderCode, String certificateTypeName) throws SignatureException {
        String accessToken = getAccessToken();

        String cerTypeUrl = sharePreUrl + "getIndexByHolderCode";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeStr = sdf.format(new Date());
        SnowflakeIdWorker3rd idWorker = new SnowflakeIdWorker3rd();

        int id = idWorker.createId();
        String accessId = timeStr + accountId + id;

        RequestParam requestParam = new RequestParam();
        ClientInfo clientInfo = new ClientInfo().setAreaCode("35000")
                .setAreaName("福建省")
                .setDeptCode("DeptCode")
                .setDeptName("DeptName")
                .setOperId("OperId")
                .setOperName("OperName")
                .setSystemName("SystemName");

        ProjectInfo projectInfo = new ProjectInfo()
                .setProjectNo("ProjectNo")
                .setTaskCode("TaskCode").setTaskName("TaskName");
        RequestHead head = new RequestHead().setAccessToken(accessToken)
                .setAccessId(accessId)
                .setAccountId(accountId)
                .setReqTime(LocalDateTime.now())
                .setClientInfo(clientInfo)
                .setProjectInfo(projectInfo);//
        requestParam.setHead(head);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("certificateHolderCode", certificateHolderCode);
        jsonObject.put("certificateTypeName", certificateTypeName);
        requestParam.setData(jsonObject);
        String jsonData = JSON.toJSONString(requestParam);

        String sign = SmCall.createSM2Signature(jsonData, priKey);

        String s = restHelper.postRestTemplateByJson(cerTypeUrl, accountId, sign, jsonData);
        return s;
    }


    private String getAccessToken() throws SignatureException {
        String token = authService.token();
        ResponseResult tokenResult = JSON.parseObject(token, ResponseResult.class);
        return tokenResult.getData().getAccessToken();
    }
}
