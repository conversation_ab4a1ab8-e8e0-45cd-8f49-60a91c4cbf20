package blockChain.utils.umpay.mascloud.mgw.service.webservice.server.data;

import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 *--�ɹ����ط���ֵ
<?xml version="1.0" encoding="utf-8"?><SendSmsResponse>
  <success>true</success>
  <rspcod>success</rspcod>
  <msgGroup>1212090135001001657725</msgGroup>
</SendSmsResponse>
--ʧ�ܷ���
<?xml version="1.0" encoding="utf-8"?><SendSmsResponse>
  <success>false</success>
  <rspcod>InvalidMobile</rspcod>
</SendSmsResponse>
 * <AUTHOR>
 *
 */
@XmlRootElement(name="SendSmsResponse")
public class SendSmsResponse  {
	private String success;
	private boolean successA;
	private String rspcod;
	private String msgGroup;


	@XmlElement(name="success")
	public String getSuccess() {
		return success;
	}
	public void setSuccess(String success) {
		this.success = success;
	}


	public boolean isSuccessA() {
		if(success==null)success="false";
		success=success.toLowerCase();
		if(success.equals("true")) {
			successA=true;
		}else {
			successA=false;
		}
		return successA;
	}
	@XmlElement(name="rspcod")
	public String getRspcod() {
		return rspcod;
	}
	public void setRspcod(String rspcod) {
		this.rspcod = rspcod;
	}
	@XmlElement(name="msgGroup")
	public String getMsgGroup() {
		return msgGroup;
	}
	public void setMsgGroup(String msgGroup) {
		this.msgGroup = msgGroup;
	}
	@Override
	public String toString() {
		return "SendSmsResponse [success=" + success + ", rspcod=" + rspcod + ", msgGroup=" + msgGroup + "]";
	}




}
