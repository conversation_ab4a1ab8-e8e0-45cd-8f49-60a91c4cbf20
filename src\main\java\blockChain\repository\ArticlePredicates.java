
package blockChain.repository;

import blockChain.entities.QArticleEntity;
import blockChain.entities.QCopyrightManager;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * <AUTHOR> XXX
 * @date 2019/11/29.
 */

public final class ArticlePredicates {
  public static Predicate titleLikeAndContentLikeAndCreateTimeBetween(String title, String content, LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {
    QArticleEntity article = QArticleEntity.articleEntity;
    BooleanBuilder builder = new BooleanBuilder();
    if(!StringUtils.isEmpty(title)){
      builder.and(article.title.contains(title));
    }
    if(!StringUtils.isEmpty(content)){
      builder.and(article.content.contains(content));
    }
    /*if(createTimeStart != null && createTimeEnd != null){
      builder.and(article.createTime.between(createTimeStart, createTimeEnd));
    }*/
    if(createTimeStart != null){
      builder.and(article.createTime.after(createTimeStart));
    }
    if(createTimeEnd != null){
      builder.and(article.createTime.before(createTimeEnd));
    }
    return builder;
  }

}

