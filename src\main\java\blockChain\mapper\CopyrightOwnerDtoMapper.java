package blockChain.mapper;

import blockChain.dto.CopyrightOwnerDto;
import blockChain.dto.UserDto;
import blockChain.entities.CopyrightOwner;
import blockChain.entities.UserEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CopyrightOwnerDtoMapper {
    CopyrightOwnerDtoMapper INSTANCE = Mappers.getMapper(CopyrightOwnerDtoMapper.class);

    CopyrightOwnerDto entityToDto(CopyrightOwner owner);

    List<CopyrightOwnerDto> entityToDto(List<CopyrightOwner> owners);
}
