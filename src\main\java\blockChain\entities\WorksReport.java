package blockChain.entities;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_worksreport")
public class WorksReport {
	public static final int REVIEWING = 1; //审核中
	public static final int PASSED = 2; //通过
	public static final int MODIFIED = 3; //驳回

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	private Long id;// 登记号
	private Integer reportStatus;// 状态
	private String reportDescript; //举报图描述
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
	private LocalDateTime certificateCreateTime; //证书生成时间
	private String onBlockChain;  //上链凭证;
	//创建时间
	@CreatedDate
	private LocalDateTime createTime;
	//更新时间
	@LastModifiedDate
	private LocalDateTime updateTime;
	@Transient
	private boolean updateFlag;
	/**
	 * 作品
	 */
	@JoinColumn(name="copyrightmanager_id")
    @OneToOne(fetch = FetchType.EAGER)
	private CopyrightManager copyrightManager;

	/**
	 * 侵权图片
	 */
	@JoinColumn(name="uploadattachment_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private UploadAttachment reportImg;

	/**
	 * 侵权报告
	 */
	@JoinColumn(name="tortReport_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private UploadAttachment tortReport;

	/**
	 * 流程记录
	 */
	@JoinColumn(name = "worksreport_id")
	@OneToMany(cascade={CascadeType.ALL}, fetch = FetchType.LAZY)
	private List<ProcessRecord> flowRecord = new ArrayList<>();
}
