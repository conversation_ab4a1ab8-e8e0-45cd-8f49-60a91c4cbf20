package blockChain.controller.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 * @date 2020/4/9 14:52
 */
@ResponseStatus(value = HttpStatus.NOT_FOUND)
public class NotFountException  extends RuntimeException {
  public NotFountException() {
    super("资源未找到");
  }

  public NotFountException(String message) {
    super(message);
  }
}
