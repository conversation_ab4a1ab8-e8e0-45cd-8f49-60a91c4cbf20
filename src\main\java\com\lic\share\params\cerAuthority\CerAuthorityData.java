package com.lic.share.params.cerAuthority;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @data
 **/
@Data
@Accessors(chain = true)
public class CerAuthorityData {

    @NotNull(message = "promoter不能为空")
    //@ApiModelProperty(value = "发起人")
    private String promoter;

    //@ApiModelProperty(value = "回执单号,撤销权限时必要")
    private String receiptNumber;

    @NotNull(message = "promoterCode不能为空")
    //@ApiModelProperty(value = "发起人代码")
    private String promoterCode;

    @NotNull(message = "authorityEffectiveDate不能为空")
    //@ApiModelProperty(value = "授权有效期起始日期 格式：yyyy-MM-dd hh:mm:ss")
    private Date authorityEffectiveDate;

    @NotNull(message = "authorityExpiringDate不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date authorityExpiringDate;

    @NotNull(message = "certificateIdentifier不能为空")
    //@ApiModelProperty(value = "电子证照标识")
    private ArrayList<String> certificateIdentifier;

    @NotNull(message = "certificateIdentifier不能为空")
    //@ApiModelProperty(value = "电子证照标识")
    private List<CerAuthorityPersonData> authorizedPerson;

}
