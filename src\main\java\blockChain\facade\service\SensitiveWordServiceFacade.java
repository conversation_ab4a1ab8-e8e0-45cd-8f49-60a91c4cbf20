package blockChain.facade.service;

import blockChain.bean.PageQuery;
import blockChain.bean.PageResponse;
import blockChain.dto.SensitiveWordDTO;
import blockChain.entities.SensitiveWordEntity;
import blockChain.exception.BaseException;
import blockChain.mapper.SensitiveWordMapper;
import blockChain.service.SensitiveWordService;
import com.querydsl.core.BooleanBuilder;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static blockChain.entities.QSensitiveWordEntity.sensitiveWordEntity;

/**
 * 敏感词库(tb_sensitive_word)业务服务接口
 *
 * <AUTHOR>
 * @since 2024-01-02 10:46:48
 */

@Slf4j
@Service
@AllArgsConstructor
public class SensitiveWordServiceFacade {

    private final SensitiveWordService service;

    @Async("doSomethingExecutor")
    public void importSensitiveWord(List<SensitiveWordDTO> sensitiveWordDTOS) {
        String retStr = "";
        int rows = 1;
        for (SensitiveWordDTO sensitiveWordDTO : sensitiveWordDTOS) {
            if (sensitiveWordDTO.getSensitiveWord() == null || sensitiveWordDTO.getSensitiveWord().length() == 0) {
                retStr = "敏感词内容为空！";
                break;
            }
            SensitiveWordEntity _oldEntity = service.findBySensitiveWordAndIsDelete(sensitiveWordDTO.getSensitiveWord(), false);
            if (_oldEntity == null) {
                SensitiveWordEntity sensitiveWordEntity = SensitiveWordMapper.INSTANCE.dtoToEntity(sensitiveWordDTO);
                service.save(sensitiveWordEntity);
            }
            rows++;
        }
        if (retStr.length() != 0) {
            retStr = "第" + rows + "行错误：" + retStr;
            throw new BaseException(500, retStr);
        }
    }

    @Transactional
    public void delete(Integer id) {
        Optional<SensitiveWordEntity> sensitiveWordEntity = service.findById(id);
        if (!sensitiveWordEntity.isPresent()) {
            throw new BaseException(500, "没有指定项目！");
        }
        sensitiveWordEntity.get().setDelete(true);
        service.save(sensitiveWordEntity.get());
    }

    @ApiOperation("查")
    @PostMapping(value = "query")
    public PageResponse<SensitiveWordDTO> query(@Valid @RequestBody PageQuery<SensitiveWordDTO> pageQuery) {
        SensitiveWordDTO sensitiveWordDTO = pageQuery.getQueryBody();
        Pageable pageable = pageQuery.getPageRequest();

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(sensitiveWordEntity.isDelete.isFalse());

        if (StringUtils.isNotBlank(sensitiveWordDTO.getSensitiveWord())) {
            builder.and(sensitiveWordEntity.sensitiveWord.eq(sensitiveWordDTO.getSensitiveWord()));
        }

        Page<SensitiveWordEntity> page = service.findAll(builder, pageable);
        return PageResponse.of(page, SensitiveWordMapper.INSTANCE.toDtoList(page.getContent()));
    }

    public List<String> getSensitiveWords() {

        // 从数据库中读取敏感词并添加到敏感词列表中
        return service.findAllByIsDelete(false)
                .stream()
                .map(SensitiveWordEntity::getSensitiveWord)
                .collect(Collectors.toList());
    }
}
