package blockChain.repository;

import blockChain.dto.StatisticDto;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/20 15:20
 */
public interface CustomizedUserRepository {

  void removeBatchById(List<Integer> userIds);

  /**
   * 获取用户发布作品统计
   *
   * @param pageable
   * @param predicate
   * @return
   */
  List<StatisticDto> getUserStatisticInCopyrightManager(Pageable pageable, Predicate... predicate);
  /**
   * count用户发布作品统计
   *
   * @param pageable
   * @param predicate
   * @return
   */
  Long countUserStatisticInCopyrightManager(Pageable pageable, Predicate... predicate);

  /**
   * 各个地区分组统计
   * @param predicate
   * @return
   */
  List<StatisticDto> countGeographyOfUsers(Predicate ... predicate);

  /**
   * 非福建地区整合统计
   * @param predicate
   * @return
   */
  StatisticDto countGeographyOfUsersCountAll(Predicate ... predicate);

  /**
   * 获取所有用户Id
   * @return
   */
  List<Integer> getAllIds(Predicate... predicate);

  void updateHomePage(Integer userId,String homePageKey);

}
