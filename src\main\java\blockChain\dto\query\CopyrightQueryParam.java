package blockChain.dto.query;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/03 16:14
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CopyrightQueryParam {
    private String worksNum;
    private String productionName;
    private String productionTypeId;
    private Integer rightOwnMode; // 权利方式
    private String certificateStartDate; //证书生成日期
    private String certificateEndDate;
    private String startDate; //申请日期
    private String endDate;
    private List<Integer> status;
    private String userName;
    private String agentName;
    private String copyrightName; //著作权人名
    private Boolean isCertificate;
    private Boolean isSubmit;
    private Boolean isreport;
    private Integer province;
    private Integer city_level;
    private Integer county_level;
    private Integer evaluatemin;
    private Integer evaluatemax;
    private Integer page;
    private Integer size;
    private Pageable of; // 分页
    private List<Integer> regNums; // 上报国家局、证照中心状态
    private List<Integer> tags; // 标签
    private Integer isAccusation; // 是否侵权
    private Integer focusWork; // 是否关注
    private Integer isSelected; // 是否精选 add 202210
    private Boolean isenable; // 是否已删除
    private Integer inactiveType; // 撤销状态
}
