package blockChain.service;

import blockChain.repository.BaseRepository;
import blockChain.repository.CallBackRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/18 17:46
 */
@Slf4j
@Service
@AllArgsConstructor
public class CallBackService implements BaseService{

  private final CallBackRepository repository;

  @Override
  public BaseRepository getRepository() {
    return null;
  }
}
