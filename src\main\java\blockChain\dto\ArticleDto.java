package blockChain.dto;

import blockChain.bean.BaseQueryParam;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/15 15:39
 */

@Getter
@Setter
public class ArticleDto  extends BaseQueryParam {
  @JsonProperty("id")
  private String uuid;

  private String title;

  private String content;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  private LocalDateTime createTimeStart;

  private LocalDateTime createTimeEnd;

}
