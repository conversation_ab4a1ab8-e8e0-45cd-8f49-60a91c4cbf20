package blockChain.mapper;

import blockChain.dto.PaperCertificateDto;
import blockChain.entities.PaperCertificateEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/16 15:51
 */
@Mapper(config = CommonConfig.class, uses = {CopyrightManagerDtoMapper.class, ProcessRecordDtoMapper.class})
public interface PaperCertificateDtoMapper {
  PaperCertificateDtoMapper INSTANCE = Mappers.getMapper(PaperCertificateDtoMapper.class);

  PaperCertificateEntity toEntity(PaperCertificateDto dto);

  List<PaperCertificateDto> toDto(Iterable<PaperCertificateEntity> list);

  @Mappings({
    @Mapping(target = "creatorName", source = "creator.realName"),
    @Mapping(target = "worksNum", source = "copyright.worksNum"),
    @Mapping(target = "productionName", source = "copyright.productionName"),
  })
  PaperCertificateDto toDto(PaperCertificateEntity entity);
}
