package blockChain.repository.oldSystem;

import blockChain.entities.oldSystem.OldAuthor;
import blockChain.entities.oldSystem.OldCopyrightManager;
import blockChain.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/16 16:21
 */
public interface OldCopyrightManagerRepository extends BaseRepository<OldCopyrightManager, Long> {
  @Query(value = "select count(*) from old_copyrightmanager where status_type = 6 and intherFort is NULL",nativeQuery = true)
  Long selectAll();
}
