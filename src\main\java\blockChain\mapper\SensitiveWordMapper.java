package blockChain.mapper;

import blockChain.dto.SensitiveWordDTO;
import blockChain.entities.SensitiveWordEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-02 10:46
 */
@Mapper(config = CommonConfig.class, uses = {MenuMapper.class})
public interface SensitiveWordMapper {

    SensitiveWordMapper INSTANCE = Mappers.getMapper(SensitiveWordMapper.class);

    @Mapping(target = "id", ignore = true)
    SensitiveWordEntity dtoToEntity(SensitiveWordDTO sensitiveWordDTO);

    List<SensitiveWordDTO> toDtoList(List<SensitiveWordEntity> content);
}
