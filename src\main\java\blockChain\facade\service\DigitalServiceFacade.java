package blockChain.facade.service;

import blockChain.entities.Digital;
import blockChain.service.DigitalService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/22 16:54
 */
@Service
@AllArgsConstructor
public class DigitalServiceFacade {
    @Autowired
    private DigitalService digitalService;

    @Transactional(rollbackFor = RuntimeException.class)
    public List<Digital> getDictByCode(String code){
        List<Digital> digitalList = new ArrayList<>();
        Iterable<Digital> digitalIterable = digitalService.getDictByCode(code,Digital.ENABLE);
        for(Digital digital:digitalIterable){
            digitalList.add(digital);
        }
        return digitalList;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public List<Digital> getDictByPid(Integer pid){
        List<Digital> digitalList = new ArrayList<>();
        Iterable<Digital> digitalIterable = digitalService.getDictByPid(pid,Digital.ENABLE);
        for(Digital digital:digitalIterable){
            digitalList.add(digital);
        }
        return digitalList;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public List<Digital> findByLevel(Integer level){
        return digitalService.findByLevel(level);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public Digital getById(Integer id){
      return digitalService.getById(id).orElse(new Digital());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public List<Digital> getDictByDict_Name(String dictName){
      List<Digital> digitalList = new ArrayList<>();
      Iterable<Digital> digitalIterable = digitalService.getDictByDict_Name(dictName,Digital.ENABLE);
      for(Digital digital:digitalIterable){
        digitalList.add(digital);
      }
      return digitalList;
    }
}
