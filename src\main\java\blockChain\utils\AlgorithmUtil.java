package blockChain.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.util.Base64;

/**
 * 加解密算法工具类
 */
public final class AlgorithmUtil {

    private AlgorithmUtil() {
    }

    /**
     * 生成SM4私钥
     *
     * @return SM4私钥
     */
    public static String generateSm4PrivateKey() {
        return RandomUtil.randomString("1234567890ABCDEF",32);
    }

    /**
     * 使用SM2加密并返回Base64
     *
     * @param text      内容
     * @param publicKey 公钥
     * @return SM2加密并base64编码
     */
    public static String encryptBySm2AndBase64(String text, String publicKey) {
        return encodeByBase64(encryptBySm2(text, publicKey));
    }


    /**
     * 使用SM2加密
     *
     * @param text      内容
     * @param publicKey 公钥
     * @return 加密后内容
     */
    public static String encryptBySm2(String text, String publicKey) {
        SM2 sm2 = SmUtil.sm2(null, publicKey);
        return sm2.encryptHex(text, KeyType.PublicKey);
    }

    /**
     * 使用SM2加密
     *
     * @param plainText 内容
     * @param secretKey 密钥
     * @return 加密后内容
     */
    public static String encryptBySm4AndBase64(String plainText, String secretKey) throws Exception {
        return encodeByBase64(SM4.encryptEcb(secretKey, plainText));
    }

    /**
     * 使用SM2解密
     *
     * @param cipherText 内容
     * @param secretKey  私钥
     * @return 解密后内容
     */
    public static String decryptByBase64AndSm4(String cipherText, String secretKey) throws Exception {
        return SM4.decryptEcb(secretKey, decodeByBase64(cipherText));
    }


    /**
     * 使用Base64编码
     *
     * @param text 内容
     * @return 编码后内容
     */
    public static String encodeByBase64(String text) {
        final Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(text.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 使用Base64解码
     *
     * @param text 内容
     * @return 解码后内容
     */
    public static String decodeByBase64(String text) {
        final Base64.Decoder decoder = Base64.getDecoder();
        return new String(decoder.decode(text.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * SM4加密
     */
    static class SM4 {
        private SM4() {
        }

        static {
            Security.addProvider(new BouncyCastleProvider());
        }

        public static final String ALGORITHM_NAME = "SM4";

        // 加密算法/分组加密模式/分组填充模式
        // 定义分组加密模式
        public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS7Padding";

        private static Cipher generateEcbCipher(String algorithmName, int mode, byte[] key)
                throws NoSuchPaddingException, NoSuchAlgorithmException, NoSuchProviderException, InvalidKeyException {
            Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
            Key sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
            cipher.init(mode, sm4Key);
            return cipher;
        }

        /**
         * 加密
         *
         * @param key      密钥
         * @param paramStr 待加密的字符串
         * @return 加密的密文
         * @throws Exception
         */
        public static String encryptEcb(String key, String paramStr) throws Exception {
            // 16进制字符串转byte数组
            byte[] keyData = hexStringToBytes(key);
            // 字符串转byte数组
            byte[] srcData = paramStr.getBytes(StandardCharsets.UTF_8);
            byte[] cipherData = encryptEcbPadding(keyData, srcData);
            return Base64.getEncoder().encodeToString(cipherData);
        }

        public static byte[] encryptEcbPadding(byte[] keyData, byte[] paramData) throws Exception {
            Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.ENCRYPT_MODE, keyData);
            return cipher.doFinal(paramData);
        }

        /**
         * 解密
         *
         * @param key        密钥
         * @param cipherText 待加密的字符串
         * @return 加密的密文
         * @throws Exception
         */
        public static String decryptEcb(String key, String cipherText) throws Exception {
            byte[] keyData = hexStringToBytes(key);
            byte[] cipherData = Base64.getDecoder().decode(cipherText);
            byte[] srcData = decryptEcbPadding(keyData, cipherData);
            return new String(srcData);
        }

        private static byte[] decryptEcbPadding(byte[] keyData, byte[] cipherData) throws Exception {
            Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, keyData);
            return cipher.doFinal(cipherData);
        }

        public static byte[] hexStringToBytes(String hexString) {
            if (hexString == null || hexString.length() == 0) {
                return new byte[0];
            }
            hexString = hexString.toUpperCase();
            int length = hexString.length() / 2;
            char[] hexChars = hexString.toCharArray();
            byte[] data = new byte[length];
            for (int i = 0; i < length; i++) {
                int pos = i * 2;
                data[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]) & 0xff);
            }
            return data;
        }

        private static byte charToByte(char c) {
            return (byte) "0123456789ABCDEF".indexOf(c);
        }
    }

}
