
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the org.tempuri package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _LoginByAccount_QNAME = new QName("http://tempuri.org/", "LoginByAccount");
    private final static QName _LoginByAccountBySM2Key_QNAME = new QName("http://tempuri.org/", "LoginByAccountBySM2Key");
    private final static QName _LoginByAccountBySM2KeyResponse_QNAME = new QName("http://tempuri.org/", "LoginByAccountBySM2KeyResponse");
    private final static QName _LoginByAccountResponse_QNAME = new QName("http://tempuri.org/", "LoginByAccountResponse");
    private final static QName _AllDeleteAndAllAdd_QNAME = new QName("http://tempuri.org/", "allDeleteAndAllAdd");
    private final static QName _AllDeleteAndAllAddResponse_QNAME = new QName("http://tempuri.org/", "allDeleteAndAllAddResponse");
    private final static QName _CrossNetCheck_QNAME = new QName("http://tempuri.org/", "crossNetCheck");
    private final static QName _CrossNetCheckResponse_QNAME = new QName("http://tempuri.org/", "crossNetCheckResponse");
    private final static QName _EndAddAllDeleteAll_QNAME = new QName("http://tempuri.org/", "endAddAllDeleteAll");
    private final static QName _EndAddAllDeleteAllResponse_QNAME = new QName("http://tempuri.org/", "endAddAllDeleteAllResponse");
    private final static QName _GetCatalogidByMatterid_QNAME = new QName("http://tempuri.org/", "getCatalogidByMatterid");
    private final static QName _GetCatalogidByMatteridResponse_QNAME = new QName("http://tempuri.org/", "getCatalogidByMatteridResponse");
    private final static QName _GetProblemCollect_QNAME = new QName("http://tempuri.org/", "getProblemCollect");
    private final static QName _GetProblemCollectResponse_QNAME = new QName("http://tempuri.org/", "getProblemCollectResponse");
    private final static QName _GetSM2PublicKey_QNAME = new QName("http://tempuri.org/", "getSM2PublicKey");
    private final static QName _GetSM2PublicKeyResponse_QNAME = new QName("http://tempuri.org/", "getSM2PublicKeyResponse");
    private final static QName _GetSM4Key_QNAME = new QName("http://tempuri.org/", "getSM4Key");
    private final static QName _GetSM4KeyResponse_QNAME = new QName("http://tempuri.org/", "getSM4KeyResponse");
    private final static QName _GetTimeGroup_QNAME = new QName("http://tempuri.org/", "getTimeGroup");
    private final static QName _GetTimeGroupResponse_QNAME = new QName("http://tempuri.org/", "getTimeGroupResponse");
    private final static QName _GetXmlByCatalogid_QNAME = new QName("http://tempuri.org/", "getXmlByCatalogid");
    private final static QName _GetXmlByCatalogidResponse_QNAME = new QName("http://tempuri.org/", "getXmlByCatalogidResponse");
    private final static QName _PushInterAllXml_QNAME = new QName("http://tempuri.org/", "pushInterAllXml");
    private final static QName _PushInterAllXmlResponse_QNAME = new QName("http://tempuri.org/", "pushInterAllXmlResponse");
    private final static QName _PushLargeXml_QNAME = new QName("http://tempuri.org/", "pushLargeXml");
    private final static QName _PushLargeXmlResponse_QNAME = new QName("http://tempuri.org/", "pushLargeXmlResponse");
    private final static QName _PushXml_QNAME = new QName("http://tempuri.org/", "pushXml");
    private final static QName _PushXmlBackflow_QNAME = new QName("http://tempuri.org/", "pushXmlBackflow");
    private final static QName _PushXmlBackflowInnerFun_QNAME = new QName("http://tempuri.org/", "pushXmlBackflowInnerFun");
    private final static QName _PushXmlBackflowInnerFunResponse_QNAME = new QName("http://tempuri.org/", "pushXmlBackflowInnerFunResponse");
    private final static QName _PushXmlBackflowResponse_QNAME = new QName("http://tempuri.org/", "pushXmlBackflowResponse");
    private final static QName _PushXmlBySM4_QNAME = new QName("http://tempuri.org/", "pushXmlBySM4");
    private final static QName _PushXmlBySM4Response_QNAME = new QName("http://tempuri.org/", "pushXmlBySM4Response");
    private final static QName _PushXmlInnerFun_QNAME = new QName("http://tempuri.org/", "pushXmlInnerFun");
    private final static QName _PushXmlInnerFunResponse_QNAME = new QName("http://tempuri.org/", "pushXmlInnerFunResponse");
    private final static QName _PushXmlResponse_QNAME = new QName("http://tempuri.org/", "pushXmlResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.tempuri
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link LoginByAccount }
     * 
     */
    public LoginByAccount createLoginByAccount() {
        return new LoginByAccount();
    }

    /**
     * Create an instance of {@link LoginByAccountBySM2Key }
     * 
     */
    public LoginByAccountBySM2Key createLoginByAccountBySM2Key() {
        return new LoginByAccountBySM2Key();
    }

    /**
     * Create an instance of {@link LoginByAccountBySM2KeyResponse }
     * 
     */
    public LoginByAccountBySM2KeyResponse createLoginByAccountBySM2KeyResponse() {
        return new LoginByAccountBySM2KeyResponse();
    }

    /**
     * Create an instance of {@link LoginByAccountResponse }
     * 
     */
    public LoginByAccountResponse createLoginByAccountResponse() {
        return new LoginByAccountResponse();
    }

    /**
     * Create an instance of {@link AllDeleteAndAllAdd }
     * 
     */
    public AllDeleteAndAllAdd createAllDeleteAndAllAdd() {
        return new AllDeleteAndAllAdd();
    }

    /**
     * Create an instance of {@link AllDeleteAndAllAddResponse }
     * 
     */
    public AllDeleteAndAllAddResponse createAllDeleteAndAllAddResponse() {
        return new AllDeleteAndAllAddResponse();
    }

    /**
     * Create an instance of {@link CrossNetCheck }
     * 
     */
    public CrossNetCheck createCrossNetCheck() {
        return new CrossNetCheck();
    }

    /**
     * Create an instance of {@link CrossNetCheckResponse }
     * 
     */
    public CrossNetCheckResponse createCrossNetCheckResponse() {
        return new CrossNetCheckResponse();
    }

    /**
     * Create an instance of {@link EndAddAllDeleteAll }
     * 
     */
    public EndAddAllDeleteAll createEndAddAllDeleteAll() {
        return new EndAddAllDeleteAll();
    }

    /**
     * Create an instance of {@link EndAddAllDeleteAllResponse }
     * 
     */
    public EndAddAllDeleteAllResponse createEndAddAllDeleteAllResponse() {
        return new EndAddAllDeleteAllResponse();
    }

    /**
     * Create an instance of {@link GetCatalogidByMatterid }
     * 
     */
    public GetCatalogidByMatterid createGetCatalogidByMatterid() {
        return new GetCatalogidByMatterid();
    }

    /**
     * Create an instance of {@link GetCatalogidByMatteridResponse }
     * 
     */
    public GetCatalogidByMatteridResponse createGetCatalogidByMatteridResponse() {
        return new GetCatalogidByMatteridResponse();
    }

    /**
     * Create an instance of {@link GetProblemCollect }
     * 
     */
    public GetProblemCollect createGetProblemCollect() {
        return new GetProblemCollect();
    }

    /**
     * Create an instance of {@link GetProblemCollectResponse }
     * 
     */
    public GetProblemCollectResponse createGetProblemCollectResponse() {
        return new GetProblemCollectResponse();
    }

    /**
     * Create an instance of {@link GetSM2PublicKey }
     * 
     */
    public GetSM2PublicKey createGetSM2PublicKey() {
        return new GetSM2PublicKey();
    }

    /**
     * Create an instance of {@link GetSM2PublicKeyResponse }
     * 
     */
    public GetSM2PublicKeyResponse createGetSM2PublicKeyResponse() {
        return new GetSM2PublicKeyResponse();
    }

    /**
     * Create an instance of {@link GetSM4Key }
     * 
     */
    public GetSM4Key createGetSM4Key() {
        return new GetSM4Key();
    }

    /**
     * Create an instance of {@link GetSM4KeyResponse }
     * 
     */
    public GetSM4KeyResponse createGetSM4KeyResponse() {
        return new GetSM4KeyResponse();
    }

    /**
     * Create an instance of {@link GetTimeGroup }
     * 
     */
    public GetTimeGroup createGetTimeGroup() {
        return new GetTimeGroup();
    }

    /**
     * Create an instance of {@link GetTimeGroupResponse }
     * 
     */
    public GetTimeGroupResponse createGetTimeGroupResponse() {
        return new GetTimeGroupResponse();
    }

    /**
     * Create an instance of {@link GetXmlByCatalogid }
     * 
     */
    public GetXmlByCatalogid createGetXmlByCatalogid() {
        return new GetXmlByCatalogid();
    }

    /**
     * Create an instance of {@link GetXmlByCatalogidResponse }
     * 
     */
    public GetXmlByCatalogidResponse createGetXmlByCatalogidResponse() {
        return new GetXmlByCatalogidResponse();
    }

    /**
     * Create an instance of {@link PushInterAllXml }
     * 
     */
    public PushInterAllXml createPushInterAllXml() {
        return new PushInterAllXml();
    }

    /**
     * Create an instance of {@link PushInterAllXmlResponse }
     * 
     */
    public PushInterAllXmlResponse createPushInterAllXmlResponse() {
        return new PushInterAllXmlResponse();
    }

    /**
     * Create an instance of {@link PushLargeXml }
     * 
     */
    public PushLargeXml createPushLargeXml() {
        return new PushLargeXml();
    }

    /**
     * Create an instance of {@link PushLargeXmlResponse }
     * 
     */
    public PushLargeXmlResponse createPushLargeXmlResponse() {
        return new PushLargeXmlResponse();
    }

    /**
     * Create an instance of {@link PushXml }
     * 
     */
    public PushXml createPushXml() {
        return new PushXml();
    }

    /**
     * Create an instance of {@link PushXmlBackflow }
     * 
     */
    public PushXmlBackflow createPushXmlBackflow() {
        return new PushXmlBackflow();
    }

    /**
     * Create an instance of {@link PushXmlBackflowInnerFun }
     * 
     */
    public PushXmlBackflowInnerFun createPushXmlBackflowInnerFun() {
        return new PushXmlBackflowInnerFun();
    }

    /**
     * Create an instance of {@link PushXmlBackflowInnerFunResponse }
     * 
     */
    public PushXmlBackflowInnerFunResponse createPushXmlBackflowInnerFunResponse() {
        return new PushXmlBackflowInnerFunResponse();
    }

    /**
     * Create an instance of {@link PushXmlBackflowResponse }
     * 
     */
    public PushXmlBackflowResponse createPushXmlBackflowResponse() {
        return new PushXmlBackflowResponse();
    }

    /**
     * Create an instance of {@link PushXmlBySM4 }
     * 
     */
    public PushXmlBySM4 createPushXmlBySM4() {
        return new PushXmlBySM4();
    }

    /**
     * Create an instance of {@link PushXmlBySM4Response }
     * 
     */
    public PushXmlBySM4Response createPushXmlBySM4Response() {
        return new PushXmlBySM4Response();
    }

    /**
     * Create an instance of {@link PushXmlInnerFun }
     * 
     */
    public PushXmlInnerFun createPushXmlInnerFun() {
        return new PushXmlInnerFun();
    }

    /**
     * Create an instance of {@link PushXmlInnerFunResponse }
     * 
     */
    public PushXmlInnerFunResponse createPushXmlInnerFunResponse() {
        return new PushXmlInnerFunResponse();
    }

    /**
     * Create an instance of {@link PushXmlResponse }
     * 
     */
    public PushXmlResponse createPushXmlResponse() {
        return new PushXmlResponse();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LoginByAccount }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link LoginByAccount }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "LoginByAccount")
    public JAXBElement<LoginByAccount> createLoginByAccount(LoginByAccount value) {
        return new JAXBElement<LoginByAccount>(_LoginByAccount_QNAME, LoginByAccount.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LoginByAccountBySM2Key }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link LoginByAccountBySM2Key }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "LoginByAccountBySM2Key")
    public JAXBElement<LoginByAccountBySM2Key> createLoginByAccountBySM2Key(LoginByAccountBySM2Key value) {
        return new JAXBElement<LoginByAccountBySM2Key>(_LoginByAccountBySM2Key_QNAME, LoginByAccountBySM2Key.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LoginByAccountBySM2KeyResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link LoginByAccountBySM2KeyResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "LoginByAccountBySM2KeyResponse")
    public JAXBElement<LoginByAccountBySM2KeyResponse> createLoginByAccountBySM2KeyResponse(LoginByAccountBySM2KeyResponse value) {
        return new JAXBElement<LoginByAccountBySM2KeyResponse>(_LoginByAccountBySM2KeyResponse_QNAME, LoginByAccountBySM2KeyResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LoginByAccountResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link LoginByAccountResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "LoginByAccountResponse")
    public JAXBElement<LoginByAccountResponse> createLoginByAccountResponse(LoginByAccountResponse value) {
        return new JAXBElement<LoginByAccountResponse>(_LoginByAccountResponse_QNAME, LoginByAccountResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AllDeleteAndAllAdd }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link AllDeleteAndAllAdd }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "allDeleteAndAllAdd")
    public JAXBElement<AllDeleteAndAllAdd> createAllDeleteAndAllAdd(AllDeleteAndAllAdd value) {
        return new JAXBElement<AllDeleteAndAllAdd>(_AllDeleteAndAllAdd_QNAME, AllDeleteAndAllAdd.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AllDeleteAndAllAddResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link AllDeleteAndAllAddResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "allDeleteAndAllAddResponse")
    public JAXBElement<AllDeleteAndAllAddResponse> createAllDeleteAndAllAddResponse(AllDeleteAndAllAddResponse value) {
        return new JAXBElement<AllDeleteAndAllAddResponse>(_AllDeleteAndAllAddResponse_QNAME, AllDeleteAndAllAddResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CrossNetCheck }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link CrossNetCheck }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "crossNetCheck")
    public JAXBElement<CrossNetCheck> createCrossNetCheck(CrossNetCheck value) {
        return new JAXBElement<CrossNetCheck>(_CrossNetCheck_QNAME, CrossNetCheck.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CrossNetCheckResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link CrossNetCheckResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "crossNetCheckResponse")
    public JAXBElement<CrossNetCheckResponse> createCrossNetCheckResponse(CrossNetCheckResponse value) {
        return new JAXBElement<CrossNetCheckResponse>(_CrossNetCheckResponse_QNAME, CrossNetCheckResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EndAddAllDeleteAll }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link EndAddAllDeleteAll }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "endAddAllDeleteAll")
    public JAXBElement<EndAddAllDeleteAll> createEndAddAllDeleteAll(EndAddAllDeleteAll value) {
        return new JAXBElement<EndAddAllDeleteAll>(_EndAddAllDeleteAll_QNAME, EndAddAllDeleteAll.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EndAddAllDeleteAllResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link EndAddAllDeleteAllResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "endAddAllDeleteAllResponse")
    public JAXBElement<EndAddAllDeleteAllResponse> createEndAddAllDeleteAllResponse(EndAddAllDeleteAllResponse value) {
        return new JAXBElement<EndAddAllDeleteAllResponse>(_EndAddAllDeleteAllResponse_QNAME, EndAddAllDeleteAllResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetCatalogidByMatterid }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetCatalogidByMatterid }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getCatalogidByMatterid")
    public JAXBElement<GetCatalogidByMatterid> createGetCatalogidByMatterid(GetCatalogidByMatterid value) {
        return new JAXBElement<GetCatalogidByMatterid>(_GetCatalogidByMatterid_QNAME, GetCatalogidByMatterid.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetCatalogidByMatteridResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetCatalogidByMatteridResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getCatalogidByMatteridResponse")
    public JAXBElement<GetCatalogidByMatteridResponse> createGetCatalogidByMatteridResponse(GetCatalogidByMatteridResponse value) {
        return new JAXBElement<GetCatalogidByMatteridResponse>(_GetCatalogidByMatteridResponse_QNAME, GetCatalogidByMatteridResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetProblemCollect }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetProblemCollect }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getProblemCollect")
    public JAXBElement<GetProblemCollect> createGetProblemCollect(GetProblemCollect value) {
        return new JAXBElement<GetProblemCollect>(_GetProblemCollect_QNAME, GetProblemCollect.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetProblemCollectResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetProblemCollectResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getProblemCollectResponse")
    public JAXBElement<GetProblemCollectResponse> createGetProblemCollectResponse(GetProblemCollectResponse value) {
        return new JAXBElement<GetProblemCollectResponse>(_GetProblemCollectResponse_QNAME, GetProblemCollectResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetSM2PublicKey }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetSM2PublicKey }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getSM2PublicKey")
    public JAXBElement<GetSM2PublicKey> createGetSM2PublicKey(GetSM2PublicKey value) {
        return new JAXBElement<GetSM2PublicKey>(_GetSM2PublicKey_QNAME, GetSM2PublicKey.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetSM2PublicKeyResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetSM2PublicKeyResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getSM2PublicKeyResponse")
    public JAXBElement<GetSM2PublicKeyResponse> createGetSM2PublicKeyResponse(GetSM2PublicKeyResponse value) {
        return new JAXBElement<GetSM2PublicKeyResponse>(_GetSM2PublicKeyResponse_QNAME, GetSM2PublicKeyResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetSM4Key }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetSM4Key }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getSM4Key")
    public JAXBElement<GetSM4Key> createGetSM4Key(GetSM4Key value) {
        return new JAXBElement<GetSM4Key>(_GetSM4Key_QNAME, GetSM4Key.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetSM4KeyResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetSM4KeyResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getSM4KeyResponse")
    public JAXBElement<GetSM4KeyResponse> createGetSM4KeyResponse(GetSM4KeyResponse value) {
        return new JAXBElement<GetSM4KeyResponse>(_GetSM4KeyResponse_QNAME, GetSM4KeyResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetTimeGroup }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetTimeGroup }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getTimeGroup")
    public JAXBElement<GetTimeGroup> createGetTimeGroup(GetTimeGroup value) {
        return new JAXBElement<GetTimeGroup>(_GetTimeGroup_QNAME, GetTimeGroup.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetTimeGroupResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetTimeGroupResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getTimeGroupResponse")
    public JAXBElement<GetTimeGroupResponse> createGetTimeGroupResponse(GetTimeGroupResponse value) {
        return new JAXBElement<GetTimeGroupResponse>(_GetTimeGroupResponse_QNAME, GetTimeGroupResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetXmlByCatalogid }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetXmlByCatalogid }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getXmlByCatalogid")
    public JAXBElement<GetXmlByCatalogid> createGetXmlByCatalogid(GetXmlByCatalogid value) {
        return new JAXBElement<GetXmlByCatalogid>(_GetXmlByCatalogid_QNAME, GetXmlByCatalogid.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetXmlByCatalogidResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetXmlByCatalogidResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "getXmlByCatalogidResponse")
    public JAXBElement<GetXmlByCatalogidResponse> createGetXmlByCatalogidResponse(GetXmlByCatalogidResponse value) {
        return new JAXBElement<GetXmlByCatalogidResponse>(_GetXmlByCatalogidResponse_QNAME, GetXmlByCatalogidResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushInterAllXml }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushInterAllXml }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushInterAllXml")
    public JAXBElement<PushInterAllXml> createPushInterAllXml(PushInterAllXml value) {
        return new JAXBElement<PushInterAllXml>(_PushInterAllXml_QNAME, PushInterAllXml.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushInterAllXmlResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushInterAllXmlResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushInterAllXmlResponse")
    public JAXBElement<PushInterAllXmlResponse> createPushInterAllXmlResponse(PushInterAllXmlResponse value) {
        return new JAXBElement<PushInterAllXmlResponse>(_PushInterAllXmlResponse_QNAME, PushInterAllXmlResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushLargeXml }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushLargeXml }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushLargeXml")
    public JAXBElement<PushLargeXml> createPushLargeXml(PushLargeXml value) {
        return new JAXBElement<PushLargeXml>(_PushLargeXml_QNAME, PushLargeXml.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushLargeXmlResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushLargeXmlResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushLargeXmlResponse")
    public JAXBElement<PushLargeXmlResponse> createPushLargeXmlResponse(PushLargeXmlResponse value) {
        return new JAXBElement<PushLargeXmlResponse>(_PushLargeXmlResponse_QNAME, PushLargeXmlResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXml }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXml }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXml")
    public JAXBElement<PushXml> createPushXml(PushXml value) {
        return new JAXBElement<PushXml>(_PushXml_QNAME, PushXml.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXmlBackflow }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXmlBackflow }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXmlBackflow")
    public JAXBElement<PushXmlBackflow> createPushXmlBackflow(PushXmlBackflow value) {
        return new JAXBElement<PushXmlBackflow>(_PushXmlBackflow_QNAME, PushXmlBackflow.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXmlBackflowInnerFun }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXmlBackflowInnerFun }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXmlBackflowInnerFun")
    public JAXBElement<PushXmlBackflowInnerFun> createPushXmlBackflowInnerFun(PushXmlBackflowInnerFun value) {
        return new JAXBElement<PushXmlBackflowInnerFun>(_PushXmlBackflowInnerFun_QNAME, PushXmlBackflowInnerFun.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXmlBackflowInnerFunResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXmlBackflowInnerFunResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXmlBackflowInnerFunResponse")
    public JAXBElement<PushXmlBackflowInnerFunResponse> createPushXmlBackflowInnerFunResponse(PushXmlBackflowInnerFunResponse value) {
        return new JAXBElement<PushXmlBackflowInnerFunResponse>(_PushXmlBackflowInnerFunResponse_QNAME, PushXmlBackflowInnerFunResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXmlBackflowResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXmlBackflowResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXmlBackflowResponse")
    public JAXBElement<PushXmlBackflowResponse> createPushXmlBackflowResponse(PushXmlBackflowResponse value) {
        return new JAXBElement<PushXmlBackflowResponse>(_PushXmlBackflowResponse_QNAME, PushXmlBackflowResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXmlBySM4 }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXmlBySM4 }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXmlBySM4")
    public JAXBElement<PushXmlBySM4> createPushXmlBySM4(PushXmlBySM4 value) {
        return new JAXBElement<PushXmlBySM4>(_PushXmlBySM4_QNAME, PushXmlBySM4 .class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXmlBySM4Response }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXmlBySM4Response }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXmlBySM4Response")
    public JAXBElement<PushXmlBySM4Response> createPushXmlBySM4Response(PushXmlBySM4Response value) {
        return new JAXBElement<PushXmlBySM4Response>(_PushXmlBySM4Response_QNAME, PushXmlBySM4Response.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXmlInnerFun }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXmlInnerFun }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXmlInnerFun")
    public JAXBElement<PushXmlInnerFun> createPushXmlInnerFun(PushXmlInnerFun value) {
        return new JAXBElement<PushXmlInnerFun>(_PushXmlInnerFun_QNAME, PushXmlInnerFun.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXmlInnerFunResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXmlInnerFunResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXmlInnerFunResponse")
    public JAXBElement<PushXmlInnerFunResponse> createPushXmlInnerFunResponse(PushXmlInnerFunResponse value) {
        return new JAXBElement<PushXmlInnerFunResponse>(_PushXmlInnerFunResponse_QNAME, PushXmlInnerFunResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PushXmlResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PushXmlResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pushXmlResponse")
    public JAXBElement<PushXmlResponse> createPushXmlResponse(PushXmlResponse value) {
        return new JAXBElement<PushXmlResponse>(_PushXmlResponse_QNAME, PushXmlResponse.class, null, value);
    }

}
