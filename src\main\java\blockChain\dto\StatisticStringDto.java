package blockChain.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@ApiModel(description = "统计DTO")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class StatisticStringDto {
    private String key;
    private String name;
    private String amount;

    public StatisticStringDto(String key, String name, Long amount) {
        this.key = key;
        this.name = name;
        this.amount = String.valueOf(amount);
    }
}
