package blockChain.facade.service;

import blockChain.bean.PageResponse;
import blockChain.controller.exception.NotFountException;
import blockChain.dto.ArticleDto;
import blockChain.entities.ArticleEntity;
import blockChain.mapper.ArticleDtoMapper;
import blockChain.repository.ArticlePredicates;
import blockChain.service.ArticleService;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR> <PERSON>
 * @date 2020/4/15 15:36
 */
@Service
@AllArgsConstructor
public class ArticleServiceFacade {

  private ArticleService service;

  @Transactional
  public void create(ArticleDto dto) {
      Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(0);
      Assert.notNull(currentUserId, "匿名用户不允许发送站内消息");
      Assert.hasText(dto.getTitle(), "标题不能为空");
      Assert.hasText(dto.getContent(), "内容不能为空");
      Assert.isTrue(dto.getTitle().length()<=255, "标题长度不能超过255");

    dto.setUuid(UUID.randomUUID().toString());
    ArticleEntity entity = ArticleDtoMapper.INSTANCE.toEntity(dto);
    service.save(entity);
  }

  @Transactional
  public void delete(String uuid) {
    ArticleEntity entity = service.findByUuid(uuid).orElseThrow(NotFountException::new);
    service.delete(entity);
  }

  @Transactional
  public void update(ArticleDto dto) {
    Integer currentUserId = SecurityUtils.getOptionalCurrentUserId().orElse(0);
    Assert.notNull(currentUserId, "匿名用户不允许发送站内消息");
    Assert.hasText(dto.getTitle(), "标题不能为空");
    Assert.hasText(dto.getContent(), "内容不能为空");
    Assert.isTrue(dto.getTitle().length()<=255, "标题长度不能超过255");

    ArticleEntity entity = service.findByUuid(dto.getUuid()).orElseThrow(NotFountException::new);
    ArticleDtoMapper.INSTANCE.update(dto, entity);
  }

  @Transactional(readOnly = true)
  public ArticleDto getOne(String uuid) {
    ArticleEntity entity = service.findByUuid(uuid).orElseThrow(NotFountException::new);
    return ArticleDtoMapper.INSTANCE.toDto(entity);
  }

  @Transactional(readOnly = true)
  public PageResponse<ArticleDto> queryArticle(Pageable pageable, String title, String content, LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {

    Page<ArticleEntity> page = service.findAll(ArticlePredicates.titleLikeAndContentLikeAndCreateTimeBetween(title, content, createTimeStart, createTimeEnd), pageable);

    return PageResponse.of((long)pageable.getPageNumber()+1, (long)pageable.getPageSize(), page.getTotalElements(), ArticleDtoMapper.INSTANCE.toDto(page));
  }
}
