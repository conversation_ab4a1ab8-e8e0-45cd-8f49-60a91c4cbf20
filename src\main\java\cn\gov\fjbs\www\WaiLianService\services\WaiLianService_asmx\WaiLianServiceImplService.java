/**
 * WaiLianServiceImplService.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx;

public interface WaiLianServiceImplService extends javax.xml.rpc.Service {
    public java.lang.String getWaiLianServiceAsmxAddress();

    public cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImpl getWaiLianServiceAsmx() throws javax.xml.rpc.ServiceException;

    public cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImpl getWaiLianServiceAsmx(java.net.URL portAddress) throws javax.xml.rpc.ServiceException;
}
