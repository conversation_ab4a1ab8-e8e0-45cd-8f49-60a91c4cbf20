package blockChain.repository;

import blockChain.entities.UserEntity;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends BaseRepository<UserEntity, Integer>, CustomizedUserRepository {

    /**
     * get表示查找一个
     *
     * @param username
     * @return
     */
    List<UserEntity> findAllByUserName(String username);

    Optional<UserEntity> getByUuid(String uuid);

    Long countAllByRegisterTimeBetween(LocalDateTime begin, LocalDateTime end);

    Long countAllByRegisterTimeBefore(LocalDateTime lastDateTime);

    List<UserEntity> findByCardId(String cardId);

    List<UserEntity> findByRealName(String realName);
}
