package blockChain.service;

import blockChain.entities.FormDataEntity;
import blockChain.repository.FormDataRepository;
import blockChain.utils.StringUtils;
import com.querydsl.core.BooleanBuilder;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static blockChain.entities.QFormDataEntity.formDataEntity;

/**
 * <AUTHOR>
 * @date 2024/9/3
 */
@Service
@AllArgsConstructor
public class FormDataService implements BaseService<FormDataRepository, FormDataEntity, Long> {
    private FormDataRepository repository;

    @Override
    public FormDataRepository getRepository() {
        return repository;
    }

    public List<FormDataEntity> findOne(String name, String certificateNumber, String idCardType) {
        return repository.findFirstByApplyNameAndCertificateNumberAndIdCardTypeOrderByIdDesc(name, certificateNumber, idCardType);
    }

    public List<FormDataEntity> findData(String applyName, String name, String certificateNumber, String countries, String province, String city, String county, String fullAddress) {
        BooleanBuilder builder = new BooleanBuilder();
        if (!StringUtils.isEmpty(applyName))
            builder.and(formDataEntity.applyName.eq(applyName));
        if (!StringUtils.isEmpty(name))
            builder.and(formDataEntity.name.eq(name));
        if (!StringUtils.isEmpty(certificateNumber))
            builder.and(formDataEntity.certificateNumber.eq(certificateNumber));
        if (!StringUtils.isEmpty(countries))
            builder.and(formDataEntity.countries.eq(countries));
        if (!StringUtils.isEmpty(countries))
            builder.and(formDataEntity.province.eq(province));
        if (!StringUtils.isEmpty(countries))
            builder.and(formDataEntity.city.eq(city));
        if (!StringUtils.isEmpty(county))
            builder.and(formDataEntity.county.eq(county));
        if (!StringUtils.isEmpty(fullAddress))
            builder.and(formDataEntity.fullAddress.eq(fullAddress));
        return repository.findAll(builder);
    }
}
