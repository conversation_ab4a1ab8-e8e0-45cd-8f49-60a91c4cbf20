package blockChain.mapper.transform;

import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper
public class CopyrightManagerTransform {
    public List<Integer> StringToIntegerList(String string){
        List<Integer> list = new ArrayList<>();
        if(string != null && !string.equals("")) {
            String[] temp = string.split(",");
            for (String str : temp) {
                list.add(Integer.parseInt(str));
            }
        }
        return list;
    }

    public String IntegerListToString(List<Integer> list){
        String string = "";
        if(list!=null) {
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    string = string + list.get(i);
                } else {
                    string = string + "," + list.get(i);
                }
            }
        }
        return string;
    }



}
