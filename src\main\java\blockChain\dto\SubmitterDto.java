package blockChain.dto;

import lombok.Data;

@Data
public class SubmitterDto {
    private Long id;
    private String copyrightName;// 提交者姓名
    private String copyrightOwner;// 联系人
    private String copyrightTelephone;// 电话号码
    private String copyrightPhone;// 电话
    private String copyrightAddress;// 详细住址
    private String copyrightCode;// 邮编
    private String copyrightEmail;// E-mail
    private String copyrightFax;// 传真

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCopyrightName() {
        return copyrightName;
    }

    public void setCopyrightName(String copyrightName) {
        this.copyrightName = copyrightName;
    }

    public String getCopyrightOwner() {
        return copyrightOwner;
    }

    public void setCopyrightOwner(String copyrightOwner) {
        this.copyrightOwner = copyrightOwner;
    }

    public String getCopyrightTelephone() {
        return copyrightTelephone;
    }

    public void setCopyrightTelephone(String copyrightTelephone) {
        this.copyrightTelephone = copyrightTelephone;
    }

    public String getCopyrightPhone() {
        return copyrightPhone;
    }

    public void setCopyrightPhone(String copyrightPhone) {
        this.copyrightPhone = copyrightPhone;
    }

    public String getCopyrightAddress() {
        return copyrightAddress;
    }

    public void setCopyrightAddress(String copyrightAddress) {
        this.copyrightAddress = copyrightAddress;
    }

    public String getCopyrightCode() {
        return copyrightCode;
    }

    public void setCopyrightCode(String copyrightCode) {
        this.copyrightCode = copyrightCode;
    }

    public String getCopyrightEmail() {
        return copyrightEmail;
    }

    public void setCopyrightEmail(String copyrightEmail) {
        this.copyrightEmail = copyrightEmail;
    }

    public String getCopyrightFax() {
        return copyrightFax;
    }

    public void setCopyrightFax(String copyrightFax) {
        this.copyrightFax = copyrightFax;
    }
}
