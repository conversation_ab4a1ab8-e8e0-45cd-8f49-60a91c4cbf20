package blockChain.nationalBean;

import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class CopyrightManageCancelBean {
	/**
	 * 撤销信息
	 */
	private CopyrightCancelBean copyrightcancle;
	/**
	 * 申请人
	 */
	private SubmitterBean submitter;
	/**
	 * 代理人
	 */
	private AgentBean agent;
	/**
	 * 登记机构Bean
	 */
	private OrganizationBean org;
	/**
	 * 添加数据用户id
	 */
//	private String user_name;
	private String userName;


	public CopyrightCancelBean getCopyrightcancle() {
		return copyrightcancle;
	}
	public void setCopyrightcancle(CopyrightCancelBean copyrightcancle) {
		this.copyrightcancle = copyrightcancle;
	}
	public SubmitterBean getSubmitter() {
		return submitter;
	}
	public void setSubmitter(SubmitterBean submitter) {
		this.submitter = submitter;
	}
	public AgentBean getAgent() {
		return agent;
	}
	public void setAgent(AgentBean agent) {
		this.agent = agent;
	}
//	public String getUser_name() {
//		return user_name;
//	}
//	public void setUser_name(String user_name) {
//		this.user_name = user_name;
//	}
	public OrganizationBean getOrg() {
		return org;
	}
	public void setOrg(OrganizationBean org) {
		this.org = org;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}

}
