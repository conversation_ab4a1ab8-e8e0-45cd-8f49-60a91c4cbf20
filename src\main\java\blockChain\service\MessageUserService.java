package blockChain.service;

import blockChain.entities.message.MessageUserEntity;
import blockChain.entities.message.QMessageUserEntity;
import blockChain.repository.MessageUserRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/12/18 17:46
 */
@Service
@AllArgsConstructor
public class MessageUserService implements BaseService<MessageUserRepository, MessageUserEntity, Long> {

  private final MessageUserRepository repository;

  @Override
  public MessageUserRepository getRepository() {
    return repository;
  }


  public Optional<MessageUserEntity> findByUserIdAndMessageUuid(Integer userId, String messageId) {
    QMessageUserEntity qMessageUserEntity = QMessageUserEntity.messageUserEntity;
    return repository.findOne(qMessageUserEntity.user.userId.eq(userId).and(qMessageUserEntity.message.uuid.eq(messageId)));
  }

  /**
   * 移除通知
   *
   * @param messageId messageId
   * @param userIds userIds
   */
  public void remove(Long messageId, List<Integer> userIds) {
    repository.deleteByMessage_IdAndUser_UserIdIn(messageId, userIds);
  }
}
