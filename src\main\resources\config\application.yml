# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

spring:
  application:
    name: ssdc-admin
  activiti:
    asyncExecutorEnabled: false
    asyncExecutorActivate: false
  servlet:
    multipart:
      enabled: true
      max-request-size: 500MB
      max-file-size: 500MB
  profiles:
    active: local,swagger
  jackson:
    serialization:
      write_dates_as_timestamps: false
      indent_output: true
    time-zone: GMT+8
  jpa:
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL57Dialect
    database-platform: org.hibernate.dialect.MySQL57Dialect
  mvc:
    favicon:
      enabled: false

management:
  endpoints:
    web:
      exposure:
        include: "info,health,prometheus"
  health:
    redis:
      enabled: false

ctsi:
  async:
    core-pool-size: 2
    max-pool-size: 50
    queue-capacity: 10000
  swagger:
    default-include-pattern: /.*
    title: ctsi API
    description: ctsi API documentation
    version: 0.0.1
    terms-of-service-url:
    contact-name:
    contact-url:
    contact-email:
    license:
    license-url:

  systemconfig:
    webfiles: /Users/<USER>/allSelf/server/webfiles

  jwtfilter:
    enable: false
  security:
    authentication:
      jwt:
        secret: my-secret-token-to-change-in-production
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  captcha:
    keyProvider: simpleCaptchaKeyProvider
    cache: guavaCaptchaCache
  access:
    groupId: ${project.groupId}
    projectName: ${project.artifactId}
    projectVersion: ${project.version}
    userName: liuqiang1
    enable: true
    url: https://s.ctbiyi.com/controller
    filePath: /logs
    scheduledRate: 180000
  userService: userServiceImpl
  sqlService: sqlServiceImpl
  metaDataService: metaDataServiceImpl
  menuService: menuServiceImpl
  bigDataService:

mybatis:
  configLocation: classpath:/mybatis-config.xml

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  pageSizeZero: true

server:
  servlet:
    context-path: /api/qkl

block:
  file:
    fileRoot: media/upload
    imgSize: 2097152 # 2*1024*1024
    fileSize: 20971520 # 20*1024*1024
    videoSize: 104857600 # 100*1024*1024
  images:
    imageRoot: certimages
  logs:
    filePath: ~/staff/logs/block/
  convergence:
    wsdl: http://220.160.52.148:809/Convergence/webservice/ConvergenceService?wsdl
    userName: hjpt_bqbkxt
    passWord: hjpt@321
    catalogId_manager: WEB736
    catalogId_owner: WEB735
    catalogId_author: KEY956977848
  autoEvaluationDay: 3
  evaluation:
    wsdl: http://api.zwfw.fujian.gov.cn:71/api-gateway/gateway/96hyvzxl/wailian/webservice/getSaveEvaluation?wsdl
    namespace: http://linewell.com/ws/
    serviceWsdl: http://api.zwfw.fujian.gov.cn:71/api-gateway/gateway/96hyvzxl/wailian/webservice/getServiceList?wsdl
    ruleWsdl: http://api.zwfw.fujian.gov.cn:71/api-gateway/gateway/96hyvzxl/wailian/webservice/getEvaluationRules?wsdl
  backend:
    isOn: true
    oauth:
      tokenUrl: https://fwzx.fjzwt.cn:28063/bcsp/backend/oauth/token
      clientId: cbbd577710c4456f8ba40691aa5118a5
      clientSecret: H3xJN-mr9jVZ7Ud8
    projectIdGet:
      url: https://fwzx.fjzwt.cn:28063/bcsp/backend/adapter/swbxx/unifyTestAddress/projectIdGet
    projectIdActive:
      url: https://fwzx.fjzwt.cn:28063/bcsp/backend/adapter/swbxx/unifyTestAddress/projectIdActive
    callbackUrl: http://**************:9000
    startWorkflow:
      url: https://fwzx.fjzwt.cn:28063/bcsp/backend/open/api/startWorkflow
      acceptId: 61c8f6b3fe3ee492e00642f1e6520148
      patchId: 979fe20a8e08a8eb435f62e387781376
      patchEndId: 4ea329d9b131e598e223ca25a01b621a
      transactId: 1592fe6327148ebbef45dedec99e3bac
      nodeTransId: b5381d4f6f6d70bd1c151b14278f2613
      baseInfoId: ccf02f30746cefdc9af327a4a6a298a3
      systemNo: 79A368DF3E585ABE3847756C67F57341
  info:
    deptCode: 11350000003591037R
    serviceCode: 003591504GF05462
    baseCode: 000739001000
    implementCode: 11350000003591037R2000739001000
    taskhandleitem: 11350000003591037R200073900100001
    belongSystem: 福建省作品自愿登记系统
    belongSystemRetry: F634628FE7BE9549400CAE29FC2D0A47
  mzt:
    url: http://**************:8901
    appId: 63c1f2dc-6b7e-44bc-ad84-7d73c0fd0243
    appSecret: 30f5a35484594f5e88b1c7f7d2c5bfc6
    appToken: /prod-api/outside/oauth2/getAppToken
    personalAuthorization: /outside/user/personalAuthorization
    PAVerification: /outside/user/PAVerification
    accessToken: /prod-api/outside/oauth2/token
    userInfo: /prod-api/outside/oauth2/getUserInfo
    verify: /prod-api/outside/oauth2/verify
    logout: /prod-api/outside/oauth2/appLogout
    personAgencyQuery: /prod-api/mc-personal-auth/outside/agency/personAgencyQuery
    companyAgencyQuery: /prod-api/mc-personal-auth/outside/agency/companyAgencyQuery
    attnAgencyQuery: /prod-api/mc-personal-auth/outside/agency/attnAgencyQuery
  zhongshu:
    paasid: app_zjszpzydjxt
    token: P1Voursy3SUuY5e5ecFVrTRl6VzN8612
    sm2_public_key: 046786463163cabe085eaddfecae7dc14f52277dd37dd47e4c3fe50fe3428190480556c16716ce7dca60d00ae28dab106dd701e201ccd1257dc8d039882590730b
    modelKey: 08fa436074bf4871bf577cb3c77ca527
    url: https://allinone-gateway.e-govt.cn:10001
    getFormData: /ebus/ability_znfwzs/api/form/getFormData
    formDataReflux: /ebus/ability_znfwzs/api/form/formDataReflux
  loginCodeValidity: 30 # 30min
  submitInterval: 30 # defalut 30s
  submitIntervalMax: 1000


application-runtime-properties:
  app-name: 区块链
  city-digital-define-code: ct
  province-digital-define-code: pn
  province-gat-digital-define-code: pn_gat
  province-fujian-digital-define-Pid: 409
  country-digital-define-Pid: 157
  cn-digital-define-code: cn1
  product-type-digital-define-code: zplx
  owner-type-digital-define-code: zzqrlb
