/**
 * ApasInfoServiceImplServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx;

public class ApasInfoServiceImplServiceLocator extends org.apache.axis.client.Service implements cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceImplService {

    public ApasInfoServiceImplServiceLocator() {
    }


    public ApasInfoServiceImplServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public ApasInfoServiceImplServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for ApasInfoServiceAsmx
    private java.lang.String ApasInfoServiceAsmx_address = "http://www.fjbs.gov.cn:82/WaiLianService/services/ApasInfoService.asmx";

    public java.lang.String getApasInfoServiceAsmxAddress() {
        return ApasInfoServiceAsmx_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String ApasInfoServiceAsmxWSDDServiceName = "ApasInfoService.asmx";

    public java.lang.String getApasInfoServiceAsmxWSDDServiceName() {
        return ApasInfoServiceAsmxWSDDServiceName;
    }

    public void setApasInfoServiceAsmxWSDDServiceName(java.lang.String name) {
        ApasInfoServiceAsmxWSDDServiceName = name;
    }

    public cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceImpl getApasInfoServiceAsmx() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(ApasInfoServiceAsmx_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getApasInfoServiceAsmx(endpoint);
    }

    public cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceImpl getApasInfoServiceAsmx(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceAsmxSoapBindingStub _stub = new cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceAsmxSoapBindingStub(portAddress, this);
            _stub.setPortName(getApasInfoServiceAsmxWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setApasInfoServiceAsmxEndpointAddress(java.lang.String address) {
        ApasInfoServiceAsmx_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceImpl.class.isAssignableFrom(serviceEndpointInterface)) {
                cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceAsmxSoapBindingStub _stub = new cn.gov.fjbs.www.WaiLianService.services.ApasInfoService_asmx.ApasInfoServiceAsmxSoapBindingStub(new java.net.URL(ApasInfoServiceAsmx_address), this);
                _stub.setPortName(getApasInfoServiceAsmxWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("ApasInfoService.asmx".equals(inputPortName)) {
            return getApasInfoServiceAsmx();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://www.fjbs.gov.cn:82/WaiLianService/services/ApasInfoService.asmx", "ApasInfoServiceImplService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://www.fjbs.gov.cn:82/WaiLianService/services/ApasInfoService.asmx", "ApasInfoService.asmx"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("ApasInfoServiceAsmx".equals(portName)) {
            setApasInfoServiceAsmxEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
