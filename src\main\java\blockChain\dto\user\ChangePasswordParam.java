package blockChain.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "变更密码请求参数类")
public class ChangePasswordParam {

	@ApiModelProperty(notes = "旧密码,未设置旧密码放空", example = "123456")
	private String passwordOld;

	@ApiModelProperty(notes = "新密码", example = "12345678", required = true)
	@NotBlank(message = "密码不能为空")
	@Length(min = 8, max = 20, message = "密码必须包含至少一个字母和数字，且长度在[8,20]个字之间")
	private String password;
}
