package cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx;

public class WaiLianServiceImplProxy implements cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImpl {
  private String _endpoint = null;
  private cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImpl waiLianServiceImpl = null;
  
  public WaiLianServiceImplProxy() {
    _initWaiLianServiceImplProxy();
  }
  
  public WaiLianServiceImplProxy(String endpoint) {
    _endpoint = endpoint;
    _initWaiLianServiceImplProxy();
  }
  
  private void _initWaiLianServiceImplProxy() {
    try {
      waiLianServiceImpl = (new cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImplServiceLocator()).getWaiLianServiceAsmx();
      if (waiLianServiceImpl != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)waiLianServiceImpl)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)waiLianServiceImpl)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (waiLianServiceImpl != null)
      ((javax.xml.rpc.Stub)waiLianServiceImpl)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public cn.gov.fjbs.www.WaiLianService.services.WaiLianService_asmx.WaiLianServiceImpl getWaiLianServiceImpl() {
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl;
  }
  
  public com.linewell.apas.wailian.monitor.SwapMonitor swapLog(java.lang.String xmlString, java.lang.String deptcode, java.lang.String wsspaction) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.swapLog(xmlString, deptcode, wsspaction);
  }
  
  public java.lang.String getCurTrafficControl(java.lang.String test) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.getCurTrafficControl(test);
  }
  
  public java.lang.String releaseQueue(java.lang.String sn) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.releaseQueue(sn);
  }
  
  public java.lang.String getProJid(java.lang.String deptcode, java.lang.String password, java.lang.String projectName) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.getProJid(deptcode, password, projectName);
  }
  
  public java.lang.String apasserviceGetByOrgcode(java.lang.String orgcode) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.apasserviceGetByOrgcode(orgcode);
  }
  
  public java.lang.Object[] reToJson(java.lang.Object[][] rs) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.reToJson(rs);
  }
  
  public java.lang.String apasserviceGetByOrgcodeAndLicenceName(java.lang.String orgcode, java.lang.String licenceName) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.apasserviceGetByOrgcodeAndLicenceName(orgcode, licenceName);
  }
  
  public java.lang.String apasserviceGetByLicenceNam(java.lang.String licenceName) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.apasserviceGetByLicenceNam(licenceName);
  }
  
  public java.lang.String newSubmit(java.lang.String deptcode, java.lang.String password, java.lang.String xmlString) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.newSubmit(deptcode, password, xmlString);
  }
  
  public java.lang.String submit(java.lang.String deptcode, java.lang.String password, java.lang.String xmlString) throws java.rmi.RemoteException{
    if (waiLianServiceImpl == null)
      _initWaiLianServiceImplProxy();
    return waiLianServiceImpl.submit(deptcode, password, xmlString);
  }
  
  
}