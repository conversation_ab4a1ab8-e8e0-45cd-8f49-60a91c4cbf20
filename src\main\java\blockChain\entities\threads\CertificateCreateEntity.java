package blockChain.entities.threads;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Created by x<PERSON><PERSON><PERSON> on 2020/10/23.
 */
@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name="tb_certificate_create")
public class CertificateCreateEntity
{
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String userName;

  @CreatedDate
  private LocalDateTime createTime;

  private LocalDateTime finishTime;

  @Column(columnDefinition = "text")
  private String result;

  private String resultCode;

  private String worksNum;

  private String productName;
}
