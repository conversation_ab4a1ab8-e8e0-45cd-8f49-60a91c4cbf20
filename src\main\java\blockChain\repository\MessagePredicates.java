package blockChain.repository;

import blockChain.dto.message.MessageDto;
import blockChain.entities.message.QMessageEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/10 9:51
 */
public final class MessagePredicates {
  public static Predicate pageQueryByTitleAndByContentAndCreateTimeBetween(String title, String content, LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {
    QMessageEntity qMessageEntity = QMessageEntity.messageEntity;

    BooleanBuilder builder = new BooleanBuilder();

    if (StringUtils.hasText(title)) {
      builder.and(qMessageEntity.title.contains(title));
    }
    if (StringUtils.hasText(content)) {
      builder.and(qMessageEntity.content.contains(content));
    }
    if(createTimeStart != null){
      builder.and(qMessageEntity.createTime.after(createTimeStart));
    }
    if(createTimeEnd != null){
      builder.and(qMessageEntity.createTime.before(createTimeEnd));
    }
    return builder;
  }
}
