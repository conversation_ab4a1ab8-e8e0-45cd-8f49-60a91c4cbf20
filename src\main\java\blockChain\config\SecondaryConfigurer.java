/*
package blockChain.config;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration

@EnableTransactionManagement

@EnableJpaRepositories(

  transactionManagerRef = "transactionManagerSecondary",

  entityManagerFactoryRef = "entityManagerFactorySecondary",

  basePackages = {"blockChain.secondResponsitory"})

public class SecondaryConfigurer {

  */
/*@Autowired
  private Environment env;

  private final DataSource secondaryDataSource;

  public SecondaryConfigurer(@Qualifier("secondaryDataSource") DataSource secondaryDataSource) {
    this.secondaryDataSource = secondaryDataSource;
  }

  @Resource
  private Properties jpaProperties;*//*

  @Bean(name = "entityManagerSecondary")
  public EntityManager entityManager(@Qualifier("entityManagerFactorySecondary") EntityManagerFactory factory) {
    return factory.createEntityManager();
  }

  @Bean(name = "entityManagerFactorySecondary")
  public LocalContainerEntityManagerFactoryBean entityManagerFactorySecondary (EntityManagerFactoryBuilder builder,@Qualifier("secondaryDataSource") DataSource dataSource) {
    LocalContainerEntityManagerFactoryBean entityManagerFactory = builder
      .dataSource(dataSource)
      .properties(hibernateProperties())
      .packages("blockChain.secondEntities") //设置实体类所在位置
      .persistenceUnit("secondaryPersistenceUnit")
      .build();
    return entityManagerFactory;
  }

  protected Map<String, String> hibernateProperties() {
    return new HashMap<String, String>() {
      {
        put("hibernate.dialect", "org.hibernate.dialect.MySQL57Dialect");
        put("hibernate.hbm2ddl.auto", "update");
        put("hibernate.current_session_context_class", "org.springframework.orm.hibernate5.SpringSessionContext");
      }
    };
  }
  */
/*private Map<String, String> getVendorProperties() {
    Map<String, String> jpaProperties = new HashMap<>(16);
    jpaProperties.put("hibernate.hbm2ddl.auto", "update");
    jpaProperties.put("hibernate.show_sql", env.getProperty("spring.jpa.show-sql"));
    jpaProperties.put("hibernate.format_sql", env.getProperty("spring.jpa.hibernate.format_sql"));
    jpaProperties.put("hibernate.dialect", env.getProperty("spring.jpa.hibernate.primary-dialect"));
    jpaProperties.put("hibernate.current_session_context_class", "org.springframework.orm.hibernate5.SpringSessionContext");
    return jpaProperties;
  }*//*



  @Bean(name = "transactionManagerSecondary")
  PlatformTransactionManager transactionManagerSecondary(@Qualifier("entityManagerFactorySecondary") LocalContainerEntityManagerFactoryBean factoryBean) {
    return new JpaTransactionManager(factoryBean.getObject());
  }

  */
/**
   * 新配置数据源wmsJpaQueryFactory
   *
   * @param entityManager
   * @return
   *//*

  @Bean
  public JPAQueryFactory wmsJpaQueryFactory(@Qualifier("entityManagerSecondary") EntityManager entityManager) {
    return new JPAQueryFactory(entityManager);
  }

}
*/
