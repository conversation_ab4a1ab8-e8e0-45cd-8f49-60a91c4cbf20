package blockChain.utils;

import blockChain.config.GatewayAutoConfiguration;
import blockChain.constant.ErrorConstant;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.*;

/**
 * Http 客户端工具类
 *
 * <AUTHOR>
 */
public class HttpClientUtils {
    static final int WARN_TIME_OUT = 3000;

    // 将每个路由基础的连接增加到
    public static final int DEFAULT_MAX_PER_ROUTE = 300;
    // 将最大连接数增加到
    public static final int MAX_TOTAL = 600;
    // 连接超时时间
    public static final int REQUEST_TIMEOUT = 2 * 1000;
    // 读取数据超时时间
    public static final int REQUEST_SOCKET_TIME = 30 * 1000;

    private static CloseableHttpClient closeableHttpClient;
    private static PoolingHttpClientConnectionManager connManager;

    static CloseableHttpClient getHttpClient() {
        if (null == closeableHttpClient) {
            synchronized (HttpClientUtils.class) {
                if (null == closeableHttpClient) {
                    // 采用绕过验证的方式处理https请求
                    Registry<ConnectionSocketFactory> socketFactoryRegistry = null;
                    try {
                        SSLContext sslcontext = createIgnoreVerifySSL();
                        // 设置协议http和https对应的处理socket链接工厂的对象
                        socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                                .register("http", PlainConnectionSocketFactory.INSTANCE)
                                .register("https", new SSLConnectionSocketFactory(sslcontext, NoopHostnameVerifier.INSTANCE)).build();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (null != socketFactoryRegistry) {
                        connManager = new PoolingHttpClientConnectionManager(
                                socketFactoryRegistry);
                    } else {
                        connManager = new PoolingHttpClientConnectionManager();
                    }
                    connManager.setDefaultMaxPerRoute(DEFAULT_MAX_PER_ROUTE);
                    connManager.setMaxTotal(MAX_TOTAL);
                    RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(REQUEST_SOCKET_TIME).setConnectTimeout(REQUEST_TIMEOUT).build();
                    // 创建自定义的httpclient对象
                    closeableHttpClient = HttpClients.custom().setConnectionManager(connManager).setDefaultRequestConfig(requestConfig).build();
                }
            }
        }
        return closeableHttpClient;
    }

    public static HttpResponse httpGet(String url) throws Exception {
        return httpGet(url, null, null, null);
    }

    /**
     * 发送http get请求
     *
     * @param url
     * @return
     * @throws Exception
     */
    public static HttpResponse httpGet(String url, Map<String, String> params) throws Exception {
        return httpGet(url, params, null, null);
    }

    /**
     * 发送http get请求
     *
     * @throws Exception
     */
    public static HttpResponse httpGet(String url, Map<String, String> params, Map<String, String> headers, String encode)
            throws Exception {
        if (encode == null) {
            encode = "utf-8";
        }
        HttpGet httpGet;
        if (null == params) {
            httpGet = new HttpGet(url);
        } else {
            URIBuilder uriBuilder = new URIBuilder(url);
            // 组织请求参数
            List<NameValuePair> paramList = new ArrayList<NameValuePair>();
            if (params != null && params.size() > 0) {
                Set<String> keySet = params.keySet();
                for (String key : keySet) {
                    paramList.add(new BasicNameValuePair(key, params.get(key)));
                }
            }
            uriBuilder.setParameters(paramList);
            httpGet = new HttpGet(uriBuilder.build());
        }
        // 设置header
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }
        return execute(httpGet, encode);
    }

    /**
     * 发送http post请求参数拼接方式
     *
     * @param url
     * @return
     */
    public static HttpResponse httpPostParams(String url) throws Exception {
        return httpPostParams(url, null, null, null);
    }

    /**
     * 发送http post请求参数拼接方式
     *
     * @param url
     * @return
     * @throws Exception
     */
    public static HttpResponse httpPostParams(String url, Map<String, String> params) throws Exception {
        return httpPostParams(url, params, null, null);
    }

    /**
     * 发送http post请求参数拼接方式
     *
     * @throws Exception
     */
    public static HttpResponse httpPostParams(String url, Map<String, String> params, Map<String, String> headers, String encode)
            throws Exception {
        if (encode == null) {
            encode = "utf-8";
        }
        HttpPost httpPost;
        if (null == params) {
            httpPost = new HttpPost(url);
        } else {
            URIBuilder uriBuilder = new URIBuilder(url);
            // 组织请求参数
            List<NameValuePair> paramList = new ArrayList<NameValuePair>();
            if (params != null && params.size() > 0) {
                Set<String> keySet = params.keySet();
                for (String key : keySet) {
                    paramList.add(new BasicNameValuePair(key, params.get(key)));
                }
            }
            uriBuilder.setParameters(paramList);
            httpPost = new HttpPost(uriBuilder.build());
        }
        // 设置header
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        return execute(httpPost, encode);
    }

    public static HttpResponse execute(HttpRequestBase request, String encode) throws Exception {
    	 long startTime = System.currentTimeMillis();
         GatewayAutoConfiguration.preExecute(request);
         HttpResponse response = new HttpResponse();
         // 获取结果实体
         HttpEntity entity = null;
        try {
            // 执行请求操作，并拿到结果（同步阻塞）
            CloseableHttpResponse httpResponse = getHttpClient().execute(request);
            entity = httpResponse.getEntity();
            // 按指定编码转换结果实体为String类型
            String content = EntityUtils.toString(entity, encode);
            response.setStatusCode(httpResponse.getStatusLine().getStatusCode());
            response.setReasonPhrase(httpResponse.getStatusLine().getReasonPhrase());
            response.setHeaders(httpResponse.getAllHeaders());
            response.setBody(content);
            boolean success;
            success = GatewayAutoConfiguration.afterExecute(response);
            if (!success) {
                // 失败时，重新请求
                return execute(request, encode);
            }
            return response;
        } finally {
            if (null != entity) {
                EntityUtils.consume(entity);
            }
            // 释放连接，回到连接池
            request.releaseConnection();
            long spend = System.currentTimeMillis() - startTime;
            if (spend >= WARN_TIME_OUT) {

            } else {

            }
        }
    }

    /**
     * 执行http请求
     *
     * @param request
     * @return
     * @throws Exception
     */
    static CloseableHttpResponse execute(HttpUriRequest request) throws Exception {
        GatewayAutoConfiguration.preExecute(request);
        // 执行请求操作，并拿到结果（同步阻塞）
        CloseableHttpResponse httpResponse = getHttpClient().execute(request);
        return httpResponse;
    }

    /**
     * 发送http post请求
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static HttpResponse httpPost(String url, String params) throws Exception {
        return httpPost(url, params, null, null);
    }

    /**
     * 发送http post请求
     *
     * @param url
     * @param params
     * @param headers
     * @param encode
     * @return
     * @throws Exception
     */
    public static HttpResponse httpPost(String url, String params, Map<String, String> headers, String encode) throws Exception {
        if (encode == null) {
            encode = "utf-8";
        }
        HttpPost httpPost = new HttpPost(url);

        // 设置header
        httpPost.setHeader("Content-type", "application/json;charset=UTF-8");
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        // 组织请求参数
        StringEntity stringEntity = new StringEntity(params, encode);
        httpPost.setEntity(stringEntity);
        return execute(httpPost, encode);
    }


    /**
     * 发送 http post 请求，参数以form表单键值对的形式提交。
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static HttpResponse httpPostForm(String url, Map<String, String> params) throws Exception {
        return httpPostForm(url, params, null, null);
    }

    /**
     * 发送 http post 请求，参数以form表单键值对的形式提交。
     *
     * @throws Exception
     */
    public static HttpResponse httpPostForm(String url, Map<String, String> params, Map<String, String> headers, String encode) throws Exception {
        if (encode == null) {
            encode = "utf-8";
        }
        HttpPost httpPost = new HttpPost(url);
        // 设置header
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        // 组织请求参数
        List<NameValuePair> paramList = new ArrayList<NameValuePair>();
        if (params != null && params.size() > 0) {
            Set<String> keySet = params.keySet();
            for (String key : keySet) {
                paramList.add(new BasicNameValuePair(key, params.get(key)));
            }
        }
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(paramList, encode));
        } catch (UnsupportedEncodingException e1) {

        }
        return execute(httpPost, encode);
    }

    /**
     * post form
     */
    public static org.apache.http.HttpResponse doPost(String url,
                                                      Map<String, String> headers,
                                                      Map<String, String> bodys)
            throws Exception {
        HttpClient httpClient = HttpClientBuilder.create().build();

        HttpPost request = new HttpPost(url);
        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }

        if (bodys != null) {
            List<NameValuePair> nameValuePairList = new ArrayList<>();

            for (String key : bodys.keySet()) {
                nameValuePairList.add(new BasicNameValuePair(key, bodys.get(key)));
            }
            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(nameValuePairList, "utf-8");
            formEntity.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
            request.setEntity(formEntity);
        }

        return httpClient.execute(request);
    }

    public static org.apache.http.HttpResponse post(String url, String params, Map<String, String> headers, String encode) throws Exception {
        HttpClient httpClient = HttpClientBuilder.create().build();
        if (encode == null) {
            encode = "utf-8";
        }
        HttpPost httpPost = new HttpPost(url);

        // 设置header
        httpPost.setHeader("Content-type", "application/json;charset=UTF-8");
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        // 组织请求参数
        StringEntity stringEntity = new StringEntity(params, encode);
        httpPost.setEntity(stringEntity);
        return httpClient.execute(httpPost);
    }

    public static org.apache.http.HttpResponse get(String url, Map<String, String> params, Map<String, String> headers) throws Exception {
        HttpClient httpClient = HttpClientBuilder.create().build();

        HttpGet httpGet;
        if (null == params) {
            httpGet = new HttpGet(url);
        } else {
            URIBuilder uriBuilder = new URIBuilder(url);
            // 组织请求参数
            List<NameValuePair> paramList = new ArrayList<NameValuePair>();
            if (params != null && params.size() > 0) {
                Set<String> keySet = params.keySet();
                for (String key : keySet) {
                    paramList.add(new BasicNameValuePair(key, params.get(key)));
                }
            }
            uriBuilder.setParameters(paramList);
            httpGet = new HttpGet(uriBuilder.build());
        }
        // 设置header
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }

        return httpClient.execute(httpGet);
    }

    /**
     * 发送http put请求
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static HttpResponse httpPutRaw(String url, Object params) throws Exception {
        return httpPutRaw(url, params, null, null);
    }

    /**
     * 发送http put请求
     *
     * @param url
     * @param encode
     * @return
     * @throws Exception
     */
    public static HttpResponse httpPutRaw(String url, Object params, Map<String, String> headers, String encode) throws Exception {
        if (encode == null) {
            encode = "utf-8";
        }
        HttpPut httpPut = new HttpPut(url);

        // 设置header
        httpPut.setHeader("Content-type", "application/json");
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPut.setHeader(entry.getKey(), entry.getValue());
            }
        }
        // 组织请求参数
        StringEntity stringEntity = new StringEntity(JsonUtil.toJson(params), encode);
        httpPut.setEntity(stringEntity);
        return execute(httpPut, encode);
    }

    /**
     * 发送http delete请求
     *
     * @param url
     * @return
     * @throws Exception
     */
    public static HttpResponse httpDelete(String url) throws Exception {
        return httpDelete(url, null, null);
    }

    /**
     * 发送http delete请求
     *
     * @throws IOException
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     */
    public static HttpResponse httpDelete(String url, Map<String, String> headers, String encode) throws Exception {
        if (encode == null) {
            encode = "utf-8";
        }
        HttpDelete httpDelete = new HttpDelete(url);
        // 设置header
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpDelete.setHeader(entry.getKey(), entry.getValue());
            }
        }
        return execute(httpDelete, encode);
    }

    /**
     * 关闭连接池
     */
    public static void destory() {
        try {
            getHttpClient().close();
        } catch (Exception e) {

        }
        closeableHttpClient = null;
    }

    /**
     * Http请求返回值
     *
     * <AUTHOR>
     */
    public static class HttpResponse {
        /**
         * 状态码
         */
        private int statusCode;
        /**
         * 请求头
         */
        private Header[] headers;
        /**
         * 实体内容
         */
        private String body;
        /**
         * 请求错误原因
         */
        private String reasonPhrase;

        public int getStatusCode() {
            return statusCode;
        }

        public void setStatusCode(int statusCode) {
            this.statusCode = statusCode;
        }

        public Header[] getHeaders() {
            return headers;
        }

        public void setHeaders(Header[] headers) {
            this.headers = headers;
        }

        public String getBody() {
            return body;
        }

        public void setBody(String body) {
            this.body = body;
        }

        public String getReasonPhrase() {
            return reasonPhrase;
        }

        public void setReasonPhrase(String reasonPhrase) {
            this.reasonPhrase = reasonPhrase;
        }

        /**
         * 判断请求是否成功
         *
         * @return
         */
        public boolean isSuccess() {
            return statusCode == ErrorConstant.OK;
        }

        /**
         * 获取请求失败描述
         *
         * @return
         */
        public String getErrorMsg() {
            if (null != headers) {
                for (Header header : headers) {
                    if ("X-Error-Code".equalsIgnoreCase(header.getName())) {
                        return header.toString();
                    }
                }
            }
            return reasonPhrase;
        }

        @Override
        public String toString() {
            return "HttpResponse{" +
                    "statusCode=" + statusCode +
                    ", headers=" + Arrays.toString(headers) +
                    ", body='" + body + '\'' +
                    ", reasonPhrase='" + reasonPhrase + '\'' +
                    '}';
        }
    }

    /**
     * 绕过验证
     *
     * @return
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     */
    public static SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sc = SSLContext.getInstance("TLSv1.2");

        // 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] paramArrayOfX509Certificate, String paramString)
                    throws CertificateException {
            }

            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] paramArrayOfX509Certificate, String paramString)
                    throws CertificateException {
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        sc.init(null, new TrustManager[]{trustManager}, null);
        return sc;
    }

    /**
     * 发送http get请求
     *
     * @param url
     * @return
     * @throws Exception
     */
    public static HttpResponse httpGetAuth(String url, Map<String, String> params) throws Exception {
        return httpGetAuth(url, params, null, null);
    }
    /**
     * 发送http get请求
     *
     * @throws Exception
     */
    public static HttpResponse httpGetAuth(String url, Map<String, String> params, Map<String, String> headers, String encode)
            throws Exception {
        if (encode == null) {
            encode = "utf-8";
        }
        HttpGet httpGet;
        if (null == params) {
            httpGet = new HttpGet(url);
        } else {
            URIBuilder uriBuilder = new URIBuilder(url);
            // 组织请求参数
            List<NameValuePair> paramList = new ArrayList<NameValuePair>();
            if (params != null && params.size() > 0) {
                Set<String> keySet = params.keySet();
                for (String key : keySet) {
                    paramList.add(new BasicNameValuePair(key, params.get(key)));
                }
            }
            uriBuilder.setParameters(paramList);
            httpGet = new HttpGet(uriBuilder.build());
        }
        // 设置header
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }
        return executeAuth(httpGet, encode);
    }

    public static HttpResponse executeAuth(HttpRequestBase request, String encode) throws Exception {
   	    long startTime = System.currentTimeMillis();

        HttpResponse response = new HttpResponse();
        // 获取结果实体
        HttpEntity entity = null;
       try {
           // 执行请求操作，并拿到结果（同步阻塞）
           CloseableHttpResponse httpResponse = getHttpClient().execute(request);
           entity = httpResponse.getEntity();
           // 按指定编码转换结果实体为String类型
           String content = EntityUtils.toString(entity, encode);
           response.setStatusCode(httpResponse.getStatusLine().getStatusCode());
           response.setReasonPhrase(httpResponse.getStatusLine().getReasonPhrase());
           response.setHeaders(httpResponse.getAllHeaders());
           response.setBody(content);
           if (response.getStatusCode() != ErrorConstant.OK) {

           }
           boolean success;
           success = GatewayAutoConfiguration.afterExecute(response);
           if (!success) {
               // 失败时，重新请求
               return executeAuth(request, encode);
           }
           return response;
       } finally {
           if (null != entity) {
               EntityUtils.consume(entity);
           }
           // 释放连接，回到连接池
           request.releaseConnection();
           long spend = System.currentTimeMillis() - startTime;
           if (spend >= WARN_TIME_OUT) {

           } else {

           }
       }
   }
}
