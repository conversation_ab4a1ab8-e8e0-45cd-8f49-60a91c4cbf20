package blockChain.service;

import blockChain.entities.PaperCertificateEntity;
import blockChain.repository.PaperCertificateRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/16 15:41
 */
@Service
@AllArgsConstructor
public class PaperCertificateService implements BaseService<PaperCertificateRepository, PaperCertificateEntity, Long>{
  private  PaperCertificateRepository repository;
  @Override
  public PaperCertificateRepository getRepository() {
    return repository;
  }

  public Optional<PaperCertificateEntity> findByUuid(String uuid) {
    return repository.findByUuid(uuid);
  }
}
