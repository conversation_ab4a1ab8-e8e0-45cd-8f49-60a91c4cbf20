package blockChain.dto.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/13 9:18
 */
@Getter
@Setter
@ApiModel("智能客服配置表")
public class QuestionAnswerMessageDto {

  @ApiModelProperty("UUID")
  @JsonProperty("id")
  private String uuid;

  @ApiModelProperty("触发关键字")
  private String key;

  @ApiModelProperty("回复答案")
  private String answer;

  private LocalDateTime createTime;

  private LocalDateTime createTimeStart;
  private LocalDateTime createTimeEnd;

}
