package blockChain.service;

import blockChain.entities.message.AlertMessage;
import blockChain.repository.AlertMessageRepository;
import com.querydsl.core.types.Predicate;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/9 12:08
 */
@Service
@AllArgsConstructor
public class AlertMessageService implements BaseService<AlertMessageRepository, AlertMessage, Long>{
  private AlertMessageRepository repository;

  @Override
  public AlertMessageRepository getRepository() {
    return repository;
  }

  public Optional<AlertMessage> findByUuid(String uuid) {
    return repository.findByUuid(uuid);
  }

}
