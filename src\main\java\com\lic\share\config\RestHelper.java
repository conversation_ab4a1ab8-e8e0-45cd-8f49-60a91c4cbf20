package com.lic.share.config;

import com.alibaba.fastjson.JSONObject;
import com.lic.share.params.RequestParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <pre>
 * ClassName RestHelper
 * Description
 * Author glq
 * Date 2020-6-2
 * </pre>
 */
@Component
public class RestHelper {

//    @Autowired
//    RestTemplate restTemplate;
    RestTemplate restTemplate = new RestTemplate();


    private String getRestTemplate(String url, String accountId, String sign) {
        HttpHeaders header = new HttpHeaders();
        header.set("Accept", "application/json");
        header.add("X-Account-Id", accountId);
        header.add("X-Sign", sign);
        HttpEntity<JSONObject> httpEntity = new HttpEntity(header);
        ResponseEntity<String> resEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        return resEntity.getBody();
    }

    //post的参数在RequestBody
    public String postRestTemplateByJson(String url, String accountId, String sign, String content) {
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON_UTF8);

        header.add("X-Account-Id", accountId);
        header.add("X-Sign", sign);

        HttpEntity<String> httpEntity = new HttpEntity<>(content, header);
        ResponseEntity<String> responseResult = restTemplate.postForEntity(url, httpEntity, String.class);
        return responseResult.getBody();
    }

    //post的参数在RequestBody
    public String postRestTemplateByJson(String url, String content) {
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<String> httpEntity = new HttpEntity<>(content, header);
        ResponseEntity<String> responseResult = restTemplate.postForEntity(url, httpEntity, String.class);
        return responseResult.getBody();
    }

    //post的参数在为表单形式
    public String postRestTemplateByFrom(String url, String accountId, String sign, LinkedMultiValueMap<String, String> map) {
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        if(StringUtils.isNoneBlank(accountId)) {
            header.add("X-Account-Id", accountId);
            header.add("X-Sign", sign);
        }

        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(map, header);
        JSONObject response = restTemplate.postForObject(url, httpEntity, JSONObject.class);
        return response.toJSONString();
    }

    public String postRestTemplateByFrom(String url, MultiValueMap<String, String> map) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(map, headers);
        JSONObject response = restTemplate.postForObject(url, httpEntity, JSONObject.class);
        return response.toJSONString();
    }
}
