package blockChain.controller;

import blockChain.bean.Constant;
import blockChain.bean.JwtResult;
import blockChain.bean.QueryParam;
import blockChain.config.SpringConfig;
import blockChain.dto.AgentDto;
import blockChain.dto.CopyrightOwnerDto;
import blockChain.dto.SubmitterDto;
import blockChain.dto.UserDto;
import blockChain.entities.CopyrightOwner;
import blockChain.entities.Digital;
import blockChain.entities.RoleEntity;
import blockChain.entities.UserEntity;
import blockChain.exception.EntityNotFoundException;
import blockChain.facade.service.DigitalServiceFacade;
import blockChain.facade.service.OutSideServiceFacade;
import blockChain.facade.service.UserServiceFacade;
import blockChain.utils.CollectionTools;
import com.ctsi.ssdc.model.UserForm;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.security.UserLoginValidator;
import com.ctsi.ssdc.security.jwt.JWTConfigurer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@Api("对接第三方用户管理")
@Slf4j
@RestController
@RequestMapping("apiuser2")
@RequiredArgsConstructor
public class LoginNewController {

    @Value("${ctsi.RSA-prikey:}")
    private String rsaPrikey = "";

    @Autowired
    private UserServiceFacade userServiceFacade;

    @Autowired(required = false)
    private UserLoginValidator userLoginValidator;

    @Autowired
    private DigitalServiceFacade digitalServiceFacade;
    private final OutSideServiceFacade outSideServiceFacade;

    private final SpringConfig config;
    private final static Logger logger = LoggerFactory.getLogger(LoginNewController.class);

    @ApiOperation("验证是否已登录")
    @GetMapping("loginlogin")
    public void loginlogin(HttpServletRequest request, HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Credentials", "true");
        String uri = config.getFrontUrl() + "api/qkl/apiuser2/checkSession";
        try {
//            response.sendRedirect("https://mztapp.fujian.gov.cn:8304/dataset/UnifiedController/checkLoginStatus.do" +
//                    "?checkbackurl=" + uri + "&callerCode=2c9bbe5d71ff3606017201801f3f75d72c9bbe5d71ff3606");
            response.sendRedirect("https://iam.e-govt.cn:8901/outside/oauth2/authorize?response_type=code&appId=843f5010-644d-479a-9b41-ef06d8d3e637&" +
                    "redirect_uri=" + uri + "&state=123456&login_type=0");
        } catch (IOException e) {
            log.error("error", e);
        }
    }

    @ApiOperation("验证是否已登录-new")
    @GetMapping("checkSession")
    public void checkSession(HttpServletRequest request, HttpServletResponse response) {
        // 跳转到 SSO 登录界面前，先判断自己本地 session 中是否存在用户信息，不存在则跳转到地址福建省统一身份认证平台
        response.setHeader("Access-Control-Allow-Credentials", "true");
        String uri = config.getFrontUrl() + "api/qkl/apiuser2/loginReturn";
        String loginflag = request.getParameter("loginflag");
        try {
            if (loginflag != null && "true".equals(loginflag)) {
                String trustticket = request.getParameter("trustticket");
                response.sendRedirect(config.getFrontUrl() + "api/qkl/apiuser2/loginReturn?trustticket=" + trustticket);
            } else {
                // 首先用户访问福建省统一身份认证平台
                response.sendRedirect("https://iam.e-govt.cn:8901/outside/oauth2/authorize?" +
                        "response_type=code&appId=" + config.getAppId() +
                        "&redirect_uri=" + uri +
                        "&state=123456&login_type=0");
            }
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    @ApiOperation("用户本系统登录验证-new")
    @GetMapping("loginReturn")
    public void loginReturn(HttpServletRequest request, HttpServletResponse response) {
        log.info("loginReturn : " + request.toString());
        String code = request.getParameter("code");
        UserEntity currentUser = null;
        if (!StringUtils.isEmpty(code)) {
            currentUser = userServiceFacade.updateProvinceUser2(code);
        }
        String uri;
        if (currentUser == null)
            uri = config.getFrontUrl() + "indexPage";
        else
            uri = config.getFrontUrl() + "indexPage?userName=" + currentUser.getUserName() + "&code=" + currentUser.getRandKey();
        try {
            response.sendRedirect(uri);
        } catch (IOException e) {
            log.error("error", e);
        }
    }

    @ApiOperation("获取用户登录信息")
    @PostMapping("loginIn")
    public ResponseEntity<UserController.JwtToken> loginIn(@Valid @RequestBody QueryParam queryParam, HttpServletRequest request) {
        //获取登陆人真实ip
        String ipAddr = CollectionTools.getIpAddr(request);

        String userName = queryParam.getString("userName");
        String randKey = queryParam.getString("code");

        if (StringUtils.isEmpty(userName) || StringUtils.isEmpty(randKey)) {
            throw new EntityNotFoundException("该用户登录已过期或未进行实名认证，请重新登录或登录闽政通APP进行实名认证后再进行登录。");
        }
        UserDto currentUser = userServiceFacade.getByUserName(userName);
        if (StringUtils.isEmpty(currentUser.getCardId()) || !randKey.equals(currentUser.getRandKey()) || LocalDateTime.now().isAfter(currentUser.getRandKeyExpireTime())) {
            throw new EntityNotFoundException("该用户登录已过期或未进行实名认证，请重新登录或登录闽政通APP进行实名认证后再进行登录。");
        }
        UserForm user = new UserForm();
        user.setPassword("abc123456@");
        user.setUsername(currentUser.getUserName());
        user.setRememberme(0);
        JwtResult jwtObject = userServiceFacade.identityAuthorize(user, userLoginValidator);
        String jwt = jwtObject.getJwt();

        HttpHeaders httpHeaders = new HttpHeaders();
        String token = JWTConfigurer.AUTHORIZATION_BEARER + jwt;

        httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, token);
        //组装用户信息
        CscpUserDetail userDetail = jwtObject.getUserDetail();
        UserDto userDto = userServiceFacade.findUserById(userDetail.getId());
        if (userDto.getIsWorkstation() != null && userDto.getIsWorkstation() == Constant.BYTE_TRUE)
            userDto.setRoleName("福建省版权登记" + userDto.getRoleName());
        //设置登录用户ip
        userDto.setIp(ipAddr);
        userServiceFacade.updateUser(userDto);
        CopyrightOwnerDto copyrightOwnerDto = new CopyrightOwnerDto();
        SubmitterDto submitterDto = new SubmitterDto();
        AgentDto agentDto = new AgentDto();
        //自动填充的著作权人和填报人信息
        setOwner(copyrightOwnerDto, submitterDto, agentDto, userDto);
        Map<String, Object> userMap = new HashMap<String, Object>() {{
            put("id", userDetail.getId());
            put("userName", userDetail.getUsername());
            put("authorities", userDetail.getAuthorities());
            put("visibleLevelCity", userDto.getVisibleLevelCity());
            put("visibleLevelProvince", userDto.getVisibleLevelProvince());
            put("visibleLevelCounty", userDto.getVisibleLevelCounty());
            put("isProxyer", userDto.getIsProxyer());
            put("realName", userDto.getRealName());
            put("userMessage", userDto);
            put("isManager", (userDto.getRoleId() == RoleEntity.ROLE_USER) ? false : true);
            put("owner", copyrightOwnerDto);
            put("submitter", submitterDto);
            put("homePageKey", userDto.getHomePageKey());
        }};
        //if(userDto.getIsProxyer()!=null && userDto.getIsProxyer()==2){
        userMap.put("agent", agentDto);
        //}

        // 添加log
        logger.info("用户登录：" + userDto.getUserName());
        return new ResponseEntity<>(new UserController.JwtToken(token, userMap), httpHeaders, HttpStatus.OK);
    }

    @ApiOperation("退出登录-new appLogout")
    @PostMapping("logout")
    public void loginout(HttpServletRequest request, HttpServletResponse response) {
        int currentUserId = SecurityUtils.getCurrentUserId();
        UserEntity user = userServiceFacade.findById(currentUserId);
        log.info("用户退出登录：" + user.getUserName());
        outSideServiceFacade.logout(user);
    }

    private int parseIntSafe(String number) {
        try {
            return Integer.parseInt(number);
        } catch (Exception e) {
            log.error("error", e);
        }
        return 0;
    }

    private void setOwner(CopyrightOwnerDto owner, SubmitterDto submitter, AgentDto
            agent, UserDto user) {
        owner.setCopyName(user.getRealName());
        owner.setCopyCountries(user.getCountryName());
        owner.setCopyProvince(user.getProvinceName());
        owner.setCopyCity(user.getCityName());
        owner.setCopyCounty(user.getCountyName());
        owner.setCopyCategory(user.getUserId());
        if (user.getIdentityKind() == 1) {
            owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.PEOPLE);
            owner.setCopyCertificate(user.getCardType());
        } else {
            if (user.getLegalPersionType() == null) {
                owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.OTHERORG);
                owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.OTHER2);
            } else {
                switch (user.getLegalPersionType()) {
                    case UserEntity.LegalPersionTypeValue.COMPANYPEOPLE:
                    case UserEntity.LegalPersionTypeValue.INDIVIDUAL:
                        owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.COMPANYPEOPLE);
                        owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.CODE);
                        break;
                    case UserEntity.LegalPersionTypeValue.SOCIALORG:
                        owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.SOCIALORG);
                        owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.SOCIALORGZS);
                        break;
                    case UserEntity.LegalPersionTypeValue.INSTITUTION:
                        if (user.getCardId().startsWith("11")) {
                            owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.GOVERNMENT);
                            owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.GOVERNMENTZS);
                        } else {// 事业单位 统一社会信用代码前2位：12
                            owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.INSTITUTION);
                            owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.INSTITUTIONZS);
                        }
                        break;
                    default:
                        owner.setCopyCategory(CopyrightOwner.CopyCategoryValue.OTHERORG);
                        owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.OTHER2);
                        break;
                }
            }
//      owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.CODE);
        }
        owner.setCopyIdCard(user.getCardId());
        // 证照调用去掉
//    CertificateEntity certificateEntity = new CertificateEntity();
//    if (config.getIsExtranet()) {
//      certificateEntity = certificateServiceFacade.getIndexByHolderCode(user.getCardId(), user.getRealName(), new ArrayList<Integer>(){{add(owner.getCopyCertificate());}});
//      if (StringUtils.isEmpty(certificateEntity.getId()) && owner.getCopyCertificate() == CopyrightOwner.CopyCertificateValue.IDCARD) {
//        certificateEntity = certificateServiceFacade.getIndexByHolderCode(user.getCardId(), user.getRealName(), new ArrayList<Integer>(){{add(CopyrightOwner.CopyCertificateValue.RESIDENT);}});
//        if (!StringUtils.isEmpty(certificateEntity.getId()))
//          owner.setCopyCertificate(CopyrightOwner.CopyCertificateValue.RESIDENT);
//      }
//    }
//    owner.setCopyCertificateZM(certificateEntity);
        //设置著作权人地址字符串
        if (user.getCountryName() != null && user.getProvinceName() != null && user.getCityName() != null &&
                !user.getCountryName().isEmpty() && !user.getProvinceName().isEmpty() && !user.getCityName().isEmpty()) {
            Digital country = digitalServiceFacade.getById(parseIntSafe(user.getCountryName()));
            Digital province = digitalServiceFacade.getById(parseIntSafe(user.getProvinceName()));
            Digital city = digitalServiceFacade.getById(parseIntSafe(user.getCityName()));
            String copyAreaNamesStr = country.getDict_name() + "," + province.getDict_name() + "," + city.getDict_name();
            if (user.getCountyName() != null && !user.getCountyName().isEmpty()) {
                Digital county = digitalServiceFacade.getById(parseIntSafe(user.getCountyName()));
                copyAreaNamesStr += "," + county.getDict_name();
            }
            owner.setCopyAreaNamesStr(copyAreaNamesStr);
        }

        submitter.setCopyrightName(user.getRealName());
        submitter.setCopyrightAddress(user.getAddress());
        submitter.setCopyrightPhone(user.getPhoneNum());
        submitter.setCopyrightEmail(user.getEmail());

        agent.setAgentName(user.getRealName());
        agent.setAgentPhone(user.getPhoneNum());
        agent.setAgentAddress(user.getAddress());
        agent.setAgentEmail(user.getEmail());
    }

    @ApiOperation("获取用户登录信息-new 验证用户令牌 verify")
    @PostMapping("checkToken")
    public ResponseEntity<Map<String, Object>> checkToken() {
//        logger.info("获取用户登录信息-new 验证用户令牌 verify");
        int currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == 0)
            return new ResponseEntity<Map<String, Object>>(HttpStatus.UNAUTHORIZED);

        UserEntity user = userServiceFacade.findById(currentUserId);
        Map<String, Object> resultMap = outSideServiceFacade.verify(user);
        return new ResponseEntity<Map<String, Object>>(resultMap, HttpStatus.OK);
    }
}
