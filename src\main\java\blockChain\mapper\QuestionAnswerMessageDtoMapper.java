package blockChain.mapper;

import blockChain.bean.PageResponse;
import blockChain.dto.message.AlertMessageDto;
import blockChain.dto.message.QuestionAnswerMessageDto;
import blockChain.entities.message.QuestionAnswerMessageEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/13 9:53
 */
@Mapper(config = CommonConfig.class)
public interface QuestionAnswerMessageDtoMapper {
  QuestionAnswerMessageDtoMapper INSTANCE = Mappers.getMapper(QuestionAnswerMessageDtoMapper.class);

  @Mappings({
    @Mapping(target = "uuid", ignore = true),
    @Mapping(target = "id", ignore = true),
    @Mapping(target = "createTime", ignore = true),
    @Mapping(target = "updateTime", ignore = true),
  })
  QuestionAnswerMessageEntity toEntity(QuestionAnswerMessageDto dto);

  @Mappings({
    @Mapping(target = "uuid", ignore = true),
    @Mapping(target = "id", ignore = true),
    @Mapping(target = "createTime", ignore = true),
    @Mapping(target = "updateTime", ignore = true),
  })
  void update(QuestionAnswerMessageDto dto,@MappingTarget QuestionAnswerMessageEntity entity);

  QuestionAnswerMessageDto toDto(QuestionAnswerMessageEntity entity);

  default PageResponse<QuestionAnswerMessageDto> toDto(Page<QuestionAnswerMessageEntity> page){
    List<QuestionAnswerMessageDto> list = toDto(page.getContent());
    return PageResponse.of((long)page.getPageable().getPageNumber()+1, (long)page.getPageable().getPageSize(), page.getTotalElements(), list);
  }

  List<QuestionAnswerMessageDto> toDto(List<QuestionAnswerMessageEntity> content);
}
