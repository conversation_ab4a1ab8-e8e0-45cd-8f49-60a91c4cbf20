package blockChain.entities;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 闽政通应用令牌表
 *
 * <AUTHOR>
 * @date 2024/9/2
 */
@Entity
@Data
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
@Table(name = "tb_app_token")
public class AppTokenEntity {

    /**
     * 应用令牌ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * appToken值
     */
    private String appToken;
    /**
     * 过期时间
     */
    private LocalDateTime expirationTime;
    /**
     * 创建时间
     */
    @CreatedDate
    private LocalDateTime createTime;
}
