package blockChain.utils;
import javax.xml.bind.DatatypeConverter;
import java.io.*;

/**
 * <AUTHOR>
 * @date 2019/12/1 23:30
 */
public class ImageUtil {

    //十六进制字符串转图片
    public static void Hex2Image (String hex2) throws Exception {
        byte[] hex = DatatypeConverter.parseHexBinary(hex2.replace("0x",""));
        //解压字节
        hex = BZip2Utils.decompress(hex);
        InputStream is=new ByteArrayInputStream(hex);
        InputStreamReader isr = new InputStreamReader(is);
        BufferedReader br = new BufferedReader(isr);
        String str = null;
        StringBuilder sb = new StringBuilder();
        while ((str = br.readLine()) != null) {
            System.out.println(str);
            sb.append(str);
        }
        saveToImgFile(sb.toString().toUpperCase(),"f://dd.jpg");
    }
    private static void saveToImgFile(String src,String output){
        if(src==null||src.length()==0){
            return;
        }
        try{
            FileOutputStream out = new FileOutputStream(new File(output));
            byte[] bytes = src.getBytes();
            for(int i=0;i<bytes.length;i+=2){
                out.write(charToInt(bytes[i])*16+charToInt(bytes[i+1]));
            }
            out.close();
        }catch(Exception e){
            e.printStackTrace();
        }
    }
    private static int charToInt(byte ch){
        int val = 0;
        if(ch>=0x30&&ch<=0x39){
            val=ch-0x30;
        }else if(ch>=0x41&&ch<=0x46){
            val=ch-0x41+10;
        }
        return val;
    }

    //图片转十六进制字符串
    public static String ImageToHex(String imgUrl) throws Exception {

        try{
            StringBuffer sb = new StringBuffer();
            FileInputStream fis = new FileInputStream(imgUrl);
            BufferedInputStream bis = new BufferedInputStream(fis);
            java.io.ByteArrayOutputStream bos=new java.io.ByteArrayOutputStream();

            byte[] buff=new byte[1024];
            int len=0;
            while((len=fis.read(buff))!=-1){
                bos.write(buff,0,len);
            }
            //得到图片的字节数组
            byte[] result=bos.toByteArray();
            //压缩字节
            result = BZip2Utils.compress(result);

            //System.out.println("++++"+byte2HexStr(result));
            //字节数组转成十六进制
            //String str=byte2HexStr(result);
            String str= MD5Util.getMD5EncodeStr(result);
            return "0x"+str;
        }catch(IOException e){
            return null;
        }

    }

    //文件转十六进制字符串
    public static String FileToHex(String url) throws Exception {

        try{
            StringBuffer sb = new StringBuffer();
            FileInputStream fis = new FileInputStream(url);
            BufferedInputStream bis = new BufferedInputStream(fis);
            java.io.ByteArrayOutputStream bos=new java.io.ByteArrayOutputStream();

            byte[] buff=new byte[1024];
            int len=0;
            while((len=fis.read(buff))!=-1){
                bos.write(buff,0,len);
            }
            //得到图片的字节数组
            byte[] result=bos.toByteArray();
            //压缩字节
            result = BZip2Utils.compress(result);

            //System.out.println("++++"+byte2HexStr(result));
            //字节数组转成十六进制
            //String str=byte2HexStr(result);
            return byteToHex(result);
        }catch(IOException e){
            return null;
        }

    }

    //十六进制字符串转文件
    public static void HextoFile (String hex2,String fileUrl) throws Exception {
        byte[] hex = DatatypeConverter.parseHexBinary(hex2.replace("0x",""));
        //解压字节
        hex = BZip2Utils.decompress(hex);
        InputStream is=new ByteArrayInputStream(hex);
        InputStreamReader isr = new InputStreamReader(is);
        BufferedReader br = new BufferedReader(isr);
        String str = null;
        StringBuilder sb = new StringBuilder();
        while ((str = br.readLine()) != null) {
            System.out.println(str);
            sb.append(str);
        }
        saveToImgFile(sb.toString().toUpperCase(),fileUrl);
    }

    public static String byteToHex(byte[] bytes){
        byte[] result = bytes;
        return "0x"+DatatypeConverter.printHexBinary(result);
    }

    public static String hextoString(String hex){
        String temp = hex.replaceFirst("0x","");
        byte[] result = DatatypeConverter.parseHexBinary(temp);
        return new String(result);
    }

    /*
     *  实现字节数组向十六进制的转换方法一
     */
    public static String byte2HexStr(byte[] b) {
        String hs="";
        String stmp="";
        for (int n=0;n<b.length;n++) {
            stmp=(Integer.toHexString(b[n] & 0XFF));
            if (stmp.length()==1) hs=hs+"0"+stmp;
            else hs=hs+stmp;
        }
        return hs.toUpperCase();
    }

    private static byte uniteBytes(String src0, String src1) {
        byte b0 = Byte.decode("0x" + src0).byteValue();
        b0 = (byte) (b0 << 4);
        byte b1 = Byte.decode("0x" + src1).byteValue();
        byte ret = (byte) (b0 | b1);
        return ret;
    }
    /*
     *实现字节数组向十六进制的转换的方法二
     */
    public static String bytesToHexString(byte[] src){

        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();

    }

}
