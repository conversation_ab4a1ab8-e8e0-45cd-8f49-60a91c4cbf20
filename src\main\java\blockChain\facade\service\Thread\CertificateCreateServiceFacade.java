package blockChain.facade.service.Thread;

import blockChain.entities.pictureCompare.PictureCompareEntity;
import blockChain.entities.threads.CertificateCreateEntity;
import blockChain.service.PictureCompareService;
import blockChain.service.Thread.CertificateCreateService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2019/11/22 16:54
 */
@Service
@AllArgsConstructor
public class CertificateCreateServiceFacade {
    @Autowired
    private CertificateCreateService certificateCreateService;

    @Transactional(rollbackFor = RuntimeException.class)
    public void save(CertificateCreateEntity certificateCreateEntity){
        certificateCreateService.save(certificateCreateEntity);
    }

}
