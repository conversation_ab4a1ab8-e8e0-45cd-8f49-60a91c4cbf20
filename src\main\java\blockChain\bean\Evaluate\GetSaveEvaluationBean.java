package blockChain.bean.Evaluate;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * Created by epcsoft on 2020/8/3.
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "Case")
@Data
public class GetSaveEvaluationBean implements Serializable
{
  private static final long serialVersionUID = 1L;
  String  sysNO;//	系统编号（服务接入后提供）	String	是
  String  businessCode;//	省网事项编码	String	是
  String  businessName;//	省网事项名称	String	是
  String  projectNo;//	办件申报号	String	是
  Integer  proStatus;//	办理状态（待受理=1，已受理=2，已办结=3），填数字。	String	是
  String  regionCode;//	6位行政区划编码，详见附件：区划编码	String	是
  String  acceptDate;//	受理时间，格式：yyyy-MM-dd HH:mm:ss	String	否(当proStatus=2、3时必填)
  Integer  userProp;//	申请人类型（自然人=1，企业法人=2，事业法人=3，社会组织法人=4，非法人企业=5，行政机关=6，其他组织=9），填数字。	String	是
  String  userName;//	申请单位名称/申请人名称	String	是
  String  userPageType;//	申请人证件类型，详见附件：证件类型	String	是
  String  certKey;//	申请人证件号码 	String	是
  String handleUserName;//	代理人姓名 	String	否（非本人或企业法人时，必填）
    String handleUserPageType;
    String handleUserPageCode;
  String  handleUserPage;
  //String  Type;//	代理人证件类型，详见附件：证件类型	String	否（非本人或企业法人时，必填）
  //String  Code;//	代理人证件号码	String	否（非本人或企业法人时，必填）
  String  resultDate;//	办结时间，格式：yyyy-MM-dd HH:mm:ss	String	否(当proStatus=3时必填)
  String  serviceTime;//	服务时间，格式：yyyy-MM-dd HH:mm:ss	String	是
  Integer  evaluateType;//	评价类型（第一次主动评价=1，追加评价=2），填数字	String	是
  String  projectName;//	办件名称	String	是
  String  nodeName;//	环节名称	String	是
  String  contactMobile;//	评价人手机号码	String	是
  Integer  pf;//	评价渠道（pc端=1，移动服务端=2，二维码=3，政务大厅平板电脑=4，政务大厅自助终端=5，电话=6，短信=7），填数字	String	是
  Integer  type;//	评价地址网络方式（互联网=1，政务信息网=2，政务外网公共区=3），填数字	String	是
  Integer  alternate;//	整体满意度（1-5）	String	是（值为1和2时，评价详情和文字评价至少一项必填）
  String  appraisald;//	评价详情（勾选的评价详情编号，勾选多个用逗号隔开。例：501,503,504）	String	否
  Integer  appraisaldnum;//	评价详情勾选数量	String	Appraisald不为空时必填
  String  writingevalua;//	文字评价 	String	否
  String  assessTime;//	评价时间，格式:yyyy-MM-dd HH:mm:ss	String	是

  public String toXmlString() {
    return "<?xml version=\"1.0\" encoding=\"GB2312\"?>"
            + "<Case>"
            + "<sysNO>" +sysNO+ "</sysNO>"
            + "<businessCode>" +businessCode+ "</businessCode>"
            + "<businessName>" +businessName+ "</businessName>"
            + "<projectNo>" +projectNo+ "</projectNo>"
            + "<proStatus>" +proStatus+ "</proStatus>"
            + "<regionCode>" +regionCode+ "</regionCode>"
            + "<acceptDate>" +acceptDate+ "</acceptDate>"
            + "<userProp>" +userProp+ "</userProp>"
            + "<userName>" +userName+ "</userName>"
            + "<userPageType>" +userPageType+ "</userPageType>"
            + "<certKey>" +certKey+ "</certKey>"
            + "<handleUserName>" +handleUserName+ "</handleUserName>"
            + "<handleUserPageType>" +handleUserPageType+ "</handleUserPageType>"
            + "<handleUserPageCode>" +handleUserPageCode+ "</handleUserPageCode>"
            + "<handleUserPage>" +handleUserPage+ "</handleUserPage>"
            + "<resultDate>" +resultDate+ "</resultDate>"
            + "<serviceTime>" +serviceTime+ "</serviceTime>"
            + "<evaluateType>" +evaluateType+ "</evaluateType>"
            + "<projectName>作品自愿登记：" +projectName+ "</projectName>" // 纯数字不能评价
            + "<nodeName>" +nodeName+ "</nodeName>"
            + "<contactMobile>" +contactMobile+ "</contactMobile>"
            + "<pf>" +pf+ "</pf>"
            + "<type>" +type+ "</type>"
            + "<alternate>" +alternate+ "</alternate>"
            + "<appraisald>" +appraisald+ "</appraisald>"
            + "<appraisaldnum>" +appraisaldnum+ "</appraisaldnum>"
            + "<writingevalua>" +writingevalua+ "</writingevalua>"
            + "<assessTime>" + assessTime+ "</assessTime>"
            + "</Case>";
  }
}
