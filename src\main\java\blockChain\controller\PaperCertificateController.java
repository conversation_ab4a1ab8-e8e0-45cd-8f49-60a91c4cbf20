package blockChain.controller;

import blockChain.bean.BaseResponseDto;
import blockChain.bean.PageResponse;
import blockChain.dto.PaperCertificateDto;
import blockChain.dto.query.PaperCertificateDtoQueryParam;
import blockChain.facade.service.PaperCertificateServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2020/4/16 15:20
 */
@Api("纸质证书申请")
@Slf4j
@RestController
@RequestMapping("paper_cert")
@AllArgsConstructor
public class PaperCertificateController {

  private PaperCertificateServiceFacade serviceFacade;

  @ApiOperation(value = "申请", nickname = "createPaperCertificate", notes = "Creates a new instance of a `PaperCertificate`.", tags={ "纸质证书申请", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("create")
  public ResponseEntity<BaseResponseDto> createPaperCertificate(@Valid @RequestBody PaperCertificateDto dto) {
     serviceFacade.create(dto);
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

  @ApiOperation(value = "修改我的申请", nickname = "updatePaperCertificate", notes = "只允许修改REJECT和AWAITING状态的申请", tags={ "纸质证书申请", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("update")
  public ResponseEntity<BaseResponseDto> update(@Valid @RequestBody PaperCertificateDto dto) {
    serviceFacade.update(dto.getUuid(), dto.getReason());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }

  @ApiOperation(value = "删除申请", nickname = "deletePaperCertificate", notes = "删除的时候只要传入UUID即可", tags={ "纸质证书申请", })
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("remove")
  public ResponseEntity<BaseResponseDto> deletePaperCertificate(@Valid @RequestBody PaperCertificateDto dto) {
    serviceFacade.delete(dto.getUuid());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }

  @ApiOperation(value = "处理申请", nickname = "handlePaperCertificate", notes = "删除的时候只要传入UUID即可", tags={ "纸质证书申请", })
  @ApiResponses(value = {
    @ApiResponse(code = 204, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("handle")
  public ResponseEntity<BaseResponseDto> handlePaperCertificate(@Valid @RequestBody PaperCertificateDto dto) {
    serviceFacade.handle(dto.getUuids(), dto.getRejectReason(), dto.getStatus());
    return BaseResponseDto.ok(HttpStatus.NO_CONTENT);
  }

  @ApiOperation(value = "查询‘我’的申请", nickname = "queryPaperCertificate", notes = "query", tags={ "纸质证书申请", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("mine/query")
  public ResponseEntity<PageResponse<PaperCertificateDto>> queryMinePaperCertificate(@Valid @RequestBody PaperCertificateDtoQueryParam dto) {
    PageResponse<PaperCertificateDto> page = serviceFacade.queryMinePaperCertificate(dto);
    return ResponseEntity.ok(page);
  }

  @ApiOperation(value = "查询所有的申请", nickname = "queryPaperCertificate", notes = "query", tags={ "纸质证书申请", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("query")
  public ResponseEntity<PageResponse<PaperCertificateDto>> queryPaperCertificate(@Valid @RequestBody PaperCertificateDtoQueryParam dto) {
    PageResponse<PaperCertificateDto> page = serviceFacade.queryPaperCertificate(dto);
    return ResponseEntity.ok(page);
  }

  @ApiOperation(value = "查询所有的申请（审核通过的）", nickname = "queryPaperCertificate", notes = "query", tags={ "纸质证书申请", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("passed/query")
  public ResponseEntity<PageResponse<PaperCertificateDto>> queryPaperCertificateForPassed(@Valid @RequestBody PaperCertificateDtoQueryParam dto) {
    PageResponse<PaperCertificateDto> page = serviceFacade.queryPaperCertificateForPassed(dto);
    return ResponseEntity.ok(page);
  }

  @ApiOperation(value = "查询一个", nickname = "getOne", notes = "query", tags={ "纸质证书申请", })
  @ApiResponses(value = {
    @ApiResponse(code = 200, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("get")
  public ResponseEntity<PaperCertificateDto> getOne(@Valid @RequestBody PaperCertificateDto dto) {
    PaperCertificateDto page = serviceFacade.getOne(dto.getUuid());
    return ResponseEntity.ok(page);
  }


  @ApiOperation(value = "记录或更新物流信息", nickname = "updateLogisticsNum", notes = "Creates a new instance of a `PaperCertificate`.", tags={ "纸质证书申请", })
  @ApiResponses(value = {
    @ApiResponse(code = 201, message = "Successful response.", response = BaseResponseDto.class) })
  @PostMapping("logistics/update")
  public ResponseEntity<BaseResponseDto> updateLogisticsNum(@Valid @RequestBody PaperCertificateDto dto) {
    serviceFacade.updateLogisticsNum(dto.getUuid(), dto.getLogisticsNum());
    return BaseResponseDto.ok(HttpStatus.ACCEPTED);
  }

}
