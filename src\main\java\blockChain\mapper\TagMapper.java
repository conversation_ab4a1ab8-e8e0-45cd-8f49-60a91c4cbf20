package blockChain.mapper;

import blockChain.dto.TagDto;
import blockChain.entities.RoleEntity;
import blockChain.entities.TagEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/05/2022/5/24
 */
@Mapper(config = CommonConfig.class, uses = {MenuMapper.class})
public interface TagMapper {

  TagMapper INSTANCE = Mappers.getMapper(TagMapper.class);

  TagDto toTagDto(TagEntity tag);

  List<TagDto> toTagDtoList(List<TagEntity> tagList);

  @Mapping(target = "name", source = "roleName")
  TagEntity toTagEntity(RoleEntity roleEntity);

  List<TagEntity> toTagEntityList(List<RoleEntity> roleEntityList);
}
